// ملف JavaScript للتقارير الحديثة
alert('تم تحميل ملف JavaScript بنجاح');

// متغيرات عامة
let currentCategory = null;
let currentFilter = 'all';

// إضافة event listener عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة بنجاح');

    // إضافة event delegation للتقارير
    document.addEventListener('click', function(e) {
        console.log('تم النقر على:', e.target);
        if (e.target.closest('.report-card-modern')) {
            alert('تم العثور على بطاقة التقرير');
            const card = e.target.closest('.report-card-modern');
            const reportType = card.getAttribute('data-report-type');
            if (reportType) {
                console.log('تم النقر على التقرير:', reportType);
                generateSpecificReport(reportType);
            } else {
                alert('لم يتم العثور على نوع التقرير');
            }
        }
    });
});

// بيانات التقارير (مؤقتة)
let reportsData = {
    financial: [
        { id: 1, name: 'كشف حساب العملاء', description: 'تفاصيل حسابات العملاء والمديونيات', type: 'financial' },
        { id: 2, name: 'كشف حساب الموردين', description: 'تفاصيل حسابات الموردين والمدفوعات', type: 'financial' },
        { id: 3, name: 'قائمة الدخل', description: 'الإيرادات والمصروفات وصافي الربح', type: 'financial' },
        { id: 4, name: 'الميزانية العمومية', description: 'الأصول والخصوم وحقوق الملكية', type: 'financial' }
    ],
    sales: [
        { id: 5, name: 'تقرير المبيعات اليومية', description: 'مبيعات اليوم الحالي', type: 'sales' },
        { id: 6, name: 'تقرير المبيعات الشهرية', description: 'مبيعات الشهر الحالي', type: 'sales' },
        { id: 7, name: 'تحليل أداء العملاء', description: 'أفضل العملاء والمبيعات', type: 'sales' },
        { id: 8, name: 'تقرير الأرباح', description: 'هامش الربح والأرباح الصافية', type: 'sales' }
    ],
    purchases: [
        { id: 9, name: 'تقرير المشتريات اليومية', description: 'مشتريات اليوم الحالي', type: 'purchases' },
        { id: 10, name: 'تقرير المشتريات الشهرية', description: 'مشتريات الشهر الحالي', type: 'purchases' },
        { id: 11, name: 'تحليل أداء الموردين', description: 'أفضل الموردين والمشتريات', type: 'purchases' }
    ],
    inventory: [
        { id: 12, name: 'تقرير المخزون الحالي', description: 'الكميات المتاحة والقيم', type: 'inventory' },
        { id: 13, name: 'حركة المخزون', description: 'الوارد والصادر والرصيد', type: 'inventory' },
        { id: 14, name: 'تقرير النواقص', description: 'المنتجات التي تحتاج إعادة طلب', type: 'inventory' }
    ],
    customers: [
        { id: 15, name: 'قائمة العملاء', description: 'جميع بيانات العملاء', type: 'customers' },
        { id: 16, name: 'المديونيات', description: 'العملاء المدينون والمبالغ', type: 'customers' },
        { id: 17, name: 'تحليل سلوك العملاء', description: 'أنماط الشراء والتفضيلات', type: 'customers' }
    ],
    suppliers: [
        { id: 18, name: 'قائمة الموردين', description: 'جميع بيانات الموردين', type: 'suppliers' },
        { id: 19, name: 'المدفوعات للموردين', description: 'المبالغ المدفوعة والمستحقة', type: 'suppliers' },
        { id: 20, name: 'تقييم الموردين', description: 'أداء الموردين والجودة', type: 'suppliers' }
    ]
};

let currentCategory = null;
let currentReportData = null;
let currentFilter = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة التقارير الحديثة');
    
    // تهيئة النظام
    initReportsSystem();
    
    // إضافة مستمعي الأحداث
    setupEventListeners();
    
    // تحديث الإحصائيات
    updateStats();
});

// دالة تهيئة نظام التقارير
function initReportsSystem() {
    console.log('تهيئة نظام التقارير...');
    
    // تهيئة البحث والتصفية
    initSearchAndFilters();
    
    // تعيين التواريخ الافتراضية
    setDefaultDates();
}

// دالة إعداد مستمعي الأحداث
function setupEventListeners() {
    // حقل البحث
    const searchInput = document.getElementById('search-reports');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterReports();
        });
    }
    
    // مرشحات التصفية
    const filterCategory = document.getElementById('filter-category');
    const filterPeriod = document.getElementById('filter-period');
    
    if (filterCategory) {
        filterCategory.addEventListener('change', filterReports);
    }
    
    if (filterPeriod) {
        filterPeriod.addEventListener('change', filterReports);
    }
}

// دالة تهيئة البحث والتصفية
function initSearchAndFilters() {
    console.log('تهيئة البحث والتصفية...');
}

// دالة تعيين التواريخ الافتراضية
function setDefaultDates() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    
    if (startDateInput) {
        startDateInput.value = firstDay.toISOString().split('T')[0];
    }
    
    if (endDateInput) {
        endDateInput.value = today.toISOString().split('T')[0];
    }
}

// دالة تحديث الإحصائيات
function updateStats() {
    const totalReports = Object.values(reportsData).reduce((sum, category) => sum + category.length, 0);
    const salesReports = reportsData.sales.length;
    const purchasesReports = reportsData.purchases.length;
    const financialReports = reportsData.financial.length;
    
    console.log('إحصائيات التقارير:', {
        total: totalReports,
        sales: salesReports,
        purchases: purchasesReports,
        financial: financialReports
    });
}

// دالة تصفية التقارير
function filterReports() {
    const searchTerm = document.getElementById('search-reports').value.toLowerCase();
    const categoryFilter = document.getElementById('filter-category').value;
    const periodFilter = document.getElementById('filter-period').value;
    
    console.log('تصفية التقارير:', { searchTerm, categoryFilter, periodFilter });
    
    // تطبيق التصفية على البطاقات
    const categoryCards = document.querySelectorAll('.category-card-modern');
    
    categoryCards.forEach(card => {
        const category = card.getAttribute('data-category');
        const title = card.querySelector('h3').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();
        
        let showCard = true;
        
        // تصفية البحث
        if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
            showCard = false;
        }
        
        // تصفية الفئة
        if (categoryFilter && category !== categoryFilter) {
            showCard = false;
        }
        
        card.style.display = showCard ? 'flex' : 'none';
    });
}

// دالة مسح التصفية
function clearFilters() {
    document.getElementById('search-reports').value = '';
    document.getElementById('filter-category').value = '';
    document.getElementById('filter-period').value = '';
    
    // إظهار جميع البطاقات
    const categoryCards = document.querySelectorAll('.category-card-modern');
    categoryCards.forEach(card => {
        card.style.display = 'flex';
    });
}

// دالة اختيار فئة التقرير
function selectCategory(category) {
    console.log('تم اختيار فئة:', category);

    currentCategory = category;

    // إخفاء قسم الفئات وإظهار قسم التقرير المحدد
    document.querySelector('.report-categories-modern').style.display = 'none';
    document.getElementById('selected-report').style.display = 'block';

    // تحديث عنوان ووصف التقرير
    const titles = {
        'financial': 'التقارير المالية',
        'sales': 'تقارير المبيعات',
        'purchases': 'تقارير المشتريات',
        'inventory': 'تقارير المخزون',
        'customers': 'تقارير العملاء',
        'suppliers': 'تقارير الموردين'
    };

    const descriptions = {
        'financial': 'كشوف الحسابات والأرصدة والقيود المحاسبية',
        'sales': 'فواتير المبيعات والأرباح وتحليل أداء العملاء',
        'purchases': 'فواتير المشتريات وتحليل أداء الموردين',
        'inventory': 'الجرد والحركات والكميات المتاحة والنواقص',
        'customers': 'بيانات العملاء والمديونيات وتحليل السلوك',
        'suppliers': 'بيانات الموردين والمدفوعات وتقييم الأداء'
    };

    document.getElementById('report-title').textContent = titles[category] || 'تقرير مفصل';
    document.getElementById('report-description').textContent = descriptions[category] || 'وصف التقرير المحدد';

    // عرض قائمة التقارير المتاحة
    displayAvailableReports(category);
}



// دالة اختيار فئة التقرير
function selectCategory(category) {
    console.log('تم اختيار فئة:', category);

    // تحديث الفئة الحالية
    currentCategory = category;

    // إخفاء قسم الفئات وإظهار قسم التقرير المحدد
    document.querySelector('.report-categories-modern').style.display = 'none';
    document.getElementById('selected-report').style.display = 'block';

    // تحديث عنوان ووصف التقرير
    const titles = {
        'financial': 'المالية',
        'sales': 'المبيعات',
        'purchases': 'المشتريات',
        'inventory': 'المخزون',
        'customers': 'العملاء',
        'suppliers': 'الموردين'
    };

    document.getElementById('report-title').textContent = titles[category] || 'تقرير مفصل';

    // تحديث breadcrumb
    const breadcrumbCategory = document.getElementById('breadcrumb-category');
    if (breadcrumbCategory) {
        breadcrumbCategory.textContent = titles[category] || 'تقرير مفصل';
    }

    // عرض التقارير المتاحة للفئة المختارة
    displayAvailableReports(category);
}

// دالة عرض التقارير المتاحة
function displayAvailableReports(category) {
    const reportContent = document.getElementById('report-content');
    if (!reportContent) return;

    const categoryOptions = {
        'financial': [
            { value: 'balance-sheet', text: 'قائمة المركز المالي (الميزانية العمومية)', icon: 'fas fa-building', description: 'عرض الأصول والخصوم وحقوق الملكية' },
            { value: 'profit-loss', text: 'قائمة الأرباح والخسائر', icon: 'fas fa-chart-line', description: 'تحليل الإيرادات والمصروفات وصافي الربح' },
            { value: 'cash-flow', text: 'قائمة التدفقات النقدية', icon: 'fas fa-water', description: 'تتبع التدفقات النقدية التشغيلية والاستثمارية والتمويلية' },
            { value: 'equity-changes', text: 'قائمة التغيرات في حقوق الملكية', icon: 'fas fa-exchange-alt', description: 'تتبع التغيرات في رأس المال والاحتياطيات' },
            { value: 'account-statement', text: 'كشف الحساب', icon: 'fas fa-file-alt', description: 'عرض تفصيلي لحركة حساب معين' },
            { value: 'trial-balance-movement', text: 'ميزان المراجعة مع الحركة', icon: 'fas fa-balance-scale', description: 'الأرصدة الافتتاحية والحركة والأرصدة الختامية' },
            { value: 'trial-balance', text: 'ميزان المراجعة', icon: 'fas fa-balance-scale-right', description: 'الأرصدة النهائية لجميع الحسابات' },
            { value: 'expenses-report', text: 'تقرير المصروفات', icon: 'fas fa-money-bill', description: 'تفاصيل جميع المصروفات مصنفة حسب النوع' },
            { value: 'journal-entries', text: 'تقرير قيود اليومية', icon: 'fas fa-book', description: 'جميع القيود المحاسبية مع التوازن' },
            { value: 'vat-summary', text: 'ملخص تقرير ضريبة القيمة المضافة', icon: 'fas fa-percentage', description: 'ملخص شهري وربع سنوي لضريبة القيمة المضافة' },
            { value: 'vat-details', text: 'تفاصيل ضريبة القيمة المضافة', icon: 'fas fa-list-alt', description: 'تفاصيل كل فاتورة مع الضريبة' },
            { value: 'vat-declaration', text: 'إقرار ضريبة القيمة المضافة', icon: 'fas fa-file-contract', description: 'النموذج الرسمي للإقرار الضريبي' },
            { value: 'general-ledger', text: 'دفتر الأستاذ العام', icon: 'fas fa-book-open', description: 'سجل تفصيلي لجميع المعاملات المحاسبية' }
        ],
        'sales': [
            { value: 'daily', text: 'تقرير المبيعات اليومية', icon: 'fas fa-calendar-day', description: 'مبيعات اليوم الحالي' },
            { value: 'monthly', text: 'تقرير المبيعات الشهرية', icon: 'fas fa-calendar-alt', description: 'مبيعات الشهر الحالي' },
            { value: 'by-customer', text: 'المبيعات حسب العميل', icon: 'fas fa-user', description: 'تحليل مبيعات كل عميل' },
            { value: 'by-product', text: 'المبيعات حسب المنتج', icon: 'fas fa-box', description: 'أداء المنتجات في المبيعات' }
        ],
        'purchases': [
            { value: 'daily', text: 'تقرير المشتريات اليومية', icon: 'fas fa-calendar-day', description: 'مشتريات اليوم الحالي' },
            { value: 'monthly', text: 'تقرير المشتريات الشهرية', icon: 'fas fa-calendar-alt', description: 'مشتريات الشهر الحالي' },
            { value: 'by-supplier', text: 'المشتريات حسب المورد', icon: 'fas fa-user-tie', description: 'تحليل مشتريات كل مورد' },
            { value: 'by-category', text: 'المشتريات حسب الفئة', icon: 'fas fa-tags', description: 'تصنيف المشتريات حسب الفئة' }
        ],
        'inventory': [
            { value: 'current-stock', text: 'تقرير المخزون الحالي', icon: 'fas fa-warehouse', description: 'الكميات والقيم الحالية' },
            { value: 'stock-movement', text: 'حركة المخزون', icon: 'fas fa-exchange-alt', description: 'الوارد والصادر لكل منتج' },
            { value: 'low-stock', text: 'تقرير النواقص', icon: 'fas fa-exclamation-triangle', description: 'المنتجات التي تحتاج إعادة طلب' },
            { value: 'stock-valuation', text: 'تقييم المخزون', icon: 'fas fa-calculator', description: 'قيمة المخزون بأسعار التكلفة والبيع' }
        ],
        'customers': [
            { value: 'customer-list', text: 'قائمة العملاء', icon: 'fas fa-users', description: 'جميع بيانات العملاء' },
            { value: 'customer-balances', text: 'أرصدة العملاء', icon: 'fas fa-money-bill-wave', description: 'المديونيات والمدفوعات' },
            { value: 'customer-analysis', text: 'تحليل العملاء', icon: 'fas fa-chart-pie', description: 'أنماط الشراء والتفضيلات' },
            { value: 'customer-aging', text: 'أعمار الديون', icon: 'fas fa-clock', description: 'تحليل أعمار ديون العملاء' }
        ],
        'suppliers': [
            { value: 'supplier-list', text: 'قائمة الموردين', icon: 'fas fa-user-tie', description: 'جميع بيانات الموردين' },
            { value: 'supplier-balances', text: 'أرصدة الموردين', icon: 'fas fa-credit-card', description: 'المستحقات والمدفوعات' },
            { value: 'supplier-analysis', text: 'تحليل الموردين', icon: 'fas fa-chart-bar', description: 'أداء الموردين والجودة' },
            { value: 'supplier-evaluation', text: 'تقييم الموردين', icon: 'fas fa-star', description: 'تقييم أداء وموثوقية الموردين' }
        ]
    };

    const reports = categoryOptions[category] || [];

    if (reports.length === 0) {
        reportContent.innerHTML = '<div class="no-reports">لا توجد تقارير متاحة لهذه الفئة</div>';
        return;
    }

    reportContent.innerHTML = `
        <div class="available-reports-modern">
            <div class="reports-grid-modern">
                ${reports.map(report => `
                    <div class="report-card-modern" data-report-type="${report.value}">
                        <div class="report-icon-modern">
                            <i class="${report.icon}"></i>
                        </div>
                        <div class="report-details-modern">
                            <h5>${report.text}</h5>
                            <p>${report.description}</p>
                        </div>
                        <div class="report-action-modern">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// دالة إنشاء تقرير محدد
function generateSpecificReport(reportType) {
    alert('تم النقر على التقرير: ' + reportType);
    console.log('إنشاء تقرير محدد:', reportType);
    console.log('الفئة الحالية:', currentCategory);

    // تحديث المتغيرات العامة - استخدام الفئة الحالية
    currentFilter = reportType;

    console.log('المتغيرات المحدثة:', { currentCategory, currentFilter });

    // إظهار زر إنشاء التقرير
    console.log('محاولة إظهار زر إنشاء التقرير...');
    showGenerateButton();

    // تحديث عنوان التقرير ليعكس التقرير المحدد
    console.log('تحديث عنوان التقرير...');
    updateReportTitle(reportType);

    // إنشاء التقرير مباشرة مع تمرير الفئة الصحيحة
    console.log('إنشاء التقرير الوهمي...');
    generateMockReport(currentCategory, '2023-01-01', '2023-12-31', 'detailed', 'table');
}

// دالة لإظهار زر إنشاء التقرير عند اختيار تقرير محدد
function showGenerateButton() {
    const generateBtn = document.getElementById('generate-report-btn');
    if (generateBtn) {
        generateBtn.style.display = 'flex';
    }
}

// دالة تبديل عرض خيارات التقرير
function toggleReportControls() {
    const content = document.getElementById('report-controls-content');
    const btn = document.querySelector('.toggle-controls-btn i');

    if (content && btn) {
        if (content.style.display === 'none') {
            content.style.display = 'block';
            btn.className = 'fas fa-chevron-up';
        } else {
            content.style.display = 'none';
            btn.className = 'fas fa-chevron-down';
        }
    }
}

// دالة تحديث عنوان التقرير
function updateReportTitle(reportType) {
    const reportTitles = {
        // التقارير المالية
        'balance-sheet': 'قائمة المركز المالي (الميزانية العمومية)',
        'profit-loss': 'قائمة الأرباح والخسائر',
        'cash-flow': 'قائمة التدفقات النقدية',
        'equity-changes': 'قائمة التغيرات في حقوق الملكية',
        'account-statement': 'كشف الحساب',
        'trial-balance-movement': 'ميزان المراجعة مع الحركة',
        'trial-balance': 'ميزان المراجعة',
        'expenses-report': 'تقرير المصروفات',
        'journal-entries': 'تقرير قيود اليومية',
        'vat-summary': 'ملخص تقرير ضريبة القيمة المضافة',
        'vat-details': 'تفاصيل ضريبة القيمة المضافة',
        'vat-declaration': 'إقرار ضريبة القيمة المضافة',
        'general-ledger': 'دفتر الأستاذ العام',

        // تقارير المبيعات
        'daily': currentCategory === 'sales' ? 'تقرير المبيعات اليومية' : 'تقرير المشتريات اليومية',
        'monthly': currentCategory === 'sales' ? 'تقرير المبيعات الشهرية' : 'تقرير المشتريات الشهرية',
        'by-customer': 'المبيعات حسب العميل',
        'by-product': 'المبيعات حسب المنتج',

        // تقارير المشتريات
        'by-supplier': 'المشتريات حسب المورد',
        'by-category': 'المشتريات حسب الفئة',

        // تقارير المخزون
        'current-stock': 'تقرير المخزون الحالي',
        'stock-movement': 'حركة المخزون',
        'low-stock': 'تقرير النواقص',
        'stock-valuation': 'تقييم المخزون',

        // تقارير العملاء
        'customer-list': 'قائمة العملاء',
        'customer-balances': 'أرصدة العملاء',
        'customer-analysis': 'تحليل العملاء',
        'customer-aging': 'أعمار الديون',

        // تقارير الموردين
        'supplier-list': 'قائمة الموردين',
        'supplier-balances': 'أرصدة الموردين',
        'supplier-analysis': 'تحليل الموردين',
        'supplier-evaluation': 'تقييم الموردين'
    };

    const reportTitle = document.getElementById('report-title');
    if (reportTitle && reportTitles[reportType]) {
        reportTitle.textContent = reportTitles[reportType];
    }
}

// دالة تعيين التواريخ السريعة
function setQuickDate(period) {
    const today = new Date();
    let startDate, endDate = today;

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.quick-date-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');

    switch(period) {
        case 'today':
            startDate = today;
            break;
        case 'week':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - today.getDay());
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            startDate = new Date(today.getFullYear(), quarter * 3, 1);
            break;
        default:
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
    }

    // تحديث حقول التاريخ
    document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
}

// دالة العودة
function goBack() {
    document.querySelector('.report-categories-modern').style.display = 'grid';
    document.getElementById('selected-report').style.display = 'none';
    currentCategory = null;
    currentReportData = null;
}

// دالة إنشاء التقرير المفصل
function generateDetailedReport() {
    const startDate = document.getElementById('start-date')?.value || '2023-01-01';
    const endDate = document.getElementById('end-date')?.value || '2023-12-31';
    const reportType = document.getElementById('report-type')?.value || 'detailed';
    const outputFormat = document.getElementById('output-format')?.value || 'table';

    if (!currentCategory) {
        alert('يرجى اختيار فئة التقرير');
        return;
    }

    console.log('إنشاء تقرير:', { category: currentCategory, filter: currentFilter, startDate, endDate, reportType, outputFormat });

    // محاكاة إنشاء التقرير
    generateMockReport(currentCategory, startDate, endDate, reportType, outputFormat);
}

// دالة إنشاء تقرير وهمي محسن
function generateMockReport(category, startDate, endDate, reportType, outputFormat) {
    const reportContent = document.getElementById('report-content');
    const additionalFilter = currentFilter || '';

    console.log('إنشاء تقرير:', { category, additionalFilter, currentFilter });

    // إظهار مؤشر التحميل
    reportContent.innerHTML = `
        <div class="loading-indicator">
            <div class="spinner"></div>
            <p>جاري إنشاء التقرير...</p>
        </div>
    `;

    // محاكاة وقت التحميل
    setTimeout(() => {
        // بيانات وهمية حسب الفئة
        const mockData = getMockDataForCategory(category, additionalFilter);
        const statistics = generateStatistics(mockData, category, additionalFilter);

        console.log('البيانات والإحصائيات:', { mockData, statistics, additionalFilter });

        reportContent.innerHTML = `
            <div class="report-generated-modern">
                <!-- شريط خيارات التقرير -->
                <div class="report-controls-modern">
                    <div class="controls-header">
                        <h4><i class="fas fa-cog"></i> خيارات التقرير</h4>
                        <button class="toggle-controls-btn" onclick="toggleReportControls()">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="controls-content" id="report-controls-content">
                        <div class="controls-grid">
                            <div class="control-group">
                                <label><i class="fas fa-calendar-alt"></i> الفترة الزمنية</label>
                                <div class="date-range-control">
                                    <input type="date" id="report-start-date" class="control-input" value="${startDate}">
                                    <span>إلى</span>
                                    <input type="date" id="report-end-date" class="control-input" value="${endDate}">
                                </div>
                            </div>

                            <div class="control-group">
                                <label><i class="fas fa-list-alt"></i> نوع التقرير</label>
                                <select id="report-type-control" class="control-select">
                                    <option value="summary" ${reportType === 'summary' ? 'selected' : ''}>ملخص عام</option>
                                    <option value="detailed" ${reportType === 'detailed' ? 'selected' : ''}>تفصيلي شامل</option>
                                    <option value="comparative" ${reportType === 'comparative' ? 'selected' : ''}>مقارن بالفترات السابقة</option>
                                    <option value="analytical" ${reportType === 'analytical' ? 'selected' : ''}>تحليلي متقدم</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <label><i class="fas fa-chart-bar"></i> تنسيق العرض</label>
                                <select id="output-format-control" class="control-select">
                                    <option value="table" ${outputFormat === 'table' ? 'selected' : ''}>جدول تفصيلي</option>
                                    <option value="chart" ${outputFormat === 'chart' ? 'selected' : ''}>رسوم بيانية</option>
                                    <option value="both" ${outputFormat === 'both' ? 'selected' : ''}>جدول ورسوم بيانية</option>
                                    <option value="dashboard" ${outputFormat === 'dashboard' ? 'selected' : ''}>لوحة معلومات</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <button class="update-report-btn" onclick="updateCurrentReport()">
                                    <i class="fas fa-sync-alt"></i> تحديث التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="report-table-modern ${currentCategory === 'financial' ? 'financial-statement' : ''}">
                    <div class="table-header">
                        <h4><i class="fas fa-table"></i> ${getReportTableTitle(currentCategory, additionalFilter)}</h4>
                        <div class="table-actions">
                            <button class="btn-small btn-secondary" onclick="exportTableToExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                            <button class="btn-small btn-secondary" onclick="printTable()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="modern-table" id="report-table">
                            <thead>
                                <tr>
                                    ${mockData.headers.map(header => `<th>${header}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${mockData.rows.map((row, index) => `
                                    <tr class="table-row ${getRowClass(currentCategory, additionalFilter, row, index)}" style="animation-delay: ${index * 0.1}s">
                                        ${row.map((cell, cellIndex) => `<td class="${getCellClass(currentCategory, additionalFilter, cellIndex, cell)}">${formatCellValue(cell, currentCategory, additionalFilter, cellIndex)}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div class="table-footer">
                        <p>إجمالي السجلات: <strong>${mockData.rows.length}</strong></p>
                    </div>
                </div>

                ${outputFormat === 'chart' || outputFormat === 'both' || outputFormat === 'dashboard' ? `
                <div class="report-charts">
                    <h4><i class="fas fa-chart-bar"></i> الرسوم البيانية</h4>
                    <div class="charts-grid">
                        <div class="chart-container">
                            <canvas id="report-chart-1"></canvas>
                        </div>
                        <div class="chart-container">
                            <canvas id="report-chart-2"></canvas>
                        </div>
                    </div>
                </div>
                ` : ''}

                ${statistics ? `
                <div class="report-statistics">
                    <h4><i class="fas fa-chart-pie"></i> الإحصائيات الرئيسية</h4>
                    <div class="stats-grid">
                        ${statistics.map(stat => `
                            <div class="stat-item">
                                <div class="stat-icon ${stat.color}">
                                    <i class="${stat.icon}"></i>
                                </div>
                                <div class="stat-details">
                                    <h5>${stat.label}</h5>
                                    <span class="stat-value">${stat.value}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        // إضافة أنماط CSS للانيميشن
        addTableAnimationStyles();

        // حفظ بيانات التقرير الحالي
        currentReportData = {
            category,
            startDate,
            endDate,
            reportType,
            outputFormat,
            additionalFilter,
            data: mockData,
            statistics
        };

        // إنشاء الرسوم البيانية إذا كانت مطلوبة
        if (outputFormat === 'chart' || outputFormat === 'both' || outputFormat === 'dashboard') {
            setTimeout(() => generateCharts(mockData, category), 500);
        }
    }, 1500);
}

// دالة الحصول على بيانات وهمية حسب الفئة والمرشح
function getMockDataForCategory(category, filter = '') {
    const mockDataMap = {
        financial: {
            headers: getFinancialHeaders(filter),
            rows: getFinancialRows(filter)
        },
        sales: {
            headers: ['التاريخ', 'رقم الفاتورة', 'العميل', 'المبلغ', 'الحالة'],
            rows: filter === 'paid' ? [
                ['2023-12-01', 'INV-001', 'أحمد محمد علي', '5,500 ر.س', 'مدفوعة'],
                ['2023-12-02', 'INV-002', 'شركة الأمل للتجارة', '12,000 ر.س', 'مدفوعة'],
                ['2023-12-04', 'INV-004', 'مؤسسة النور', '8,750 ر.س', 'مدفوعة'],
                ['2023-12-05', 'INV-006', 'محمد عبدالله', '3,200 ر.س', 'مدفوعة'],
                ['2023-12-06', 'INV-008', 'شركة الإبداع', '15,500 ر.س', 'مدفوعة']
            ] : filter === 'pending' ? [
                ['2023-12-03', 'INV-003', 'فاطمة علي', '3,200 ر.س', 'معلقة'],
                ['2023-12-05', 'INV-005', 'شركة المستقبل', '9,800 ر.س', 'معلقة'],
                ['2023-12-07', 'INV-007', 'علي أحمد', '4,500 ر.س', 'معلقة']
            ] : filter === 'overdue' ? [
                ['2023-11-25', 'INV-095', 'شركة التطوير', '7,200 ر.س', 'متأخرة'],
                ['2023-11-28', 'INV-098', 'أحمد سالم', '2,800 ر.س', 'متأخرة'],
                ['2023-11-30', 'INV-099', 'مؤسسة الخليج', '12,500 ر.س', 'متأخرة']
            ] : [
                ['2023-12-01', 'INV-001', 'أحمد محمد علي', '5,500 ر.س', 'مدفوعة'],
                ['2023-12-02', 'INV-002', 'شركة الأمل للتجارة', '12,000 ر.س', 'مدفوعة'],
                ['2023-12-03', 'INV-003', 'فاطمة علي', '3,200 ر.س', 'معلقة'],
                ['2023-12-04', 'INV-004', 'مؤسسة النور', '8,750 ر.س', 'مدفوعة'],
                ['2023-12-05', 'INV-005', 'شركة المستقبل', '9,800 ر.س', 'معلقة'],
                ['2023-12-06', 'INV-006', 'محمد عبدالله', '3,200 ر.س', 'مدفوعة']
            ]
        },
        purchases: {
            headers: ['التاريخ', 'رقم الفاتورة', 'المورد', 'المبلغ', 'الحالة'],
            rows: filter === 'paid' ? [
                ['2023-12-01', 'PUR-001', 'شركة التقنية المتقدمة', '15,000 ر.س', 'مدفوعة'],
                ['2023-12-03', 'PUR-003', 'شركة المواد الأولية', '22,000 ر.س', 'مدفوعة'],
                ['2023-12-04', 'PUR-004', 'أحمد محمد للخضار', '1,200 ر.س', 'مدفوعة'],
                ['2023-12-05', 'PUR-005', 'مؤسسة الكمبيوتر', '8,500 ر.س', 'مدفوعة']
            ] : filter === 'pending' ? [
                ['2023-12-02', 'PUR-002', 'مؤسسة الإمداد الشامل', '8,500 ر.س', 'معلقة'],
                ['2023-12-06', 'PUR-006', 'شركة الأدوات', '5,200 ر.س', 'معلقة'],
                ['2023-12-07', 'PUR-007', 'مورد المكتب', '3,800 ر.س', 'معلقة']
            ] : [
                ['2023-12-01', 'PUR-001', 'شركة التقنية المتقدمة', '15,000 ر.س', 'مدفوعة'],
                ['2023-12-02', 'PUR-002', 'مؤسسة الإمداد الشامل', '8,500 ر.س', 'معلقة'],
                ['2023-12-03', 'PUR-003', 'شركة المواد الأولية', '22,000 ر.س', 'مدفوعة'],
                ['2023-12-04', 'PUR-004', 'أحمد محمد للخضار', '1,200 ر.س', 'مدفوعة'],
                ['2023-12-05', 'PUR-005', 'مؤسسة الكمبيوتر', '8,500 ر.س', 'مدفوعة'],
                ['2023-12-06', 'PUR-006', 'شركة الأدوات المكتبية', '5,200 ر.س', 'معلقة']
            ]
        },
        inventory: {
            headers: ['المنتج', 'الكمية المتاحة', 'سعر الوحدة', 'القيمة الإجمالية', 'الحالة'],
            rows: filter === 'in-stock' ? [
                ['لابتوب ديل XPS', '25', '2,500 ر.س', '62,500 ر.س', 'متوفر'],
                ['طابعة HP LaserJet', '15', '800 ر.س', '12,000 ر.س', 'متوفر'],
                ['ماوس لاسلكي لوجيتك', '50', '45 ر.س', '2,250 ر.س', 'متوفر'],
                ['كيبورد ميكانيكي', '30', '150 ر.س', '4,500 ر.س', 'متوفر'],
                ['شاشة سامسونج 24 بوصة', '18', '650 ر.س', '11,700 ر.س', 'متوفر']
            ] : filter === 'low-stock' ? [
                ['كابل USB-C', '8', '25 ر.س', '200 ر.س', 'منخفض'],
                ['سماعات بلوتوث', '5', '120 ر.س', '600 ر.س', 'منخفض'],
                ['حامل لابتوب', '3', '85 ر.س', '255 ر.س', 'منخفض'],
                ['ذاكرة فلاش 64GB', '7', '35 ر.س', '245 ر.س', 'منخفض']
            ] : filter === 'out-of-stock' ? [
                ['كاميرا ويب HD', '0', '180 ر.س', '0 ر.س', 'منتهي'],
                ['مكبر صوت محمول', '0', '95 ر.س', '0 ر.س', 'منتهي'],
                ['شاحن لاسلكي', '0', '75 ر.س', '0 ر.س', 'منتهي']
            ] : [
                ['لابتوب ديل XPS', '25', '2,500 ر.س', '62,500 ر.س', 'متوفر'],
                ['طابعة HP LaserJet', '15', '800 ر.س', '12,000 ر.س', 'متوفر'],
                ['ماوس لاسلكي لوجيتك', '50', '45 ر.س', '2,250 ر.س', 'متوفر'],
                ['كيبورد ميكانيكي', '30', '150 ر.س', '4,500 ر.س', 'متوفر'],
                ['كابل USB-C', '8', '25 ر.س', '200 ر.س', 'منخفض'],
                ['كاميرا ويب HD', '0', '180 ر.س', '0 ر.س', 'منتهي']
            ]
        },
        customers: {
            headers: ['اسم العميل', 'إجمالي المشتريات', 'المدفوع', 'المتبقي', 'آخر عملية'],
            rows: filter === 'active' ? [
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01'],
                ['شركة الأمل للتجارة', '45,000 ر.س', '45,000 ر.س', '0 ر.س', '2023-12-02'],
                ['مؤسسة النور', '35,000 ر.س', '30,000 ر.س', '5,000 ر.س', '2023-12-04'],
                ['محمد عبدالله', '18,500 ر.س', '18,500 ر.س', '0 ر.س', '2023-12-06']
            ] : filter === 'debtors' ? [
                ['فاطمة عبدالله', '12,000 ر.س', '8,000 ر.س', '4,000 ر.س', '2023-11-28'],
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01'],
                ['شركة التطوير', '22,000 ر.س', '15,000 ر.س', '7,000 ر.س', '2023-11-25'],
                ['مؤسسة النور', '35,000 ر.س', '30,000 ر.س', '5,000 ر.س', '2023-12-04']
            ] : filter === 'vip' ? [
                ['شركة الأمل للتجارة', '45,000 ر.س', '45,000 ر.س', '0 ر.س', '2023-12-02'],
                ['مؤسسة النور', '35,000 ر.س', '30,000 ر.س', '5,000 ر.س', '2023-12-04'],
                ['شركة المستقبل', '28,000 ر.س', '25,000 ر.س', '3,000 ر.س', '2023-12-05'],
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01']
            ] : filter === 'new' ? [
                ['عميل جديد 1', '5,500 ر.س', '5,500 ر.س', '0 ر.س', '2023-12-07'],
                ['عميل جديد 2', '3,200 ر.س', '3,200 ر.س', '0 ر.س', '2023-12-06'],
                ['عميل جديد 3', '8,750 ر.س', '8,750 ر.س', '0 ر.س', '2023-12-05'],
                ['عميل جديد 4', '2,100 ر.س', '2,100 ر.س', '0 ر.س', '2023-12-04']
            ] : filter === 'returning' ? [
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01'],
                ['فاطمة عبدالله', '12,000 ر.س', '8,000 ر.س', '4,000 ر.س', '2023-11-28'],
                ['محمد عبدالله', '18,500 ر.س', '18,500 ر.س', '0 ر.س', '2023-12-06'],
                ['شركة التطوير', '22,000 ر.س', '15,000 ر.س', '7,000 ر.س', '2023-11-25']
            ] : filter === 'high-value' ? [
                ['شركة الأمل للتجارة', '45,000 ر.س', '45,000 ر.س', '0 ر.س', '2023-12-02'],
                ['مؤسسة النور', '35,000 ر.س', '30,000 ر.س', '5,000 ر.س', '2023-12-04'],
                ['شركة المستقبل', '28,000 ر.س', '25,000 ر.س', '3,000 ر.س', '2023-12-05'],
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01']
            ] : [
                ['أحمد محمد علي', '25,000 ر.س', '20,000 ر.س', '5,000 ر.س', '2023-12-01'],
                ['شركة الأمل للتجارة', '45,000 ر.س', '45,000 ر.س', '0 ر.س', '2023-12-02'],
                ['فاطمة عبدالله', '12,000 ر.س', '8,000 ر.س', '4,000 ر.س', '2023-11-28'],
                ['مؤسسة النور', '35,000 ر.س', '30,000 ر.س', '5,000 ر.س', '2023-12-04'],
                ['محمد عبدالله', '18,500 ر.س', '18,500 ر.س', '0 ر.س', '2023-12-06'],
                ['شركة المستقبل', '28,000 ر.س', '25,000 ر.س', '3,000 ر.س', '2023-12-05']
            ]
        },
        suppliers: {
            headers: ['اسم المورد', 'إجمالي المشتريات', 'المدفوع', 'المتبقي', 'آخر عملية'],
            rows: filter === 'active' ? [
                ['شركة التقنية المتقدمة', '85,000 ر.س', '75,000 ر.س', '10,000 ر.س', '2023-12-01'],
                ['مؤسسة الإمداد الشامل', '35,000 ر.س', '35,000 ر.س', '0 ر.س', '2023-12-02'],
                ['شركة المواد الأولية', '65,000 ر.س', '50,000 ر.س', '15,000 ر.س', '2023-11-30'],
                ['مؤسسة الكمبيوتر', '42,000 ر.س', '42,000 ر.س', '0 ر.س', '2023-12-05']
            ] : filter === 'creditors' ? [
                ['شركة التقنية المتقدمة', '85,000 ر.س', '75,000 ر.س', '10,000 ر.س', '2023-12-01'],
                ['شركة المواد الأولية', '65,000 ر.س', '50,000 ر.س', '15,000 ر.س', '2023-11-30'],
                ['شركة الأدوات المكتبية', '28,000 ر.س', '20,000 ر.س', '8,000 ر.س', '2023-12-06'],
                ['مورد المكتب', '15,000 ر.س', '10,000 ر.س', '5,000 ر.س', '2023-12-07']
            ] : [
                ['شركة التقنية المتقدمة', '85,000 ر.س', '75,000 ر.س', '10,000 ر.س', '2023-12-01'],
                ['مؤسسة الإمداد الشامل', '35,000 ر.س', '35,000 ر.س', '0 ر.س', '2023-12-02'],
                ['شركة المواد الأولية', '65,000 ر.س', '50,000 ر.س', '15,000 ر.س', '2023-11-30'],
                ['أحمد محمد للخضار', '8,500 ر.س', '8,500 ر.س', '0 ر.س', '2023-12-04'],
                ['مؤسسة الكمبيوتر', '42,000 ر.س', '42,000 ر.س', '0 ر.س', '2023-12-05'],
                ['شركة الأدوات المكتبية', '28,000 ر.س', '20,000 ر.س', '8,000 ر.س', '2023-12-06']
            ]
        }
    };

    return mockDataMap[category] || mockDataMap.financial;
}

// دوال مساعدة للقوائم المالية
function getFinancialHeaders(filter) {
    const headersMap = {
        'balance-sheet': ['البند', 'المبلغ (ر.س)', 'النسبة %', 'الملاحظات'],
        'profit-loss': ['البند', 'المبلغ (ر.س)', 'النسبة %', 'المقارنة مع العام السابق'],
        'cash-flow': ['النشاط', 'التدفق الداخل', 'التدفق الخارج', 'صافي التدفق'],
        'equity-changes': ['البند', 'الرصيد الافتتاحي', 'التغيرات', 'الرصيد الختامي'],
        'account-statement': ['التاريخ', 'البيان', 'المدين', 'الدائن', 'الرصيد'],
        'trial-balance-movement': ['رقم الحساب', 'اسم الحساب', 'الرصيد الافتتاحي', 'المدين', 'الدائن', 'الرصيد الختامي'],
        'trial-balance': ['رقم الحساب', 'اسم الحساب', 'المدين', 'الدائن'],
        'expenses-report': ['التاريخ', 'نوع المصروف', 'المبلغ', 'الوصف', 'المورد'],
        'journal-entries': ['رقم القيد', 'التاريخ', 'الحساب', 'المدين', 'الدائن'],
        'vat-summary': ['الفترة', 'المبيعات الخاضعة', 'ضريبة المخرجات', 'المشتريات الخاضعة', 'ضريبة المدخلات', 'صافي الضريبة'],
        'vat-details': ['التاريخ', 'رقم الفاتورة', 'اسم العميل/المورد', 'المبلغ قبل الضريبة', 'الضريبة 15%', 'المبلغ الإجمالي'],
        'vat-declaration': ['البند', 'الكود', 'المبلغ (ر.س)', 'الوصف'],
        'general-ledger': ['التاريخ', 'رقم القيد', 'البيان', 'المدين', 'الدائن']
    };

    return headersMap[filter] || ['الحساب', 'الرصيد الافتتاحي', 'المدين', 'الدائن', 'الرصيد الختامي'];
}

function getFinancialRows(filter) {
    const rowsMap = {
        'balance-sheet': [
            ['الأصول المتداولة', '', '', ''],
            ['النقدية والنقدية المعادلة', '250,000', '15.2%', 'سيولة عالية'],
            ['العملاء والذمم المدينة', '180,000', '10.9%', 'بعد خصم المخصص'],
            ['المخزون', '320,000', '19.4%', 'بسعر التكلفة أو السوق أيهما أقل'],
            ['مصروفات مدفوعة مقدماً', '25,000', '1.5%', 'إيجارات وتأمينات'],
            ['إجمالي الأصول المتداولة', '775,000', '47.0%', ''],
            ['', '', '', ''],
            ['الأصول غير المتداولة', '', '', ''],
            ['الأثاث والمعدات', '650,000', '39.4%', 'بالتكلفة'],
            ['مجمع الإهلاك', '(125,000)', '(7.6%)', 'إهلاك متراكم'],
            ['صافي الأثاث والمعدات', '525,000', '31.8%', ''],
            ['استثمارات طويلة الأجل', '350,000', '21.2%', 'أسهم وسندات'],
            ['إجمالي الأصول غير المتداولة', '875,000', '53.0%', ''],
            ['', '', '', ''],
            ['إجمالي الأصول', '1,650,000', '100.0%', ''],
            ['', '', '', ''],
            ['الخصوم المتداولة', '', '', ''],
            ['الموردين والذمم الدائنة', '125,000', '7.6%', 'مستحقة خلال 30 يوم'],
            ['قروض قصيرة الأجل', '85,000', '5.2%', 'تستحق خلال السنة'],
            ['مصروفات مستحقة', '45,000', '2.7%', 'رواتب ومكافآت'],
            ['ضرائب مستحقة', '35,000', '2.1%', 'ضريبة دخل'],
            ['إجمالي الخصوم المتداولة', '290,000', '17.6%', ''],
            ['', '', '', ''],
            ['الخصوم غير المتداولة', '', '', ''],
            ['قروض طويلة الأجل', '180,000', '10.9%', 'تستحق بعد سنة'],
            ['مخصصات طويلة الأجل', '25,000', '1.5%', 'مكافآت نهاية الخدمة'],
            ['إجمالي الخصوم غير المتداولة', '205,000', '12.4%', ''],
            ['', '', '', ''],
            ['إجمالي الخصوم', '495,000', '30.0%', ''],
            ['', '', '', ''],
            ['حقوق الملكية', '', '', ''],
            ['رأس المال المدفوع', '800,000', '48.5%', '80,000 سهم × 10 ر.س'],
            ['الاحتياطي القانوني', '120,000', '7.3%', '15% من رأس المال'],
            ['الأرباح المحتجزة', '185,000', '11.2%', 'أرباح سنوات سابقة'],
            ['أرباح السنة الحالية', '50,000', '3.0%', 'صافي ربح 2023'],
            ['إجمالي حقوق الملكية', '1,155,000', '70.0%', ''],
            ['', '', '', ''],
            ['إجمالي الخصوم وحقوق الملكية', '1,650,000', '100.0%', '']
        ],

        'profit-loss': [
            ['الإيرادات', '', '', ''],
            ['مبيعات المنتجات', '1,250,000', '89.3%', '+12.5% عن العام السابق'],
            ['مبيعات الخدمات', '120,000', '8.6%', '+8.2% عن العام السابق'],
            ['إيرادات أخرى', '30,000', '2.1%', '+15.0% عن العام السابق'],
            ['إجمالي الإيرادات', '1,400,000', '100.0%', '+11.8% عن العام السابق'],
            ['', '', '', ''],
            ['تكلفة المبيعات', '', '', ''],
            ['تكلفة البضاعة المباعة', '750,000', '53.6%', '+10.2% عن العام السابق'],
            ['تكلفة الخدمات المقدمة', '72,000', '5.1%', '+7.8% عن العام السابق'],
            ['إجمالي تكلفة المبيعات', '822,000', '58.7%', '+9.8% عن العام السابق'],
            ['', '', '', ''],
            ['إجمالي الربح', '578,000', '41.3%', '+14.5% عن العام السابق'],
            ['', '', '', ''],
            ['المصروفات التشغيلية', '', '', ''],
            ['مصروفات إدارية', '180,000', '12.9%', '+5.2% عن العام السابق'],
            ['مصروفات تسويق ومبيعات', '125,000', '8.9%', '+18.5% عن العام السابق'],
            ['مصروفات عمومية', '85,000', '6.1%', '+3.8% عن العام السابق'],
            ['إجمالي المصروفات التشغيلية', '390,000', '27.9%', '+8.7% عن العام السابق'],
            ['', '', '', ''],
            ['الربح التشغيلي', '188,000', '13.4%', '+28.2% عن العام السابق'],
            ['', '', '', ''],
            ['الإيرادات والمصروفات الأخرى', '', '', ''],
            ['إيرادات استثمارات', '25,000', '1.8%', '+22.0% عن العام السابق'],
            ['مصروفات فوائد', '(18,000)', '(1.3%)', '+12.5% عن العام السابق'],
            ['أرباح (خسائر) صرف عملات', '3,000', '0.2%', 'تحسن من خسارة العام السابق'],
            ['صافي الإيرادات الأخرى', '10,000', '0.7%', '+45.2% عن العام السابق'],
            ['', '', '', ''],
            ['الربح قبل الضرائب', '198,000', '14.1%', '+29.8% عن العام السابق'],
            ['ضريبة الدخل', '(48,000)', '(3.4%)', '+25.0% عن العام السابق'],
            ['', '', '', ''],
            ['صافي ربح السنة', '150,000', '10.7%', '+31.6% عن العام السابق']
        ],

        'cash-flow': [
            ['الأنشطة التشغيلية', '', '', ''],
            ['صافي الربح', '150,000', '0', '150,000'],
            ['تعديلات للبنود غير النقدية:', '', '', ''],
            ['الإهلاك والاستنفاد', '0', '85,000', '85,000'],
            ['مخصص الديون المشكوك فيها', '0', '12,000', '12,000'],
            ['التغيرات في رأس المال العامل:', '', '', ''],
            ['زيادة في العملاء', '0', '(35,000)', '(35,000)'],
            ['نقص في المخزون', '25,000', '0', '25,000'],
            ['زيادة في الموردين', '18,000', '0', '18,000'],
            ['زيادة في المصروفات المستحقة', '8,000', '0', '8,000'],
            ['صافي النقد من الأنشطة التشغيلية', '201,000', '62,000', '263,000'],
            ['', '', '', ''],
            ['الأنشطة الاستثمارية', '', '', ''],
            ['شراء أثاث ومعدات', '0', '(125,000)', '(125,000)'],
            ['بيع استثمارات', '45,000', '0', '45,000'],
            ['شراء استثمارات جديدة', '0', '(80,000)', '(80,000)'],
            ['صافي النقد من الأنشطة الاستثمارية', '45,000', '205,000', '(160,000)'],
            ['', '', '', ''],
            ['الأنشطة التمويلية', '', '', ''],
            ['قروض جديدة', '100,000', '0', '100,000'],
            ['سداد قروض', '0', '(75,000)', '(75,000)'],
            ['توزيعات أرباح مدفوعة', '0', '(85,000)', '(85,000)'],
            ['زيادة رأس المال', '200,000', '0', '200,000'],
            ['صافي النقد من الأنشطة التمويلية', '300,000', '160,000', '140,000'],
            ['', '', '', ''],
            ['صافي الزيادة في النقدية', '546,000', '427,000', '243,000'],
            ['النقدية في بداية السنة', '125,000', '0', '125,000'],
            ['النقدية في نهاية السنة', '671,000', '427,000', '368,000']
        ],

        'equity-changes': [
            ['رأس المال المدفوع', '600,000', '200,000', '800,000'],
            ['الاحتياطي القانوني', '105,000', '15,000', '120,000'],
            ['الاحتياطي الاختياري', '50,000', '25,000', '75,000'],
            ['الأرباح المحتجزة', '120,000', '65,000', '185,000'],
            ['أرباح السنة الحالية', '0', '150,000', '150,000'],
            ['توزيعات أرباح', '0', '(85,000)', '(85,000)'],
            ['تحويل للاحتياطي القانوني', '0', '(15,000)', '(15,000)'],
            ['تحويل للاحتياطي الاختياري', '0', '(25,000)', '(25,000)'],
            ['إجمالي حقوق الملكية', '875,000', '330,000', '1,205,000']
        ],

        'trial-balance': [
            ['1001', 'النقدية بالصندوق', '45,000', '0'],
            ['1002', 'البنك الأهلي', '205,000', '0'],
            ['1101', 'العملاء', '180,000', '0'],
            ['1102', 'مخصص الديون المشكوك فيها', '0', '12,000'],
            ['1201', 'المخزون', '320,000', '0'],
            ['1301', 'الأثاث والمعدات', '650,000', '0'],
            ['1302', 'مجمع إهلاك الأثاث والمعدات', '0', '125,000'],
            ['2001', 'الموردين', '0', '125,000'],
            ['2101', 'قروض قصيرة الأجل', '0', '85,000'],
            ['2102', 'مصروفات مستحقة', '0', '45,000'],
            ['2103', 'ضرائب مستحقة', '0', '35,000'],
            ['2201', 'قروض طويلة الأجل', '0', '180,000'],
            ['3001', 'رأس المال', '0', '800,000'],
            ['3101', 'الاحتياطي القانوني', '0', '120,000'],
            ['3201', 'الأرباح المحتجزة', '0', '185,000'],
            ['4001', 'مبيعات المنتجات', '0', '1,250,000'],
            ['4002', 'مبيعات الخدمات', '0', '120,000'],
            ['4003', 'إيرادات أخرى', '0', '30,000'],
            ['5001', 'تكلفة البضاعة المباعة', '750,000', '0'],
            ['5002', 'تكلفة الخدمات', '72,000', '0'],
            ['6001', 'مصروفات إدارية', '180,000', '0'],
            ['6002', 'مصروفات تسويق', '125,000', '0'],
            ['6003', 'مصروفات عمومية', '85,000', '0'],
            ['الإجمالي', '2,612,000', '2,612,000']
        ],

        'account-statement': [
            ['2023-01-01', 'رصيد افتتاحي', '0', '0', '50,000'],
            ['2023-01-05', 'إيداع نقدي', '0', '25,000', '75,000'],
            ['2023-01-10', 'سحب نقدي', '15,000', '0', '60,000'],
            ['2023-01-15', 'تحصيل من عميل', '0', '35,000', '95,000'],
            ['2023-01-20', 'دفع للمورد', '22,000', '0', '73,000'],
            ['2023-01-25', 'إيداع شيك', '0', '18,000', '91,000'],
            ['2023-01-30', 'رسوم بنكية', '500', '0', '90,500']
        ],

        'general-ledger': [
            ['2023-01-01', '001', 'رصيد افتتاحي', '50,000', '0'],
            ['2023-01-05', '002', 'إيداع نقدي من المبيعات', '0', '25,000'],
            ['2023-01-10', '003', 'سحب نقدي للمصروفات', '15,000', '0'],
            ['2023-01-15', '004', 'تحصيل من العميل أحمد', '0', '35,000'],
            ['2023-01-20', '005', 'دفع للمورد شركة التقنية', '22,000', '0'],
            ['2023-01-25', '006', 'إيداع شيك العميل فاطمة', '0', '18,000'],
            ['2023-01-30', '007', 'رسوم بنكية شهرية', '500', '0']
        ],

        'account-statement': [
            ['2023-01-01', 'رصيد افتتاحي', '0', '0', '50,000'],
            ['2023-01-05', 'إيداع نقدي', '0', '25,000', '75,000'],
            ['2023-01-10', 'سحب نقدي', '15,000', '0', '60,000'],
            ['2023-01-15', 'تحصيل من عميل', '0', '35,000', '95,000'],
            ['2023-01-20', 'دفع للمورد', '22,000', '0', '73,000'],
            ['2023-01-25', 'إيداع شيك', '0', '18,000', '91,000'],
            ['2023-01-30', 'رسوم بنكية', '500', '0', '90,500']
        ],

        'trial-balance-movement': [
            ['1001', 'النقدية بالصندوق', '30,000', '125,000', '80,000', '75,000'],
            ['1002', 'البنك الأهلي', '150,000', '85,000', '45,000', '190,000'],
            ['1101', 'العملاء', '120,000', '180,000', '150,000', '150,000'],
            ['1102', 'مخصص الديون المشكوك فيها', '10,000', '5,000', '15,000', '20,000'],
            ['1201', 'المخزون', '280,000', '95,000', '120,000', '255,000'],
            ['1301', 'الأثاث والمعدات', '600,000', '50,000', '0', '650,000'],
            ['1302', 'مجمع إهلاك الأثاث والمعدات', '100,000', '0', '25,000', '125,000'],
            ['2001', 'الموردين', '100,000', '85,000', '110,000', '125,000'],
            ['2101', 'قروض قصيرة الأجل', '70,000', '15,000', '30,000', '85,000'],
            ['2102', 'مصروفات مستحقة', '35,000', '25,000', '35,000', '45,000'],
            ['3001', 'رأس المال', '600,000', '0', '200,000', '800,000'],
            ['3101', 'الاحتياطي القانوني', '105,000', '0', '15,000', '120,000'],
            ['4001', 'مبيعات المنتجات', '0', '0', '1,250,000', '1,250,000'],
            ['5001', 'تكلفة البضاعة المباعة', '0', '750,000', '0', '750,000'],
            ['6001', 'مصروفات إدارية', '0', '180,000', '0', '180,000'],
            ['الإجمالي', '', '2,200,000', '1,680,000', '2,080,000', '2,200,000']
        ],

        'expenses-report': [
            ['2023-12-01', 'مصروفات إدارية', '15,000', 'رواتب الموظفين', 'الشركة'],
            ['2023-12-02', 'مصروفات تسويق', '8,500', 'إعلانات جوجل', 'جوجل'],
            ['2023-12-03', 'مصروفات عمومية', '3,200', 'فواتير كهرباء', 'شركة الكهرباء'],
            ['2023-12-04', 'مصروفات إدارية', '2,800', 'قرطاسية ومستلزمات', 'مكتبة الرياض'],
            ['2023-12-05', 'مصروفات تسويق', '12,000', 'حملة إعلانية', 'وكالة الإبداع'],
            ['2023-12-06', 'مصروفات عمومية', '1,500', 'صيانة المعدات', 'شركة الصيانة'],
            ['2023-12-07', 'مصروفات إدارية', '5,200', 'اجتماعات وضيافة', 'فندق الريتز'],
            ['2023-12-08', 'مصروفات تسويق', '7,800', 'معرض تجاري', 'مركز المعارض']
        ],

        'journal-entries': [
            ['001', '2023-12-01', 'النقدية', '50,000', '0'],
            ['001', '2023-12-01', 'رأس المال', '0', '50,000'],
            ['002', '2023-12-02', 'المخزون', '85,000', '0'],
            ['002', '2023-12-02', 'الموردين', '0', '85,000'],
            ['003', '2023-12-03', 'العملاء', '115,000', '0'],
            ['003', '2023-12-03', 'المبيعات', '0', '100,000'],
            ['003', '2023-12-03', 'ضريبة القيمة المضافة', '0', '15,000'],
            ['004', '2023-12-04', 'تكلفة البضاعة المباعة', '65,000', '0'],
            ['004', '2023-12-04', 'المخزون', '0', '65,000'],
            ['005', '2023-12-05', 'مصروفات إدارية', '25,000', '0'],
            ['005', '2023-12-05', 'النقدية', '0', '25,000']
        ],

        'vat-summary': [
            ['يناير 2023', '850,000', '127,500', '520,000', '78,000', '49,500'],
            ['فبراير 2023', '920,000', '138,000', '580,000', '87,000', '51,000'],
            ['مارس 2023', '1,100,000', '165,000', '650,000', '97,500', '67,500'],
            ['الربع الأول 2023', '2,870,000', '430,500', '1,750,000', '262,500', '168,000'],
            ['أبريل 2023', '980,000', '147,000', '590,000', '88,500', '58,500'],
            ['مايو 2023', '1,050,000', '157,500', '620,000', '93,000', '64,500'],
            ['يونيو 2023', '1,150,000', '172,500', '680,000', '102,000', '70,500'],
            ['الربع الثاني 2023', '3,180,000', '477,000', '1,890,000', '283,500', '193,500']
        ],

        'vat-details': [
            ['2023-12-01', 'INV-001', 'أحمد محمد علي', '100,000', '15,000', '115,000'],
            ['2023-12-02', 'INV-002', 'شركة الأمل للتجارة', '200,000', '30,000', '230,000'],
            ['2023-12-03', 'PUR-001', 'شركة التقنية المتقدمة', '150,000', '22,500', '172,500'],
            ['2023-12-04', 'INV-003', 'فاطمة عبدالله', '80,000', '12,000', '92,000'],
            ['2023-12-05', 'PUR-002', 'مؤسسة الإمداد الشامل', '120,000', '18,000', '138,000'],
            ['2023-12-06', 'INV-004', 'مؤسسة النور', '180,000', '27,000', '207,000'],
            ['2023-12-07', 'PUR-003', 'شركة المواد الأولية', '95,000', '14,250', '109,250'],
            ['2023-12-08', 'INV-005', 'شركة المستقبل', '160,000', '24,000', '184,000']
        ],

        'vat-declaration': [
            ['المبيعات الخاضعة للضريبة', '301', '2,870,000', 'إجمالي المبيعات الخاضعة لضريبة 15%'],
            ['ضريبة المخرجات', '302', '430,500', 'ضريبة القيمة المضافة على المبيعات'],
            ['المشتريات الخاضعة للضريبة', '303', '1,750,000', 'إجمالي المشتريات الخاضعة لضريبة 15%'],
            ['ضريبة المدخلات', '304', '262,500', 'ضريبة القيمة المضافة على المشتريات'],
            ['صافي الضريبة المستحقة', '305', '168,000', 'الفرق بين ضريبة المخرجات والمدخلات'],
            ['تعديلات الفترة السابقة', '306', '0', 'تعديلات على إقرارات سابقة'],
            ['إجمالي الضريبة المستحقة', '307', '168,000', 'المبلغ النهائي المستحق للسداد'],
            ['الضريبة المدفوعة مقدماً', '308', '150,000', 'المبالغ المدفوعة خلال الفترة'],
            ['صافي المبلغ المستحق', '309', '18,000', 'المبلغ المتبقي للسداد أو المسترد']
        ]
    };

    return rowsMap[filter] || [
        ['النقدية', '50,000 ر.س', '125,000 ر.س', '85,000 ر.س', '90,000 ر.س'],
        ['العملاء', '25,000 ر.س', '75,000 ر.س', '60,000 ر.س', '40,000 ر.س'],
        ['الموردين', '15,000 ر.س', '45,000 ر.س', '55,000 ر.س', '25,000 ر.س'],
        ['المبيعات', '0 ر.س', '0 ر.س', '200,000 ر.س', '200,000 ر.س'],
        ['المشتريات', '150,000 ر.س', '150,000 ر.س', '0 ر.س', '150,000 ر.س']
    ];
}

// دوال مساعدة
function getCategoryTitle(category) {
    const titles = {
        'financial': 'التقارير المالية',
        'sales': 'تقارير المبيعات',
        'purchases': 'تقارير المشتريات',
        'inventory': 'تقارير المخزون',
        'customers': 'تقارير العملاء',
        'suppliers': 'تقارير الموردين'
    };
    return titles[category] || 'تقرير';
}

function getReportTypeText(type) {
    const types = {
        'summary': 'ملخص',
        'detailed': 'مفصل',
        'comparative': 'مقارن'
    };
    return types[type] || 'ملخص';
}

function getOutputFormatText(format) {
    const formats = {
        'table': 'جدول',
        'chart': 'رسم بياني',
        'both': 'جدول ورسم بياني'
    };
    return formats[format] || 'جدول';
}

// دوال الطباعة والتصدير
function printReports() {
    if (currentReportData) {
        // طباعة التقرير الحالي
        const printWindow = window.open('', '_blank');
        const reportHTML = generatePrintableReport(currentReportData);

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة التقرير</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .report-header { text-align: center; margin-bottom: 30px; }
                    .report-summary { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
                </style>
            </head>
            <body>
                ${reportHTML}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    } else {
        window.print();
    }
}

function exportToExcel() {
    if (currentReportData) {
        alert('سيتم تصدير التقرير إلى Excel قريباً');
        // هنا يمكن إضافة كود تصدير Excel الفعلي
    } else {
        alert('يرجى إنشاء تقرير أولاً');
    }
}

function exportToPDF() {
    if (currentReportData) {
        alert('سيتم تصدير التقرير إلى PDF قريباً');
        // هنا يمكن إضافة كود تصدير PDF الفعلي
    } else {
        alert('يرجى إنشاء تقرير أولاً');
    }
}

function generatePrintableReport(reportData) {
    const { category, startDate, endDate, reportType, outputFormat, data } = reportData;

    return `
        <div class="report-header">
            <h1>${getCategoryTitle(category)}</h1>
            <h3>من ${startDate} إلى ${endDate}</h3>
        </div>

        <div class="report-summary">
            <p><strong>نوع التقرير:</strong> ${getReportTypeText(reportType)}</p>
            <p><strong>تنسيق الإخراج:</strong> ${getOutputFormatText(outputFormat)}</p>
            <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>

        <table>
            <thead>
                <tr>
                    ${data.headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.rows.map(row => `
                    <tr>
                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// دالة إنشاء تقرير عام
function generateReport() {
    alert('يرجى اختيار فئة التقرير أولاً');
}

// دوال مساعدة جديدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function getFilterText(filter) {
    if (!filter) return 'جميع البيانات';

    const filterTexts = {
        // التقارير المالية
        'balance-sheet': 'قائمة المركز المالي',
        'profit-loss': 'قائمة الأرباح والخسائر',
        'cash-flow': 'قائمة التدفقات النقدية',
        'equity-changes': 'قائمة التغيرات في حقوق الملكية',
        'account-statement': 'كشف الحساب',
        'trial-balance-movement': 'ميزان المراجعة مع الحركة',
        'trial-balance': 'ميزان المراجعة',
        'expenses-report': 'تقرير المصروفات',
        'journal-entries': 'تقرير قيود اليومية',
        'vat-summary': 'ملخص تقرير ضريبة القيمة المضافة',
        'vat-details': 'تفاصيل ضريبة القيمة المضافة',
        'vat-declaration': 'إقرار ضريبة القيمة المضافة',
        'general-ledger': 'دفتر الأستاذ العام',

        // تقارير المبيعات
        'paid': 'المدفوعة فقط',
        'pending': 'المعلقة فقط',
        'overdue': 'المتأخرة فقط',
        'top-customers': 'أفضل العملاء',
        'daily': 'اليومية',
        'weekly': 'الأسبوعية',
        'monthly': 'الشهرية',
        'by-product': 'حسب المنتج',

        // تقارير المشتريات
        'top-suppliers': 'أفضل الموردين',
        'by-category': 'حسب الفئة',

        // تقارير المخزون
        'in-stock': 'المتوفرة فقط',
        'low-stock': 'المنخفضة فقط',
        'out-of-stock': 'المنتهية فقط',
        'high-value': 'عالية القيمة فقط',
        'fast-moving': 'سريعة الحركة',
        'slow-moving': 'بطيئة الحركة',
        'expired': 'منتهية الصلاحية',

        // تقارير العملاء
        'active': 'النشطين فقط',
        'inactive': 'غير النشطين فقط',
        'vip': 'المميزين فقط',
        'debtors': 'المدينين فقط',
        'new': 'الجدد فقط',
        'returning': 'المتكررين فقط',
        'by-region': 'حسب المنطقة',

        // تقارير الموردين
        'preferred': 'المفضلين فقط',
        'creditors': 'الدائنين فقط',
        'reliable': 'الموثوقين فقط',
        'high-volume': 'عالي الحجم'
    };

    return filterTexts[filter] || filter;
}

function generateStatistics(mockData, category, filter = '') {
    // إنشاء إحصائيات حسب الفئة والمرشح
    const statsMap = {
        'financial': {
            'balance-sheet': [
                { label: 'إجمالي الأصول', value: '1,650,000 ر.س', icon: 'fas fa-coins', color: 'success' },
                { label: 'إجمالي الخصوم', value: '495,000 ر.س', icon: 'fas fa-credit-card', color: 'warning' },
                { label: 'حقوق الملكية', value: '1,155,000 ر.س', icon: 'fas fa-user-tie', color: 'primary' },
                { label: 'نسبة المديونية', value: '30.0%', icon: 'fas fa-percentage', color: 'success' }
            ],
            'profit-loss': [
                { label: 'إجمالي الإيرادات', value: '1,400,000 ر.س', icon: 'fas fa-chart-line', color: 'success' },
                { label: 'إجمالي التكاليف', value: '822,000 ر.س', icon: 'fas fa-money-bill', color: 'warning' },
                { label: 'صافي الربح', value: '150,000 ر.س', icon: 'fas fa-trophy', color: 'primary' },
                { label: 'هامش الربح', value: '10.7%', icon: 'fas fa-percentage', color: 'success' }
            ],
            'cash-flow': [
                { label: 'التدفق التشغيلي', value: '263,000 ر.س', icon: 'fas fa-cogs', color: 'success' },
                { label: 'التدفق الاستثماري', value: '(160,000) ر.س', icon: 'fas fa-chart-line', color: 'warning' },
                { label: 'التدفق التمويلي', value: '140,000 ر.س', icon: 'fas fa-university', color: 'primary' },
                { label: 'صافي التدفق', value: '243,000 ر.س', icon: 'fas fa-water', color: 'success' }
            ],
            'equity-changes': [
                { label: 'رأس المال', value: '800,000 ر.س', icon: 'fas fa-building', color: 'primary' },
                { label: 'الاحتياطيات', value: '195,000 ر.س', icon: 'fas fa-shield-alt', color: 'info' },
                { label: 'الأرباح المحتجزة', value: '185,000 ر.س', icon: 'fas fa-piggy-bank', color: 'success' },
                { label: 'إجمالي حقوق الملكية', value: '1,205,000 ر.س', icon: 'fas fa-user-tie', color: 'primary' }
            ],
            'trial-balance': [
                { label: 'إجمالي المدين', value: '2,612,000 ر.س', icon: 'fas fa-plus', color: 'success' },
                { label: 'إجمالي الدائن', value: '2,612,000 ر.س', icon: 'fas fa-minus', color: 'info' },
                { label: 'الفرق', value: '0 ر.س', icon: 'fas fa-balance-scale', color: 'primary' },
                { label: 'عدد الحسابات', value: '23 حساب', icon: 'fas fa-list', color: 'success' }
            ],
            'account-statement': [
                { label: 'الرصيد الافتتاحي', value: '50,000 ر.س', icon: 'fas fa-play', color: 'info' },
                { label: 'إجمالي المدين', value: '37,500 ر.س', icon: 'fas fa-plus', color: 'warning' },
                { label: 'إجمالي الدائن', value: '78,000 ر.س', icon: 'fas fa-minus', color: 'success' },
                { label: 'الرصيد الختامي', value: '90,500 ر.س', icon: 'fas fa-stop', color: 'primary' }
            ],
            'general-ledger': [
                { label: 'عدد القيود', value: '7 قيود', icon: 'fas fa-list-ol', color: 'info' },
                { label: 'إجمالي المدين', value: '122,500 ر.س', icon: 'fas fa-plus', color: 'success' },
                { label: 'إجمالي الدائن', value: '133,000 ر.س', icon: 'fas fa-minus', color: 'warning' },
                { label: 'صافي الحركة', value: '10,500 ر.س', icon: 'fas fa-exchange-alt', color: 'primary' }
            ],
            'trial-balance-movement': [
                { label: 'إجمالي الأرصدة الافتتاحية', value: '2,200,000 ر.س', icon: 'fas fa-play', color: 'info' },
                { label: 'إجمالي الحركة المدينة', value: '1,680,000 ر.س', icon: 'fas fa-plus', color: 'success' },
                { label: 'إجمالي الحركة الدائنة', value: '2,080,000 ر.س', icon: 'fas fa-minus', color: 'warning' },
                { label: 'إجمالي الأرصدة الختامية', value: '2,200,000 ر.س', icon: 'fas fa-stop', color: 'primary' }
            ],
            'expenses-report': [
                { label: 'إجمالي المصروفات', value: '56,000 ر.س', icon: 'fas fa-money-bill', color: 'warning' },
                { label: 'مصروفات إدارية', value: '23,000 ر.س', icon: 'fas fa-user-tie', color: 'primary' },
                { label: 'مصروفات تسويق', value: '28,300 ر.س', icon: 'fas fa-bullhorn', color: 'info' },
                { label: 'مصروفات عمومية', value: '4,700 ر.س', icon: 'fas fa-cogs', color: 'success' }
            ],
            'journal-entries': [
                { label: 'عدد القيود', value: '5 قيود', icon: 'fas fa-book', color: 'info' },
                { label: 'إجمالي المدين', value: '355,000 ر.س', icon: 'fas fa-plus', color: 'success' },
                { label: 'إجمالي الدائن', value: '355,000 ر.س', icon: 'fas fa-minus', color: 'success' },
                { label: 'التوازن', value: 'متوازن', icon: 'fas fa-check-circle', color: 'primary' }
            ],
            'vat-summary': [
                { label: 'إجمالي المبيعات الخاضعة', value: '6,050,000 ر.س', icon: 'fas fa-shopping-cart', color: 'success' },
                { label: 'إجمالي ضريبة المخرجات', value: '907,500 ر.س', icon: 'fas fa-arrow-up', color: 'primary' },
                { label: 'إجمالي ضريبة المدخلات', value: '546,000 ر.س', icon: 'fas fa-arrow-down', color: 'warning' },
                { label: 'صافي الضريبة المستحقة', value: '361,500 ر.س', icon: 'fas fa-balance-scale', color: 'info' }
            ],
            'vat-details': [
                { label: 'عدد الفواتير', value: '8 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'إجمالي قبل الضريبة', value: '1,085,000 ر.س', icon: 'fas fa-calculator', color: 'primary' },
                { label: 'إجمالي الضريبة', value: '162,750 ر.س', icon: 'fas fa-percentage', color: 'warning' },
                { label: 'إجمالي شامل الضريبة', value: '1,247,750 ر.س', icon: 'fas fa-money-bill-wave', color: 'success' }
            ],
            'vat-declaration': [
                { label: 'ضريبة المخرجات', value: '430,500 ر.س', icon: 'fas fa-arrow-up', color: 'success' },
                { label: 'ضريبة المدخلات', value: '262,500 ر.س', icon: 'fas fa-arrow-down', color: 'warning' },
                { label: 'صافي المستحق', value: '168,000 ر.س', icon: 'fas fa-balance-scale', color: 'primary' },
                { label: 'المتبقي للسداد', value: '18,000 ر.س', icon: 'fas fa-credit-card', color: 'info' }
            ],
            'default': [
                { label: 'إجمالي الأصول', value: '1,650,000 ر.س', icon: 'fas fa-coins', color: 'success' },
                { label: 'إجمالي الخصوم', value: '495,000 ر.س', icon: 'fas fa-credit-card', color: 'warning' },
                { label: 'صافي الربح', value: '150,000 ر.س', icon: 'fas fa-chart-line', color: 'primary' },
                { label: 'معدل العائد', value: '10.7%', icon: 'fas fa-arrow-up', color: 'success' }
            ]
        },
        'sales': {
            'paid': [
                { label: 'الفواتير المدفوعة', value: '44,950 ر.س', icon: 'fas fa-check-circle', color: 'success' },
                { label: 'عدد الفواتير', value: '5 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '8,990 ر.س', icon: 'fas fa-calculator', color: 'primary' },
                { label: 'معدل التحصيل', value: '100%', icon: 'fas fa-percentage', color: 'success' }
            ],
            'pending': [
                { label: 'الفواتير المعلقة', value: '17,500 ر.س', icon: 'fas fa-clock', color: 'warning' },
                { label: 'عدد الفواتير', value: '3 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '5,833 ر.س', icon: 'fas fa-calculator', color: 'primary' },
                { label: 'أيام التأخير', value: '12 يوم', icon: 'fas fa-calendar', color: 'warning' }
            ],
            'overdue': [
                { label: 'الفواتير المتأخرة', value: '22,500 ر.س', icon: 'fas fa-exclamation-triangle', color: 'warning' },
                { label: 'عدد الفواتير', value: '3 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط التأخير', value: '18 يوم', icon: 'fas fa-calendar-times', color: 'warning' },
                { label: 'نسبة المخاطر', value: 'عالية', icon: 'fas fa-shield-alt', color: 'warning' }
            ],
            'default': [
                { label: 'إجمالي المبيعات', value: '62,450 ر.س', icon: 'fas fa-shopping-cart', color: 'primary' },
                { label: 'عدد الفواتير', value: '6 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '10,408 ر.س', icon: 'fas fa-calculator', color: 'success' },
                { label: 'معدل النمو', value: '+8.3%', icon: 'fas fa-arrow-up', color: 'success' }
            ]
        },
        'purchases': {
            'paid': [
                { label: 'المشتريات المدفوعة', value: '46,700 ر.س', icon: 'fas fa-check-circle', color: 'success' },
                { label: 'عدد الفواتير', value: '4 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '11,675 ر.س', icon: 'fas fa-calculator', color: 'primary' },
                { label: 'معدل السداد', value: '100%', icon: 'fas fa-percentage', color: 'success' }
            ],
            'pending': [
                { label: 'المشتريات المعلقة', value: '17,500 ر.س', icon: 'fas fa-clock', color: 'warning' },
                { label: 'عدد الفواتير', value: '3 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '5,833 ر.س', icon: 'fas fa-calculator', color: 'primary' },
                { label: 'أيام الاستحقاق', value: '8 أيام', icon: 'fas fa-calendar', color: 'warning' }
            ],
            'default': [
                { label: 'إجمالي المشتريات', value: '64,200 ر.س', icon: 'fas fa-truck', color: 'primary' },
                { label: 'عدد الفواتير', value: '6 فواتير', icon: 'fas fa-file-invoice', color: 'info' },
                { label: 'متوسط الفاتورة', value: '10,700 ر.س', icon: 'fas fa-calculator', color: 'success' },
                { label: 'توفير التكاليف', value: '8,500 ر.س', icon: 'fas fa-piggy-bank', color: 'success' }
            ]
        },
        'inventory': {
            'in-stock': [
                { label: 'المنتجات المتوفرة', value: '138 منتج', icon: 'fas fa-check-circle', color: 'success' },
                { label: 'قيمة المخزون', value: '92,950 ر.س', icon: 'fas fa-warehouse', color: 'primary' },
                { label: 'متوسط القيمة', value: '673 ر.س', icon: 'fas fa-calculator', color: 'info' },
                { label: 'معدل الدوران', value: '4.8 مرة', icon: 'fas fa-sync', color: 'success' }
            ],
            'low-stock': [
                { label: 'المنتجات المنخفضة', value: '23 منتج', icon: 'fas fa-exclamation-triangle', color: 'warning' },
                { label: 'قيمة المخزون', value: '1,300 ر.س', icon: 'fas fa-warehouse', color: 'warning' },
                { label: 'يحتاج إعادة طلب', value: '18 منتج', icon: 'fas fa-shopping-cart', color: 'warning' },
                { label: 'أولوية عالية', value: '5 منتجات', icon: 'fas fa-flag', color: 'warning' }
            ],
            'out-of-stock': [
                { label: 'المنتجات المنتهية', value: '3 منتجات', icon: 'fas fa-times-circle', color: 'warning' },
                { label: 'خسارة مبيعات', value: '2,850 ر.س', icon: 'fas fa-chart-line-down', color: 'warning' },
                { label: 'طلبات معلقة', value: '12 طلب', icon: 'fas fa-clock', color: 'info' },
                { label: 'وقت التوريد', value: '5-7 أيام', icon: 'fas fa-calendar', color: 'info' }
            ],
            'default': [
                { label: 'إجمالي المنتجات', value: '164 منتج', icon: 'fas fa-boxes', color: 'primary' },
                { label: 'قيمة المخزون', value: '94,250 ر.س', icon: 'fas fa-warehouse', color: 'success' },
                { label: 'منتجات منخفضة', value: '23 منتج', icon: 'fas fa-exclamation-triangle', color: 'warning' },
                { label: 'معدل الدوران', value: '4.2 مرة', icon: 'fas fa-sync', color: 'info' }
            ]
        },
        'customers': {
            'active': [
                { label: 'العملاء النشطين', value: '4 عملاء', icon: 'fas fa-user-check', color: 'success' },
                { label: 'إجمالي المشتريات', value: '123,500 ر.س', icon: 'fas fa-shopping-cart', color: 'primary' },
                { label: 'متوسط الشراء', value: '30,875 ر.س', icon: 'fas fa-calculator', color: 'info' },
                { label: 'معدل التكرار', value: '2.3 مرة/شهر', icon: 'fas fa-sync', color: 'success' }
            ],
            'debtors': [
                { label: 'العملاء المدينين', value: '4 عملاء', icon: 'fas fa-money-bill-wave', color: 'warning' },
                { label: 'إجمالي المديونيات', value: '21,000 ر.س', icon: 'fas fa-credit-card', color: 'warning' },
                { label: 'متوسط الدين', value: '5,250 ر.س', icon: 'fas fa-calculator', color: 'info' },
                { label: 'أقدم دين', value: '15 يوم', icon: 'fas fa-calendar', color: 'warning' }
            ],
            'default': [
                { label: 'إجمالي العملاء', value: '6 عملاء', icon: 'fas fa-users', color: 'primary' },
                { label: 'عملاء نشطين', value: '4 عملاء', icon: 'fas fa-user-check', color: 'success' },
                { label: 'إجمالي المديونيات', value: '17,000 ر.س', icon: 'fas fa-money-bill-wave', color: 'warning' },
                { label: 'متوسط الشراء', value: '22,583 ر.س', icon: 'fas fa-chart-bar', color: 'info' }
            ]
        },
        'suppliers': {
            'active': [
                { label: 'الموردين النشطين', value: '4 موردين', icon: 'fas fa-user-check', color: 'success' },
                { label: 'إجمالي المشتريات', value: '227,000 ر.س', icon: 'fas fa-truck', color: 'primary' },
                { label: 'متوسط الطلب', value: '56,750 ر.س', icon: 'fas fa-calculator', color: 'info' },
                { label: 'معدل التوريد', value: '95%', icon: 'fas fa-percentage', color: 'success' }
            ],
            'creditors': [
                { label: 'الموردين الدائنين', value: '4 موردين', icon: 'fas fa-money-bill-wave', color: 'warning' },
                { label: 'إجمالي المستحقات', value: '38,000 ر.س', icon: 'fas fa-credit-card', color: 'warning' },
                { label: 'متوسط المستحق', value: '9,500 ر.س', icon: 'fas fa-calculator', color: 'info' },
                { label: 'أقرب استحقاق', value: '5 أيام', icon: 'fas fa-calendar', color: 'warning' }
            ],
            'default': [
                { label: 'إجمالي الموردين', value: '6 موردين', icon: 'fas fa-user-tie', color: 'primary' },
                { label: 'موردين نشطين', value: '4 موردين', icon: 'fas fa-user-check', color: 'success' },
                { label: 'إجمالي المستحقات', value: '33,000 ر.س', icon: 'fas fa-money-bill-wave', color: 'warning' },
                { label: 'متوسط الطلب', value: '44,167 ر.س', icon: 'fas fa-chart-bar', color: 'info' }
            ]
        }
    };

    const categoryStats = statsMap[category];
    if (!categoryStats) return null;

    return categoryStats[filter] || categoryStats['default'] || null;
}

function addTableAnimationStyles() {
    if (!document.querySelector('#table-animation-styles')) {
        const style = document.createElement('style');
        style.id = 'table-animation-styles';
        style.textContent = `
            .table-row {
                opacity: 0;
                animation: fadeInRow 0.5s ease forwards;
            }

            @keyframes fadeInRow {
                from { opacity: 0; transform: translateX(-20px); }
                to { opacity: 1; transform: translateX(0); }
            }

            .loading-indicator {
                text-align: center;
                padding: 60px 20px;
            }

            .spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }

            .report-statistics {
                background: white;
                border-radius: 12px;
                padding: 25px;
                margin-bottom: 25px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .report-statistics h4 {
                margin: 0 0 20px 0;
                color: #2c3e50;
                font-size: 18px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
                transition: all 0.2s ease;
            }

            .stat-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: white;
            }

            .stat-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            .stat-icon.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
            .stat-icon.warning { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }
            .stat-icon.info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }

            .stat-details h5 {
                margin: 0 0 5px 0;
                color: #6c757d;
                font-size: 14px;
                font-weight: 500;
            }

            .stat-value {
                font-size: 18px;
                font-weight: 600;
                color: #2c3e50;
            }

            .table-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .table-header h4 {
                margin: 0;
                color: #2c3e50;
                font-size: 18px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .table-actions {
                display: flex;
                gap: 10px;
            }

            .btn-small {
                padding: 8px 15px;
                font-size: 12px;
                border-radius: 6px;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .btn-small.btn-secondary {
                background: #6c757d;
                color: white;
            }

            .btn-small.btn-secondary:hover {
                background: #5a6268;
                transform: translateY(-1px);
            }

            .table-container {
                overflow-x: auto;
                border-radius: 8px;
            }

            .table-footer {
                margin-top: 15px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                text-align: center;
            }

            .table-footer p {
                margin: 0;
                color: #6c757d;
                font-size: 14px;
            }
        `;
        document.head.appendChild(style);
    }
}

function generateCharts(mockData, category) {
    // هنا يمكن إضافة كود إنشاء الرسوم البيانية باستخدام Chart.js
    console.log('إنشاء الرسوم البيانية للفئة:', category);
}

function exportTableToExcel() {
    alert('سيتم تصدير الجدول إلى Excel قريباً');
}

function printTable() {
    const table = document.getElementById('report-table');
    if (table) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة الجدول</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                </style>
            </head>
            <body>
                <h2>تقرير مفصل</h2>
                ${table.outerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// دوال مساعدة للتنسيق والأنماط
function getReportTableTitle(category, filter) {
    const titles = {
        'financial': {
            'balance-sheet': 'قائمة المركز المالي (الميزانية العمومية)',
            'profit-loss': 'قائمة الأرباح والخسائر',
            'cash-flow': 'قائمة التدفقات النقدية',
            'equity-changes': 'قائمة التغيرات في حقوق الملكية',
            'account-statement': 'كشف الحساب',
            'trial-balance-movement': 'ميزان المراجعة مع الحركة',
            'trial-balance': 'ميزان المراجعة',
            'expenses-report': 'تقرير المصروفات',
            'journal-entries': 'تقرير قيود اليومية',
            'vat-summary': 'ملخص تقرير ضريبة القيمة المضافة',
            'vat-details': 'تفاصيل ضريبة القيمة المضافة',
            'vat-declaration': 'إقرار ضريبة القيمة المضافة',
            'general-ledger': 'دفتر الأستاذ العام'
        }
    };

    if (category === 'financial' && titles.financial[filter]) {
        return titles.financial[filter];
    }

    return 'البيانات التفصيلية';
}

function getRowClass(category, filter, row, index) {
    if (category !== 'financial') return '';

    const firstCell = row[0] || '';

    // قائمة المركز المالي
    if (filter === 'balance-sheet') {
        if (firstCell.includes('الأصول') || firstCell.includes('الخصوم') || firstCell.includes('حقوق الملكية')) {
            return 'balance-sheet-section';
        }
        if (firstCell.includes('إجمالي')) {
            return 'balance-sheet-total';
        }
        if (firstCell === '') {
            return 'financial-spacer';
        }
    }

    // قائمة الأرباح والخسائر
    if (filter === 'profit-loss') {
        if (firstCell.includes('الإيرادات') || firstCell.includes('مبيعات') || firstCell.includes('إيرادات')) {
            return 'profit-loss-revenue';
        }
        if (firstCell.includes('تكلفة') || firstCell.includes('مصروفات')) {
            return 'profit-loss-expense';
        }
        if (firstCell.includes('إجمالي') || firstCell.includes('صافي') || firstCell.includes('الربح')) {
            return 'profit-loss-total';
        }
        if (firstCell === '') {
            return 'financial-spacer';
        }
    }

    // قائمة التدفقات النقدية
    if (filter === 'cash-flow') {
        if (firstCell.includes('التشغيلية')) {
            return 'cash-flow-operating';
        }
        if (firstCell.includes('الاستثمارية')) {
            return 'cash-flow-investing';
        }
        if (firstCell.includes('التمويلية')) {
            return 'cash-flow-financing';
        }
        if (firstCell.includes('صافي')) {
            return 'cash-flow-total';
        }
        if (firstCell === '') {
            return 'financial-spacer';
        }
    }

    // ميزان المراجعة
    if (filter === 'trial-balance' && firstCell === 'الإجمالي') {
        return 'trial-balance-total';
    }

    // قيود اليومية
    if (filter === 'journal-entries') {
        return 'journal-entry-number';
    }

    return '';
}

function getCellClass(category, filter, cellIndex, cellValue) {
    if (category !== 'financial') return '';

    // ميزان المراجعة - أعمدة المدين والدائن
    if (filter === 'trial-balance') {
        if (cellIndex === 2) return 'trial-balance-debit financial-amount';
        if (cellIndex === 3) return 'trial-balance-credit financial-amount';
    }

    // كشف الحساب
    if (filter === 'account-statement') {
        if (cellIndex === 2) return 'account-statement-debit financial-amount';
        if (cellIndex === 3) return 'account-statement-credit financial-amount';
        if (cellIndex === 4) return 'account-statement-balance financial-amount';
    }

    // دفتر الأستاذ والقيود
    if (filter === 'general-ledger' || filter === 'journal-entries') {
        if (cellIndex === 3) return 'journal-entry-debit financial-amount';
        if (cellIndex === 4) return 'journal-entry-credit financial-amount';
    }

    // الأعمدة المالية العامة
    if (cellIndex === 1 && (cellValue.includes('ر.س') || cellValue.includes('%'))) {
        return 'financial-amount';
    }

    if (cellValue.includes('%')) {
        return 'financial-percentage';
    }

    return '';
}

function formatCellValue(cellValue, category, filter, cellIndex) {
    if (category !== 'financial') return cellValue;

    // تنسيق الأرقام المالية
    if (typeof cellValue === 'string' && cellValue.includes('ر.س')) {
        const isNegative = cellValue.includes('(') && cellValue.includes(')');
        if (isNegative) {
            return `<span class="financial-negative">${cellValue}</span>`;
        } else if (cellValue !== '0 ر.س') {
            return `<span class="financial-positive">${cellValue}</span>`;
        } else {
            return `<span class="financial-zero">${cellValue}</span>`;
        }
    }

    // تنسيق النسب المئوية
    if (typeof cellValue === 'string' && cellValue.includes('%')) {
        const isNegative = cellValue.includes('-') || (cellValue.includes('(') && cellValue.includes(')'));
        if (isNegative) {
            return `<span class="financial-negative">${cellValue}</span>`;
        } else {
            return `<span class="financial-positive">${cellValue}</span>`;
        }
    }

    return cellValue;
}

// دوال التحكم في التقرير
function toggleReportControls() {
    const content = document.getElementById('report-controls-content');
    const button = document.querySelector('.toggle-controls-btn i');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        button.className = 'fas fa-chevron-up';
    } else {
        content.style.display = 'none';
        button.className = 'fas fa-chevron-down';
    }
}



function updateCurrentReport() {
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    const reportType = document.getElementById('report-type-control').value;
    const outputFormat = document.getElementById('output-format-control').value;

    if (!startDate || !endDate) {
        alert('يرجى اختيار الفترة الزمنية');
        return;
    }

    // إعادة إنشاء التقرير بالخيارات الجديدة
    generateMockReport(currentCategory, startDate, endDate, reportType, outputFormat);
}


