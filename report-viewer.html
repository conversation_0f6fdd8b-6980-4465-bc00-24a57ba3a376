<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض التقرير - نظام إدارة الأعمال</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/reports.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        /* أنماط خاصة بصفحة عرض التقرير */
        .report-viewer-container {
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .report-options {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .report-options .option-group {
            flex: 1;
            min-width: 200px;
        }
        
        .report-options label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .report-options select,
        .report-options input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .report-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .report-actions .btn-group {
            display: flex;
            gap: 10px;
        }
        
        .report-title {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .report-title h2 {
            margin: 0;
            color: #333;
        }
        
        .report-title .report-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .report-content {
            overflow-x: auto;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .report-table th,
        .report-table td {
            padding: 10px 15px;
            border: 1px solid #ddd;
            text-align: right;
        }
        
        .report-table th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        
        .report-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .report-table tfoot td {
            font-weight: 600;
            background-color: #f2f2f2;
        }
        
        @media print {
            .report-options,
            .report-actions,
            .sidebar,
            .navbar,
            .footer,
            .main-header {
                display: none !important;
            }
            
            body {
                background-color: white;
                font-size: 12pt;
            }
            
            .report-viewer-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            
            .report-table {
                width: 100%;
                border-collapse: collapse;
            }
            
            .report-table th, 
            .report-table td {
                border: 1px solid #000;
            }
            
            .report-title {
                margin-bottom: 20px;
                text-align: center;
            }
            
            .report-title h2 {
                font-size: 18pt;
                margin-bottom: 10px;
            }
            
            .report-title .report-date {
                font-size: 12pt;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
        </div>
    </header>

    <!-- صفحة عرض التقرير -->
    <section class="report-page">
        <div class="container">
            <div class="report-viewer-container">
                <!-- خيارات التقرير -->
                <div class="report-options">
                    <div class="option-group">
                        <label for="account">الحساب:</label>
                        <select id="account" class="form-control">
                            <option value="">-- اختر الحساب --</option>
                            <option value="1001">الصندوق الرئيسي</option>
                            <option value="1002">البنك الأهلي</option>
                            <option value="1003">بنك الراجحي</option>
                            <option value="1004">حساب المبيعات</option>
                            <option value="1005">حساب المشتريات</option>
                            <option value="1006">حساب المصروفات</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label for="customer">العميل/المورد:</label>
                        <select id="customer" class="form-control">
                            <option value="">-- اختر العميل/المورد --</option>
                            <optgroup label="العملاء">
                                <option value="2001">شركة الأمل</option>
                                <option value="2002">مؤسسة النور</option>
                                <option value="2003">شركة الإبداع</option>
                                <option value="2004">مؤسسة الفجر</option>
                            </optgroup>
                            <optgroup label="الموردين">
                                <option value="3001">شركة التوريدات العامة</option>
                                <option value="3002">مؤسسة الإمداد</option>
                                <option value="3003">شركة المستلزمات</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="option-group">
                        <label for="start-date">من تاريخ:</label>
                        <input type="date" id="start-date" class="form-control">
                    </div>
                    <div class="option-group">
                        <label for="end-date">إلى تاريخ:</label>
                        <input type="date" id="end-date" class="form-control">
                    </div>
                </div>
                
                <!-- أزرار التقرير -->
                <div class="report-actions">
                    <div class="btn-group">
                        <button id="refresh-btn" class="btn btn-primary"><i class="fas fa-sync-alt"></i> تحديث</button>
                    </div>
                    <div class="btn-group">
                        <button id="export-btn" class="btn btn-success"><i class="fas fa-file-export"></i> تصدير</button>
                        <button id="print-btn" class="btn btn-info"><i class="fas fa-print"></i> طباعة</button>
                    </div>
                </div>
                
                <!-- عنوان التقرير -->
                <div class="report-title">
                    <h2 id="report-title">كشف حساب</h2>
                    <div class="report-date" id="report-date-range">الفترة: 01/01/2023 - 30/06/2023</div>
                </div>
                
                <!-- محتوى التقرير -->
                <div class="report-content">
                    <table class="report-table">
                        <thead>
                            <tr id="report-header-row">
                                <th>التاريخ</th>
                                <th>المستند</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody id="report-table-body">
                            <!-- سيتم إضافة صفوف التقرير هنا عبر JavaScript -->
                        </tbody>
                        <tfoot>
                            <tr id="report-footer-row">
                                <td colspan="3">الإجمالي</td>
                                <td id="total-debit">2,500.00 ر.س</td>
                                <td id="total-credit">4,500.00 ر.س</td>
                                <td id="total-balance">2,000.00 ر.س</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script src="js/report-viewer.js"></script>
</body>
</html>