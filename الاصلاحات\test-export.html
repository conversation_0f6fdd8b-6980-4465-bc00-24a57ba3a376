<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف التصدير</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .test-table th, .test-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .test-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            color: white;
        }
        .excel-btn {
            background: #27ae60;
        }
        .pdf-btn {
            background: #e74c3c;
        }
        .print-btn {
            background: #3498db;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار وظائف التصدير</h1>
        <p>هذه صفحة اختبار للتأكد من عمل وظائف التصدير بشكل صحيح.</p>
        
        <div class="test-buttons">
            <button class="test-btn excel-btn" onclick="testExcelExport()">
                <i class="fas fa-file-excel"></i> اختبار Excel
            </button>
            <button class="test-btn pdf-btn" onclick="testPDFExport()">
                <i class="fas fa-file-pdf"></i> اختبار PDF
            </button>
            <button class="test-btn print-btn" onclick="testPrint()">
                <i class="fas fa-print"></i> اختبار الطباعة
            </button>
        </div>
        
        <table class="test-table" id="test-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2023-06-20</td>
                    <td>أحمد محمد علي</td>
                    <td>1,250.00 ر.س</td>
                    <td>مدفوعة</td>
                </tr>
                <tr>
                    <td>2023-06-19</td>
                    <td>شركة النور للتجارة</td>
                    <td>3,500.00 ر.س</td>
                    <td>معلقة</td>
                </tr>
                <tr>
                    <td>2023-06-18</td>
                    <td>فاطمة عبدالله</td>
                    <td>750.00 ر.س</td>
                    <td>مدفوعة</td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="2">الإجمالي</td>
                    <td>5,500.00 ر.س</td>
                    <td>-</td>
                </tr>
            </tfoot>
        </table>
        
        <div id="status"></div>
    </div>

    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div style="padding: 10px; margin: 10px 0; border-radius: 5px; background: ${type === 'success' ? '#d4edda' : '#f8d7da'}; color: ${type === 'success' ? '#155724' : '#721c24'};">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        function testExcelExport() {
            try {
                const table = document.getElementById('test-table');
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.table_to_sheet(table);
                
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');
                XLSX.writeFile(wb, 'test-export.xlsx');
                
                showStatus('تم تصدير Excel بنجاح!', 'success');
            } catch (error) {
                showStatus('خطأ في تصدير Excel: ' + error.message, 'error');
                console.error(error);
            }
        }

        function testPDFExport() {
            try {
                const element = document.querySelector('.test-container');
                
                html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');
                    
                    const imgWidth = 210;
                    const pageHeight = 295;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    
                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                    pdf.save('test-export.pdf');
                    
                    showStatus('تم تصدير PDF بنجاح!', 'success');
                }).catch(error => {
                    showStatus('خطأ في تصدير PDF: ' + error.message, 'error');
                    console.error(error);
                });
            } catch (error) {
                showStatus('خطأ في تصدير PDF: ' + error.message, 'error');
                console.error(error);
            }
        }

        function testPrint() {
            try {
                window.print();
                showStatus('تم فتح نافذة الطباعة بنجاح!', 'success');
            } catch (error) {
                showStatus('خطأ في الطباعة: ' + error.message, 'error');
                console.error(error);
            }
        }

        // اختبار تحميل المكتبات
        window.onload = function() {
            let status = 'جميع المكتبات محملة بنجاح: ';
            
            if (typeof XLSX !== 'undefined') {
                status += 'Excel ✓ ';
            } else {
                status += 'Excel ✗ ';
            }
            
            if (typeof window.jspdf !== 'undefined') {
                status += 'PDF ✓ ';
            } else {
                status += 'PDF ✗ ';
            }
            
            if (typeof html2canvas !== 'undefined') {
                status += 'Canvas ✓';
            } else {
                status += 'Canvas ✗';
            }
            
            showStatus(status, 'info');
        };
    </script>
</body>
</html>
