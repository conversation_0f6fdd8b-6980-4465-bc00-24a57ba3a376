<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حل مشكلة الحرف العربي (ؤ)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
            border-right: 4px solid #3498db;
            padding-right: 10px;
        }
        .solution {
            background-color: #f8f9fa;
            border-right: 4px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .solution h3 {
            color: #2ecc71;
            margin-top: 0;
        }
        .warning {
            background-color: #fff3cd;
            border-right: 4px solid #f1c40f;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .warning h3 {
            color: #e67e22;
            margin-top: 0;
        }
        .error-example {
            background-color: #f8d7da;
            border-right: 4px solid #e74c3c;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            direction: ltr;
            text-align: left;
        }
        .code {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
        .keyboard-shortcut {
            display: inline-block;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 2px 5px;
            font-family: monospace;
            margin: 0 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>حل مشكلة الحرف العربي (ؤ)</h1>
        
        <p>لوحظ وجود مشكلة متكررة عند محاولة تشغيل الأوامر في سطر الأوامر (Command Prompt) أو PowerShell، حيث يظهر حرف عربي (ؤ) قبل الأمر مما يؤدي إلى فشل تنفيذ الأمر.</p>
        
        <div class="error-example">
(TraeAI-3) C:\Users\<USER>\Downloads\Monjiz [1:] > ؤ'' /c start c:\Users\<USER>\Downloads\Monjiz\start_server.bat
ؤ : The term 'ؤ' is not recognized as the name of a cmdlet, function, script file, or operable program. 
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.   
At line:1 char:1
+ ؤ'' /c start c:\Users\<USER>\Downloads\Monjiz\start_server.bat
+ ~~~
    + CategoryInfo          : ObjectNotFound: (ؤ:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException</div>
        
        <h2>سبب المشكلة</h2>
        <p>هذه المشكلة مرتبطة بإعدادات لغة لوحة المفاتيح أو نظام التشغيل. عندما تكون لغة لوحة المفاتيح مضبوطة على العربية، قد يتم إدخال حرف عربي (ؤ) بشكل غير مرئي قبل الأوامر التي تكتبها.</p>
        
        <div class="warning">
            <h3>ملاحظة هامة</h3>
            <p>هذه المشكلة تحدث غالباً عندما تكون لغة لوحة المفاتيح مضبوطة على العربية أثناء استخدام سطر الأوامر أو PowerShell.</p>
            <p>في بعض الحالات، قد تحتاج إلى تغيير لغة النظام الافتراضية. <a href="system-language.html" style="color: #9b59b6; font-weight: bold;">انقر هنا</a> للحصول على تعليمات حول كيفية تغيير لغة النظام في ويندوز.</p>
        </div>
        
        <h2>الحلول المقترحة</h2>
        
        <div class="solution">
            <h3>الحل 1: استخدام ملفات التشغيل المباشر</h3>
            <p>قمنا بإنشاء عدة ملفات لتشغيل النظام بدون مشاكل:</p>
            <ul>
                <li><strong class="code">open_launcher.vbs</strong>: <strong>الحل المفضل</strong> - ملف VBScript يمكنك النقر عليه مباشرة لفتح صفحة المشغل الرئيسية.</li>
                <li><strong class="code">open_launcher.ps1</strong>: ملف PowerShell لفتح صفحة المشغل.</li>
                <li><strong class="code">start_server.bat</strong>: ملف Batch لفتح صفحة المشغل.</li>
            </ul>
            <p>هذه الملفات ستقوم بفتح <strong class="code">launcher.html</strong> التي تحتوي على روابط لجميع صفحات النظام.</p>
        </div>
        
        <div class="solution">
            <h3>الحل 2: تغيير لغة لوحة المفاتيح</h3>
            <p>قبل كتابة أي أمر في سطر الأوامر، تأكد من تبديل لغة لوحة المفاتيح إلى الإنجليزية (EN).</p>
            <p>يمكنك استخدام اختصار لوحة المفاتيح <span class="keyboard-shortcut">Alt + Shift</span> أو <span class="keyboard-shortcut">Windows + Space</span> للتبديل بين اللغات.</p>
            <p><a href="keyboard-language.html" style="color: #3498db; font-weight: bold;">انقر هنا</a> للحصول على تعليمات مفصلة حول كيفية تغيير لغة لوحة المفاتيح في ويندوز.</p>
        </div>
        
        <div class="solution">
            <h3>الحل 3: فتح الملفات مباشرة</h3>
            <p>يمكنك فتح ملفات HTML مباشرة من مستكشف الملفات بالنقر المزدوج عليها.</p>
            <p>استخدم <strong class="code">launcher.html</strong> للوصول إلى جميع صفحات النظام بسهولة.</p>
        </div>
        
        <div class="buttons">
            <a href="launcher.html" class="btn btn-green">فتح مشغل النظام</a>
            <a href="README.md" class="btn">عرض ملف التعليمات</a>
        </div>
    </div>
</body>
</html>