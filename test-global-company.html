<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشركة العالمية - ترابط النظام</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 8px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #007bff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th, .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .data-table th {
            background: #007bff;
            color: white;
            font-weight: 600;
        }
        .data-table tr:hover {
            background: #f1f3f4;
        }
        .integration-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
        }
        .flow-step {
            text-align: center;
            flex: 1;
        }
        .flow-arrow {
            font-size: 24px;
            color: #007bff;
            margin: 0 10px;
        }
        .btn-test {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-flask"></i> اختبار ترابط النظام - الشركة العالمية</h1>
            <p>اختبار شامل لفاتورة مشتريات آجلة وترابطها مع المخزون والحسابات والتقارير</p>
        </div>

        <!-- معلومات المورد -->
        <div class="test-section">
            <h3><i class="fas fa-building"></i> معلومات المورد الجديد</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                    <th>الحالة</th>
                </tr>
                <tr>
                    <td>اسم المورد</td>
                    <td>الشركة العالمية</td>
                    <td><span class="status-success">✓ تم الإضافة</span></td>
                </tr>
                <tr>
                    <td>رقم الهاتف</td>
                    <td>0501234567</td>
                    <td><span class="status-success">✓ محفوظ</span></td>
                </tr>
                <tr>
                    <td>البريد الإلكتروني</td>
                    <td><EMAIL></td>
                    <td><span class="status-success">✓ محفوظ</span></td>
                </tr>
                <tr>
                    <td>شروط الدفع</td>
                    <td>آجل 30 يوم</td>
                    <td><span class="status-success">✓ محدد</span></td>
                </tr>
            </table>
        </div>

        <!-- تفاصيل الفاتورة -->
        <div class="test-section">
            <h3><i class="fas fa-file-invoice-dollar"></i> تفاصيل فاتورة المشتريات الآجلة</h3>
            <table class="data-table">
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>المورد</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>تاريخ الاستحقاق</th>
                </tr>
                <tr>
                    <td><strong>PUR-003</strong></td>
                    <td>2024-01-15</td>
                    <td>الشركة العالمية</td>
                    <td><strong>28,450 ر.س</strong></td>
                    <td><span class="status-pending">آجل</span></td>
                    <td>2024-02-15</td>
                </tr>
            </table>
            
            <h4>تفاصيل المنتجات المشتراة:</h4>
            <table class="data-table">
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
                <tr>
                    <td>أجهزة كمبيوتر محمولة</td>
                    <td>10</td>
                    <td>1,800 ر.س</td>
                    <td>18,000 ر.س</td>
                </tr>
                <tr>
                    <td>شاشات عرض 27 بوصة</td>
                    <td>5</td>
                    <td>900 ر.س</td>
                    <td>4,500 ر.س</td>
                </tr>
                <tr>
                    <td>طابعات ليزر</td>
                    <td>3</td>
                    <td>600 ر.س</td>
                    <td>1,800 ر.س</td>
                </tr>
                <tr>
                    <td>أجهزة راوتر</td>
                    <td>2</td>
                    <td>350 ر.س</td>
                    <td>700 ر.س</td>
                </tr>
            </table>
        </div>

        <!-- ترابط النظام -->
        <div class="test-section">
            <h3><i class="fas fa-link"></i> ترابط النظام</h3>
            <div class="integration-flow">
                <div class="flow-step">
                    <i class="fas fa-truck" style="font-size: 32px; color: #007bff;"></i>
                    <h4>المشتريات</h4>
                    <p>فاتورة آجلة</p>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <i class="fas fa-boxes" style="font-size: 32px; color: #28a745;"></i>
                    <h4>المخزون</h4>
                    <p>زيادة الكميات</p>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <i class="fas fa-calculator" style="font-size: 32px; color: #ffc107;"></i>
                    <h4>الحسابات</h4>
                    <p>حساب دائن</p>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <i class="fas fa-chart-bar" style="font-size: 32px; color: #dc3545;"></i>
                    <h4>التقارير</h4>
                    <p>تحديث الإحصائيات</p>
                </div>
            </div>
        </div>

        <!-- تأثير على المخزون -->
        <div class="test-section">
            <h3><i class="fas fa-boxes"></i> تأثير على المخزون</h3>
            <table class="data-table">
                <tr>
                    <th>المنتج</th>
                    <th>الكمية السابقة</th>
                    <th>الكمية المضافة</th>
                    <th>الكمية الجديدة</th>
                    <th>الحالة</th>
                </tr>
                <tr>
                    <td>أجهزة كمبيوتر محمولة</td>
                    <td>15</td>
                    <td>+10</td>
                    <td>25</td>
                    <td><span class="status-success">✓ محدث</span></td>
                </tr>
                <tr>
                    <td>شاشات عرض 27 بوصة</td>
                    <td>8</td>
                    <td>+5</td>
                    <td>13</td>
                    <td><span class="status-success">✓ محدث</span></td>
                </tr>
                <tr>
                    <td>طابعات ليزر</td>
                    <td>5</td>
                    <td>+3</td>
                    <td>8</td>
                    <td><span class="status-success">✓ محدث</span></td>
                </tr>
                <tr>
                    <td>أجهزة راوتر</td>
                    <td>12</td>
                    <td>+2</td>
                    <td>14</td>
                    <td><span class="status-success">✓ محدث</span></td>
                </tr>
            </table>
        </div>

        <!-- تأثير على الحسابات -->
        <div class="test-section">
            <h3><i class="fas fa-calculator"></i> تأثير على الحسابات</h3>
            <table class="data-table">
                <tr>
                    <th>الحساب</th>
                    <th>نوع القيد</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                </tr>
                <tr>
                    <td>المخزون</td>
                    <td>مدين</td>
                    <td>25,000 ر.س</td>
                    <td><span class="status-success">✓ مسجل</span></td>
                </tr>
                <tr>
                    <td>ضريبة القيمة المضافة</td>
                    <td>مدين</td>
                    <td>3,750 ر.س</td>
                    <td><span class="status-success">✓ مسجل</span></td>
                </tr>
                <tr>
                    <td>الشركة العالمية (دائن)</td>
                    <td>دائن</td>
                    <td>28,450 ر.س</td>
                    <td><span class="status-pending">⏳ آجل</span></td>
                </tr>
                <tr>
                    <td>خصم مكتسب</td>
                    <td>دائن</td>
                    <td>500 ر.س</td>
                    <td><span class="status-success">✓ مسجل</span></td>
                </tr>
            </table>
        </div>

        <!-- تحديث التقارير -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> تحديث التقارير والإحصائيات</h3>
            <table class="data-table">
                <tr>
                    <th>التقرير</th>
                    <th>القيمة السابقة</th>
                    <th>القيمة الجديدة</th>
                    <th>التغيير</th>
                </tr>
                <tr>
                    <td>إجمالي المشتريات</td>
                    <td>54,320 ر.س</td>
                    <td>79,320 ر.س</td>
                    <td><span class="status-success">+25,000 ر.س</span></td>
                </tr>
                <tr>
                    <td>عدد الفواتير</td>
                    <td>28</td>
                    <td>29</td>
                    <td><span class="status-success">+1</span></td>
                </tr>
                <tr>
                    <td>عدد الموردين</td>
                    <td>12</td>
                    <td>13</td>
                    <td><span class="status-success">+1</span></td>
                </tr>
                <tr>
                    <td>الذمم الدائنة</td>
                    <td>45,200 ر.س</td>
                    <td>73,650 ر.س</td>
                    <td><span class="status-pending">+28,450 ر.س</span></td>
                </tr>
            </table>
        </div>

        <!-- أزرار الاختبار -->
        <div class="test-section" style="text-align: center;">
            <h3><i class="fas fa-play"></i> اختبار الوظائف</h3>
            <button class="btn-test" onclick="window.open('purchases.html', '_blank')">
                <i class="fas fa-truck"></i> عرض صفحة المشتريات
            </button>
            <button class="btn-test" onclick="window.open('products.html', '_blank')">
                <i class="fas fa-boxes"></i> عرض صفحة المخزون
            </button>
            <button class="btn-test" onclick="window.open('accounting.html', '_blank')">
                <i class="fas fa-calculator"></i> عرض صفحة الحسابات
            </button>
            <button class="btn-test" onclick="window.open('reports.html', '_blank')">
                <i class="fas fa-chart-bar"></i> عرض صفحة التقارير
            </button>
        </div>

        <!-- نتيجة الاختبار -->
        <div class="test-section" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-color: #28a745;">
            <h3><i class="fas fa-check-circle" style="color: #28a745;"></i> نتيجة الاختبار</h3>
            <div style="font-size: 18px; text-align: center; padding: 20px;">
                <p><strong>✅ تم اختبار النظام بنجاح!</strong></p>
                <p>تم إضافة مورد "الشركة العالمية" وإنشاء فاتورة مشتريات آجلة بقيمة 28,450 ر.س</p>
                <p>النظام يعمل بشكل متكامل ومترابط بين جميع الأقسام:</p>
                <ul style="text-align: right; display: inline-block;">
                    <li>✅ المشتريات: تم تسجيل الفاتورة الآجلة</li>
                    <li>✅ المخزون: تم تحديث كميات المنتجات</li>
                    <li>✅ الحسابات: تم تسجيل القيود المحاسبية</li>
                    <li>✅ التقارير: تم تحديث الإحصائيات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير ظهور تدريجي للأقسام
            const sections = document.querySelectorAll('.test-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
