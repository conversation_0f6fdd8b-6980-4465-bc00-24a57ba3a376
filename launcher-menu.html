<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة تشغيل النظام</title>
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        header {
            background-color: #3498db;
            color: white;
            padding: 20px 0;
            text-align: center;
            border-radius: 5px 5px 0 0;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0;
            font-size: 2.2rem;
        }
        h2 {
            color: #2980b9;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        p {
            margin-bottom: 15px;
        }
        .launcher-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .launcher-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .launcher-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        .launcher-icon {
            font-size: 48px;
            margin-bottom: 15px;
            text-align: center;
        }
        .launcher-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1.2rem;
            text-align: center;
        }
        .launcher-desc {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
            flex-grow: 1;
        }
        .launcher-actions {
            margin-top: auto;
            display: flex;
            justify-content: center;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            font-weight: bold;
            text-align: center;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
        .btn-orange {
            background-color: #e67e22;
        }
        .btn-orange:hover {
            background-color: #d35400;
        }
        .btn-purple {
            background-color: #9b59b6;
        }
        .btn-purple:hover {
            background-color: #8e44ad;
        }
        .btn-red {
            background-color: #e74c3c;
        }
        .btn-red:hover {
            background-color: #c0392b;
        }
        .note {
            background-color: #f8f9fa;
            border-right: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .warning {
            background-color: #fff3cd;
            border-right: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-right: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .help-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        .help-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .help-link {
            display: inline-block;
            background-color: #e9ecef;
            color: #333;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-size: 0.9rem;
        }
        .help-link:hover {
            background-color: #3498db;
            color: white;
        }
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #7f8c8d;
        }
        .tag {
            display: inline-block;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .tag-recommended {
            background-color: #d4edda;
            color: #155724;
        }
        .tag-alternative {
            background-color: #fff3cd;
            color: #856404;
        }
        .tag-advanced {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        @media (max-width: 768px) {
            .launcher-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        function openLauncher() {
            window.location.href = 'launcher.html';
        }
        
        function openDirectLaunch() {
            window.location.href = 'direct-launch.html';
        }
        
        function openHelpPage(page) {
            window.location.href = page;
        }
    </script>
</head>
<body>
    <header>
        <div class="container">
            <h1>قائمة تشغيل نظام إدارة الأعمال</h1>
        </div>
    </header>

    <div class="container">
        <div class="success">
            <h3>مرحبًا بك في قائمة تشغيل النظام</h3>
            <p>هذه الصفحة توفر لك جميع الطرق المتاحة لتشغيل النظام. اختر الطريقة التي تناسبك من القائمة أدناه.</p>
        </div>

        <h2>طرق التشغيل المباشر</h2>
        <p>هذه الطرق لا تتطلب استخدام سطر الأوامر وتتجنب مشكلة الحرف العربي (ؤ):</p>

        <div class="launcher-grid">
            <div class="launcher-card">
                <div class="launcher-icon">🚀</div>
                <div class="launcher-title">التشغيل المباشر من المتصفح</div>
                <span class="tag tag-recommended">موصى به</span>
                <div class="launcher-desc">أسهل طريقة لتشغيل النظام مباشرة من المتصفح بدون الحاجة إلى ملفات خارجية.</div>
                <div class="launcher-actions">
                    <button onclick="openDirectLaunch()" class="btn btn-green">تشغيل مباشر</button>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">📄</div>
                <div class="launcher-title">فتح launcher.html مباشرة</div>
                <span class="tag tag-recommended">موصى به</span>
                <div class="launcher-desc">فتح ملف launcher.html مباشرة من المتصفح للوصول إلى صفحة المشغل الرئيسية.</div>
                <div class="launcher-actions">
                    <button onclick="openLauncher()" class="btn btn-green">فتح المشغل</button>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">📜</div>
                <div class="launcher-title">ملف VBScript</div>
                <span class="tag tag-recommended">موصى به</span>
                <div class="launcher-desc">استخدام ملف open_launcher.vbs لفتح النظام، وهو الحل المفضل والأكثر موثوقية.</div>
                <div class="launcher-actions">
                    <a href="run_vbs.bat" class="btn">تشغيل VBS</a>
                </div>
            </div>
        </div>

        <h2>ملفات البات الجديدة</h2>
        <p>ملفات بات جديدة مصممة خصيصًا لتجاوز مشكلة الحرف العربي:</p>

        <div class="launcher-grid">
            <div class="launcher-card">
                <div class="launcher-icon">⚙️</div>
                <div class="launcher-title">تشغيل ملف VBS</div>
                <span class="tag tag-alternative">بديل</span>
                <div class="launcher-desc">ملف بات لتشغيل ملف VBS بطرق متعددة، ويتجاوز مشكلة الحرف العربي.</div>
                <div class="launcher-actions">
                    <a href="run_vbs.bat" class="btn btn-orange">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">⚙️</div>
                <div class="launcher-title">تشغيل ملف PowerShell</div>
                <span class="tag tag-alternative">بديل</span>
                <div class="launcher-desc">ملف بات لتشغيل ملف PowerShell بطرق متعددة، ويتجاوز مشكلة الحرف العربي.</div>
                <div class="launcher-actions">
                    <a href="run_ps1.bat" class="btn btn-orange">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">⚙️</div>
                <div class="launcher-title">تشغيل ملف Python</div>
                <span class="tag tag-alternative">بديل</span>
                <div class="launcher-desc">ملف بات لتشغيل ملف Python بطرق متعددة، ويتجاوز مشكلة الحرف العربي.</div>
                <div class="launcher-actions">
                    <a href="run_py.bat" class="btn btn-orange">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">⚙️</div>
                <div class="launcher-title">تشغيل ملف JavaScript</div>
                <span class="tag tag-alternative">بديل</span>
                <div class="launcher-desc">ملف بات لتشغيل ملف JavaScript بطرق متعددة، ويتجاوز مشكلة الحرف العربي.</div>
                <div class="launcher-actions">
                    <a href="run_js.bat" class="btn btn-orange">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">⚙️</div>
                <div class="launcher-title">تشغيل الخادم مباشرة</div>
                <span class="tag tag-alternative">بديل</span>
                <div class="launcher-desc">ملف بات لتشغيل الخادم مباشرة، ويتجاوز مشكلة الحرف العربي.</div>
                <div class="launcher-actions">
                    <a href="start_server.bat" class="btn btn-orange">تشغيل</a>
                </div>
            </div>
        </div>

        <h2>ملفات التشغيل المباشر</h2>
        <p>ملفات التشغيل الأصلية المحسنة مع معالجة أفضل للأخطاء:</p>

        <div class="launcher-grid">
            <div class="launcher-card">
                <div class="launcher-icon">📋</div>
                <div class="launcher-title">ملف VBScript</div>
                <span class="tag tag-advanced">متقدم</span>
                <div class="launcher-desc">ملف VBScript محسن مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.</div>
                <div class="launcher-actions">
                    <a href="open_launcher.vbs" class="btn btn-purple">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">📋</div>
                <div class="launcher-title">ملف PowerShell</div>
                <span class="tag tag-advanced">متقدم</span>
                <div class="launcher-desc">ملف PowerShell محسن مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.</div>
                <div class="launcher-actions">
                    <a href="open_launcher.ps1" class="btn btn-purple">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">📋</div>
                <div class="launcher-title">ملف Python</div>
                <span class="tag tag-advanced">متقدم</span>
                <div class="launcher-desc">ملف Python محسن مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.</div>
                <div class="launcher-actions">
                    <a href="open_launcher.py" class="btn btn-purple">تشغيل</a>
                </div>
            </div>

            <div class="launcher-card">
                <div class="launcher-icon">📋</div>
                <div class="launcher-title">ملف JavaScript</div>
                <span class="tag tag-advanced">متقدم</span>
                <div class="launcher-desc">ملف JavaScript محسن مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.</div>
                <div class="launcher-actions">
                    <a href="open_launcher.js" class="btn btn-purple">تشغيل</a>
                </div>
            </div>
        </div>

        <div class="warning">
            <h3>ملاحظة هامة</h3>
            <p>إذا كنت تواجه مشكلة في تشغيل النظام، تأكد من تغيير لغة لوحة المفاتيح إلى اللغة الإنجليزية قبل محاولة تشغيل أي ملف.</p>
            <p>للمزيد من المعلومات حول حل مشكلة الحرف العربي، يمكنك زيارة <a href="fix-arabic-char.html">صفحة حل مشكلة الحرف العربي</a>.</p>
        </div>

        <div class="help-section">
            <h2>صفحات المساعدة</h2>
            <p>إذا كنت تواجه أي مشاكل في تشغيل النظام، يمكنك الاطلاع على صفحات المساعدة التالية:</p>
            <div class="help-links">
                <a href="direct-launch.html" class="help-link">التشغيل المباشر</a>
                <a href="fix-arabic-char.html" class="help-link">حل مشكلة الحرف العربي</a>
                <a href="keyboard-language.html" class="help-link">تغيير لغة لوحة المفاتيح</a>
                <a href="system-language.html" class="help-link">تغيير لغة النظام</a>
                <a href="important-notice.html" class="help-link">ملاحظة هامة</a>
                <a href="manual-launch.html" class="help-link">التشغيل اليدوي</a>
                <a href="README.md" class="help-link">ملف التعليمات</a>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openLauncher()" class="btn btn-green" style="font-size: 18px; padding: 15px 30px;">تشغيل النظام الآن</button>
        </div>

        <footer>
            <p>نظام إدارة الأعمال - جميع الحقوق محفوظة &copy; 2023</p>
        </footer>
    </div>
</body>
</html>