<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح إضافة المنتج - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .steps-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .steps-list h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .steps-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .steps-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .form-demo {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .form-demo h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .form-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشكلة إضافة المنتج</h1>

        <!-- المشاكل المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشاكل المكتشفة</h2>
            <div class="highlight">
                <h3>المشاكل الحالية:</h3>
                <p><strong>1. لا يوجد حقل سعر التكلفة</strong> - الحقل موجود لكن قد يكون مخفي</p>
                <p><strong>2. المنتج الجديد لا يضاف في قائمة المنتجات</strong> - مشكلة في التحديث</p>
            </div>
            
            <div class="form-demo">
                <h3>النموذج المتوقع:</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم المنتج *</label>
                        <input type="text" placeholder="اسم المنتج" value="جهاز اختبار">
                    </div>
                    <div class="form-group">
                        <label>الفئة *</label>
                        <input type="text" placeholder="الفئة" value="إلكترونيات">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الكود *</label>
                        <input type="text" placeholder="رمز المنتج" value="TEST-001">
                    </div>
                    <div class="form-group">
                        <label>الوصف</label>
                        <input type="text" placeholder="وصف المنتج" value="جهاز للاختبار">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>سعر البيع *</label>
                        <input type="number" placeholder="0.00" value="500">
                    </div>
                    <div class="form-group">
                        <label>سعر التكلفة</label>
                        <input type="number" placeholder="0.00" value="400">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الكمية الحالية</label>
                        <input type="number" placeholder="0" value="10">
                    </div>
                    <div class="form-group">
                        <label>الحد الأدنى للمخزون</label>
                        <input type="number" placeholder="0" value="5">
                    </div>
                </div>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="fixes-list">
                <h3>تم إصلاح:</h3>
                <ul>
                    <li>تحسين التحقق من البيانات - سعر التكلفة اختياري</li>
                    <li>إضافة console.log مفصل لتتبع المشاكل</li>
                    <li>تحديث دالة updateProductsDataDisplay لإعادة تحميل البيانات</li>
                    <li>تحسين دالة updateProductsTableDisplay مع تتبع الأخطاء</li>
                    <li>التأكد من حفظ البيانات في localStorage</li>
                </ul>
            </div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>🧪 خطوات اختبار الإصلاح</h2>
            
            <div class="steps-list">
                <h3>اختبر إضافة منتج جديد:</h3>
                <ol>
                    <li><strong>افتح صفحة المنتجات</strong></li>
                    <li><strong>افتح Developer Tools (F12)</strong> وانتقل لتبويب Console</li>
                    <li><strong>اضغط "منتج جديد"</strong></li>
                    <li><strong>تحقق من وجود جميع الحقول:</strong>
                        <ul>
                            <li>اسم المنتج (مطلوب)</li>
                            <li>الفئة (مطلوب)</li>
                            <li>الكود (مطلوب)</li>
                            <li>سعر البيع (مطلوب)</li>
                            <li>سعر التكلفة (اختياري)</li>
                            <li>الكمية (اختياري)</li>
                        </ul>
                    </li>
                    <li><strong>أدخل البيانات:</strong>
                        <ul>
                            <li>اسم المنتج: "جهاز اختبار"</li>
                            <li>الفئة: "إلكترونيات"</li>
                            <li>الكود: "TEST-001"</li>
                            <li>سعر البيع: "500"</li>
                            <li>سعر التكلفة: "400" (اختياري)</li>
                            <li>الكمية: "10"</li>
                        </ul>
                    </li>
                    <li><strong>اضغط "حفظ"</strong></li>
                    <li><strong>راقب Console للرسائل</strong></li>
                    <li><strong>يجب أن تظهر:</strong> "تم إضافة المنتج بنجاح"</li>
                    <li><strong>تحقق من الجدول:</strong> المنتج الجديد يظهر في القائمة</li>
                </ol>
            </div>
        </div>

        <!-- اختبار سريع -->
        <div class="test-section">
            <h2>⚡ اختبار سريع</h2>
            <button class="btn" onclick="testAddProductWithConsole()">📦 اختبار إضافة المنتج مع Console</button>
            <button class="btn" onclick="checkProductData()">📊 فحص بيانات المنتجات</button>
            <button class="btn" onclick="clearProductData()">🗑️ مسح البيانات</button>
            <button class="btn" onclick="addTestProduct()">➕ إضافة منتج تجريبي</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ جميع الحقول موجودة:</strong> اسم، فئة، كود، سعر بيع، سعر تكلفة، كمية</li>
                    <li><strong>✅ سعر التكلفة اختياري:</strong> يمكن تركه فارغ</li>
                    <li><strong>✅ رسائل Console واضحة:</strong> تتبع كل خطوة</li>
                    <li><strong>✅ حفظ البيانات:</strong> يحفظ في localStorage</li>
                    <li><strong>✅ تحديث الجدول:</strong> المنتج يظهر فوراً</li>
                    <li><strong>✅ رسالة النجاح:</strong> "تم إضافة المنتج بنجاح"</li>
                    <li><strong>✅ تحديث العداد:</strong> "عرض 1 - X من Y منتج"</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار إضافة المنتج مع Console
        function testAddProductWithConsole() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="success">
                    📦 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح Developer Tools (اضغط F12)<br>
                    2️⃣ انتقل لتبويب Console<br>
                    3️⃣ اضغط "منتج جديد"<br>
                    4️⃣ تحقق من وجود جميع الحقول<br>
                    5️⃣ أدخل البيانات (اسم، فئة، كود، سعر)<br>
                    6️⃣ اضغط "حفظ"<br>
                    7️⃣ راقب رسائل Console<br>
                    8️⃣ تحقق من ظهور المنتج في الجدول<br><br>
                    
                    💡 <strong>إذا لم يظهر المنتج، أرسل لي رسائل Console!</strong>
                </div>
            `);
        }

        // فحص بيانات المنتجات
        function checkProductData() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات المنتجات:</strong><br><br>
                    
                    📦 عدد المنتجات: ${productsData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                    📝 مفتاح التخزين: monjizProducts<br><br>
                    
                    <strong>آخر 3 منتجات:</strong><br>
                    ${productsData.slice(-3).map((p, i) => 
                        `${productsData.length - 2 + i}. ${p.name} - ${p.price} ر.س`
                    ).join('<br>')}<br><br>
                    
                    ${productsData.length > 0 ? 
                        '✅ <strong>البيانات موجودة ومحفوظة!</strong>' :
                        '❌ <strong>لا توجد بيانات - أضف منتج جديد</strong>'
                    }
                </div>
            `);
        }

        // مسح بيانات المنتجات
        function clearProductData() {
            localStorage.removeItem('monjizProducts');
            showResult('🗑️ تم مسح جميع بيانات المنتجات', 'info');
        }

        // إضافة منتج تجريبي
        function addTestProduct() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const testProduct = {
                id: Date.now(),
                name: 'منتج تجريبي',
                category: 'إلكترونيات',
                code: 'TEST-' + Date.now(),
                price: 500,
                cost: 400,
                quantity: 10,
                minQuantity: 5,
                description: 'منتج للاختبار',
                createdAt: new Date().toISOString()
            };
            
            productsData.push(testProduct);
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            showResult(`
                <div class="success">
                    ➕ <strong>تم إضافة منتج تجريبي!</strong><br><br>
                    📦 اسم المنتج: ${testProduct.name}<br>
                    💰 السعر: ${testProduct.price} ر.س<br>
                    📊 إجمالي المنتجات: ${productsData.length}<br><br>
                    💡 <strong>افتح صفحة المنتجات لرؤية المنتج الجديد!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح مشاكل إضافة المنتج!</strong><br><br>
                    
                    <strong>الإصلاحات المطبقة:</strong><br>
                    ✅ تحسين التحقق من البيانات<br>
                    ✅ إضافة console.log مفصل<br>
                    ✅ تحديث دوال العرض<br>
                    ✅ إصلاح حفظ البيانات<br><br>
                    
                    🧪 <strong>اختبر إضافة منتج جديد الآن!</strong><br>
                    💡 <strong>استخدم Developer Tools لمراقبة العملية!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
