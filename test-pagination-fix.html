<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التنقل بين الصفحات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .demo-pagination {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .demo-pagination h3 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
        }
        .pagination-demo {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .page-btn-demo {
            background: white;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .page-btn-demo:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }
        .page-btn-demo.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        .page-btn-demo.disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }
        .page-btn-demo.disabled:hover {
            background: #f8f9fa;
            transform: none;
        }
        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .features-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .features-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #dc3545; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>📄 اختبار إصلاح التنقل بين الصفحات</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>التنقل من صفحة 1 إلى صفحة 2 لا يعمل في المشتريات والعملاء - التنقل غير موجود في وسط الصفحة</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>أزرار التنقل لا تعمل</li>
                        <li>لا توجد دوال JavaScript للتنقل</li>
                        <li>التنقل في مواضع مختلفة</li>
                        <li>عدم تحديث البيانات عند التنقل</li>
                        <li>أنماط CSS غير مكتملة</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>تنقل فعال بين الصفحات</li>
                        <li>دوال JavaScript كاملة</li>
                        <li>تنقل موحد في وسط الصفحة</li>
                        <li>تحديث البيانات تلقائياً</li>
                        <li>تصميم احترافي ومتجاوب</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- عرض توضيحي للتنقل -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للتنقل</h2>
            <div class="highlight">
                <h3>هكذا سيبدو التنقل الجديد:</h3>
            </div>
            
            <div class="demo-pagination">
                <h3>تنقل تجريبي</h3>
                <div style="text-align: center; color: #6c757d; margin-bottom: 15px;">
                    عرض <span id="demo-start">1</span> - <span id="demo-end">10</span> من <span id="demo-total">25</span> نتيجة
                </div>
                <div class="pagination-demo">
                    <button class="page-btn-demo prev disabled" onclick="changeDemoPage('prev')">
                        <i class="fas fa-chevron-right"></i> السابق
                    </button>
                    <button class="page-btn-demo active" onclick="changeDemoPage(1)">1</button>
                    <button class="page-btn-demo" onclick="changeDemoPage(2)">2</button>
                    <button class="page-btn-demo" onclick="changeDemoPage(3)">3</button>
                    <button class="page-btn-demo next" onclick="changeDemoPage('next')">
                        التالي <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div id="demo-result" style="text-align: center; margin-top: 15px;"></div>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>صفحة المشتريات</th>
                        <th>صفحة العملاء</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أنماط CSS للتنقل</td>
                        <td>✅ مضافة</td>
                        <td>✅ مضافة</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>دوال JavaScript</td>
                        <td>✅ مضافة</td>
                        <td>✅ مضافة</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>تحديث HTML</td>
                        <td>✅ محديث</td>
                        <td>✅ محديث</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>بيانات تجريبية</td>
                        <td>✅ مضافة</td>
                        <td>✅ مضافة</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>التنقل في الوسط</td>
                        <td>✅ مطبق</td>
                        <td>✅ مطبق</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>التجاوب مع الشاشات</td>
                        <td>✅ مطبق</td>
                        <td>✅ مطبق</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الصفحات المصلحة -->
        <div class="test-section">
            <h2>📄 الصفحات المصلحة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>صفحة المشتريات</h3>
                    <p>تنقل فعال بين 25 عنصر مع 10 عناصر لكل صفحة</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>صفحة العملاء</h3>
                    <p>تنقل فعال بين 25 عميل مع 10 عملاء لكل صفحة</p>
                    <button class="btn" onclick="openCustomersPage()">فتح الصفحة</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار التنقل المصلح:</h3>
                <p>سنختبر التنقل في كلا الصفحتين للتأكد من عمله بشكل صحيح</p>
            </div>
            
            <button class="btn" onclick="startPaginationTest()">🚀 بدء اختبار التنقل</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار التنقل:</h3>
                <ol>
                    <li><strong>جرب التنقل التجريبي:</strong> اضغط أزرار التنقل أعلاه</li>
                    <li><strong>فتح صفحة المشتريات:</strong> اضغط "فتح الصفحة"</li>
                    <li><strong>اختبار التنقل:</strong> جرب الانتقال بين الصفحات 1، 2، 3</li>
                    <li><strong>اختبار الأزرار:</strong> جرب "السابق" و "التالي"</li>
                    <li><strong>فتح صفحة العملاء:</strong> كرر نفس الاختبار</li>
                    <li><strong>التحقق:</strong> تأكد من تحديث البيانات عند التنقل</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="openCustomersPage()">👥 فتح صفحة العملاء</button>
            <button class="btn" onclick="testPaginationDemo()">📄 اختبار التنقل التجريبي</button>
            <button class="btn" onclick="checkDataStatus()">📊 فحص حالة البيانات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>التنقل يعمل:</strong> الانتقال بين الصفحات 1، 2، 3 بسلاسة</li>
                    <li><strong>البيانات تتحديث:</strong> عرض بيانات مختلفة في كل صفحة</li>
                    <li><strong>الأزرار تعمل:</strong> "السابق" و "التالي" يعملان بشكل صحيح</li>
                    <li><strong>التنقل في الوسط:</strong> أزرار التنقل في وسط الصفحة</li>
                    <li><strong>معلومات النتائج:</strong> عرض "1-10 من 25" بشكل صحيح</li>
                    <li><strong>تصميم متجاوب:</strong> يعمل على جميع أحجام الشاشات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let demoCurrentPage = 1;
        const demoTotalPages = 3;
        const demoItemsPerPage = 10;
        const demoTotalItems = 25;

        // دالة تغيير الصفحة التجريبية
        function changeDemoPage(page) {
            if (page === 'prev') {
                if (demoCurrentPage > 1) {
                    demoCurrentPage--;
                }
            } else if (page === 'next') {
                if (demoCurrentPage < demoTotalPages) {
                    demoCurrentPage++;
                }
            } else if (typeof page === 'number') {
                if (page >= 1 && page <= demoTotalPages) {
                    demoCurrentPage = page;
                }
            }
            
            updateDemoPagination();
            showDemoResult(`تم الانتقال إلى الصفحة ${demoCurrentPage}`, 'success');
        }

        // دالة تحديث التنقل التجريبي
        function updateDemoPagination() {
            const startResult = (demoCurrentPage - 1) * demoItemsPerPage + 1;
            const endResult = Math.min(demoCurrentPage * demoItemsPerPage, demoTotalItems);
            
            document.getElementById('demo-start').textContent = startResult;
            document.getElementById('demo-end').textContent = endResult;
            document.getElementById('demo-total').textContent = demoTotalItems;
            
            // تحديث الأزرار
            const buttons = document.querySelectorAll('.page-btn-demo');
            buttons.forEach(btn => {
                btn.classList.remove('active', 'disabled');
                
                if (btn.textContent.trim() === demoCurrentPage.toString()) {
                    btn.classList.add('active');
                }
                
                if (btn.classList.contains('prev') && demoCurrentPage === 1) {
                    btn.classList.add('disabled');
                }
                
                if (btn.classList.contains('next') && demoCurrentPage === demoTotalPages) {
                    btn.classList.add('disabled');
                }
            });
        }

        // بدء اختبار التنقل
        function startPaginationTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار التنقل المصلح!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ جرب التنقل التجريبي أعلاه<br>
                    2️⃣ افتح صفحة المشتريات<br>
                    3️⃣ جرب الانتقال بين الصفحات 1، 2، 3<br>
                    4️⃣ اختبر أزرار "السابق" و "التالي"<br>
                    5️⃣ افتح صفحة العملاء وكرر الاختبار<br><br>
                    
                    <strong>🎯 اضغط أزرار فتح الصفحات للاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 جرب التنقل بين الصفحات في أسفل الجدول!', 'info');
        }

        // فتح صفحة العملاء
        function openCustomersPage() {
            window.open('customers.html', '_blank');
            showResult('👥 تم فتح صفحة العملاء<br>💡 جرب التنقل بين الصفحات في أسفل الجدول!', 'info');
        }

        // اختبار التنقل التجريبي
        function testPaginationDemo() {
            showResult(`
                <div class="info">
                    📄 <strong>اختبار التنقل التجريبي:</strong><br><br>
                    
                    <strong>🔧 الميزات المطبقة:</strong><br>
                    • تنقل فعال بين الصفحات<br>
                    • تحديث معلومات النتائج<br>
                    • أزرار "السابق" و "التالي"<br>
                    • تعطيل الأزرار عند الحاجة<br>
                    • تصميم احترافي ومتجاوب<br><br>
                    
                    <strong>📋 جرب التنقل أعلاه:</strong><br>
                    • اضغط على أرقام الصفحات<br>
                    • جرب أزرار "السابق" و "التالي"<br>
                    • لاحظ تحديث معلومات النتائج<br><br>
                    
                    ✅ <strong>نفس هذا التنقل مطبق في جميع الصفحات!</strong>
                </div>
            `);
        }

        // فحص حالة البيانات
        function checkDataStatus() {
            showResult(`
                <div class="info">
                    📊 <strong>فحص حالة البيانات:</strong><br><br>
                    
                    <strong>📈 البيانات المضافة:</strong><br>
                    🛒 المشتريات: 25 عنصر (10 لكل صفحة = 3 صفحات)<br>
                    👥 العملاء: 25 عميل (10 لكل صفحة = 3 صفحات)<br><br>
                    
                    <strong>🔧 الوظائف المطبقة:</strong><br>
                    • تحميل البيانات تلقائياً عند فتح الصفحة<br>
                    • تقسيم البيانات إلى صفحات<br>
                    • تحديث الجدول عند التنقل<br>
                    • عرض معلومات النتائج<br>
                    • أزرار تنقل ذكية<br><br>
                    
                    ✅ <strong>جميع البيانات جاهزة للاختبار!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // عرض نتائج العرض التوضيحي
        function showDemoResult(message, type = 'info') {
            document.getElementById('demo-result').innerHTML = `<div style="color: #667eea; font-weight: bold;">${message}</div>`;
        }

        // تحديث التنقل التجريبي عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateDemoPagination();
            showResult(`
                <div class="info">
                    📄 <strong>تم إصلاح التنقل بين الصفحات بنجاح!</strong><br><br>
                    ✅ أنماط CSS احترافية للتنقل<br>
                    ✅ دوال JavaScript كاملة وفعالة<br>
                    ✅ تنقل موحد في وسط الصفحة<br>
                    ✅ تحديث البيانات تلقائياً<br>
                    ✅ بيانات تجريبية للاختبار<br>
                    ✅ تصميم متجاوب للشاشات المختلفة<br><br>
                    🧪 <strong>اضغط "بدء اختبار التنقل" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
