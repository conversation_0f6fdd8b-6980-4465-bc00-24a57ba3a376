<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشغل نظام إدارة الأعمال</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #2980b9;
            text-align: center;
            margin-bottom: 30px;
        }
        .buttons {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 15px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            height: 100px;
        }
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn i {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .note {
            background-color: #f8f9fa;
            border-right: 4px solid #2980b9;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        .note h3 {
            margin-top: 0;
            color: #2980b9;
        }
        .note p {
            margin-bottom: 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>مشغل نظام إدارة الأعمال</h1>
        
        <div class="buttons">
            <a href="index.html" class="btn">
                <i class="fas fa-home"></i>
                الرئيسية
            </a>
            <a href="sales.html" class="btn">
                <i class="fas fa-shopping-cart"></i>
                المبيعات
            </a>
            <a href="purchases.html" class="btn">
                <i class="fas fa-truck"></i>
                المشتريات
            </a>
            <a href="customers.html" class="btn">
                <i class="fas fa-users"></i>
                العملاء
            </a>
            <a href="products.html" class="btn">
                <i class="fas fa-box"></i>
                المخزون
            </a>
            <a href="reports.html" class="btn">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <a href="accounting.html" class="btn">
                <i class="fas fa-calculator"></i>
                الحسابات
            </a>
        </div>
        
        <div class="note">
            <h3>ملاحظة</h3>
            <p>هذه الصفحة تساعدك على فتح صفحات النظام المختلفة بدون مشاكل. انقر على أي زر للانتقال إلى الصفحة المطلوبة.</p>
            <p>إذا واجهت أي مشكلة في تشغيل النظام، يمكنك استخدام أحد ملفات التشغيل المباشر (<strong>open_launcher.vbs</strong>، <strong>open_launcher.ps1</strong>، أو <strong>start_server.bat</strong>) لفتح هذه الصفحة مباشرة.</p>
            <p><a href="fix-arabic-char.html" style="color: #e74c3c; font-weight: bold;">انقر هنا</a> لمعرفة كيفية حل مشكلة الحرف العربي (ؤ) التي تظهر عند تشغيل الأوامر.</p>
        </div>
    </div>
</body>
</html>