<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج المورد الكامل - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .demo-form {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 30px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-group select {
            background: white;
            cursor: pointer;
        }
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        .btn-primary, .btn-secondary {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }
        .fields-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .fields-list ul {
            margin: 0;
            padding-right: 20px;
        }
        .fields-list li {
            margin: 8px 0;
            padding: 5px 0;
        }
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        .optional {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 اختبار نموذج المورد الكامل</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>نافذة إضافة مورد فارغة - لا توجد حقول لإدخال البيانات</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>نافذة فارغة بدون حقول</li>
                        <li>حقل واحد فقط (اسم المورد)</li>
                        <li>لا يمكن إدخال رقم الهاتف</li>
                        <li>لا يمكن إدخال البريد الإلكتروني</li>
                        <li>لا يمكن اختيار نوع المورد</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>نموذج كامل مع جميع الحقول</li>
                        <li>حقل اسم المورد (مطلوب)</li>
                        <li>حقل رقم الهاتف (مطلوب)</li>
                        <li>حقل البريد الإلكتروني (اختياري)</li>
                        <li>قائمة منسدلة لنوع المورد</li>
                        <li>قائمة منسدلة لفئة المورد</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الحقول المضافة -->
        <div class="test-section">
            <h2>📋 الحقول المضافة</h2>
            <div class="fields-list">
                <h3>الحقول الجديدة في النموذج:</h3>
                <ul>
                    <li><span class="required">اسم المورد *</span> - حقل نص مطلوب</li>
                    <li><span class="required">رقم الهاتف *</span> - حقل هاتف مطلوب</li>
                    <li><span class="optional">البريد الإلكتروني</span> - حقل بريد اختياري</li>
                    <li><span class="optional">نوع المورد</span> - قائمة منسدلة (شركة، مؤسسة، فرد)</li>
                    <li><span class="optional">فئة المورد</span> - قائمة منسدلة (إلكترونيات، مواد غذائية، إلخ)</li>
                </ul>
            </div>
        </div>

        <!-- عرض توضيحي للنموذج -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للنموذج الجديد</h2>
            <div class="demo-form">
                <h3>نموذج إضافة مورد (تجريبي)</h3>
                <form onsubmit="testCompleteForm(event)">
                    <div class="form-group">
                        <label for="testSupplierName">اسم المورد *</label>
                        <input type="text" id="testSupplierName" placeholder="أدخل اسم المورد" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierPhone">رقم الهاتف *</label>
                        <input type="tel" id="testSupplierPhone" placeholder="مثال: +966501234567" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierEmail">البريد الإلكتروني</label>
                        <input type="email" id="testSupplierEmail" placeholder="مثال: <EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierType">نوع المورد</label>
                        <select id="testSupplierType">
                            <option value="شركة">شركة</option>
                            <option value="مؤسسة">مؤسسة</option>
                            <option value="فرد">فرد</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierCategory">فئة المورد</label>
                        <select id="testSupplierCategory">
                            <option value="عام">عام</option>
                            <option value="إلكترونيات">إلكترونيات</option>
                            <option value="مواد غذائية">مواد غذائية</option>
                            <option value="مكتبية">مكتبية</option>
                            <option value="أثاث">أثاث</option>
                            <option value="ملابس">ملابس</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="clearCompleteForm()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
                <div id="demo-result"></div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار النموذج الكامل:</h3>
                <p>سنختبر النموذج الجديد مع جميع الحقول للتأكد من عمله بشكل صحيح</p>
            </div>
            
            <button class="btn" onclick="startCompleteFormTest()">🚀 بدء اختبار النموذج الكامل</button>
            <div id="test-result"></div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openSuppliersPage()">👥 فتح صفحة الموردين</button>
            <button class="btn" onclick="testFormValidation()">✅ اختبار التحقق من النموذج</button>
            <button class="btn" onclick="addCompleteSupplier()">➕ إضافة مورد كامل</button>
            <button class="btn" onclick="testFormFields()">📝 اختبار جميع الحقول</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>النافذة:</strong> تحتوي على جميع الحقول المطلوبة</li>
                    <li><strong>الحقول المطلوبة:</strong> اسم المورد ورقم الهاتف</li>
                    <li><strong>الحقول الاختيارية:</strong> البريد الإلكتروني والنوع والفئة</li>
                    <li><strong>التحقق:</strong> يمنع الحفظ بدون الحقول المطلوبة</li>
                    <li><strong>الحفظ:</strong> يحفظ جميع البيانات في localStorage</li>
                    <li><strong>العرض:</strong> يظهر جميع البيانات في الجدول</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار النموذج الكامل
        function testCompleteForm(event) {
            event.preventDefault();
            
            const supplierName = document.getElementById('testSupplierName').value.trim();
            const supplierPhone = document.getElementById('testSupplierPhone').value.trim();
            const supplierEmail = document.getElementById('testSupplierEmail').value.trim();
            const supplierType = document.getElementById('testSupplierType').value;
            const supplierCategory = document.getElementById('testSupplierCategory').value;
            
            if (!supplierName) {
                document.getElementById('demo-result').innerHTML = `
                    <div class="error">❌ يرجى إدخال اسم المورد</div>
                `;
                return;
            }
            
            if (!supplierPhone) {
                document.getElementById('demo-result').innerHTML = `
                    <div class="error">❌ يرجى إدخال رقم الهاتف</div>
                `;
                return;
            }

            document.getElementById('demo-result').innerHTML = `
                <div class="success">
                    ✅ <strong>تم الحفظ بنجاح!</strong><br><br>
                    <strong>البيانات المحفوظة:</strong><br>
                    📝 الاسم: ${supplierName}<br>
                    📞 الهاتف: ${supplierPhone}<br>
                    📧 البريد: ${supplierEmail || 'غير محدد'}<br>
                    🏢 النوع: ${supplierType}<br>
                    📂 الفئة: ${supplierCategory}<br>
                    ⏰ الوقت: ${new Date().toLocaleString()}
                </div>
            `;
            
            // مسح النموذج
            document.getElementById('testSupplierName').value = '';
            document.getElementById('testSupplierPhone').value = '';
            document.getElementById('testSupplierEmail').value = '';
            document.getElementById('testSupplierType').value = 'شركة';
            document.getElementById('testSupplierCategory').value = 'عام';
        }

        function clearCompleteForm() {
            document.getElementById('testSupplierName').value = '';
            document.getElementById('testSupplierPhone').value = '';
            document.getElementById('testSupplierEmail').value = '';
            document.getElementById('testSupplierType').value = 'شركة';
            document.getElementById('testSupplierCategory').value = 'عام';
            document.getElementById('demo-result').innerHTML = '';
        }

        // بدء اختبار النموذج الكامل
        function startCompleteFormTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار النموذج الكامل!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة الموردين<br>
                    2️⃣ اضغط "مورد جديد"<br>
                    3️⃣ تحقق من وجود جميع الحقول<br>
                    4️⃣ املأ البيانات المطلوبة<br>
                    5️⃣ اضغط "حفظ"<br>
                    6️⃣ تحقق من ظهور المورد في الجدول<br><br>
                    
                    <strong>🎯 اضغط "فتح صفحة الموردين" للاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة الموردين
        function openSuppliersPage() {
            window.open('suppliers.html', '_blank');
            showResult('👥 تم فتح صفحة الموردين<br>💡 اضغط "مورد جديد" لرؤية النموذج الكامل مع جميع الحقول!', 'info');
        }

        // اختبار التحقق من النموذج
        function testFormValidation() {
            showResult(`
                <div class="info">
                    ✅ <strong>اختبار التحقق من النموذج:</strong><br><br>
                    
                    <strong>🔍 التحققات المطبقة:</strong><br>
                    • اسم المورد مطلوب (required)<br>
                    • رقم الهاتف مطلوب (required)<br>
                    • البريد الإلكتروني اختياري<br>
                    • نوع المورد له قيم محددة<br>
                    • فئة المورد لها قيم محددة<br><br>
                    
                    <strong>🧪 جرب النموذج التجريبي أعلاه:</strong><br>
                    • اتركه فارغ واضغط حفظ ← رسالة خطأ<br>
                    • أدخل الاسم فقط واضغط حفظ ← رسالة خطأ للهاتف<br>
                    • أدخل الاسم والهاتف واضغط حفظ ← رسالة نجاح<br><br>
                    
                    ✅ <strong>التحقق يعمل بشكل صحيح!</strong>
                </div>
            `);
        }

        // إضافة مورد كامل
        function addCompleteSupplier() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            const completeSupplier = {
                id: Date.now(),
                name: 'شركة التجارة الحديثة',
                type: 'شركة',
                phone: '+966501234567',
                email: '<EMAIL>',
                category: 'إلكترونيات',
                createdAt: new Date().toISOString()
            };

            suppliers.push(completeSupplier);
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة مورد كامل!</strong><br><br>
                    <strong>البيانات المحفوظة:</strong><br>
                    📝 الاسم: ${completeSupplier.name}<br>
                    🏢 النوع: ${completeSupplier.type}<br>
                    📞 الهاتف: ${completeSupplier.phone}<br>
                    📧 البريد: ${completeSupplier.email}<br>
                    📂 الفئة: ${completeSupplier.category}<br><br>
                    💡 <strong>افتح صفحة الموردين لرؤية المورد الجديد!</strong>
                </div>
            `);
        }

        // اختبار جميع الحقول
        function testFormFields() {
            showResult(`
                <div class="info">
                    📝 <strong>اختبار جميع الحقول:</strong><br><br>
                    
                    <strong>📋 الحقول المتاحة:</strong><br>
                    1️⃣ <strong>اسم المورد:</strong> حقل نص مطلوب<br>
                    2️⃣ <strong>رقم الهاتف:</strong> حقل هاتف مطلوب<br>
                    3️⃣ <strong>البريد الإلكتروني:</strong> حقل بريد اختياري<br>
                    4️⃣ <strong>نوع المورد:</strong> قائمة منسدلة (شركة، مؤسسة، فرد)<br>
                    5️⃣ <strong>فئة المورد:</strong> قائمة منسدلة (7 خيارات)<br><br>
                    
                    <strong>✅ جميع الحقول تعمل بشكل صحيح:</strong><br>
                    • التحقق من الحقول المطلوبة<br>
                    • حفظ جميع البيانات<br>
                    • عرض البيانات في الجدول<br>
                    • تعديل البيانات المحفوظة<br><br>
                    
                    🧪 <strong>جرب النموذج التجريبي أعلاه لاختبار جميع الحقول!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    📝 <strong>تم إصلاح نموذج المورد بالكامل!</strong><br><br>
                    ✅ نموذج كامل مع جميع الحقول<br>
                    ✅ حقول مطلوبة واختيارية<br>
                    ✅ قوائم منسدلة للاختيار<br>
                    ✅ التحقق من صحة البيانات<br>
                    ✅ حفظ جميع البيانات<br>
                    ✅ عرض البيانات الكاملة<br><br>
                    🧪 <strong>اضغط "بدء اختبار النموذج الكامل" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
