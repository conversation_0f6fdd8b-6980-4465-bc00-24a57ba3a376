# نظام إدارة الأعمال

## حل مشكلة الحرف العربي (ؤ)

تم ملاحظة وجود مشكلة متكررة عند محاولة تشغيل الأوامر في سطر الأوامر، حيث يظهر حرف عربي (ؤ) قبل الأمر مما يؤدي إلى فشل تنفيذ الأمر. هذه المشكلة مرتبطة بإعدادات لغة لوحة المفاتيح أو نظام التشغيل.

### الحلول المقترحة:

1. **استخدام ملفات التشغيل المباشر (تم تحديثها):**
   - قمنا بتحسين عدة ملفات لتشغيل النظام بدون مشاكل مع معالجة أفضل للأخطاء:
     - `open_launcher.vbs`: **الحل المفضل** - ملف VBScript محسن يمكنك النقر عليه مباشرة لفتح صفحة المشغل الرئيسية. يستخدم عدة طرق بديلة لفتح الملف في حالة فشل الطريقة الأساسية.
     - `open_launcher.ps1`: ملف PowerShell محسن لفتح صفحة المشغل مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.
     - `open_launcher.py`: ملف Python محسن لفتح صفحة المشغل مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.
     - `open_launcher.js`: ملف JavaScript (Node.js) محسن لفتح صفحة المشغل مع معالجة أفضل للأخطاء ودعم لطرق بديلة متعددة.
     - `start_server.bat`: ملف Batch جديد لفتح صفحة المشغل بطريقة مباشرة.
   
   - **ملفات تشغيل إضافية جديدة (لتجاوز مشكلة الحرف العربي):**
     - `run_vbs.bat`: ملف Batch جديد لتشغيل ملف VBS بطريقة تتجاوز مشكلة الحرف العربي.
     - `run_ps1.bat`: ملف Batch جديد لتشغيل ملف PowerShell بطريقة تتجاوز مشكلة الحرف العربي.
     - `run_py.bat`: ملف Batch جديد لتشغيل ملف Python بطريقة تتجاوز مشكلة الحرف العربي.
     - `run_js.bat`: ملف Batch جديد لتشغيل ملف JavaScript بطريقة تتجاوز مشكلة الحرف العربي.
   
   - هذه الملفات ستقوم بفتح `launcher.html` التي تحتوي على روابط لجميع صفحات النظام، وتتضمن آليات متعددة للتعامل مع مشكلة الحرف العربي (ؤ).

2. **تغيير لغة لوحة المفاتيح:**
   - قبل كتابة أي أمر في سطر الأوامر، تأكد من تبديل لغة لوحة المفاتيح إلى الإنجليزية (EN).
   - يمكنك استخدام اختصار لوحة المفاتيح `Alt + Shift` أو `Windows + Space` للتبديل بين اللغات.

3. **فتح الملفات مباشرة:**
   - يمكنك فتح ملفات HTML مباشرة من مستكشف الملفات بالنقر المزدوج عليها.
   - استخدم `launcher.html` للوصول إلى جميع صفحات النظام بسهولة.

## هيكل النظام

يتكون النظام من الصفحات التالية:

- **الرئيسية (index.html)**: لوحة التحكم الرئيسية مع إحصائيات ومؤشرات الأداء.
- **المبيعات (sales.html)**: إدارة المبيعات والفواتير.
- **المشتريات (purchases.html)**: إدارة المشتريات وفواتير الشراء.
- **العملاء (customers.html)**: إدارة بيانات العملاء.
- **المخزون (products.html)**: إدارة المنتجات والمخزون.
- **التقارير (reports.html)**: تقارير وإحصائيات النظام.
- **الحسابات (accounting.html)**: إدارة الحسابات المالية.

## كيفية الاستخدام

1. **الطريقة الأسهل والأكثر مباشرة**: افتح ملف `launcher-menu.html` في المتصفح، وهو قائمة مركزية تعرض جميع طرق تشغيل النظام في مكان واحد مع واجهة سهلة الاستخدام.
2. بدلاً من ذلك، يمكنك فتح ملف `direct-launch.html` في المتصفح، ثم النقر على زر "تشغيل النظام" لفتح المشغل مباشرة بدون أي مشاكل.
2. انقر على ملف `open_launcher.vbs` لفتح صفحة المشغل (هذا هو الحل المفضل والأكثر موثوقية).
3. إذا واجهت أي مشكلة مع الحرف العربي (ؤ)، يمكنك استخدام ملفات البات الجديدة التي تتجاوز هذه المشكلة:
   - `run_vbs.bat` (لتشغيل ملف VBS بطريقة تتجاوز مشكلة الحرف العربي)
   - `run_ps1.bat` (لتشغيل ملف PowerShell بطريقة تتجاوز مشكلة الحرف العربي)
   - `run_py.bat` (لتشغيل ملف Python بطريقة تتجاوز مشكلة الحرف العربي)
   - `run_js.bat` (لتشغيل ملف JavaScript بطريقة تتجاوز مشكلة الحرف العربي)
4. يمكنك أيضًا تجربة أحد الملفات البديلة المحسنة مباشرة:
   - `open_launcher.ps1` (ملف PowerShell محسن مع معالجة أفضل للأخطاء)
   - `open_launcher.py` (ملف Python محسن مع دعم لطرق بديلة متعددة)
   - `open_launcher.js` (ملف JavaScript محسن يتطلب تثبيت Node.js)
   - `start_server.bat` (ملف Batch جديد لفتح المشغل مباشرة)
5. يمكنك أيضًا فتح ملف `launcher.html` مباشرة من مستكشف الملفات بالنقر المزدوج عليه.
6. من صفحة المشغل، يمكنك النقر على أي قسم للانتقال إليه.
7. استخدم واجهة النظام لإدارة المبيعات، المشتريات، العملاء، المخزون، والتقارير.

**ملاحظة**: جميع ملفات التشغيل تم تحسينها لتجنب مشكلة الحرف العربي (ؤ) وتتضمن آليات متعددة لفتح المشغل في حالة فشل الطريقة الأساسية.

## صفحات المساعدة

تم إضافة صفحات مساعدة خاصة لحل مشكلة الحرف العربي:

- **fix-arabic-char.html**: صفحة تشرح مشكلة الحرف العربي (ؤ) وتقدم حلولاً لها.
- **keyboard-language.html**: صفحة تشرح كيفية تغيير لغة لوحة المفاتيح في ويندوز.
- **system-language.html**: صفحة تشرح كيفية تغيير لغة النظام الافتراضية في ويندوز.
- **important-notice.html**: صفحة تؤكد وجود المشكلة وتقدم الحلول المحسنة.
- **manual-launch.html**: صفحة تشرح كيفية تشغيل النظام يدويًا من خلال مستكشف الملفات.
- **direct-launch.html**: صفحة جديدة توفر طريقة مباشرة وسريعة لتشغيل النظام من المتصفح بدون الحاجة إلى ملفات خارجية.
- **launcher-menu.html**: صفحة قائمة مركزية تجمع جميع طرق تشغيل النظام في مكان واحد مع واجهة سهلة الاستخدام.

## ملاحظات هامة

- **تحديثات الملفات**: تم تحديث جميع ملفات التشغيل (`open_launcher.vbs`، `open_launcher.ps1`، `open_launcher.py`، `open_launcher.js`) لتعمل بشكل أفضل مع معالجة محسنة للأخطاء وطرق بديلة متعددة لفتح المشغل.
- **ملفات جديدة**: 
  - تم إضافة ملف `start_server.bat` لتوفير طريقة إضافية لفتح المشغل.
  - تم إضافة ملفات بات جديدة (`run_vbs.bat`، `run_ps1.bat`، `run_py.bat`، `run_js.bat`) لتشغيل ملفات التشغيل المختلفة بطريقة تتجاوز مشكلة الحرف العربي (ؤ).
  - تم إضافة ملف `important-notice.html` يشرح المشكلة والحلول بشكل مفصل.
  - تم إضافة ملف `direct-launch.html` لتوفير طريقة مباشرة وسريعة لتشغيل النظام من المتصفح بدون الحاجة إلى ملفات خارجية.
  - تم إضافة ملف `launcher-menu.html` كقائمة مركزية تجمع جميع طرق تشغيل النظام في مكان واحد مع واجهة سهلة الاستخدام.
- **معالجة مشكلة الحرف العربي**: 
  - جميع الملفات المحدثة تتضمن آليات للتعامل مع مشكلة الحرف العربي (ؤ) التي تظهر عند تشغيل الأوامر.
  - ملفات البات الجديدة تعمل كوسيط لتشغيل الملفات الأخرى بطريقة تتجاوز مشكلة الحرف العربي.
- **النظام مصمم للعمل بدون الحاجة إلى خادم ويب**، ويمكن تشغيله مباشرة من المتصفح.
- **دعم اللغة العربية**: تم تصميم النظام ليعمل باللغة العربية مع دعم الاتجاه من اليمين إلى اليسار (RTL).
- **تخزين البيانات**: جميع البيانات تُخزن محلياً في متصفحك باستخدام localStorage.
- **حل المشاكل**: 
  - إذا استمرت مشكلة الحرف العربي (ؤ)، استخدم أحد ملفات البات الجديدة (`run_vbs.bat`، `run_ps1.bat`، `run_py.bat`، `run_js.bat`) التي تم تصميمها خصيصًا لتجاوز هذه المشكلة.
  - يمكنك أيضًا التحقق من إعدادات لغة النظام ولوحة المفاتيح أو استخدام أحد ملفات التشغيل المحدثة.
- **الطريقة المفضلة**: 
  - الطريقة الأسهل والأكثر مباشرة هي استخدام ملف `launcher-menu.html` الذي يوفر قائمة مركزية لجميع طرق تشغيل النظام في مكان واحد مع واجهة سهلة الاستخدام.
  - يمكن أيضًا استخدام ملف `direct-launch.html` الذي يوفر واجهة مستخدم سهلة لتشغيل النظام مباشرة من المتصفح.
  - يُنصح أيضًا باستخدام ملف `open_launcher.vbs` كطريقة بديلة لفتح النظام، حيث أنه موثوق في التعامل مع مشكلة الحرف العربي.
  - إذا استمرت المشكلة، استخدم ملف `run_vbs.bat` الذي يعمل كوسيط لتشغيل ملف VBS بطريقة تتجاوز مشكلة الحرف العربي.