# إصلاح مشكلة وظائف إدارة الحسابات

هذا الملف يشرح كيفية إصلاح مشكلة وظائف إدارة الحسابات في نظام إدارة الأعمال، مثل نظرة عامة، دليل الحسابات، قيد اليومية، سند القبض، وسند الصرف.

## المشكلة

تواجه وظائف إدارة الحسابات مشكلة في العمل بشكل صحيح، وقد تكون هذه المشكلة ناتجة عن:

1. مشكلة في التعامل مع الأحرف العربية في نظام التشغيل
2. مشكلة في تحميل ملفات JavaScript الخاصة بوظائف الحسابات
3. مشكلة في عرض التبويبات وتفعيلها

## الحل

تم إضافة الملفات التالية لإصلاح المشكلة:

1. **ملف `js/accounting-fix.js`**: يقوم بإصلاح مشكلة وظائف إدارة الحسابات من خلال إعادة تعيين مستمعي الأحداث للتبويبات وإعادة تحميل البيانات في كل تبويب.

2. **ملف `css/accounting-fix.css`**: يضيف الأنماط اللازمة لعرض وظائف إدارة الحسابات بشكل صحيح.

3. **ملف `open_accounting.py`**: سكريبت Python لفتح صفحة الحسابات مباشرة مع معالجة مشكلة الأحرف العربية.

4. **ملف `open_accounting.vbs`**: سكريبت VBScript لفتح صفحة الحسابات مباشرة في حالة استمرار المشكلة مع ملف Python.

5. **ملف `open_accounting.ps1`**: سكريبت PowerShell لفتح صفحة الحسابات مباشرة كخيار إضافي.

6. **ملف `open_accounting.bat`**: ملف دفعي (Batch) لفتح صفحة الحسابات مباشرة كخيار إضافي.

7. **ملف `Create_Accounting_Shortcut.vbs`**: سكريبت VBScript لإنشاء اختصار لصفحة الحسابات على سطح المكتب.

8. **ملف `accounting-redirect.html`**: صفحة توجيه HTML تقوم بتوجيه المستخدم تلقائيًا إلى صفحة الحسابات مع رسالة توضيحية.

9. **ملف `accounting-fix-guide.html`**: دليل إصلاح شامل بواجهة مستخدم سهلة الاستخدام يشرح جميع الحلول المتاحة بالتفصيل.

10. **ملف `accounting-solutions.html`**: صفحة حلول تفاعلية تعرض جميع الحلول المتاحة في واجهة واحدة سهلة الاستخدام.

## كيفية استخدام الحل

### الطريقة 1: استخدام ملفات التشغيل المباشر

اختر أحد الملفات التالية حسب تفضيلك:

1. انقر نقرًا مزدوجًا على ملف `open_accounting.vbs` لفتح صفحة الحسابات مباشرة (مناسب لجميع إصدارات Windows).
2. انقر نقرًا مزدوجًا على ملف `open_accounting.bat` لفتح صفحة الحسابات مباشرة (مناسب لجميع إصدارات Windows).
3. انقر بزر الماوس الأيمن على ملف `open_accounting.ps1` واختر "Run with PowerShell" لفتح صفحة الحسابات مباشرة (مناسب لـ Windows 7 وما بعده).

### الطريقة 2: استخدام سكريبت Python

1. افتح موجه الأوامر (Command Prompt) أو PowerShell.
2. انتقل إلى مجلد النظام باستخدام الأمر `cd`.
3. قم بتشغيل السكريبت باستخدام الأمر:
   ```
   python open_accounting.py
   ```

### الطريقة 3: إنشاء اختصار على سطح المكتب

1. انقر نقرًا مزدوجًا على ملف `Create_Accounting_Shortcut.vbs`.
2. سيتم إنشاء اختصار لصفحة الحسابات على سطح المكتب باسم "إدارة الحسابات - Monjiz".
3. يمكنك استخدام هذا الاختصار للوصول السريع إلى صفحة الحسابات في أي وقت.

### الطريقة 4: استخدام صفحة التوجيه

1. افتح ملف `accounting-redirect.html` في أي متصفح.
2. سيتم توجيهك تلقائيًا إلى صفحة الحسابات بعد 5 ثوانٍ، أو يمكنك النقر على زر "الانتقال إلى صفحة الحسابات".

### الطريقة 5: الفتح من خلال المشغل الرئيسي

1. قم بتشغيل النظام باستخدام `open_launcher.vbs` أو `open_launcher.py`.
2. من الصفحة الرئيسية، انقر على زر "الحسابات" في شريط التنقل.

### الطريقة 6: استخدام دليل الإصلاح الشامل

1. افتح ملف `accounting-fix-guide.html` في أي متصفح.
2. اتبع التعليمات المفصلة في الدليل لإصلاح المشكلة.
3. استخدم الروابط السريعة المتوفرة في الدليل للوصول إلى الصفحات المطلوبة.

### الطريقة 7: استخدام صفحة الحلول التفاعلية

1. افتح ملف `accounting-solutions.html` في أي متصفح.
2. اختر الحل المناسب من الخيارات المتاحة في الصفحة.
3. استخدم الروابط المباشرة للوصول السريع إلى الصفحات المطلوبة.

## ملاحظات هامة

1. **تأكد من تغيير لغة لوحة المفاتيح إلى الإنجليزية** قبل تشغيل أي من ملفات السكريبت لتجنب مشكلة الحرف العربي (ؤ) الذي يظهر قبل الأوامر في موجه الأوامر.

2. إذا استمرت المشكلة، يمكنك فتح ملف `accounting.html` مباشرة من مستكشف الملفات.

3. تم تحديث ملف `accounting.html` لإضافة ارتباط بملفات الإصلاح الجديدة، لذا يجب استخدام النسخة المحدثة من الملف.

4. للحصول على أفضل تجربة، نوصي باستخدام متصفح Google Chrome أو Microsoft Edge الحديث.

## الدعم الفني

إذا واجهتك أي مشكلة في استخدام هذا الحل، يرجى التواصل مع فريق الدعم الفني.