// بيانات التقرير (بيانات تجريبية)
const reportData = {
    // بيانات المبيعات الشهرية
    monthlySales: {
        2023: [
            12500, 15800, 18200, 22000, 25500, 28350, 0, 0, 0, 0, 0, 0 // يناير إلى يونيو 2023
        ],
        2022: [
            10200, 12500, 14800, 18500, 21000, 24500, 26800, 28000, 25500, 22000, 19500, 23000 // يناير إلى ديسمبر 2022
        ]
    },
    // بيانات المبيعات حسب الفئة
    categorySales: {
        month: {
            labels: ['إلكترونيات', 'ملابس', 'أثاث', 'أدوات منزلية', 'مستلزمات مكتبية'],
            data: [35, 25, 15, 15, 10]
        },
        quarter: {
            labels: ['إلكترونيات', 'ملابس', 'أثاث', 'أدوات منزلية', 'مستلزمات مكتبية'],
            data: [40, 20, 18, 12, 10]
        },
        year: {
            labels: ['إلكترونيات', 'ملابس', 'أثاث', 'أدوات منزلية', 'مستلزمات مكتبية'],
            data: [38, 22, 16, 14, 10]
        }
    },
    // بيانات تفاصيل المبيعات
    salesDetails: [
        {
            date: '2023-06-20',
            invoiceId: 'INV-2023-001',
            customer: 'أحمد محمد',
            products: 'هاتف ذكي، سماعات لاسلكية',
            paymentMethod: 'بطاقة ائتمان',
            total: 1250.00,
            profit: 375.00
        },
        {
            date: '2023-06-20',
            invoiceId: 'INV-2023-002',
            customer: 'شركة النور للتجارة',
            products: 'أجهزة كمبيوتر (5)، طابعات (2)',
            paymentMethod: 'تحويل بنكي',
            total: 3500.00,
            profit: 1050.00
        },
        {
            date: '2023-06-19',
            invoiceId: 'INV-2023-003',
            customer: 'سارة عبدالله',
            products: 'كاميرا رقمية، بطاقة ذاكرة',
            paymentMethod: 'نقدي',
            total: 750.00,
            profit: 225.00
        },
        {
            date: '2023-06-19',
            invoiceId: 'INV-2023-004',
            customer: 'مؤسسة الأمل',
            products: 'أثاث مكتبي، مستلزمات مكتبية',
            paymentMethod: 'تحويل بنكي',
            total: 4200.00,
            profit: 1260.00
        },
        {
            date: '2023-06-18',
            invoiceId: 'INV-2023-005',
            customer: 'خالد العمري',
            products: 'سماعات، شاحن متنقل',
            paymentMethod: 'نقدي',
            total: 500.00,
            profit: 150.00
        },
        {
            date: '2023-06-18',
            invoiceId: 'INV-2023-006',
            customer: 'شركة البناء الحديث',
            products: 'أجهزة تكييف (3)، مراوح (5)',
            paymentMethod: 'شيك',
            total: 8750.00,
            profit: 2625.00
        },
        {
            date: '2023-06-17',
            invoiceId: 'INV-2023-007',
            customer: 'محمد علي',
            products: 'تلفزيون، مكبر صوت',
            paymentMethod: 'بطاقة ائتمان',
            total: 1200.00,
            profit: 360.00
        },
        {
            date: '2023-06-17',
            invoiceId: 'INV-2023-008',
            customer: 'نورة سعيد',
            products: 'جهاز لوحي',
            paymentMethod: 'نقدي',
            total: 350.00,
            profit: 105.00
        },
        {
            date: '2023-06-16',
            invoiceId: 'INV-2023-009',
            customer: 'شركة الأفق',
            products: 'أجهزة كمبيوتر محمولة (3)، شاشات (3)',
            paymentMethod: 'تحويل بنكي',
            total: 5500.00,
            profit: 1650.00
        },
        {
            date: '2023-06-15',
            invoiceId: 'INV-2023-010',
            customer: 'عبدالله محمد',
            products: 'طابعة، حبر، ورق',
            paymentMethod: 'بطاقة ائتمان',
            total: 1800.00,
            profit: 540.00
        }
    ]
};

// إنشاء رسم بياني للمبيعات الشهرية
function createMonthlySalesChart() {
    const ctx = document.getElementById('monthly-sales-chart').getContext('2d');
    const selectedYear = document.getElementById('sales-chart-year').value;
    const monthsLabels = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    
    // إذا كان الرسم البياني موجودًا بالفعل، قم بتدميره
    if (window.monthlySalesChart instanceof Chart) {
        window.monthlySalesChart.destroy();
    }
    
    window.monthlySalesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: monthsLabels,
            datasets: [{
                label: `المبيعات الشهرية ${selectedYear}`,
                data: reportData.monthlySales[selectedYear],
                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA') + ' ر.س';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.raw.toLocaleString('ar-SA') + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

// إنشاء رسم بياني للمبيعات حسب الفئة
function createCategorySalesChart() {
    const ctx = document.getElementById('category-sales-chart').getContext('2d');
    const selectedPeriod = document.getElementById('category-chart-period').value;
    const categoryData = reportData.categorySales[selectedPeriod];
    
    // إذا كان الرسم البياني موجودًا بالفعل، قم بتدميره
    if (window.categorySalesChart instanceof Chart) {
        window.categorySalesChart.destroy();
    }
    
    window.categorySalesChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: categoryData.labels,
            datasets: [{
                data: categoryData.data,
                backgroundColor: [
                    'rgba(52, 152, 219, 0.7)',
                    'rgba(155, 89, 182, 0.7)',
                    'rgba(46, 204, 113, 0.7)',
                    'rgba(241, 196, 15, 0.7)',
                    'rgba(231, 76, 60, 0.7)'
                ],
                borderColor: [
                    'rgba(52, 152, 219, 1)',
                    'rgba(155, 89, 182, 1)',
                    'rgba(46, 204, 113, 1)',
                    'rgba(241, 196, 15, 1)',
                    'rgba(231, 76, 60, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.raw + '%';
                        }
                    }
                }
            }
        }
    });
}

// عرض تفاصيل المبيعات في الجدول
function displaySalesDetails() {
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';
    
    reportData.salesDetails.forEach(sale => {
        const row = document.createElement('tr');
        
        // تنسيق التاريخ
        const formattedDate = new Date(sale.date).toLocaleDateString('ar-SA');
        
        // تنسيق المبالغ
        const formattedTotal = new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(sale.total);
        
        const formattedProfit = new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(sale.profit);
        
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${sale.invoiceId}</td>
            <td>${sale.customer}</td>
            <td>${sale.products}</td>
            <td>${sale.paymentMethod}</td>
            <td>${formattedTotal}</td>
            <td>${formattedProfit}</td>
        `;
        
        tableBody.appendChild(row);
    });
}

// تطبيق التصفية على التقرير
function applyReportFilter() {
    // الحصول على نوع التقرير المحدد
    const reportType = document.getElementById('report-type-select').value;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    // تحديد نوع التقرير وعرض البيانات المناسبة
    if (reportType.includes('sales')) {
        // عرض تقارير المبيعات
        displaySalesDetails();
        // تحديث الرسوم البيانية للمبيعات
        createMonthlySalesChart();
        createCategorySalesChart();
        updateReportTitle('تقرير المبيعات');
    } else if (reportType.includes('account') || reportType.includes('voucher') || 
               reportType.includes('customer') || reportType.includes('trial_balance') || 
               reportType.includes('profit_loss') || reportType.includes('daily_journal') || 
               reportType.includes('general_ledger') || reportType.includes('balance_sheet') || 
               reportType.includes('entries')) {
        // عرض تقارير الحسابات في نفس النافذة
        displayAccountingReport(reportType, startDate, endDate);
        updateReportTitle(getReportTitle(reportType));
    } else if (reportType.includes('inventory') || reportType.includes('branch_transfer') || 
               reportType.includes('outgoing') || reportType.includes('incoming')) {
        // عرض تقارير المخزون
        displayInventoryReport(reportType, startDate, endDate);
        updateReportTitle(getReportTitle(reportType));
    }
    
    // تحديث فترة التقرير
    updateReportDateRange(startDate, endDate);
    
    // إظهار قسم التقرير
    document.querySelector('.report-section').style.display = 'block';

    // إظهار خانة البحث بعد عرض التقرير
    setTimeout(() => {
        const searchSection = document.getElementById('report-search-section');
        if (searchSection) {
            searchSection.style.display = 'block';
        }
    }, 100);

    console.log(`تم تطبيق التصفية على التقرير: ${getReportTitle(reportType)}`);
}

// تصدير التقرير
function exportReport() {
    alert('جاري تصدير التقرير...');
    // في التطبيق الحقيقي، سيتم تنفيذ عملية تصدير التقرير إلى ملف
}

// طباعة التقرير
function printReport() {
    alert('جاري إعداد التقرير للطباعة...');
    // في التطبيق الحقيقي، سيتم فتح نافذة طباعة
}

// الحصول على عنوان التقرير بناءً على نوع التقرير
function getReportTitle(reportType) {
    const reportTitles = {
        // تقارير الحسابات
        'account_statement': 'كشف حساب',
        'account_movement': 'حركة الحسابات اجمالي',
        'customer_statement': 'كشف حساب عميل',
        'customer_debts': 'ذمم العملاء',
        'customer_balances': 'تقرير أرصدة العملاء',
        'receipt_vouchers_print': 'طباعة سندات القبض',
        'payment_vouchers_print': 'طباعة سندات الصرف',
        'receipt_vouchers_report': 'كشف سندات القبض',
        'payment_vouchers_report': 'كشف سندات الصرف',
        'entries_report': 'تقرير عن القيود',
        'trial_balance': 'موازين المراجعة',
        'profit_loss': 'تقرير حساب الأرباح والخسائر',
        'daily_journal': 'دفتر اليومية',
        'general_ledger': 'الاستاذ العام',
        'balance_sheet': 'المزانية العمومية',
        
        // تقارير المخزون
        'inventory_count': 'الجرد',
        'branch_transfer': 'التحويل بين الفروع',
        'outgoing_voucher': 'سند إخراج',
        'incoming_voucher': 'سند إدخال',
        
        // تقارير المبيعات
        'sales_movement': 'حركة المبيعات',
        'sales_invoices': 'كشف فواتير المبعات',
        'sales_by_item': 'المبيعات حصب الأصناف',
        'sales_by_customer': 'المبيعات حسب العميل',
        'top_selling_items': 'الأصناف الأكثر مبيعا',
        'most_profitable_items': 'الأصناف الأكثر ربحا',
        'transfer_invoices': 'كشف فوائير التحويل',
        'sales_invoices_print': 'طباعة فواتير المبيعات',
        'sales_return_invoices_print': 'طباعة فواتير إرتجاع المبيعات',
        'consignment_return_invoices_print': 'طباعة فواتير إرتجاع تحت التصريف',
        'customer_items_movement': 'حركة أصناف لعميل'
    };
    
    return reportTitles[reportType] || 'تقرير';
}

// تحديث عنوان التقرير
function updateReportTitle(title) {
    // تحديث عنوان الجدول
    const tableHeader = document.querySelector('.table-header h3');
    if (tableHeader) {
        tableHeader.textContent = title;
    }
    
    // تحديث عنوان قسم التقرير المتقدم
    const reportTitle = document.getElementById('report-title');
    if (reportTitle) {
        reportTitle.textContent = title;
    }
}

// تحديث فترة التقرير
function updateReportDateRange(startDate, endDate) {
    const reportDateRange = document.getElementById('report-date-range');
    if (reportDateRange) {
        let formattedStartDate = startDate ? new Date(startDate).toLocaleDateString('ar-SA') : '';
        let formattedEndDate = endDate ? new Date(endDate).toLocaleDateString('ar-SA') : '';
        
        if (formattedStartDate && formattedEndDate) {
            reportDateRange.textContent = `الفترة: من ${formattedStartDate} إلى ${formattedEndDate}`;
        } else {
            reportDateRange.textContent = 'الفترة: كل الفترات';
        }
    }
}

// عرض تقارير الحسابات
function displayAccountingReport(reportType, startDate, endDate, accountId = null, customerId = null) {
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';
    
    // بيانات تجريبية لتقارير الحسابات
    let reportData = [];
    
    // إنشاء بيانات تجريبية مختلفة حسب نوع التقرير
    if (reportType === 'account_statement') {
        // كشف حساب
        reportData = [
            { date: '2023-06-20', document: 'سند قبض 001', description: 'تحصيل من العميل', debit: 0, credit: 1500, balance: 1500 },
            { date: '2023-06-18', document: 'فاتورة 123', description: 'مبيعات', debit: 2500, credit: 0, balance: -1000 },
            { date: '2023-06-15', document: 'سند قبض 002', description: 'تحصيل من العميل', debit: 0, credit: 3000, balance: 2000 }
        ];
        
        // في حالة وجود معرف حساب، يمكن تصفية البيانات (هنا نستخدم البيانات النموذجية كما هي)
        if (accountId) {
            console.log(`تصفية كشف الحساب للحساب رقم: ${accountId}`);
            // هنا يمكن إضافة منطق لتصفية البيانات حسب معرف الحساب
        }
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>التاريخ</th>
            <th>المستند</th>
            <th>البيان</th>
            <th>مدين</th>
            <th>دائن</th>
            <th>الرصيد</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${item.document}</td>
                <td>${item.description}</td>
                <td>${item.debit.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.credit.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.balance.toLocaleString('ar-SA')} ر.س</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="3">الإجمالي</td>
            <td>2,500.00 ر.س</td>
            <td>4,500.00 ر.س</td>
            <td>2,000.00 ر.س</td>
        `;
    } else if (reportType === 'customer_statement') {
        // كشف حساب العميل
        reportData = [
            { date: '2023-06-20', document: 'سند قبض 001', description: 'تحصيل من العميل', debit: 0, credit: 1500, balance: 1500 },
            { date: '2023-06-18', document: 'فاتورة 123', description: 'مبيعات', debit: 2500, credit: 0, balance: -1000 },
            { date: '2023-06-15', document: 'سند قبض 002', description: 'تحصيل من العميل', debit: 0, credit: 3000, balance: 2000 }
        ];
        
        // في حالة وجود معرف عميل، يمكن تصفية البيانات (هنا نستخدم البيانات النموذجية كما هي)
        if (customerId) {
            console.log(`تصفية كشف الحساب للعميل رقم: ${customerId}`);
            // هنا يمكن إضافة منطق لتصفية البيانات حسب معرف العميل
        }
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>التاريخ</th>
            <th>المستند</th>
            <th>البيان</th>
            <th>مدين</th>
            <th>دائن</th>
            <th>الرصيد</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${item.document}</td>
                <td>${item.description}</td>
                <td>${item.debit.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.credit.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.balance.toLocaleString('ar-SA')} ر.س</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="3">الإجمالي</td>
            <td>2,500.00 ر.س</td>
            <td>4,500.00 ر.س</td>
            <td>2,000.00 ر.س</td>
        `;
    } else if (reportType === 'receipt_vouchers_report' || reportType === 'payment_vouchers_report') {
        // كشف سندات القبض أو الصرف
        const voucherType = reportType === 'receipt_vouchers_report' ? 'قبض' : 'صرف';
        reportData = [
            { date: '2023-06-20', number: '001', account: 'صندوق الرئيسي', customer: 'أحمد محمد', amount: 1500, description: `سند ${voucherType}` },
            { date: '2023-06-18', number: '002', account: 'صندوق الرئيسي', customer: 'شركة النور', amount: 3000, description: `سند ${voucherType}` },
            { date: '2023-06-15', number: '003', account: 'صندوق الرئيسي', customer: 'محمد علي', amount: 2500, description: `سند ${voucherType}` }
        ];
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>التاريخ</th>
            <th>الرقم</th>
            <th>الحساب</th>
            <th>العميل</th>
            <th>المبلغ</th>
            <th>البيان</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${item.number}</td>
                <td>${item.account}</td>
                <td>${item.customer}</td>
                <td>${item.amount.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.description}</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="4">الإجمالي</td>
            <td>7,000.00 ر.س</td>
            <td></td>
        `;
    } else if (reportType === 'daily_journal') {
        // دفتر اليومية
        reportData = [
            { date: '2023-06-20', entry_no: 'JE-001', account: 'صندوق الرئيسي', description: 'تحصيل من العميل', debit: 1500, credit: 0 },
            { date: '2023-06-20', entry_no: 'JE-001', account: 'ذمم العملاء', description: 'تحصيل من العميل', debit: 0, credit: 1500 },
            { date: '2023-06-18', entry_no: 'JE-002', account: 'ذمم العملاء', description: 'مبيعات', debit: 2500, credit: 0 },
            { date: '2023-06-18', entry_no: 'JE-002', account: 'المبيعات', description: 'مبيعات', debit: 0, credit: 2500 }
        ];
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>التاريخ</th>
            <th>رقم القيد</th>
            <th>الحساب</th>
            <th>البيان</th>
            <th>مدين</th>
            <th>دائن</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${item.entry_no}</td>
                <td>${item.account}</td>
                <td>${item.description}</td>
                <td>${item.debit.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.credit.toLocaleString('ar-SA')} ر.س</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="4">الإجمالي</td>
            <td>4,000.00 ر.س</td>
            <td>4,000.00 ر.س</td>
        `;
    } else {
        // تقارير أخرى - عرض رسالة
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="7" style="text-align: center;">سيتم تنفيذ تقرير ${getReportTitle(reportType)} قريباً</td>`;
        tableBody.appendChild(row);
    }
    
    // إخفاء الرسوم البيانية لتقارير الحسابات
    document.querySelector('.charts-container').style.display = 'none';
}

// عرض تقارير المخزون
function displayInventoryReport(reportType, startDate, endDate) {
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';
    
    // بيانات تجريبية لتقارير المخزون
    let reportData = [];
    
    // إنشاء بيانات تجريبية مختلفة حسب نوع التقرير
    if (reportType === 'inventory_count') {
        // تقرير الجرد
        reportData = [
            { item_code: 'P001', item_name: 'هاتف ذكي', category: 'إلكترونيات', unit: 'قطعة', quantity: 25, cost: 800, total_cost: 20000 },
            { item_code: 'P002', item_name: 'لابتوب', category: 'إلكترونيات', unit: 'قطعة', quantity: 15, cost: 2500, total_cost: 37500 },
            { item_code: 'P003', item_name: 'سماعات لاسلكية', category: 'إلكترونيات', unit: 'قطعة', quantity: 40, cost: 150, total_cost: 6000 }
        ];
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>رمز الصنف</th>
            <th>اسم الصنف</th>
            <th>الفئة</th>
            <th>الوحدة</th>
            <th>الكمية</th>
            <th>التكلفة</th>
            <th>إجمالي التكلفة</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${item.item_code}</td>
                <td>${item.item_name}</td>
                <td>${item.category}</td>
                <td>${item.unit}</td>
                <td>${item.quantity}</td>
                <td>${item.cost.toLocaleString('ar-SA')} ر.س</td>
                <td>${item.total_cost.toLocaleString('ar-SA')} ر.س</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="4">الإجمالي</td>
            <td>80</td>
            <td></td>
            <td>63,500.00 ر.س</td>
        `;
    } else if (reportType === 'branch_transfer') {
        // تقرير التحويل بين الفروع
        reportData = [
            { date: '2023-06-20', transfer_no: 'TR-001', from_branch: 'الفرع الرئيسي', to_branch: 'فرع الشمال', items_count: 5, total_cost: 12500 },
            { date: '2023-06-18', transfer_no: 'TR-002', from_branch: 'الفرع الرئيسي', to_branch: 'فرع الجنوب', items_count: 3, total_cost: 8000 },
            { date: '2023-06-15', transfer_no: 'TR-003', from_branch: 'فرع الشرق', to_branch: 'فرع الغرب', items_count: 7, total_cost: 15000 }
        ];
        
        // إنشاء رؤوس الجدول
        document.querySelector('.report-table thead tr').innerHTML = `
            <th>التاريخ</th>
            <th>رقم التحويل</th>
            <th>من فرع</th>
            <th>إلى فرع</th>
            <th>عدد الأصناف</th>
            <th>إجمالي التكلفة</th>
        `;
        
        // إضافة الصفوف
        reportData.forEach(item => {
            const row = document.createElement('tr');
            const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${item.transfer_no}</td>
                <td>${item.from_branch}</td>
                <td>${item.to_branch}</td>
                <td>${item.items_count}</td>
                <td>${item.total_cost.toLocaleString('ar-SA')} ر.س</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // تحديث الإجمالي
        document.querySelector('.report-table tfoot tr').innerHTML = `
            <td colspan="4">الإجمالي</td>
            <td>15</td>
            <td>35,500.00 ر.س</td>
        `;
    } else {
        // تقارير أخرى - عرض رسالة
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="7" style="text-align: center;">سيتم تنفيذ تقرير ${getReportTitle(reportType)} قريباً</td>`;
        tableBody.appendChild(row);
    }

    // إخفاء الرسوم البيانية لتقارير المخزون
    document.querySelector('.charts-container').style.display = 'none';

    // إظهار خانة البحث بعد عرض التقرير
    setTimeout(() => {
        const searchSection = document.getElementById('report-search-section');
        if (searchSection) {
            searchSection.style.display = 'block';
        }
    }, 100);
}

// عرض التقرير في نفس النافذة
function openReportInNewWindow(reportType, startDate, endDate) {
    // تحديث عناصر التصفية بالقيم المطلوبة
    const reportTypeSelect = document.getElementById('report-type-select');
    if (reportTypeSelect) {
        reportTypeSelect.value = reportType;
    }
    
    // تحديث تواريخ البداية والنهاية إذا تم تحديدهما
    if (startDate) {
        document.getElementById('report-start-date').value = startDate;
    }
    
    if (endDate) {
        document.getElementById('report-end-date').value = endDate;
    }
    
    // تطبيق التصفية لعرض التقرير
    applyReportFilter();
    
    // التمرير إلى قسم التقرير
    document.querySelector('.report-section').scrollIntoView({ behavior: 'smooth' });
    
    console.log(`تم عرض تقرير ${getReportTitle(reportType)} في نفس النافذة`);
}

// إضافة مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء الرسوم البيانية
    createMonthlySalesChart();
    createCategorySalesChart();
    
    // عرض تفاصيل المبيعات
    displaySalesDetails();
    
    // مستمع حدث لتغيير سنة رسم المبيعات الشهرية
    document.getElementById('sales-chart-year').addEventListener('change', createMonthlySalesChart);
    
    // مستمع حدث لتغيير فترة رسم المبيعات حسب الفئة
    document.getElementById('category-chart-period').addEventListener('change', createCategorySalesChart);
    
    // مستمع حدث لتغيير نوع التقرير
    document.getElementById('report-type-select').addEventListener('change', function() {
        // عند تغيير نوع التقرير، نعيد عرض الرسوم البيانية إذا كان التقرير من نوع المبيعات
        const reportType = this.value;
        if (reportType.includes('sales')) {
            document.querySelector('.charts-container').style.display = 'grid';
        } else {
            document.querySelector('.charts-container').style.display = 'none';
        }
    });
    
    // مستمع حدث لزر تطبيق التصفية
    document.querySelector('.apply-filter-btn').addEventListener('click', applyReportFilter);
    
    // مستمع حدث لزر التصدير
    document.querySelector('.export-btn').addEventListener('click', exportReport);
    
    // مستمع حدث لزر الطباعة
    document.querySelector('.print-btn').addEventListener('click', printReport);
    
    // مستمع حدث لزر تحديث التقرير المتقدم
    const refreshReportBtn = document.getElementById('refresh-report-btn');
    if (refreshReportBtn) {
        refreshReportBtn.addEventListener('click', function() {
            const accountSelect = document.getElementById('account-select');
            const customerSelect = document.getElementById('customer-select');
            const startDate = document.getElementById('report-start-date').value;
            const endDate = document.getElementById('report-end-date').value;
            
            let reportType = 'account_statement';
            let accountId = null;
            let customerId = null;
            
            if (accountSelect && accountSelect.value) {
                accountId = accountSelect.value;
                reportType = 'account_statement';
            } else if (customerSelect && customerSelect.value) {
                customerId = customerSelect.value;
                reportType = 'customer_statement';
            }
            
            // تحديث عنوان التقرير
            updateReportTitle(getReportTitle(reportType));
            
            // تحديث فترة التقرير
            updateReportDateRange(startDate, endDate);
            
            // عرض التقرير المناسب
            displayAccountingReport(reportType, startDate, endDate, accountId, customerId);
            
            // إظهار قسم التقرير
            document.querySelector('.report-section').style.display = 'block';
        });
    }
    
    // مستمع حدث للتبديل بين الحساب والعميل
    const accountSelect = document.getElementById('account-select');
    const customerSelect = document.getElementById('customer-select');
    
    if (accountSelect && customerSelect) {
        accountSelect.addEventListener('change', function() {
            if (this.value) {
                customerSelect.value = '';
            }
        });
        
        customerSelect.addEventListener('change', function() {
            if (this.value) {
                accountSelect.value = '';
            }
        });
    }
    
    // مستمعي أحداث لأزرار تصدير الجدول
    document.querySelectorAll('.table-actions button').forEach(button => {
        button.addEventListener('click', function() {
            alert('جاري تصدير الجدول إلى ' + (this.textContent.includes('Excel') ? 'Excel' : 'PDF'));
        });
    });
    
    // مستمعي أحداث لأزرار ترقيم الصفحات
    document.querySelectorAll('.pagination-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('active')) {
                document.querySelector('.pagination-btn.active').classList.remove('active');
                this.classList.add('active');
                // في التطبيق الحقيقي، سيتم تحميل الصفحة المناسبة من التقرير
            }
        });
    });

    // إعداد البحث في التقارير
    setupInventoryReportSearch();
});

// دالة لإعداد البحث في تقارير المخزون
function setupInventoryReportSearch() {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterInventoryReport(this.value);
            showInventorySearchSuggestions(this.value);
        });

        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                filterInventoryReport(this.value);
            }
        });

        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideInventorySearchSuggestions();
            }, 200);
        });
    }
}

// دالة لتصفية تقارير المخزون
function filterInventoryReport(searchTerm) {
    if (!searchTerm || searchTerm.length < 1) {
        showAllInventoryRows();
        return;
    }

    const tableBody = document.getElementById('report-table-body');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let matchFound = false;

        cells.forEach(cell => {
            const cellText = cell.textContent.toLowerCase();
            if (cellText.includes(searchTerm.toLowerCase())) {
                matchFound = true;
            }
        });

        if (matchFound) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // تحديث عداد النتائج
    updateInventoryResultsCount(visibleCount, rows.length);
}

// دالة لإظهار جميع صفوف المخزون
function showAllInventoryRows() {
    const tableBody = document.getElementById('report-table-body');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
        row.style.display = '';
    });

    // إخفاء عداد النتائج
    const resultsCounter = document.getElementById('inventory-results-counter');
    if (resultsCounter) {
        resultsCounter.style.display = 'none';
    }
}

// دالة لتحديث عداد النتائج في المخزون
function updateInventoryResultsCount(visible, total) {
    let resultsCounter = document.getElementById('inventory-results-counter');
    if (!resultsCounter) {
        resultsCounter = document.createElement('div');
        resultsCounter.id = 'inventory-results-counter';
        resultsCounter.style.cssText = `
            background: #e3f2fd;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
            color: #1976d2;
            border-right: 4px solid #2196f3;
        `;

        const reportTable = document.querySelector('.report-table');
        if (reportTable) {
            reportTable.parentNode.insertBefore(resultsCounter, reportTable);
        }
    }

    resultsCounter.innerHTML = `
        <i class="fas fa-search"></i>
        عرض ${visible} من أصل ${total} نتيجة في المخزون
    `;
    resultsCounter.style.display = 'block';
}

// دالة لعرض اقتراحات البحث في المخزون
function showInventorySearchSuggestions(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) {
        hideInventorySearchSuggestions();
        return;
    }

    // إنشاء قائمة الاقتراحات إذا لم تكن موجودة
    let suggestionsContainer = document.getElementById('inventory-search-suggestions');
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'inventory-search-suggestions';
        suggestionsContainer.className = 'report-search-suggestions';

        const searchBox = document.querySelector('.report-search-section .search-box');
        if (searchBox) {
            searchBox.appendChild(suggestionsContainer);
        }
    }

    // الحصول على البيانات المطابقة
    const matchingData = getMatchingInventoryData(searchTerm.toLowerCase());

    if (matchingData.length === 0) {
        hideInventorySearchSuggestions();
        return;
    }

    // إنشاء HTML للاقتراحات
    const suggestionsHTML = matchingData.slice(0, 5).map(item => `
        <div class="report-suggestion-item" onclick="selectInventorySuggestion('${item.text}')">
            <div class="report-suggestion-name">${highlightInventoryMatch(item.text, searchTerm)}</div>
            <div class="report-suggestion-details">${item.context}</div>
        </div>
    `).join('');

    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

// دالة لإخفاء اقتراحات البحث في المخزون
function hideInventorySearchSuggestions() {
    const suggestionsContainer = document.getElementById('inventory-search-suggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

// دالة للحصول على البيانات المطابقة في المخزون
function getMatchingInventoryData(searchTerm) {
    const tableBody = document.getElementById('report-table-body');
    if (!tableBody) return [];

    const matchingData = [];
    const rows = tableBody.querySelectorAll('tr');
    const headers = document.querySelectorAll('.report-table thead th');

    rows.forEach((row, rowIndex) => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, cellIndex) => {
            const cellText = cell.textContent.trim();
            if (cellText.toLowerCase().includes(searchTerm)) {
                // تحديد السياق بناءً على موقع الخلية
                let context = '';
                if (headers[cellIndex]) {
                    context = `${headers[cellIndex].textContent} - الصف ${rowIndex + 1}`;
                }

                matchingData.push({
                    text: cellText,
                    context: context,
                    row: rowIndex,
                    cell: cellIndex
                });
            }
        });
    });

    // إزالة التكرارات
    const uniqueData = [];
    const seen = new Set();

    matchingData.forEach(item => {
        const key = item.text.toLowerCase();
        if (!seen.has(key)) {
            seen.add(key);
            uniqueData.push(item);
        }
    });

    return uniqueData;
}

// دالة لتمييز النص المطابق في المخزون
function highlightInventoryMatch(text, searchTerm) {
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
}

// دالة لاختيار اقتراح من المخزون
function selectInventorySuggestion(suggestionText) {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        searchInput.value = suggestionText;
        filterInventoryReport(suggestionText);
        hideInventorySearchSuggestions();
    }
}

// دالة عامة لتصفية أي تقرير
function filterAnyReport(searchTerm) {
    // تحديد نوع التقرير الحالي
    const reportType = document.getElementById('report-type-select')?.value;

    if (reportType && reportType.includes('inventory')) {
        filterInventoryReport(searchTerm);
    } else {
        // تصفية عامة لجميع أنواع التقارير الأخرى
        filterGeneralReport(searchTerm);
    }
}

// دالة عامة لتصفية التقارير
function filterGeneralReport(searchTerm) {
    if (!searchTerm || searchTerm.length < 1) {
        showAllGeneralRows();
        return;
    }

    // البحث في جدول التقرير العام
    const reportTable = document.querySelector('.report-table tbody');
    const salesTable = document.querySelector('.sales-details tbody');

    let targetTable = reportTable || salesTable;
    if (!targetTable) return;

    const rows = targetTable.querySelectorAll('tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let matchFound = false;

        cells.forEach(cell => {
            const cellText = cell.textContent.toLowerCase();
            if (cellText.includes(searchTerm.toLowerCase())) {
                matchFound = true;
            }
        });

        if (matchFound) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // تحديث عداد النتائج
    updateGeneralResultsCount(visibleCount, rows.length);
}

// دالة لإظهار جميع الصفوف في التقارير العامة
function showAllGeneralRows() {
    const reportTable = document.querySelector('.report-table tbody');
    const salesTable = document.querySelector('.sales-details tbody');

    let targetTable = reportTable || salesTable;
    if (!targetTable) return;

    const rows = targetTable.querySelectorAll('tr');
    rows.forEach(row => {
        row.style.display = '';
    });

    // إخفاء عداد النتائج
    const resultsCounter = document.getElementById('general-results-counter');
    if (resultsCounter) {
        resultsCounter.style.display = 'none';
    }
}

// دالة لتحديث عداد النتائج في التقارير العامة
function updateGeneralResultsCount(visible, total) {
    let resultsCounter = document.getElementById('general-results-counter');
    if (!resultsCounter) {
        resultsCounter = document.createElement('div');
        resultsCounter.id = 'general-results-counter';
        resultsCounter.style.cssText = `
            background: #e3f2fd;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
            color: #1976d2;
            border-right: 4px solid #2196f3;
        `;

        const reportSection = document.querySelector('.report-section');
        const reportTable = document.querySelector('.report-table');
        const salesTable = document.querySelector('.sales-details');

        let targetElement = reportTable || salesTable;
        if (targetElement && reportSection) {
            reportSection.insertBefore(resultsCounter, targetElement);
        }
    }

    resultsCounter.innerHTML = `
        <i class="fas fa-search"></i>
        عرض ${visible} من أصل ${total} نتيجة
    `;
    resultsCounter.style.display = 'block';
}

// تحديث دالة setupInventoryReportSearch لتكون أكثر عمومية
function setupInventoryReportSearch() {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        // إزالة المستمعات السابقة لتجنب التكرار
        searchInput.removeEventListener('input', handleReportSearch);
        searchInput.removeEventListener('keyup', handleReportSearchKeyup);
        searchInput.removeEventListener('blur', handleReportSearchBlur);

        // إضافة المستمعات الجديدة
        searchInput.addEventListener('input', handleReportSearch);
        searchInput.addEventListener('keyup', handleReportSearchKeyup);
        searchInput.addEventListener('blur', handleReportSearchBlur);
    }
}

// دوال معالجة أحداث البحث
function handleReportSearch() {
    const searchTerm = this.value;
    filterAnyReport(searchTerm);
    showGeneralSearchSuggestions(searchTerm);
}

function handleReportSearchKeyup(e) {
    if (e.key === 'Enter') {
        filterAnyReport(this.value);
    }
}

function handleReportSearchBlur() {
    setTimeout(() => {
        hideGeneralSearchSuggestions();
    }, 200);
}

// دالة لعرض اقتراحات البحث العامة
function showGeneralSearchSuggestions(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) {
        hideGeneralSearchSuggestions();
        return;
    }

    // إنشاء قائمة الاقتراحات إذا لم تكن موجودة
    let suggestionsContainer = document.getElementById('general-search-suggestions');
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'general-search-suggestions';
        suggestionsContainer.className = 'report-search-suggestions';

        const searchBox = document.querySelector('.report-search-section .search-box');
        if (searchBox) {
            searchBox.appendChild(suggestionsContainer);
        }
    }

    // الحصول على البيانات المطابقة
    const matchingData = getMatchingGeneralData(searchTerm.toLowerCase());

    if (matchingData.length === 0) {
        hideGeneralSearchSuggestions();
        return;
    }

    // إنشاء HTML للاقتراحات
    const suggestionsHTML = matchingData.slice(0, 5).map(item => `
        <div class="report-suggestion-item" onclick="selectGeneralSuggestion('${item.text}')">
            <div class="report-suggestion-name">${highlightGeneralMatch(item.text, searchTerm)}</div>
            <div class="report-suggestion-details">${item.context}</div>
        </div>
    `).join('');

    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

// دالة لإخفاء اقتراحات البحث العامة
function hideGeneralSearchSuggestions() {
    const suggestionsContainer = document.getElementById('general-search-suggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

// دالة للحصول على البيانات المطابقة العامة
function getMatchingGeneralData(searchTerm) {
    const reportTable = document.querySelector('.report-table tbody');
    const salesTable = document.querySelector('.sales-details tbody');

    let targetTable = reportTable || salesTable;
    if (!targetTable) return [];

    const matchingData = [];
    const rows = targetTable.querySelectorAll('tr');

    // الحصول على رؤوس الأعمدة
    let headers = [];
    if (reportTable) {
        headers = Array.from(document.querySelectorAll('.report-table thead th')).map(th => th.textContent);
    } else if (salesTable) {
        headers = Array.from(document.querySelectorAll('.sales-details thead th')).map(th => th.textContent);
    }

    rows.forEach((row, rowIndex) => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, cellIndex) => {
            const cellText = cell.textContent.trim();
            if (cellText.toLowerCase().includes(searchTerm)) {
                // تحديد السياق بناءً على موقع الخلية
                let context = '';
                if (headers[cellIndex]) {
                    context = `${headers[cellIndex]} - الصف ${rowIndex + 1}`;
                }

                matchingData.push({
                    text: cellText,
                    context: context,
                    row: rowIndex,
                    cell: cellIndex
                });
            }
        });
    });

    // إزالة التكرارات
    const uniqueData = [];
    const seen = new Set();

    matchingData.forEach(item => {
        const key = item.text.toLowerCase();
        if (!seen.has(key)) {
            seen.add(key);
            uniqueData.push(item);
        }
    });

    return uniqueData;
}

// دالة لتمييز النص المطابق العامة
function highlightGeneralMatch(text, searchTerm) {
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
}

// دالة لاختيار اقتراح عام
function selectGeneralSuggestion(suggestionText) {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        searchInput.value = suggestionText;
        filterAnyReport(suggestionText);
        hideGeneralSearchSuggestions();
    }
}