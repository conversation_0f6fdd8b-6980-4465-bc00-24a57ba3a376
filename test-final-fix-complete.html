<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح النهائي الكامل - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .status-fixed {
            color: #28a745;
            font-weight: bold;
        }
        .status-broken {
            color: #dc3545;
            font-weight: bold;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 الإصلاح النهائي الكامل للموردين والمنتجات</h1>

        <!-- المشاكل المصلحة -->
        <div class="test-section">
            <h2>🎯 المشاكل التي تم حلها</h2>
            <div class="highlight">
                <h3>المشاكل الأصلية:</h3>
                <p><strong>❌ التنقل في صفحة الموردين والمنتجات لا يعمل</strong></p>
                <p><strong>❌ أيقونات الإجراءات فقدت وظيفتها في الموردين والمنتجات</strong></p>
            </div>
            
            <div class="fixes-list">
                <h3>الإصلاحات المطبقة:</h3>
                <ul>
                    <li>حذف البيانات الثابتة من HTML في كلا الصفحتين</li>
                    <li>إصلاح دوال JavaScript للتنقل</li>
                    <li>تصحيح عدد الأعمدة في الجداول</li>
                    <li>إضافة أنماط CSS للأيقونات مع أولوية عالية</li>
                    <li>توحيد نظام التنقل مع المبيعات والمشتريات</li>
                    <li>إضافة 25 عنصر تجريبي لكل صفحة</li>
                    <li>تحسين تصميم الأيقونات والألوان</li>
                </ul>
            </div>
        </div>

        <!-- اختبار الصفحات -->
        <div class="test-section">
            <h2>🧪 اختبار الصفحات المصلحة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🏢</div>
                    <h3>الموردين</h3>
                    <p class="status-fixed">✅ مصلح بالكامل</p>
                    <button class="btn" onclick="testSuppliersPage()">اختبار الموردين</button>
                </div>
                <div class="page-card">
                    <div class="icon">📦</div>
                    <h3>المنتجات</h3>
                    <p class="status-fixed">✅ مصلح بالكامل</p>
                    <button class="btn" onclick="testProductsPage()">اختبار المنتجات</button>
                </div>
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>المبيعات</h3>
                    <p class="status-fixed">✅ يعمل بشكل صحيح</p>
                    <button class="btn" onclick="testSalesPage()">اختبار المبيعات</button>
                </div>
                <div class="page-card">
                    <div class="icon">🛍️</div>
                    <h3>المشتريات</h3>
                    <p class="status-fixed">✅ يعمل بشكل صحيح</p>
                    <button class="btn" onclick="testPurchasesPage()">اختبار المشتريات</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🚀 الاختبار الشامل النهائي</h2>
            <div class="highlight">
                <h3>🎯 ما يجب أن تجده الآن:</h3>
                <p><strong>🏢 الموردين:</strong> 25 مورد، تنقل يعمل، أيقونات ملونة وظيفية</p>
                <p><strong>📦 المنتجات:</strong> 25 منتج، تنقل يعمل، أيقونات ملونة وظيفية</p>
                <p><strong>🎨 تصميم موحد:</strong> نفس أسلوب المبيعات والمشتريات</p>
                <p><strong>📊 10 بنود/صفحة:</strong> عرض منظم في جميع الصفحات</p>
            </div>
            
            <button class="btn" onclick="runCompleteTest()">🧪 تشغيل الاختبار الكامل</button>
            <div id="test-result"></div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openAllPages()">🌐 فتح جميع الصفحات</button>
            <button class="btn" onclick="checkAllData()">📊 فحص جميع البيانات</button>
            <button class="btn" onclick="resetAllData()">🔄 إعادة تعيين البيانات</button>
            <button class="btn" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح الكامل يجب أن تجد:</h3>
                <ul>
                    <li><strong>🏢 الموردين:</strong> تنقل فعال + 25 مورد + أيقونات ملونة تعمل</li>
                    <li><strong>📦 المنتجات:</strong> تنقل فعال + 25 منتج + أيقونات ملونة تعمل</li>
                    <li><strong>🛒 المبيعات:</strong> تنقل فعال + 25 فاتورة + أيقونات ملونة</li>
                    <li><strong>🛍️ المشتريات:</strong> تنقل فعال + 25 فاتورة + أيقونات ملونة</li>
                    <li><strong>🎨 تصميم موحد:</strong> نفس أسلوب التنقل في جميع الصفحات</li>
                    <li><strong>📊 10 بنود/صفحة:</strong> عرض منظم ومناسب</li>
                    <li><strong>🔧 أيقونات وظيفية:</strong> عرض (أزرق)، تعديل (أصفر)، حذف (أحمر)</li>
                    <li><strong>📱 تجاوب ممتاز:</strong> يعمل على جميع أحجام الشاشات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار صفحة الموردين
        function testSuppliersPage() {
            window.open('suppliers.html', '_blank');
            showResult(`
                <div class="success">
                    🏢 <strong>تم فتح صفحة الموردين!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    ✅ التنقل بين الصفحات (1، 2، 3)<br>
                    ✅ أيقونات العرض (أزرق)، التعديل (أصفر)، الحذف (أحمر)<br>
                    ✅ عرض 10 موردين لكل صفحة<br>
                    ✅ إجمالي 25 مورد تجريبي<br><br>
                    
                    💡 <strong>انتقل لأسفل الجدول لرؤية التنقل!</strong>
                </div>
            `);
        }

        // اختبار صفحة المنتجات
        function testProductsPage() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="success">
                    📦 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    ✅ التنقل بين الصفحات (1، 2، 3)<br>
                    ✅ أيقونات العرض (أزرق)، التعديل (أصفر)، الحذف (أحمر)<br>
                    ✅ عرض 10 منتجات لكل صفحة<br>
                    ✅ إجمالي 25 منتج تجريبي<br><br>
                    
                    💡 <strong>انتقل لأسفل الجدول لرؤية التنقل!</strong>
                </div>
            `);
        }

        // اختبار صفحة المبيعات
        function testSalesPage() {
            window.open('sales.html', '_blank');
            showResult('🛒 تم فتح صفحة المبيعات للمقارنة', 'info');
        }

        // اختبار صفحة المشتريات
        function testPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛍️ تم فتح صفحة المشتريات للمقارنة', 'info');
        }

        // تشغيل الاختبار الكامل
        function runCompleteTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء الاختبار الكامل للإصلاحات!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ فتح صفحة الموردين واختبار التنقل والأيقونات<br>
                    2️⃣ فتح صفحة المنتجات واختبار التنقل والأيقونات<br>
                    3️⃣ مقارنة التصميم مع المبيعات والمشتريات<br>
                    4️⃣ التأكد من وجود 25 عنصر في كل صفحة<br>
                    5️⃣ اختبار وظائف الأيقونات (عرض، تعديل، حذف)<br>
                    6️⃣ التحقق من التنقل (10 بنود لكل صفحة)<br><br>
                    
                    <strong>🎯 ابدأ بفتح الصفحات واختبارها!</strong>
                </div>
            `);
        }

        // فتح جميع الصفحات
        function openAllPages() {
            window.open('suppliers.html', '_blank');
            window.open('products.html', '_blank');
            window.open('sales.html', '_blank');
            window.open('purchases.html', '_blank');
            showResult('🌐 تم فتح جميع الصفحات للمقارنة والاختبار', 'info');
        }

        // فحص جميع البيانات
        function checkAllData() {
            const salesData = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const purchasesData = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            const suppliersData = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص جميع البيانات:</strong><br><br>
                    
                    🛒 المبيعات: ${salesData.length} فاتورة<br>
                    🛍️ المشتريات: ${purchasesData.length} فاتورة<br>
                    🏢 الموردين: ${suppliersData.length} مورد<br>
                    📦 المنتجات: ${productsData.length} منتج<br><br>
                    
                    <strong>📋 التنقل المتوقع:</strong><br>
                    📄 صفحات المبيعات: ${Math.ceil(salesData.length / 10)}<br>
                    📄 صفحات المشتريات: ${Math.ceil(purchasesData.length / 10)}<br>
                    📄 صفحات الموردين: ${Math.ceil(suppliersData.length / 10)}<br>
                    📄 صفحات المنتجات: ${Math.ceil(productsData.length / 10)}<br><br>
                    
                    ${Math.min(salesData.length, purchasesData.length, suppliersData.length, productsData.length) >= 10 ? 
                        '✅ <strong>جميع البيانات كافية لاختبار التنقل!</strong>' :
                        '❌ <strong>بعض البيانات قليلة، استخدم "إعادة تعيين البيانات"</strong>'
                    }
                </div>
            `);
        }

        // إعادة تعيين جميع البيانات
        function resetAllData() {
            // إعادة تعيين بيانات الموردين
            const suppliersData = [];
            for (let i = 1; i <= 25; i++) {
                suppliersData.push({
                    id: i,
                    code: String(i).padStart(3, '0'),
                    name: `مورد رقم ${i}`,
                    type: ['شركة', 'فرد', 'مؤسسة'][Math.floor(Math.random() * 3)],
                    phone: `+96611234${String(i).padStart(4, '0')}`,
                    email: `supplier${i}@example.com`,
                    address: `عنوان المورد رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliersData));
            
            // إعادة تعيين بيانات المنتجات
            const productsData = [];
            for (let i = 1; i <= 25; i++) {
                productsData.push({
                    id: i,
                    code: `PRD-${String(i).padStart(3, '0')}`,
                    name: `منتج رقم ${i}`,
                    category: ['إلكترونيات', 'ملابس', 'أدوات منزلية', 'كتب'][Math.floor(Math.random() * 4)],
                    price: (Math.random() * 1000 + 50).toFixed(2),
                    quantity: Math.floor(Math.random() * 100) + 1,
                    minQuantity: Math.floor(Math.random() * 10) + 1,
                    unit: ['قطعة', 'كيلو', 'متر', 'لتر'][Math.floor(Math.random() * 4)],
                    description: `وصف المنتج رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            showResult(`
                <div class="success">
                    🔄 <strong>تم إعادة تعيين جميع البيانات!</strong><br><br>
                    📊 تم إنشاء 25 مورد جديد<br>
                    📦 تم إنشاء 25 منتج جديد<br><br>
                    💡 <strong>افتح الصفحات الآن لرؤية البيانات الجديدة!</strong>
                </div>
            `);
        }

        // مسح جميع البيانات
        function clearAllData() {
            localStorage.removeItem('monjizInvoices');
            localStorage.removeItem('monjizPurchases');
            localStorage.removeItem('monjizSuppliers');
            localStorage.removeItem('monjizProducts');
            showResult('🗑️ تم مسح جميع البيانات التجريبية', 'info');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح جميع مشاكل الموردين والمنتجات بنجاح!</strong><br><br>
                    
                    <strong>🏢 الموردين:</strong><br>
                    ✅ حذف البيانات الثابتة من HTML<br>
                    ✅ إصلاح دوال JavaScript للتنقل<br>
                    ✅ تصحيح عدد الأعمدة (5 أعمدة)<br>
                    ✅ إضافة أنماط CSS للأيقونات<br><br>
                    
                    <strong>📦 المنتجات:</strong><br>
                    ✅ حذف البيانات الثابتة من HTML<br>
                    ✅ إصلاح دوال JavaScript للتنقل<br>
                    ✅ تصحيح عدد الأعمدة (7 أعمدة)<br>
                    ✅ إضافة أنماط CSS للأيقونات<br><br>
                    
                    🧪 <strong>اضغط "تشغيل الاختبار الكامل" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
