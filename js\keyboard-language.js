// keyboard-language.js - وظائف لصفحة تغيير لغة لوحة المفاتيح

document.addEventListener('DOMContentLoaded', function() {
    // إضافة وظيفة لعرض الاختصارات بشكل تفاعلي
    const shortcutElements = document.querySelectorAll('.keyboard-shortcut');
    
    shortcutElements.forEach(element => {
        element.addEventListener('click', function() {
            // إضافة تأثير عند النقر على اختصار لوحة المفاتيح
            this.classList.add('active');
            
            // إزالة التأثير بعد 500 مللي ثانية
            setTimeout(() => {
                this.classList.remove('active');
            }, 500);
        });
    });

    // إضافة وظيفة للتحقق من لغة لوحة المفاتيح الحالية
    const checkLanguageBtn = document.getElementById('check-language-btn');
    if (checkLanguageBtn) {
        checkLanguageBtn.addEventListener('click', function() {
            const languageDisplay = document.getElementById('current-language');
            
            // محاولة الكشف عن لغة المتصفح
            const browserLanguage = navigator.language || navigator.userLanguage;
            const isArabic = browserLanguage.includes('ar');
            
            if (languageDisplay) {
                if (isArabic) {
                    languageDisplay.textContent = 'العربية (AR)';
                    languageDisplay.className = 'language-display arabic';
                } else {
                    languageDisplay.textContent = 'الإنجليزية (EN)';
                    languageDisplay.className = 'language-display english';
                }
                
                // إظهار عنصر عرض اللغة
                languageDisplay.style.display = 'block';
            }
        });
    }

    // إضافة وظيفة لعرض وإخفاء الصور التوضيحية
    const toggleImageBtns = document.querySelectorAll('.toggle-image-btn');
    
    toggleImageBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const imageContainer = document.getElementById(targetId);
            
            if (imageContainer) {
                // تبديل حالة العرض
                if (imageContainer.style.display === 'none' || imageContainer.style.display === '') {
                    imageContainer.style.display = 'block';
                    this.textContent = 'إخفاء الصورة';
                } else {
                    imageContainer.style.display = 'none';
                    this.textContent = 'عرض الصورة';
                }
            }
        });
    });

    // إضافة وظيفة لتشغيل فيديو توضيحي
    const videoBtn = document.getElementById('show-video-btn');
    const videoContainer = document.getElementById('video-tutorial');
    
    if (videoBtn && videoContainer) {
        videoBtn.addEventListener('click', function() {
            if (videoContainer.style.display === 'none' || videoContainer.style.display === '') {
                videoContainer.style.display = 'block';
                this.textContent = 'إخفاء الفيديو';
            } else {
                videoContainer.style.display = 'none';
                this.textContent = 'عرض الفيديو';
            }
        });
    }
});