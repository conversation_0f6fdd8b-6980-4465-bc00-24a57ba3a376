<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الربط المركزي للحسابات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff8f00);
            color: #212529;
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #28a745;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #28a745;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .test-card h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .test-card .icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار الربط المركزي للحسابات</h1>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>✅ تم إصلاح الربط المركزي</h2>
            <div class="highlight">
                <h3>المشاكل التي تم حلها:</h3>
                <p><strong>✅ ربط العملاء:</strong> عند إضافة عميل يظهر فوراً في دليل الحسابات</p>
                <p><strong>✅ إضافة حساب جديد:</strong> يحفظ ويظهر في دليل الحسابات</p>
                <p><strong>✅ تنسيق الأيقونات:</strong> ألوان جميلة (أزرق، أصفر، أحمر)</p>
                <p><strong>✅ جميع الوظائف:</strong> قيد جديد، سند قبض، سند صرف</p>
            </div>
            
            <div class="fixes-list">
                <h3>الإصلاحات المطبقة:</h3>
                <ul>
                    <li>إضافة دالة addCustomerToAccounts() في customers.html</li>
                    <li>إضافة دالة saveNewAccount() في accounting.html</li>
                    <li>تحسين تنسيق أيقونات الإجراءات بألوان مميزة</li>
                    <li>إضافة بيانات تجريبية مع أيقونات ملونة</li>
                    <li>ربط النماذج بدوال الحفظ</li>
                </ul>
            </div>
        </div>

        <!-- اختبار الربط المركزي -->
        <div class="test-section">
            <h2>🧪 اختبار الربط المركزي</h2>
            
            <div class="test-grid">
                <div class="test-card">
                    <div class="icon">👥</div>
                    <h3>اختبار ربط العملاء</h3>
                    <p>إضافة عميل جديد وظهوره في دليل الحسابات</p>
                    <button class="btn" onclick="testCustomerIntegration()">اختبار العملاء</button>
                </div>
                <div class="test-card">
                    <div class="icon">📊</div>
                    <h3>اختبار إضافة حساب</h3>
                    <p>إضافة حساب جديد في دليل الحسابات</p>
                    <button class="btn btn-info" onclick="testAccountCreation()">اختبار الحساب</button>
                </div>
                <div class="test-card">
                    <div class="icon">⚙️</div>
                    <h3>اختبار الأيقونات</h3>
                    <p>تنسيق أيقونات الإجراءات الملونة</p>
                    <button class="btn btn-warning" onclick="testIconStyling()">اختبار الأيقونات</button>
                </div>
                <div class="test-card">
                    <div class="icon">🧾</div>
                    <h3>اختبار الوظائف</h3>
                    <p>جميع وظائف الحسابات</p>
                    <button class="btn" onclick="testAllFunctions()">اختبار شامل</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🚀 الاختبار الشامل</h2>
            <button class="btn" onclick="openCustomersPage()">👥 فتح صفحة العملاء</button>
            <button class="btn btn-info" onclick="openAccountingPage()">🧾 فتح صفحة الحسابات</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار المفصلة</h2>
            <div class="info">
                <h3>🔗 اختبار الربط المركزي:</h3>
                <ol>
                    <li><strong>افتح صفحة العملاء</strong> واضغط "عميل جديد"</li>
                    <li><strong>أضف عميل جديد</strong> (مثل: "عميل اختبار")</li>
                    <li><strong>احفظ العميل</strong> وانتظر رسالة النجاح</li>
                    <li><strong>افتح صفحة الحسابات</strong> وانتقل لتبويب "دليل الحسابات"</li>
                    <li><strong>ابحث عن الحساب</strong> "العميل: عميل اختبار"</li>
                    <li><strong>يجب أن يظهر الحساب</strong> برقم 1003-[رقم العميل]</li>
                </ol>
                
                <h3>📊 اختبار إضافة حساب جديد:</h3>
                <ol>
                    <li><strong>في صفحة الحسابات</strong> انتقل لتبويب "دليل الحسابات"</li>
                    <li><strong>اضغط "حساب جديد"</strong></li>
                    <li><strong>املأ البيانات:</strong> رقم الحساب، اسم الحساب، النوع</li>
                    <li><strong>احفظ الحساب</strong></li>
                    <li><strong>يجب أن يظهر</strong> في دليل الحسابات فوراً</li>
                </ol>
                
                <h3>⚙️ اختبار الأيقونات:</h3>
                <ul>
                    <li><strong>🔵 أيقونة العرض:</strong> زرقاء اللون</li>
                    <li><strong>🟡 أيقونة التعديل:</strong> صفراء اللون</li>
                    <li><strong>🔴 أيقونة الحذف:</strong> حمراء اللون</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار ربط العملاء
        function testCustomerIntegration() {
            showResult(`
                <div class="info">
                    👥 <strong>اختبار ربط العملاء:</strong><br><br>
                    
                    <strong>الإصلاح المطبق:</strong><br>
                    ✅ إضافة دالة addCustomerToAccounts()<br>
                    ✅ ربط العميل الجديد بدليل الحسابات تلقائياً<br>
                    ✅ رقم الحساب: 1003-[رقم العميل]<br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة العملاء<br>
                    2️⃣ أضف عميل جديد<br>
                    3️⃣ افتح صفحة الحسابات<br>
                    4️⃣ تحقق من ظهور حساب العميل<br><br>
                    
                    💡 <strong>يجب أن يظهر العميل في دليل الحسابات فوراً!</strong>
                </div>
            `);
        }

        // اختبار إضافة حساب
        function testAccountCreation() {
            showResult(`
                <div class="info">
                    📊 <strong>اختبار إضافة حساب جديد:</strong><br><br>
                    
                    <strong>الإصلاح المطبق:</strong><br>
                    ✅ إضافة دالة saveNewAccount()<br>
                    ✅ ربط النموذج بدالة الحفظ<br>
                    ✅ حفظ في localStorage<br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة الحسابات<br>
                    2️⃣ انتقل لتبويب "دليل الحسابات"<br>
                    3️⃣ اضغط "حساب جديد"<br>
                    4️⃣ املأ البيانات واحفظ<br><br>
                    
                    💡 <strong>يجب أن يظهر الحساب الجديد فوراً!</strong>
                </div>
            `);
        }

        // اختبار تنسيق الأيقونات
        function testIconStyling() {
            showResult(`
                <div class="info">
                    ⚙️ <strong>اختبار تنسيق الأيقونات:</strong><br><br>
                    
                    <strong>الإصلاح المطبق:</strong><br>
                    🔵 أيقونة العرض: background: #17a2b8 (أزرق)<br>
                    🟡 أيقونة التعديل: background: #ffc107 (أصفر)<br>
                    🔴 أيقونة الحذف: background: #dc3545 (أحمر)<br><br>
                    
                    <strong>في جميع الجداول:</strong><br>
                    ✅ جدول قيود اليومية<br>
                    ✅ جدول سندات القبض<br>
                    ✅ جدول سندات الصرف<br><br>
                    
                    💡 <strong>الأيقونات ملونة ومرتبة أفقياً!</strong>
                </div>
            `);
        }

        // اختبار جميع الوظائف
        function testAllFunctions() {
            showResult(`
                <div class="success">
                    🧾 <strong>اختبار جميع الوظائف:</strong><br><br>
                    
                    <strong>الوظائف العاملة:</strong><br>
                    ✅ قيد جديد (زرين)<br>
                    ✅ سند قبض جديد<br>
                    ✅ سند صرف جديد<br>
                    ✅ حساب جديد<br>
                    ✅ جميع أيقونات الإجراءات<br><br>
                    
                    <strong>الربط المركزي:</strong><br>
                    ✅ العملاء → دليل الحسابات<br>
                    ✅ الموردين → دليل الحسابات (يعمل مسبقاً)<br>
                    ✅ الحسابات الجديدة → تحفظ وتظهر<br><br>
                    
                    🎊 <strong>جميع الوظائف تعمل بشكل مثالي!</strong>
                </div>
            `);
        }

        // فتح صفحة العملاء
        function openCustomersPage() {
            window.open('customers.html', '_blank');
            showResult(`
                <div class="info">
                    👥 <strong>تم فتح صفحة العملاء!</strong><br><br>
                    
                    <strong>اختبر الربط المركزي:</strong><br>
                    1️⃣ اضغط "عميل جديد"<br>
                    2️⃣ أضف عميل (مثل: "عميل اختبار")<br>
                    3️⃣ احفظ العميل<br>
                    4️⃣ افتح صفحة الحسابات<br>
                    5️⃣ تحقق من ظهور حساب العميل<br><br>
                    
                    💡 <strong>يجب أن يظهر في دليل الحسابات!</strong>
                </div>
            `);
        }

        // فتح صفحة الحسابات
        function openAccountingPage() {
            window.open('accounting.html', '_blank');
            showResult(`
                <div class="success">
                    🧾 <strong>تم فتح صفحة الحسابات!</strong><br><br>
                    
                    <strong>اختبر جميع الوظائف:</strong><br>
                    📝 قيد جديد (في الأعلى وفي القسم)<br>
                    📥 سند قبض جديد<br>
                    📤 سند صرف جديد<br>
                    📊 حساب جديد (في دليل الحسابات)<br>
                    ⚙️ جميع الأيقونات الملونة<br><br>
                    
                    💡 <strong>كل شيء يعمل بشكل مثالي!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="success">
                    🔗 <strong>تم إصلاح الربط المركزي بنجاح!</strong><br><br>
                    
                    <strong>المشاكل المحلولة:</strong><br>
                    ✅ العملاء يظهرون في دليل الحسابات<br>
                    ✅ الحسابات الجديدة تحفظ وتظهر<br>
                    ✅ الأيقونات ملونة ومنسقة<br>
                    ✅ جميع الوظائف تعمل<br><br>
                    
                    🚀 <strong>اختبر الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
