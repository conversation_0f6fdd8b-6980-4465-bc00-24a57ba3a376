' VBScript to open launcher.html without Arabic character issues
' Updated with improved error handling and multiple methods
Option Explicit

Dim objShell, objFSO, objWMI, strPath, strFile, strMsg
Dim blnSuccess, strError

' Initialize success flag
blnSuccess = False

' Set up file system objects
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get the path to the launcher.html file
strPath = objFSO.GetParentFolderName(WScript.ScriptFullName)
strFile = objFSO.BuildPath(strPath, "launcher.html")

' Check if the file exists
If objFSO.FileExists(strFile) Then
    WScript.Echo "Opening launcher at: " & strFile
    
    ' Try Method 1: Using Run method
    On Error Resume Next
    objShell.Run """" & strFile & """", 1, False
    
    If Err.Number <> 0 Then
        strError = "Method 1 failed: " & Err.Description
        Err.Clear
        
        ' Try Method 2: Using ShellExecute
        Dim objShellApp
        Set objShellApp = CreateObject("Shell.Application")
        objShellApp.ShellExecute strFile, "", "", "open", 1
        
        If Err.Number <> 0 Then
            strError = strError & vbCrLf & "Method 2 failed: " & Err.Description
            Err.Clear
            
            ' Try Method 3: Using Internet Explorer as a fallback
            Dim objIE
            Set objIE = CreateObject("InternetExplorer.Application")
            objIE.Navigate "file://" & strFile
            objIE.Visible = True
            
            If Err.Number <> 0 Then
                strError = strError & vbCrLf & "Method 3 failed: " & Err.Description
                Err.Clear
                
                strMsg = "All methods failed to open the launcher." & vbCrLf & _
                         "Error details: " & strError & vbCrLf & vbCrLf & _
                         "Please try one of the following alternatives:" & vbCrLf & _
                         "1. Open launcher.html directly from your file explorer" & vbCrLf & _
                         "2. Try using open_launcher.ps1 (PowerShell script)" & vbCrLf & _
                         "3. Try using start_server.bat"
                
                WScript.Echo strMsg
            Else
                blnSuccess = True
                WScript.Echo "Launcher opened successfully using Internet Explorer!"
            End If
            
            Set objIE = Nothing
        Else
            blnSuccess = True
            WScript.Echo "Launcher opened successfully using ShellExecute!"
        End If
        
        Set objShellApp = Nothing
    Else
        blnSuccess = True
        WScript.Echo "Launcher opened successfully using Run method!"
    End If
    On Error Goto 0
Else
    WScript.Echo "Launcher file not found at: " & strFile
End If

' Clean up objects
Set objShell = Nothing
Set objFSO = Nothing

' Exit with appropriate code
If Not blnSuccess Then
    WScript.Quit 1
End If