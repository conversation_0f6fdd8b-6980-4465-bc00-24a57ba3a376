# PowerShell script to open accounting.html directly
# Created to fix accounting management functions issue

# Get the current directory (where this script is located)
$currentDir = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition

# Build the path to accounting.html
$accountingPath = Join-Path -Path $currentDir -ChildPath "accounting.html"

# Check if the file exists
if (Test-Path -Path $accountingPath) {
    Write-Host "فتح صفحة الحسابات من: $accountingPath"
    
    try {
        # Open the accounting.html file with the default browser
        Start-Process $accountingPath
        Write-Host "تم فتح صفحة الحسابات بنجاح!"
    }
    catch {
        Write-Host "خطأ في فتح صفحة الحسابات: $_"
        Write-Host "الرجاء محاولة فتح accounting.html مباشرة من مستكشف الملفات"
    }
}
else {
    Write-Host "لم يتم العثور على ملف صفحة الحسابات في: $accountingPath" -ForegroundColor Red
}