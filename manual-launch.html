<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كيفية تشغيل النظام يدويًا</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #3498db;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
        }
        .method {
            background-color: #f8f9fa;
            border-right: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .method h2 {
            color: #3498db;
            margin-top: 0;
        }
        .steps {
            margin-right: 20px;
        }
        .step {
            margin-bottom: 15px;
        }
        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: #3498db;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            margin-left: 10px;
        }
        .code {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .note {
            background-color: #fff3cd;
            border-right: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .note h3 {
            color: #856404;
            margin-top: 0;
        }
        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
        img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>كيفية تشغيل النظام يدويًا</h1>
        
        <div class="note">
            <h3>ملاحظة هامة</h3>
            <p>نظرًا لاستمرار مشكلة الحرف العربي (ؤ) حتى عند محاولة تشغيل ملفات البات، فإن أفضل طريقة هي تشغيل الملفات يدويًا من خلال مستكشف الملفات (Windows Explorer).</p>
        </div>
        
        <div class="method">
            <h2>الطريقة 1: فتح ملف launcher.html مباشرة</h2>
            <div class="steps">
                <div class="step">
                    <span class="step-number">1</span>
                    افتح مستكشف الملفات (Windows Explorer) بالضغط على زر Windows + E.
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    انتقل إلى المجلد <span class="code">C:\Users\<USER>\Downloads\Monjiz</span> أو المجلد الذي قمت بتنزيل النظام إليه.
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    ابحث عن ملف <span class="code">launcher.html</span> وانقر عليه نقرًا مزدوجًا لفتحه في المتصفح الافتراضي.
                </div>
            </div>
        </div>
        
        <div class="method">
            <h2>الطريقة 2: تشغيل ملف VBS يدويًا</h2>
            <div class="steps">
                <div class="step">
                    <span class="step-number">1</span>
                    افتح مستكشف الملفات (Windows Explorer) بالضغط على زر Windows + E.
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    انتقل إلى المجلد <span class="code">C:\Users\<USER>\Downloads\Monjiz</span> أو المجلد الذي قمت بتنزيل النظام إليه.
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    ابحث عن ملف <span class="code">open_launcher.vbs</span> وانقر عليه بزر الماوس الأيمن.
                </div>
                <div class="step">
                    <span class="step-number">4</span>
                    اختر "تشغيل باستخدام" (Run with) أو "فتح باستخدام" (Open with) من القائمة.
                </div>
                <div class="step">
                    <span class="step-number">5</span>
                    اختر "Windows Script Host" أو "Microsoft Windows Based Script Host" من القائمة.
                </div>
            </div>
        </div>
        
        <div class="method">
            <h2>الطريقة 3: تشغيل ملفات البات يدويًا</h2>
            <div class="steps">
                <div class="step">
                    <span class="step-number">1</span>
                    افتح مستكشف الملفات (Windows Explorer) بالضغط على زر Windows + E.
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    انتقل إلى المجلد <span class="code">C:\Users\<USER>\Downloads\Monjiz</span> أو المجلد الذي قمت بتنزيل النظام إليه.
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    ابحث عن أحد ملفات البات التالية وانقر عليه نقرًا مزدوجًا:
                    <ul>
                        <li><span class="code">run_vbs.bat</span> - لتشغيل ملف VBS</li>
                        <li><span class="code">run_ps1.bat</span> - لتشغيل ملف PowerShell</li>
                        <li><span class="code">run_py.bat</span> - لتشغيل ملف Python</li>
                        <li><span class="code">run_js.bat</span> - لتشغيل ملف JavaScript</li>
                        <li><span class="code">start_server.bat</span> - لتشغيل خادم محلي</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="note">
            <h3>تغيير لغة لوحة المفاتيح</h3>
            <p>إذا كنت ترغب في استخدام سطر الأوامر مباشرة، فتأكد من تغيير لغة لوحة المفاتيح إلى الإنجليزية قبل كتابة أي أمر:</p>
            <ol>
                <li>اضغط على زر Windows + Space أو Alt + Shift لتبديل لغة لوحة المفاتيح.</li>
                <li>تأكد من اختيار اللغة الإنجليزية (EN) من قائمة اللغات.</li>
                <li>ثم قم بكتابة الأوامر في سطر الأوامر.</li>
            </ol>
        </div>
        
        <div class="buttons">
            <a href="launcher.html" class="btn btn-green">فتح مشغل النظام</a>
            <a href="important-notice.html" class="btn">ملاحظة هامة</a>
            <a href="README.md" class="btn">عرض ملف التعليمات</a>
        </div>
    </div>
</body>
</html>