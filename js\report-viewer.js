// دالة للحصول على معلمات URL
function getUrlParameters() {
    const params = {};
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    
    // الحصول على نوع التقرير
    params.type = urlParams.get('type');
    
    // الحصول على تاريخ البداية والنهاية
    params.start = urlParams.get('start');
    params.end = urlParams.get('end');
    
    // الحصول على عنوان التقرير
    params.title = urlParams.get('title');
    
    return params;
}

// دالة لتحديث عنوان التقرير
function updateReportTitle(title) {
    const reportTitleElement = document.getElementById('report-title');
    if (reportTitleElement) {
        reportTitleElement.textContent = title;
    }
}

// دالة لتحديث نطاق تاريخ التقرير
function updateReportDateRange(startDate, endDate) {
    const reportDateRangeElement = document.getElementById('report-date-range');
    if (reportDateRangeElement) {
        let dateRangeText = 'الفترة: ';
        
        if (startDate && endDate) {
            // تنسيق التواريخ
            const formattedStartDate = new Date(startDate).toLocaleDateString('ar-SA');
            const formattedEndDate = new Date(endDate).toLocaleDateString('ar-SA');
            dateRangeText += `${formattedStartDate} - ${formattedEndDate}`;
        } else if (startDate) {
            const formattedStartDate = new Date(startDate).toLocaleDateString('ar-SA');
            dateRangeText += `من ${formattedStartDate}`;
        } else if (endDate) {
            const formattedEndDate = new Date(endDate).toLocaleDateString('ar-SA');
            dateRangeText += `حتى ${formattedEndDate}`;
        } else {
            dateRangeText += 'كل الفترات';
        }
        
        reportDateRangeElement.textContent = dateRangeText;
    }
}

// دالة لتحديث حقول التاريخ في نموذج التصفية
function updateDateInputs(startDate, endDate) {
    const startDateInput = document.getElementById('report-start-date');
    const endDateInput = document.getElementById('report-end-date');
    
    if (startDateInput && startDate) {
        startDateInput.value = startDate;
    }
    
    if (endDateInput && endDate) {
        endDateInput.value = endDate;
    }
}

// دالة لعرض كشف حساب
function displayAccountStatement(accountId, startDate, endDate) {
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';
    
    // تحديث رؤوس الجدول
    document.getElementById('report-header-row').innerHTML = `
        <th>التاريخ</th>
        <th>المستند</th>
        <th>البيان</th>
        <th>مدين</th>
        <th>دائن</th>
        <th>الرصيد</th>
    `;
    
    // الحصول على الحساب المحدد إذا لم يتم تمريره
if (!accountId) {
    accountId = document.getElementById('account').value;
}

// الحصول على العميل/المورد المحدد
const customerId = document.getElementById('customer').value;
    
    // بيانات تجريبية لكشف الحساب
    let accountData = [
        { date: '2023-06-20', document: 'سند قبض 001', description: 'تحصيل من العميل', debit: 0, credit: 1500, balance: 1500 },
        { date: '2023-06-18', document: 'فاتورة 123', description: 'مبيعات', debit: 2500, credit: 0, balance: -1000 },
        { date: '2023-06-15', document: 'سند قبض 002', description: 'تحصيل من العميل', debit: 0, credit: 3000, balance: 2000 },
        { date: '2023-06-10', document: 'فاتورة 120', description: 'مبيعات', debit: 1800, credit: 0, balance: -1000 },
        { date: '2023-06-05', document: 'سند قبض 003', description: 'تحصيل من العميل', debit: 0, credit: 2200, balance: 1200 },
        { date: '2023-06-01', document: 'فاتورة 115', description: 'مبيعات', debit: 3500, credit: 0, balance: -2300 }
    ];
    
    // تخصيص البيانات حسب الحساب المحدد
    if (accountId === '1001') { // الصندوق الرئيسي
        accountData = [
            { date: '2023-06-20', document: 'سند قبض 001', description: 'تحصيل من العميل شركة الأمل', debit: 2500, credit: 0, balance: 12500 },
            { date: '2023-06-15', document: 'سند صرف 001', description: 'دفع مصاريف إيجار', debit: 0, credit: 3000, balance: 9500 },
            { date: '2023-06-10', document: 'سند قبض 002', description: 'تحصيل من العميل مؤسسة النور', debit: 1800, credit: 0, balance: 11300 },
            { date: '2023-06-05', document: 'سند صرف 002', description: 'دفع رواتب موظفين', debit: 0, credit: 5000, balance: 6300 },
            { date: '2023-06-01', document: 'رصيد افتتاحي', description: 'رصيد افتتاحي', debit: 0, credit: 0, balance: 10000 }
        ];
    } else if (accountId === '1002') { // البنك الأهلي
        accountData = [
            { date: '2023-06-28', document: 'سند صرف 003', description: 'سحب نقدي', debit: 0, credit: 4000, balance: 34500 },
            { date: '2023-06-22', document: 'إشعار إيداع 002', description: 'تحويل من عميل', debit: 12000, credit: 0, balance: 38500 },
            { date: '2023-06-18', document: 'شيك 1001', description: 'سداد فاتورة مورد', debit: 0, credit: 6500, balance: 26500 },
            { date: '2023-06-12', document: 'إشعار إيداع 001', description: 'إيداع نقدي', debit: 8000, credit: 0, balance: 33000 },
            { date: '2023-06-05', document: 'رصيد افتتاحي', description: 'رصيد افتتاحي', debit: 0, credit: 0, balance: 25000 }
        ];
    }
    
    // تخصيص البيانات حسب العميل/المورد المحدد
    if (customerId === '2001') { // شركة الأمل
        accountData = [
            { date: '2023-06-25', document: 'فاتورة 125', description: 'فاتورة مبيعات', debit: 2800, credit: 0, balance: 12300 },
            { date: '2023-06-15', document: 'سند قبض 005', description: 'تحصيل دفعة', debit: 0, credit: 3000, balance: 9500 },
            { date: '2023-06-10', document: 'فاتورة 120', description: 'فاتورة مبيعات', debit: 4500, credit: 0, balance: 12500 },
            { date: '2023-06-05', document: 'رصيد افتتاحي', description: 'رصيد افتتاحي', debit: 0, credit: 0, balance: 8000 }
        ];
    }
    
    // إضافة الصفوف
    let totalDebit = 0;
    let totalCredit = 0;
    
    accountData.forEach(item => {
        // تصفية البيانات حسب التاريخ إذا تم تحديده
        if ((startDate && new Date(item.date) < new Date(startDate)) || 
            (endDate && new Date(item.date) > new Date(endDate))) {
            return;
        }
        
        const row = document.createElement('tr');
        const formattedDate = new Date(item.date).toLocaleDateString('ar-SA');
        
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${item.document}</td>
            <td>${item.description}</td>
            <td>${item.debit.toLocaleString('ar-SA')} ر.س</td>
            <td>${item.credit.toLocaleString('ar-SA')} ر.س</td>
            <td>${item.balance.toLocaleString('ar-SA')} ر.س</td>
        `;
        
        tableBody.appendChild(row);
        
        // تحديث المجاميع
        totalDebit += item.debit;
        totalCredit += item.credit;
    });
    
    // تحديث الإجمالي
    document.getElementById('report-footer-row').innerHTML = `
        <td colspan="3">الإجمالي</td>
        <td id="total-debit">${totalDebit.toLocaleString('ar-SA')} ر.س</td>
        <td id="total-credit">${totalCredit.toLocaleString('ar-SA')} ر.س</td>
        <td id="total-balance">${(totalDebit - totalCredit).toLocaleString('ar-SA')} ر.س</td>
    `;
}

// دالة لتحديث التقرير بناءً على الخيارات المحددة
function refreshReport() {
    const accountId = document.getElementById('account-select').value;
    const customerId = document.getElementById('customer-select').value;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    // تحديث نطاق التاريخ
    updateReportDateRange(startDate, endDate);
    
    // عرض التقرير المناسب
    const params = getUrlParameters();
    if (params.type === 'account_statement') {
        displayAccountStatement(accountId || customerId, startDate, endDate);
    }
    // يمكن إضافة المزيد من أنواع التقارير هنا
}

// دالة لطباعة التقرير
function printReport() {
    window.print();
}

// دالة لتصدير التقرير
function exportReport() {
    alert('جاري تصدير التقرير...');
    // في التطبيق الحقيقي، سيتم تنفيذ عملية تصدير التقرير إلى ملف
}

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // الحصول على معلمات URL
    const params = getUrlParameters();
    
    // تحديث حقول التاريخ
    updateDateInputs(params.start, params.end);
    
    // تحديث عنوان التقرير
    if (params.title) {
        updateReportTitle(params.title);
    } else if (params.type === 'account_statement') {
        updateReportTitle('كشف حساب');
    }
    // يمكن إضافة المزيد من أنواع التقارير هنا
    
    // تحديث نطاق التاريخ
    updateReportDateRange(params.start, params.end);
    
    // عرض التقرير المناسب
    if (params.type === 'account_statement') {
        displayAccountStatement(null, params.start, params.end);
    } else if (params.type === 'customer_debts' || params.type === 'customer_balances') {
        displayAccountStatement(null, params.start, params.end); // استخدام نفس العرض مؤقتًا
    } else if (params.type === 'receipt_vouchers_report' || params.type === 'payment_vouchers_report') {
        displayAccountStatement(null, params.start, params.end); // استخدام نفس العرض مؤقتًا
    } else if (params.type === 'daily_journal' || params.type === 'general_ledger') {
        displayAccountStatement(null, params.start, params.end); // استخدام نفس العرض مؤقتًا
    }
    // يمكن إضافة المزيد من أنواع التقارير هنا
    
    // إضافة مستمعي الأحداث
document.getElementById('account').addEventListener('change', refreshReport);
document.getElementById('customer').addEventListener('change', refreshReport);
document.getElementById('start-date').addEventListener('change', refreshReport);
document.getElementById('end-date').addEventListener('change', refreshReport);
document.getElementById('refresh-btn').addEventListener('click', refreshReport);
document.getElementById('export-btn').addEventListener('click', exportReport);
document.getElementById('print-btn').addEventListener('click', printReport);
    
    // تحديث التقرير عند تغيير الحساب أو العميل أو التاريخ
function refreshReport() {
    const accountId = document.getElementById('account').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    
    // تحديث نطاق التاريخ
    updateReportDateRange(startDate, endDate);
    
    // عرض التقرير المحدث
    const params = getUrlParameters();
    if (params.type === 'account_statement') {
        displayAccountStatement(accountId, startDate, endDate);
    } else if (params.type === 'customer_debts' || params.type === 'customer_balances') {
        displayAccountStatement(accountId, startDate, endDate); // استخدام نفس العرض مؤقتًا
    } else if (params.type === 'receipt_vouchers_report' || params.type === 'payment_vouchers_report') {
        displayAccountStatement(accountId, startDate, endDate); // استخدام نفس العرض مؤقتًا
    } else if (params.type === 'daily_journal' || params.type === 'general_ledger') {
        displayAccountStatement(accountId, startDate, endDate); // استخدام نفس العرض مؤقتًا
    }
}

// تصدير التقرير
function exportReport() {
    const reportTitle = document.getElementById('report-title').textContent;
    const dateRange = document.getElementById('report-date-range').textContent;
    
    // إنشاء كائن لتصدير البيانات
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(reportTitle);
    
    // إضافة عنوان التقرير
    worksheet.addRow([reportTitle]);
    worksheet.addRow([dateRange]);
    worksheet.addRow([]);
    
    // الحصول على عناوين الأعمدة
    const headers = [];
    document.querySelectorAll('#report-table-headers th').forEach(th => {
        headers.push(th.textContent);
    });
    worksheet.addRow(headers);
    
    // إضافة بيانات الجدول
    document.querySelectorAll('#report-table-body tr').forEach(tr => {
        const rowData = [];
        tr.querySelectorAll('td').forEach(td => {
            rowData.push(td.textContent);
        });
        worksheet.addRow(rowData);
    });
    
    // إضافة صف المجموع
    const footerRow = [];
    document.querySelectorAll('#report-footer-row td').forEach(td => {
        footerRow.push(td.textContent);
    });
    worksheet.addRow(footerRow);
    
    // تنسيق الجدول
    worksheet.getColumn(1).width = 15;
    worksheet.getColumn(2).width = 30;
    worksheet.getColumn(3).width = 15;
    worksheet.getColumn(4).width = 15;
    worksheet.getColumn(5).width = 15;
    
    // تصدير الملف
    workbook.xlsx.writeBuffer().then(buffer => {
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${reportTitle}.xlsx`;
        a.click();
        URL.revokeObjectURL(url);
    });
}

// طباعة التقرير
function printReport() {
    window.print();
}

document.getElementById('account').addEventListener('change', function() {
    // إذا تم اختيار حساب، قم بإعادة ضبط اختيار العميل
    if (this.value) {
        document.getElementById('customer').value = '';
    }
});
    
    document.getElementById('customer').addEventListener('change', function() {
    // إذا تم اختيار عميل، قم بإعادة ضبط اختيار الحساب
    if (this.value) {
        document.getElementById('account').value = '';
        }
    });
});