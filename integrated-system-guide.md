# دليل النظام المدمج لدليل الحسابات

## نظرة عامة
تم دمج النظام الاحترافي الجديد لشجرة الحسابات في البرنامج الأصلي، مما يوفر خيارين للمستخدم:
1. **النظام الكلاسيكي** - النظام الأصلي المحسن
2. **النظام الاحترافي** - نظام شجرة الحسابات المتقدم

## كيفية الوصول للنظام الاحترافي

### 📍 **الطريقة الأولى - من تبويب دليل الحسابات:**
1. افتح `accounting.html`
2. انتقل إلى تبويب **"دليل الحسابات"**
3. اضغط على زر **"النظام الاحترافي"** (الزر الأخضر في الأعلى)
4. ستنتقل إلى واجهة النظام الاحترافي

### 📍 **الطريقة الثانية - مباشرة:**
1. افتح `chart-of-accounts-professional.html` مباشرة
2. ستحصل على النظام الاحترافي كاملاً

## المميزات الجديدة في النظام المدمج

### 🔄 **التبديل بين النظامين**
- **زر "النظام الاحترافي"** - للانتقال للنظام المتقدم
- **زر "العودة للنظام الكلاسيكي"** - للعودة للنظام الأصلي
- التبديل فوري بدون إعادة تحميل الصفحة

### 🎯 **النظام الاحترافي المدمج يشمل:**
- **شجرة حسابات تفاعلية** قابلة للطي والتوسيع
- **إحصائيات مباشرة** للحسابات والأرصدة
- **بحث متقدم** في الحسابات
- **إضافة حسابات** رئيسية وفرعية
- **تصدير البيانات** بصيغة JSON
- **زر خاص لإضافة البنك الفرنسي**

## حل مشكلة البنك الفرنسي

### ✅ **في النظام الاحترافي:**
1. انتقل للنظام الاحترافي
2. اضغط على **"إضافة البنك الفرنسي"**
3. سيتم إضافة الحساب تلقائياً تحت "النقدية والبنوك"
4. ستظهر رسالة تأكيد النجاح
5. الحساب سيظهر فوراً في الشجرة

### 🔍 **التحقق من النجاح:**
- ابحث عن "1105" أو "البنك الفرنسي"
- تحقق من الإحصائيات (ستزيد بحساب واحد)
- وسع الشجرة لرؤية البنك الفرنسي تحت النقدية والبنوك

## الأزرار والوظائف المتاحة

### 🔧 **في النظام الكلاسيكي:**
- **حساب جديد** - إضافة حساب بالطريقة التقليدية
- **اختبار شامل** - تشغيل جميع الاختبارات
- **اختبار إضافة** - إضافة حساب اختبار سريع
- **تشخيص سريع** - فحص حالة النظام
- **اختبار البنك الفرنسي** - محاولة إضافة البنك الفرنسي
- **النظام الاحترافي** - الانتقال للنظام المتقدم

### 🌟 **في النظام الاحترافي:**
- **إضافة حساب** - إضافة حساب جديد بالطريقة الاحترافية
- **توسيع الكل** - توسيع جميع الحسابات في الشجرة
- **طي الكل** - طي جميع الحسابات
- **إضافة البنك الفرنسي** - إضافة البنك الفرنسي مباشرة
- **تصدير** - تصدير البيانات
- **العودة للنظام الكلاسيكي** - العودة للنظام الأصلي

## الإحصائيات المتاحة

### 📊 **في النظام الاحترافي:**
- **إجمالي الحسابات** - العدد الكلي للحسابات
- **الحسابات الرئيسية** - عدد الحسابات في المستوى الأول
- **الحسابات الفرعية** - عدد الحسابات الفرعية
- **إجمالي الأرصدة** - مجموع جميع الأرصدة بالريال السعودي

## البحث والتصفية

### 🔍 **البحث في النظام الاحترافي:**
- مربع بحث في أعلى الصفحة
- البحث برقم الحساب أو الاسم
- النتائج تظهر فوراً أثناء الكتابة
- إمكانية البحث في جميع مستويات الشجرة

## إدارة الحسابات

### ➕ **إضافة حساب جديد:**
1. اضغط على "إضافة حساب"
2. املأ البيانات:
   - رقم الحساب (مطلوب)
   - اسم الحساب (مطلوب)
   - نوع الحساب (مطلوب)
   - الحساب الأب (اختياري)
   - الرصيد الافتتاحي
   - الوصف
3. اضغط "حفظ الحساب"

### ✏️ **تعديل حساب:**
1. اضغط على أيقونة التعديل (القلم الأخضر)
2. عدل البيانات المطلوبة
3. احفظ التغييرات

### 👁️ **عرض تفاصيل حساب:**
1. اضغط على أيقونة العرض (العين الزرقاء)
2. ستظهر نافذة بتفاصيل الحساب

### ➕ **إضافة حساب فرعي:**
1. اضغط على أيقونة الإضافة (+ البنفسجية)
2. سيتم تحديد الحساب الأب تلقائياً
3. املأ بيانات الحساب الفرعي

## التخزين والبيانات

### 💾 **نظام التخزين:**
- **النظام الكلاسيكي** يستخدم `chartOfAccounts` و `monjizAccounts`
- **النظام الاحترافي** يستخدم `professionalChartOfAccounts`
- كل نظام يحتفظ ببياناته منفصلة
- حفظ تلقائي عند كل تغيير

### 📤 **تصدير البيانات:**
- تصدير بصيغة JSON
- يشمل جميع الحسابات والبيانات
- اسم الملف يتضمن التاريخ

## نصائح للاستخدام الأمثل

### 🎯 **للمستخدمين الجدد:**
- ابدأ بالنظام الاحترافي للحصول على تجربة أفضل
- استخدم "إضافة البنك الفرنسي" لاختبار النظام
- جرب البحث والتصفية

### 🔧 **للمستخدمين المتقدمين:**
- استخدم النظام الكلاسيكي للوظائف المتقدمة
- جرب أدوات التشخيص والاختبار
- استخدم التصدير لعمل نسخ احتياطية

### 🚀 **لأفضل أداء:**
- استخدم البحث بدلاً من التصفح اليدوي
- وسع الأقسام المطلوبة فقط
- احفظ نسخ احتياطية دورياً

## استكشاف الأخطاء

### ❌ **إذا لم يظهر النظام الاحترافي:**
1. تأكد من تمكين JavaScript
2. أعد تحميل الصفحة
3. تحقق من وحدة التحكم للأخطاء

### ❌ **إذا لم تعمل الأزرار:**
1. تأكد من اكتمال تحميل الصفحة
2. جرب في متصفح مختلف
3. امسح ذاكرة التخزين المؤقت

### ❌ **إذا فقدت البيانات:**
1. تحقق من التخزين المحلي
2. جرب إعادة تحميل الصفحة
3. استخدم النسخة الاحتياطية المصدرة

## الخلاصة

النظام المدمج يوفر:
- ✅ **حل نهائي لمشكلة البنك الفرنسي**
- ✅ **خيارين للاستخدام** (كلاسيكي واحترافي)
- ✅ **واجهة حديثة ومتقدمة**
- ✅ **وظائف شاملة لإدارة الحسابات**
- ✅ **تجربة مستخدم محسنة**

**الآن يمكنك الاستمتاع بنظام دليل حسابات احترافي ومتكامل!** 🎉
