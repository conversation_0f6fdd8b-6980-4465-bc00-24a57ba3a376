<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار كشف حساب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .test-btn {
            background: white;
            color: #667eea;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        /* تنسيق كشف الحساب مطابق للصورة */
        .account-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }
        .account-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .account-header h2 {
            color: #34495e;
            margin: 10px 0 0 0;
            font-size: 22px;
            font-weight: 600;
        }
        .account-info {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }
        .account-info div {
            font-size: 14px;
            color: #2c3e50;
        }
        .account-info strong {
            color: #2c3e50;
            font-weight: 600;
        }
        
        /* تنسيق الجدول مطابق للصورة */
        .statement-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 13px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .statement-table thead {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }
        .statement-table th {
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            border: none;
        }
        .statement-table tbody tr {
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }
        .statement-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        .statement-table tbody tr:hover {
            background: #e3f2fd;
        }
        .statement-table td {
            padding: 12px 10px;
            text-align: center;
            border: none;
            color: #495057;
        }
        .statement-table .date-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
            background: #e8f4fd;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        .statement-table .amount {
            font-weight: 600;
            font-family: 'Courier New', monospace;
        }
        .statement-table .amount.debit {
            color: #e74c3c;
        }
        .statement-table .amount.credit {
            color: #27ae60;
        }
        .statement-table .amount.balance {
            color: #2c3e50;
            font-weight: bold;
        }
        .statement-table .empty-cell {
            color: #999;
            font-style: italic;
        }
        .statement-table tfoot {
            background: #ecf0f1;
            font-weight: bold;
        }
        .statement-table tfoot td {
            padding: 15px 10px;
            border-top: 2px solid #bdc3c7;
            color: #2c3e50;
            font-weight: bold;
        }
        
        @media print {
            .test-controls {
                display: none !important;
            }
            body {
                margin: 0 !important;
                padding: 15mm !important;
                font-size: 11px !important;
                background: white !important;
            }
            .container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .account-header {
                border-bottom: 3px solid #000 !important;
            }
            .account-header h1, .account-header h2 {
                color: #000 !important;
            }
            .account-info {
                background: #f5f5f5 !important;
                border-right: 3px solid #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table {
                box-shadow: none !important;
                border: 2px solid #000 !important;
            }
            .statement-table thead {
                background: #e0e0e0 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table th {
                background: #e0e0e0 !important;
                color: #000 !important;
                border: 1px solid #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table td {
                border: 1px solid #666 !important;
                color: #000 !important;
            }
            .statement-table tbody tr:nth-child(even) {
                background: #f9f9f9 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table .date-cell {
                background: #f0f0f0 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table .amount {
                color: #000 !important;
                font-weight: bold !important;
            }
            .statement-table tfoot {
                background: #e0e0e0 !important;
                -webkit-print-color-adjust: exact;
            }
            .statement-table tfoot td {
                border: 2px solid #000 !important;
                color: #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-controls">
            <h3>🖨️ اختبار كشف حساب</h3>
            <p>جرب الطباعة لترى التنسيق المطابق للبرنامج</p>
            <button class="test-btn" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button class="test-btn" onclick="testPrintWindow()">
                <i class="fas fa-external-link-alt"></i> طباعة في نافذة جديدة
            </button>
        </div>
        
        <div class="account-header">
            <h1>نظام إدارة الأعمال</h1>
            <h2>كشف حساب</h2>
        </div>
        
        <div class="account-info">
            <div>
                <strong>اسم الحساب:</strong> <span id="account-name">النقدية في الصندوق</span><br>
                <strong>رقم الحساب:</strong> <span id="account-code">1101</span>
            </div>
            <div>
                <strong>نوع الحساب:</strong> <span id="account-type">أصول</span><br>
                <strong>الحالة:</strong> <span style="color: #28a745;">نشط</span>
            </div>
            <div>
                <strong>من التاريخ:</strong> 2023-06-01<br>
                <strong>إلى التاريخ:</strong> 2023-06-30
            </div>
            <div>
                <strong>تاريخ الطباعة:</strong> <span id="print-date"></span><br>
                <strong>المستخدم:</strong> مدير النظام
            </div>
        </div>

        <!-- قائمة اختيار الحساب للاختبار -->
        <div style="margin: 20px 0; padding: 15px; background: #e3f2fd; border-radius: 8px;">
            <label for="test-account-selector" style="font-weight: bold; margin-left: 10px;">اختبار حساب آخر:</label>
            <select id="test-account-selector" style="padding: 8px; border-radius: 5px; border: 1px solid #ddd; min-width: 200px;">
                <option value="1101">1101 - النقدية في الصندوق</option>
                <option value="1102">1102 - البنك - الحساب الجاري</option>
                <option value="1103">1103 - العملاء</option>
                <option value="1201">1201 - المخزون</option>
                <option value="2101">2101 - الموردون</option>
                <option value="2102">2102 - مصروفات مستحقة</option>
                <option value="3101">3101 - رأس المال</option>
                <option value="4101">4101 - مبيعات</option>
                <option value="5101">5101 - تكلفة البضاعة المباعة</option>
                <option value="5102">5102 - مصروفات إدارية</option>
            </select>
            <button onclick="changeTestAccount()" style="padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer;">
                تغيير الحساب
            </button>
        </div>
        
        <table class="statement-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>الرصيد</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="date-cell">2023-06-01</span></td>
                    <td>رصيد مدور</td>
                    <td class="empty-cell">-</td>
                    <td class="empty-cell">-</td>
                    <td><span class="amount balance">5,000.00</span></td>
                </tr>
                <tr>
                    <td><span class="date-cell">2023-06-05</span></td>
                    <td>فاتورة رقم 001</td>
                    <td><span class="amount debit">1,500.00</span></td>
                    <td class="empty-cell">-</td>
                    <td><span class="amount balance">6,500.00</span></td>
                </tr>
                <tr>
                    <td><span class="date-cell">2023-06-10</span></td>
                    <td>سداد نقدي</td>
                    <td class="empty-cell">-</td>
                    <td><span class="amount credit">2,000.00</span></td>
                    <td><span class="amount balance">4,500.00</span></td>
                </tr>
                <tr>
                    <td><span class="date-cell">2023-06-15</span></td>
                    <td>فاتورة رقم 002</td>
                    <td><span class="amount debit">800.00</span></td>
                    <td class="empty-cell">-</td>
                    <td><span class="amount balance">5,300.00</span></td>
                </tr>
                <tr>
                    <td><span class="date-cell">2023-06-20</span></td>
                    <td>سداد شيك</td>
                    <td class="empty-cell">-</td>
                    <td><span class="amount credit">1,000.00</span></td>
                    <td><span class="amount balance">4,300.00</span></td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="2" style="font-weight: bold;">الإجمالي</td>
                    <td><span class="amount debit">2,300.00</span></td>
                    <td><span class="amount credit">3,000.00</span></td>
                    <td><span class="amount balance">4,300.00</span></td>
                </tr>
            </tfoot>
        </table>
        
        <div style="margin-top: 40px; text-align: center; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
            <p><i class="fas fa-building"></i> تم إنشاء هذا التقرير بواسطة نظام إدارة الأعمال</p>
            <p><i class="fas fa-calendar"></i> تاريخ الإنشاء: <span id="creation-date"></span></p>
        </div>
    </div>

    <script>
        // بيانات الحسابات
        const accountsData = {
            '1101': {
                name: 'النقدية في الصندوق',
                type: 'أصول',
                transactions: [
                    { date: '2023-06-01', description: 'رصيد مدور', debit: 0, credit: 0, balance: 15000 },
                    { date: '2023-06-05', description: 'مبيعات نقدية', debit: 5000, credit: 0, balance: 20000 },
                    { date: '2023-06-10', description: 'سحب نقدي للبنك', debit: 0, credit: 10000, balance: 10000 },
                    { date: '2023-06-15', description: 'مبيعات نقدية', debit: 3000, credit: 0, balance: 13000 },
                    { date: '2023-06-20', description: 'دفع مصروفات', debit: 0, credit: 2000, balance: 11000 }
                ]
            },
            '1102': {
                name: 'البنك - الحساب الجاري',
                type: 'أصول',
                transactions: [
                    { date: '2023-06-01', description: 'رصيد مدور', debit: 0, credit: 0, balance: 85000 },
                    { date: '2023-06-10', description: 'إيداع من الصندوق', debit: 10000, credit: 0, balance: 95000 },
                    { date: '2023-06-12', description: 'تحصيل من عميل', debit: 15000, credit: 0, balance: 110000 },
                    { date: '2023-06-18', description: 'دفع للموردين', debit: 0, credit: 20000, balance: 90000 },
                    { date: '2023-06-25', description: 'رسوم بنكية', debit: 0, credit: 500, balance: 89500 }
                ]
            },
            '1103': {
                name: 'العملاء',
                type: 'أصول',
                transactions: [
                    { date: '2023-06-01', description: 'رصيد مدور', debit: 0, credit: 0, balance: 45000 },
                    { date: '2023-06-08', description: 'مبيعات آجلة', debit: 25000, credit: 0, balance: 70000 },
                    { date: '2023-06-12', description: 'تحصيل نقدي', debit: 0, credit: 15000, balance: 55000 },
                    { date: '2023-06-20', description: 'مبيعات آجلة', debit: 18000, credit: 0, balance: 73000 },
                    { date: '2023-06-28', description: 'تحصيل شيك', debit: 0, credit: 12000, balance: 61000 }
                ]
            },
            '4101': {
                name: 'مبيعات',
                type: 'إيرادات',
                transactions: [
                    { date: '2023-06-05', description: 'مبيعات نقدية', debit: 0, credit: 5000, balance: 5000 },
                    { date: '2023-06-08', description: 'مبيعات آجلة', debit: 0, credit: 25000, balance: 30000 },
                    { date: '2023-06-15', description: 'مبيعات نقدية', debit: 0, credit: 3000, balance: 33000 },
                    { date: '2023-06-20', description: 'مبيعات آجلة', debit: 0, credit: 18000, balance: 51000 },
                    { date: '2023-06-25', description: 'مبيعات متنوعة', debit: 0, credit: 8000, balance: 59000 }
                ]
            },
            '5101': {
                name: 'تكلفة البضاعة المباعة',
                type: 'مصروفات',
                transactions: [
                    { date: '2023-06-05', description: 'تكلفة مبيعات', debit: 3000, credit: 0, balance: 3000 },
                    { date: '2023-06-08', description: 'تكلفة مبيعات', debit: 15000, credit: 0, balance: 18000 },
                    { date: '2023-06-15', description: 'تكلفة مبيعات', debit: 1800, credit: 0, balance: 19800 },
                    { date: '2023-06-20', description: 'تكلفة مبيعات', debit: 10800, credit: 0, balance: 30600 },
                    { date: '2023-06-25', description: 'تكلفة مبيعات', debit: 4800, credit: 0, balance: 35400 }
                ]
            }
        };

        // تحديث التواريخ
        const now = new Date();
        document.getElementById('print-date').textContent = now.toLocaleDateString('ar-SA') + ' - ' + now.toLocaleTimeString('ar-SA');
        document.getElementById('creation-date').textContent = now.toLocaleDateString('ar-SA');

        // دالة تغيير الحساب
        function changeTestAccount() {
            const selectedAccount = document.getElementById('test-account-selector').value;
            const accountData = accountsData[selectedAccount];

            if (!accountData) return;

            // تحديث معلومات الحساب
            document.getElementById('account-name').textContent = accountData.name;
            document.getElementById('account-code').textContent = selectedAccount;
            document.getElementById('account-type').textContent = accountData.type;

            // تحديث جدول الحركات
            const tbody = document.querySelector('.statement-table tbody');
            tbody.innerHTML = '';

            accountData.transactions.forEach(transaction => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><span class="date-cell">${transaction.date}</span></td>
                    <td>${transaction.description}</td>
                    <td>${transaction.debit > 0 ? `<span class="amount debit">${transaction.debit.toLocaleString()}.00</span>` : '<span class="empty-cell">-</span>'}</td>
                    <td>${transaction.credit > 0 ? `<span class="amount credit">${transaction.credit.toLocaleString()}.00</span>` : '<span class="empty-cell">-</span>'}</td>
                    <td><span class="amount balance">${transaction.balance.toLocaleString()}.00</span></td>
                `;
                tbody.appendChild(row);
            });

            // حساب الإجماليات
            const totalDebit = accountData.transactions.reduce((sum, t) => sum + t.debit, 0);
            const totalCredit = accountData.transactions.reduce((sum, t) => sum + t.credit, 0);
            const finalBalance = accountData.transactions[accountData.transactions.length - 1].balance;

            // تحديث تذييل الجدول
            const tfoot = document.querySelector('.statement-table tfoot');
            tfoot.innerHTML = `
                <tr>
                    <td colspan="2" style="font-weight: bold;">الإجمالي</td>
                    <td><span class="amount debit">${totalDebit.toLocaleString()}.00</span></td>
                    <td><span class="amount credit">${totalCredit.toLocaleString()}.00</span></td>
                    <td><span class="amount balance">${finalBalance.toLocaleString()}.00</span></td>
                </tr>
            `;
        }

        // تحميل الحساب الافتراضي
        changeTestAccount();

        // جعل الدالة متاحة عالمياً
        window.changeTestAccount = changeTestAccount;
        
        // دالة لفتح نافذة طباعة جديدة
        function testPrintWindow() {
            const content = document.querySelector('.container').innerHTML;
            const printWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');
            
            if (!printWindow) {
                alert('تم حظر النوافذ المنبثقة. يرجى السماح بها وإعادة المحاولة.');
                return;
            }
            
            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة - كشف حساب</title>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                    <style>
                        ${document.querySelector('style').innerHTML}
                        .test-controls { display: none !important; }
                        body { background: white; }
                        .container { box-shadow: none; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div style="text-align: center; margin: 20px; padding: 20px; background: #f0f0f0; border-radius: 10px;">
                            <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                                🖨️ طباعة
                            </button>
                            <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                                ❌ إغلاق
                            </button>
                        </div>
                        ${content.replace(/<div class="test-controls">[\s\S]*?<\/div>/, '')}
                    </div>
                </body>
                </html>
            `;
            
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
        }
    </script>
</body>
</html>
