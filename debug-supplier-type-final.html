<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص نهائي لمشكلة نوع المورد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .debug-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #6c5ce7;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #6c5ce7, #a29bfe);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(108,92,231,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108,92,231,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e17055, #fd79a8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(225,112,85,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #6c5ce7;
            border-bottom: 3px solid #6c5ce7;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .debug-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border: 2px solid #6c5ce7;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .form-group select:focus,
        .form-group input:focus {
            border-color: #6c5ce7;
            outline: none;
        }
        .critical-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص نهائي لمشكلة نوع المورد</h1>

        <!-- المشكلة الحالية -->
        <div class="debug-section">
            <h2>🚨 المشكلة الحالية</h2>
            <div class="critical-box">
                <h3>المشكلة:</h3>
                <p><strong>"الآن توجد رسالة يرجى اختيار نوع المورد"</strong></p>
                
                <h4>هذا يعني:</h4>
                <ul>
                    <li>✅ تم حل مشكلة HTML5 validation</li>
                    <li>❌ لكن JavaScript لا يقرأ قيمة نوع المورد بشكل صحيح</li>
                    <li>🔍 نحتاج تشخيص مفصل لمعرفة السبب</li>
                </ul>
            </div>
        </div>

        <!-- نموذج اختبار مباشر -->
        <div class="debug-section">
            <h2>🧪 نموذج اختبار مباشر</h2>
            <div class="test-form">
                <h3>اختبر نوع المورد هنا:</h3>
                <div class="form-group">
                    <label for="test-supplier-type">نوع المورد:</label>
                    <select id="test-supplier-type">
                        <option value="">اختر نوع المورد</option>
                        <option value="individual">فرد</option>
                        <option value="company">شركة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="test-supplier-name">اسم المورد:</label>
                    <input type="text" id="test-supplier-name" placeholder="أدخل اسم المورد">
                </div>
                <div class="form-group">
                    <label for="test-supplier-phone">رقم الهاتف:</label>
                    <input type="tel" id="test-supplier-phone" placeholder="0501234567">
                </div>
                <button class="btn" onclick="testSupplierForm()">🔍 اختبار قراءة البيانات</button>
                <button class="btn" onclick="simulateRealForm()">🎯 محاكاة النموذج الحقيقي</button>
            </div>
            <div id="test-result"></div>
        </div>

        <!-- الاختبار الحقيقي -->
        <div class="debug-section">
            <h2>🎯 الاختبار الحقيقي</h2>
            <button class="btn" onclick="openPurchasesWithFullDebug()">فتح المشتريات مع التشخيص الكامل</button>
            <div class="info">
                <h3>خطوات الاختبار مع التشخيص:</h3>
                <ol>
                    <li><strong>افتح صفحة المشتريات</strong></li>
                    <li><strong>افتح Developer Tools (F12)</strong></li>
                    <li><strong>اذهب لتبويب Console</strong></li>
                    <li><strong>اضغط "مورد جديد"</strong></li>
                    <li><strong>اختر نوع المورد من القائمة</strong></li>
                    <li><strong>راقب Console - يجب أن تظهر:</strong>
                        <ul>
                            <li>"تم حفظ نوع المورد: company" (أو individual)</li>
                        </ul>
                    </li>
                    <li><strong>املأ باقي الحقول</strong></li>
                    <li><strong>اضغط "حفظ المورد"</strong></li>
                    <li><strong>راقب Console للرسائل التشخيصية</strong></li>
                </ol>
            </div>
        </div>

        <!-- سجل التشخيص -->
        <div class="debug-section">
            <h2>📋 سجل التشخيص</h2>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
            <div id="debug-log" class="debug-log">انتظار عمليات التشخيص...</div>
        </div>

        <!-- الحلول المطبقة -->
        <div class="debug-section">
            <h2>🛠️ الحلول المطبقة</h2>
            <div class="info">
                <h3>تم إضافة 3 طبقات حماية:</h3>
                <ol>
                    <li><strong>حفظ القيمة عند التغيير:</strong> event listener يحفظ القيمة في متغير</li>
                    <li><strong>دالة مساعدة:</strong> getSavedSupplierType() للحصول على القيمة المحفوظة</li>
                    <li><strong>قراءة متعددة المصادر:</strong> من value، ثم savedValue، ثم selectedIndex</li>
                    <li><strong>تشخيص مفصل:</strong> console.log لكل خطوة</li>
                </ol>
                
                <h3>الرسائل المتوقعة في Console:</h3>
                <ul>
                    <li>"تم حفظ نوع المورد: company"</li>
                    <li>"البيانات المجمعة: {type: 'company', ...}"</li>
                    <li>"قيمة نوع المورد: "company""</li>
                    <li>"✅ جميع البيانات صحيحة"</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            updateLog();
            console.log(logEntry);
        }

        function updateLog() {
            document.getElementById('debug-log').textContent = debugLog.join('\n');
        }

        function clearLog() {
            debugLog = [];
            updateLog();
        }

        // اختبار نموذج محلي
        function testSupplierForm() {
            log('🧪 بدء اختبار النموذج المحلي');
            
            const typeElement = document.getElementById('test-supplier-type');
            const nameElement = document.getElementById('test-supplier-name');
            const phoneElement = document.getElementById('test-supplier-phone');
            
            log(`نوع المورد: "${typeElement.value}"`);
            log(`selectedIndex: ${typeElement.selectedIndex}`);
            log(`اسم المورد: "${nameElement.value}"`);
            log(`رقم الهاتف: "${phoneElement.value}"`);
            
            if (!typeElement.value) {
                log('❌ نوع المورد فارغ');
                if (typeElement.selectedIndex > 0) {
                    const selectedValue = typeElement.options[typeElement.selectedIndex].value;
                    log(`محاولة قراءة من selectedIndex: "${selectedValue}"`);
                }
                showResult('❌ يرجى اختيار نوع المورد', 'error');
            } else {
                log('✅ نوع المورد صحيح');
                showResult('✅ تم قراءة البيانات بنجاح!', 'success');
            }
        }

        // محاكاة النموذج الحقيقي
        function simulateRealForm() {
            log('🎯 محاكاة النموذج الحقيقي');
            
            const typeElement = document.getElementById('test-supplier-type');
            const nameElement = document.getElementById('test-supplier-name');
            const phoneElement = document.getElementById('test-supplier-phone');
            
            // محاكاة جمع البيانات كما في الكود الحقيقي
            let supplierType = typeElement.value;
            
            log(`القيمة الأولية: "${supplierType}"`);
            
            if (!supplierType && typeElement.selectedIndex > 0) {
                supplierType = typeElement.options[typeElement.selectedIndex].value;
                log(`تم الحصول على القيمة من selectedIndex: "${supplierType}"`);
            }
            
            const supplierData = {
                type: supplierType,
                name: nameElement.value.trim(),
                phone: phoneElement.value.trim()
            };
            
            log('البيانات المجمعة:');
            log(JSON.stringify(supplierData, null, 2));
            
            // محاكاة التحقق
            if (!supplierData.type || supplierData.type === '') {
                log('❌ فشل التحقق: نوع المورد فارغ');
                showResult('❌ يرجى اختيار نوع المورد (محاكاة)', 'error');
            } else {
                log('✅ نجح التحقق');
                showResult('✅ نجحت المحاكاة! البيانات صحيحة', 'success');
            }
        }

        // فتح المشتريات مع التشخيص
        function openPurchasesWithFullDebug() {
            log('🚀 فتح صفحة المشتريات للتشخيص الكامل');
            window.open('purchases.html', '_blank');
            showResult('🚀 تم فتح صفحة المشتريات<br>💡 افتح Developer Tools (F12) وراقب Console<br>🔍 اتبع خطوات الاختبار المذكورة أعلاه', 'info');
        }

        // عرض النتائج
        function showResult(message, type) {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // إضافة event listener للنموذج المحلي
        document.addEventListener('DOMContentLoaded', function() {
            const testTypeSelect = document.getElementById('test-supplier-type');
            testTypeSelect.addEventListener('change', function() {
                log(`تم تغيير نوع المورد المحلي إلى: "${this.value}"`);
            });
            
            log('🔧 بدء تشخيص مشكلة نوع المورد');
            showResult('🔧 جاهز للتشخيص!<br>💡 جرب النموذج أعلاه أو افتح صفحة المشتريات', 'info');
        });
    </script>
</body>
</html>
