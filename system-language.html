<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير لغة النظام في ويندوز</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #9b59b6;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
        }
        h2 {
            color: #8e44ad;
            margin-top: 30px;
            border-right: 4px solid #8e44ad;
            padding-right: 10px;
        }
        .step {
            background-color: #f8f9fa;
            border-right: 4px solid #9b59b6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step h3 {
            color: #9b59b6;
            margin-top: 0;
        }
        .warning {
            background-color: #fff3cd;
            border-right: 4px solid #f1c40f;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .warning h3 {
            color: #e67e22;
            margin-top: 0;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .image-container img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .caption {
            margin-top: 10px;
            font-style: italic;
            color: #7f8c8d;
        }
        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            background-color: #9b59b6;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        .btn:hover {
            background-color: #8e44ad;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
        .keyboard-shortcut {
            display: inline-block;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 2px 5px;
            font-family: monospace;
            margin: 0 3px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .keyboard-shortcut.active {
            background-color: #3498db;
            color: white;
            transform: scale(1.1);
        }
        .toggle-btn {
            background-color: #f1c40f;
            color: #333;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .toggle-btn:hover {
            background-color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تغيير لغة النظام في ويندوز</h1>
        
        <p>إذا كنت تواجه مشاكل متكررة مع ظهور الحرف العربي (ؤ) في سطر الأوامر، فقد يكون من المفيد تغيير لغة النظام الافتراضية أو إضافة لغة إنجليزية إلى نظامك.</p>
        
        <div class="warning">
            <h3>ملاحظة هامة</h3>
            <p>تغيير لغة النظام قد يؤثر على جميع البرامج والتطبيقات في جهازك. تأكد من فهم التغييرات التي ستقوم بها قبل المتابعة.</p>
        </div>
        
        <button id="check-system-language" class="toggle-btn">التحقق من لغة المتصفح الحالية</button>
        <div id="current-system-language" style="display: none; padding: 10px; background-color: #e8f4f8; border-radius: 5px; margin: 10px 0; text-align: center;"></div>
        
        <h2>إضافة لغة جديدة إلى ويندوز</h2>
        
        <div class="step">
            <h3>الخطوة 1: فتح إعدادات اللغة</h3>
            <ol>
                <li>انقر على زر <strong>ابدأ (Start)</strong> في شريط المهام أو اضغط على <span class="keyboard-shortcut">Windows + I</span>.</li>
                <li>اختر <strong>الإعدادات (Settings)</strong>.</li>
                <li>انقر على <strong>الوقت واللغة (Time & Language)</strong>.</li>
                <li>اختر <strong>اللغة (Language)</strong> من القائمة الجانبية.</li>
            </ol>
            <button class="toggle-btn toggle-image-btn" data-target="settings-image">عرض الصورة</button>
        </div>
        
        <div id="settings-image" class="image-container">
            <img src="https://i.imgur.com/JdKfF5t.png" alt="إعدادات ويندوز">
            <div class="caption">إعدادات ويندوز - الوقت واللغة</div>
        </div>
        
        <div class="step">
            <h3>الخطوة 2: إضافة لغة جديدة</h3>
            <ol>
                <li>انقر على <strong>إضافة لغة (Add a language)</strong>.</li>
                <li>ابحث عن <strong>English (United States)</strong> أو أي لغة إنجليزية أخرى تفضلها.</li>
                <li>حدد اللغة ثم انقر على <strong>التالي (Next)</strong>.</li>
                <li>حدد الميزات الإضافية التي ترغب في تثبيتها (اختياري).</li>
                <li>انقر على <strong>تثبيت (Install)</strong>.</li>
            </ol>
            <button class="toggle-btn toggle-image-btn" data-target="add-language-image">عرض الصورة</button>
        </div>
        
        <div id="add-language-image" class="image-container">
            <img src="https://i.imgur.com/ABYZ1AB.png" alt="إضافة لغة جديدة">
            <div class="caption">إضافة لغة جديدة في ويندوز</div>
        </div>
        
        <h2>تعيين اللغة الافتراضية</h2>
        
        <div class="step">
            <h3>الخطوة 1: تغيير اللغة الافتراضية</h3>
            <ol>
                <li>في صفحة إعدادات اللغة، ستظهر قائمة باللغات المثبتة.</li>
                <li>ابحث عن اللغة الإنجليزية التي أضفتها.</li>
                <li>انقر على اللغة ثم اختر <strong>تعيين كافتراضي (Set as default)</strong>.</li>
            </ol>
            <button class="toggle-btn toggle-image-btn" data-target="set-default-image">عرض الصورة</button>
        </div>
        
        <div id="set-default-image" class="image-container">
            <img src="https://i.imgur.com/CDYZ1AB.png" alt="تعيين اللغة الافتراضية">
            <div class="caption">تعيين اللغة الإنجليزية كلغة افتراضية</div>
        </div>
        
        <div class="step">
            <h3>الخطوة 2: إعادة تشغيل الجهاز</h3>
            <p>قد تحتاج إلى إعادة تشغيل جهاز الكمبيوتر لتطبيق التغييرات بشكل كامل.</p>
            <button class="toggle-btn info-btn" data-target="restart-info">عرض معلومات إضافية</button>
            <div id="restart-info" style="display: none; padding: 10px; background-color: #e8f4f8; border-radius: 5px; margin: 10px 0;">
                <p>في بعض الحالات، قد لا تحتاج إلى إعادة تشغيل الجهاز بالكامل. يمكنك تسجيل الخروج من حسابك وتسجيل الدخول مرة أخرى لتطبيق بعض التغييرات.</p>
                <p>للقيام بذلك:</p>
                <ol>
                    <li>اضغط على <span class="keyboard-shortcut">Ctrl + Alt + Delete</span></li>
                    <li>اختر "تسجيل الخروج" (Sign out)</li>
                    <li>سجل الدخول مرة أخرى</li>
                </ol>
            </div>
        </div>
        
        <h2>تغيير ترتيب اللغات</h2>
        
        <div class="step">
            <h3>ترتيب اللغات المفضلة</h3>
            <p>يمكنك تغيير ترتيب اللغات لتحديد أي لغة يجب استخدامها أولاً:</p>
            <ol>
                <li>في صفحة إعدادات اللغة، انقر على اللغة التي تريد تحريكها.</li>
                <li>استخدم أزرار <strong>نقل لأعلى (Move up)</strong> أو <strong>نقل لأسفل (Move down)</strong> لتغيير ترتيب اللغة.</li>
                <li>ضع اللغة الإنجليزية في أعلى القائمة إذا كنت تريد استخدامها كلغة افتراضية.</li>
            </ol>
        </div>
        
        <div class="buttons">
            <a href="launcher.html" class="btn btn-green">العودة إلى مشغل النظام</a>
            <a href="fix-arabic-char.html" class="btn">العودة إلى صفحة حل المشكلة</a>
        </div>
    </div>
    <script src="js/system-language.js"></script>
</body>
</html>