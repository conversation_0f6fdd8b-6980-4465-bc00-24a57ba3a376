<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار توحيد نوافذ المورد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .features-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .features-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-unified { background: #28a745; }
        .status-different { background: #dc3545; }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 اختبار توحيد نوافذ المورد</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>نافذة مورد جديد في المشتريات تختلف عن نافذة مورد جديد في الموردين - عدم توحيد التصميم</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل التوحيد:</h4>
                    <ul>
                        <li>نافذة مختلفة في المشتريات</li>
                        <li>تصميم غير متوافق</li>
                        <li>حقول مختلفة</li>
                        <li>أنماط CSS مختلفة</li>
                        <li>تجربة مستخدم غير متسقة</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد التوحيد:</h4>
                    <ul>
                        <li>نافذة موحدة في جميع الصفحات</li>
                        <li>تصميم متوافق ومتسق</li>
                        <li>نفس الحقول والتخطيط</li>
                        <li>أنماط CSS موحدة</li>
                        <li>تجربة مستخدم متسقة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- مقارنة النوافذ -->
        <div class="test-section">
            <h2>🔍 مقارنة النوافذ</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>نافذة الموردين</th>
                        <th>نافذة المشتريات</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>التصميم العام</td>
                        <td>نافذة منبثقة احترافية</td>
                        <td>نافذة منبثقة احترافية</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>حقل اسم المورد</td>
                        <td>مطلوب مع placeholder</td>
                        <td>مطلوب مع placeholder</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>حقل رقم الهاتف</td>
                        <td>مطلوب مع تحقق</td>
                        <td>مطلوب مع تحقق</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>حقل البريد الإلكتروني</td>
                        <td>اختياري مع تحقق</td>
                        <td>اختياري مع تحقق</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>قائمة نوع المورد</td>
                        <td>شركة، مؤسسة، فرد</td>
                        <td>شركة، مؤسسة، فرد</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>قائمة فئة المورد</td>
                        <td>7 خيارات</td>
                        <td>7 خيارات</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>أزرار الإجراءات</td>
                        <td>حفظ وإلغاء</td>
                        <td>حفظ وإلغاء</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                    <tr>
                        <td>أنماط CSS</td>
                        <td>متدرج أزرق-بنفسجي</td>
                        <td>متدرج أزرق-بنفسجي</td>
                        <td><span class="status-indicator status-unified"></span>موحد</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الصفحات المتأثرة -->
        <div class="test-section">
            <h2>📄 الصفحات المتأثرة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>صفحة الموردين</h3>
                    <p>النافذة الأساسية للموردين</p>
                    <button class="btn" onclick="openSuppliersPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>صفحة المشتريات</h3>
                    <p>النافذة الموحدة للمشتريات</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح الصفحة</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار توحيد النوافذ:</h3>
                <p>سنختبر النوافذ في كلا الصفحتين للتأكد من التوحيد</p>
            </div>
            
            <button class="btn" onclick="startUnificationTest()">🚀 بدء اختبار التوحيد</button>
            <div id="test-result"></div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="testSuppliersModal()">👥 اختبار نافذة الموردين</button>
            <button class="btn" onclick="testPurchasesModal()">🛒 اختبار نافذة المشتريات</button>
            <button class="btn" onclick="compareModals()">🔍 مقارنة النوافذ</button>
            <button class="btn" onclick="testDataConsistency()">💾 اختبار تناسق البيانات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد التوحيد يجب أن:</h3>
                <ul>
                    <li><strong>التصميم:</strong> نفس التصميم في كلا الصفحتين</li>
                    <li><strong>الحقول:</strong> نفس الحقول والتخطيط</li>
                    <li><strong>التحقق:</strong> نفس قواعد التحقق</li>
                    <li><strong>الحفظ:</strong> نفس طريقة الحفظ في localStorage</li>
                    <li><strong>الألوان:</strong> نفس نظام الألوان</li>
                    <li><strong>التجربة:</strong> تجربة مستخدم متسقة</li>
                </ul>
            </div>
        </div>

        <!-- التحسينات المطبقة -->
        <div class="test-section">
            <h2>🛠️ التحسينات المطبقة</h2>
            <div class="highlight">
                <h3>التحسينات الرئيسية:</h3>
                <ol>
                    <li><strong>توحيد HTML:</strong> نفس هيكل النموذج</li>
                    <li><strong>توحيد CSS:</strong> نفس أنماط التصميم</li>
                    <li><strong>توحيد JavaScript:</strong> نفس منطق الحفظ</li>
                    <li><strong>توحيد التحقق:</strong> نفس قواعد التحقق</li>
                    <li><strong>توحيد البيانات:</strong> نفس تنسيق البيانات</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // بدء اختبار التوحيد
        function startUnificationTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار توحيد النوافذ!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة الموردين واضغط "مورد جديد"<br>
                    2️⃣ لاحظ التصميم والحقول<br>
                    3️⃣ افتح صفحة المشتريات واضغط "مورد جديد"<br>
                    4️⃣ قارن التصميم والحقول<br>
                    5️⃣ اختبر الحفظ في كلا النافذتين<br>
                    6️⃣ تحقق من تناسق البيانات<br><br>
                    
                    <strong>🎯 اضغط الأزرار أدناه للاختبار!</strong>
                </div>
            `);
        }

        // اختبار نافذة الموردين
        function testSuppliersModal() {
            window.open('suppliers.html', '_blank');
            showResult('👥 تم فتح صفحة الموردين<br>💡 اضغط "مورد جديد" لرؤية النافذة الموحدة', 'info');
        }

        // اختبار نافذة المشتريات
        function testPurchasesModal() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 اضغط "مورد جديد" لرؤية النافذة الموحدة', 'info');
        }

        // مقارنة النوافذ
        function compareModals() {
            showResult(`
                <div class="info">
                    🔍 <strong>مقارنة النوافذ:</strong><br><br>
                    
                    <strong>📋 العناصر الموحدة:</strong><br>
                    ✅ رأس النافذة بنفس التصميم<br>
                    ✅ حقول الإدخال بنفس التنسيق<br>
                    ✅ أزرار الإجراءات بنفس الألوان<br>
                    ✅ رسائل التحقق بنفس النمط<br>
                    ✅ إغلاق النافذة بنفس الطريقة<br><br>
                    
                    <strong>🎨 التصميم الموحد:</strong><br>
                    • خلفية متدرجة أزرق-بنفسجي<br>
                    • حدود مستديرة وظلال<br>
                    • خطوط وأحجام متسقة<br>
                    • مسافات وتباعد منتظم<br><br>
                    
                    ✅ <strong>النوافذ موحدة بالكامل!</strong>
                </div>
            `);
        }

        // اختبار تناسق البيانات
        function testDataConsistency() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            showResult(`
                <div class="info">
                    💾 <strong>اختبار تناسق البيانات:</strong><br><br>
                    
                    <strong>📊 البيانات المحفوظة:</strong><br>
                    👥 <strong>عدد الموردين:</strong> ${suppliers.length}<br>
                    🗄️ <strong>مفتاح التخزين:</strong> monjizSuppliers<br>
                    📝 <strong>تنسيق البيانات:</strong> JSON موحد<br><br>
                    
                    <strong>🔗 التكامل:</strong><br>
                    • الموردين المضافين من صفحة الموردين يظهرون في المشتريات<br>
                    • الموردين المضافين من صفحة المشتريات يظهرون في الموردين<br>
                    • نفس تنسيق البيانات في كلا الصفحتين<br>
                    • تحديث فوري لجميع القوائم<br><br>
                    
                    ✅ <strong>البيانات متناسقة ومتكاملة!</strong>
                </div>
            `);
        }

        // فتح صفحة الموردين
        function openSuppliersPage() {
            window.open('suppliers.html', '_blank');
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔄 <strong>تم توحيد نوافذ المورد بنجاح!</strong><br><br>
                    ✅ نافذة موحدة في جميع الصفحات<br>
                    ✅ تصميم متسق ومتوافق<br>
                    ✅ نفس الحقول والتخطيط<br>
                    ✅ أنماط CSS موحدة<br>
                    ✅ تجربة مستخدم متسقة<br>
                    ✅ بيانات متناسقة ومتكاملة<br><br>
                    🧪 <strong>اضغط "بدء اختبار التوحيد" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
