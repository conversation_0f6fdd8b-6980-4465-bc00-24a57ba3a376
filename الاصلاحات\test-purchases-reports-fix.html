<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تقارير المشتريات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        .error {
            background: linear-gradient(45deg, #d63031, #e17055);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(214,48,49,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .fixes-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .fixes-table th,
        .fixes-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .fixes-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .fixes-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #ffc107; }
        .status-error { background: #dc3545; }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح تقارير المشتريات</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>تقرير المشتريات اليومية كان يظهر بيانات المبيعات بدلاً من المشتريات</strong></p>
                <p>السبب: تداخل في أسماء التقارير - كلاً من المبيعات والمشتريات يستخدم "daily" كمعرف</p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>تقرير المشتريات اليومية: <code>value: 'daily'</code></li>
                        <li>تقرير المبيعات اليومية: <code>value: 'daily'</code></li>
                        <li><strong>تداخل!</strong> النظام يستدعي دالة المبيعات</li>
                        <li>النتيجة: بيانات مبيعات في تقرير المشتريات</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>تقرير المشتريات اليومية: <code>value: 'purchases-daily'</code></li>
                        <li>تقرير المبيعات اليومية: <code>value: 'daily'</code></li>
                        <li><strong>لا تداخل!</strong> كل تقرير له معرف فريد</li>
                        <li>النتيجة: بيانات مشتريات صحيحة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            <table class="fixes-table">
                <thead>
                    <tr>
                        <th>التقرير</th>
                        <th>المعرف القديم</th>
                        <th>المعرف الجديد</th>
                        <th>الدالة المستدعاة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>المشتريات اليومية</td>
                        <td>daily</td>
                        <td>purchases-daily</td>
                        <td>createPurchasesDailyReport</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                    <tr>
                        <td>المشتريات الشهرية</td>
                        <td>monthly</td>
                        <td>purchases-monthly</td>
                        <td>createPurchasesMonthlyReport</td>
                        <td><span class="status-indicator status-fixed"></span>جديد</td>
                    </tr>
                    <tr>
                        <td>المشتريات حسب الفئة</td>
                        <td>by-category</td>
                        <td>purchases-by-category</td>
                        <td>createPurchasesByCategoryReport</td>
                        <td><span class="status-indicator status-fixed"></span>جديد</td>
                    </tr>
                    <tr>
                        <td>المشتريات حسب المورد</td>
                        <td>by-supplier</td>
                        <td>by-supplier</td>
                        <td>createPurchasesBySupplierReport</td>
                        <td><span class="status-indicator status-fixed"></span>موجود</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار تقارير المشتريات المصلحة:</h3>
                <p>سنختبر جميع تقارير المشتريات للتأكد من عرض البيانات الصحيحة</p>
            </div>
            
            <button class="btn" onclick="startPurchasesReportsTest()">🚀 بدء اختبار تقارير المشتريات</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار:</h3>
                <ol>
                    <li><strong>اختبار المشتريات اليومية:</strong>
                        <ul>
                            <li>فتح صفحة التقارير</li>
                            <li>اختيار "تقارير المشتريات"</li>
                            <li>فتح "المشتريات اليومية"</li>
                            <li>التحقق من عرض بيانات المشتريات وليس المبيعات</li>
                        </ul>
                    </li>
                    <li><strong>اختبار المشتريات الشهرية:</strong>
                        <ul>
                            <li>فتح "المشتريات الشهرية"</li>
                            <li>التحقق من تجميع البيانات حسب الشهر</li>
                        </ul>
                    </li>
                    <li><strong>اختبار المشتريات حسب المورد:</strong>
                        <ul>
                            <li>فتح "المشتريات حسب المورد"</li>
                            <li>التحقق من تجميع البيانات حسب المورد</li>
                        </ul>
                    </li>
                    <li><strong>اختبار المشتريات حسب الفئة:</strong>
                        <ul>
                            <li>فتح "المشتريات حسب الفئة"</li>
                            <li>التحقق من تجميع البيانات حسب فئة المورد</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openReportsPage()">📊 فتح صفحة التقارير</button>
            <button class="btn" onclick="checkPurchasesData()">💾 فحص بيانات المشتريات</button>
            <button class="btn" onclick="addTestPurchase()">🛒 إضافة فاتورة شراء تجريبية</button>
            <button class="btn" onclick="compareReports()">🔍 مقارنة تقارير المبيعات والمشتريات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>تقرير المشتريات اليومية:</strong> يعرض فواتير الشراء مع أرقام PUR-001, PUR-002...</li>
                    <li><strong>أسماء الموردين:</strong> تظهر أسماء الموردين وليس العملاء</li>
                    <li><strong>البيانات الصحيحة:</strong> مبالغ وتواريخ فواتير الشراء</li>
                    <li><strong>لا تداخل:</strong> تقارير المشتريات منفصلة عن تقارير المبيعات</li>
                    <li><strong>تقارير إضافية:</strong> المشتريات الشهرية وحسب الفئة تعمل بشكل صحيح</li>
                </ul>
            </div>
        </div>

        <!-- معلومات تقنية -->
        <div class="test-section">
            <h2>🔧 معلومات تقنية</h2>
            <div class="highlight">
                <h3>التغييرات المطبقة في الكود:</h3>
                <ol>
                    <li><strong>تحديث معرفات التقارير:</strong>
                        <ul>
                            <li><code>'daily' → 'purchases-daily'</code></li>
                            <li><code>'monthly' → 'purchases-monthly'</code></li>
                            <li><code>'by-category' → 'purchases-by-category'</code></li>
                        </ul>
                    </li>
                    <li><strong>إضافة دوال جديدة:</strong>
                        <ul>
                            <li><code>createPurchasesMonthlyReport()</code></li>
                            <li><code>createPurchasesByCategoryReport()</code></li>
                        </ul>
                    </li>
                    <li><strong>تحسين قراءة البيانات:</strong>
                        <ul>
                            <li>قراءة من <code>localStorage.getItem('monjizPurchases')</code></li>
                            <li>عرض بيانات المشتريات الحقيقية</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // بدء اختبار تقارير المشتريات
        function startPurchasesReportsTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار تقارير المشتريات المصلحة!</strong><br><br>
                    
                    <strong>المرحلة 1: اختبار المشتريات اليومية</strong><br>
                    1️⃣ افتح صفحة التقارير<br>
                    2️⃣ اختر "تقارير المشتريات"<br>
                    3️⃣ افتح "المشتريات اليومية"<br>
                    4️⃣ تحقق من عرض بيانات المشتريات (PUR-001, PUR-002...)<br><br>
                    
                    <strong>المرحلة 2: اختبار التقارير الأخرى</strong><br>
                    5️⃣ اختبر "المشتريات الشهرية"<br>
                    6️⃣ اختبر "المشتريات حسب المورد"<br>
                    7️⃣ اختبر "المشتريات حسب الفئة"<br><br>
                    
                    <strong>🎯 اضغط الأزرار أدناه لبدء الاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة التقارير
        function openReportsPage() {
            window.open('reports.html', '_blank');
            showResult('📊 تم فتح صفحة التقارير<br>💡 اختر "تقارير المشتريات" واختبر التقارير المصلحة', 'info');
        }

        // فحص بيانات المشتريات
        function checkPurchasesData() {
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            const sales = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            showResult(`
                <div class="info">
                    💾 <strong>البيانات المحفوظة في النظام:</strong><br><br>
                    🛒 <strong>فواتير الشراء:</strong> ${purchases.length} فاتورة<br>
                    💰 <strong>فواتير المبيعات:</strong> ${sales.length} فاتورة<br><br>
                    ${purchases.length > 0 ? `<strong>آخر فاتورة شراء:</strong> ${purchases[purchases.length - 1].id}<br>` : ''}
                    ${sales.length > 0 ? `<strong>آخر فاتورة مبيعات:</strong> ${sales[sales.length - 1].id}<br>` : ''}
                    <br><strong>💡 الآن تقارير المشتريات ستعرض بيانات الشراء فقط!</strong>
                </div>
            `);
        }

        // إضافة فاتورة شراء تجريبية
        function addTestPurchase() {
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            
            const testPurchase = {
                id: 'PUR-' + (purchases.length + 1).toString().padStart(3, '0'),
                date: new Date().toLocaleDateString('en-GB'),
                supplier: 'مورد تجريبي - ' + new Date().toLocaleTimeString(),
                items: [
                    { product: 'منتج تجريبي 1', quantity: 10, price: 50, total: 500 },
                    { product: 'منتج تجريبي 2', quantity: 5, price: 100, total: 500 }
                ],
                subtotal: 1000,
                tax: 150,
                total: 1150,
                payment: 'نقداً',
                createdAt: new Date().toISOString()
            };

            purchases.push(testPurchase);
            localStorage.setItem('monjizPurchases', JSON.stringify(purchases));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة فاتورة شراء تجريبية!</strong><br><br>
                    🧾 <strong>رقم الفاتورة:</strong> ${testPurchase.id}<br>
                    🏢 <strong>المورد:</strong> ${testPurchase.supplier}<br>
                    💰 <strong>المبلغ:</strong> ${testPurchase.total} ر.س<br>
                    📦 <strong>عدد الأصناف:</strong> ${testPurchase.items.length}<br><br>
                    💡 <strong>افتح تقارير المشتريات للتحقق من ظهورها!</strong>
                </div>
            `);
        }

        // مقارنة تقارير المبيعات والمشتريات
        function compareReports() {
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            const sales = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            showResult(`
                <div class="info">
                    🔍 <strong>مقارنة التقارير:</strong><br><br>
                    
                    <strong>📊 تقارير المبيعات يجب أن تعرض:</strong><br>
                    • فواتير بأرقام: INV-001, INV-002...<br>
                    • أسماء العملاء<br>
                    • بيانات المبيعات<br>
                    • عدد الفواتير: ${sales.length}<br><br>
                    
                    <strong>🛒 تقارير المشتريات يجب أن تعرض:</strong><br>
                    • فواتير بأرقام: PUR-001, PUR-002...<br>
                    • أسماء الموردين<br>
                    • بيانات المشتريات<br>
                    • عدد الفواتير: ${purchases.length}<br><br>
                    
                    <strong>✅ لا يجب أن يكون هناك تداخل بين التقريرين!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح مشكلة تقارير المشتريات!</strong><br><br>
                    ✅ تقرير المشتريات اليومية يعرض بيانات المشتريات<br>
                    ✅ لا تداخل مع تقارير المبيعات<br>
                    ✅ معرفات فريدة لكل تقرير<br>
                    ✅ دوال جديدة للمشتريات الشهرية وحسب الفئة<br><br>
                    🧪 <strong>اضغط "بدء اختبار تقارير المشتريات" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
