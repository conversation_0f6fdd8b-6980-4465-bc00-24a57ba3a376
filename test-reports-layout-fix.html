<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسين تنسيق التقارير - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .improvements-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .improvements-list ul {
            margin: 0;
            padding-right: 20px;
        }
        .improvements-list li {
            margin: 8px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .demo-layout {
            background: white;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 0;
            margin: 15px 0;
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            margin: 0;
        }
        .demo-header h3 {
            margin: 0;
            font-size: 18px;
        }
        .demo-header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .demo-summary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
        }
        .demo-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .demo-card h4 {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        .demo-card .value {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .demo-table th {
            background: #667eea;
            color: white;
            padding: 12px 8px;
            text-align: right;
            font-size: 14px;
        }
        .demo-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 اختبار تحسين تنسيق التقارير</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>تقرير المشتريات اليومية يأخذ مساحة كبيرة جداً والنافذة تحتاج ترتيب أفضل</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل التحسين:</h4>
                    <ul>
                        <li>مساحة كبيرة جداً للإحصائيات</li>
                        <li>تنسيق غير مرتب</li>
                        <li>نافذة تحتاج تمرير كثير</li>
                        <li>ألوان غير متناسقة</li>
                        <li>جدول غير منظم</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد التحسين:</h4>
                    <ul>
                        <li>مساحة مضغوطة ومنظمة</li>
                        <li>تنسيق احترافي ومرتب</li>
                        <li>نافذة مناسبة بدون تمرير مفرط</li>
                        <li>ألوان متناسقة وجذابة</li>
                        <li>جدول منظم مع عرض محدد للأعمدة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- التحسينات المطبقة -->
        <div class="test-section">
            <h2>🛠️ التحسينات المطبقة</h2>
            <div class="improvements-list">
                <h3>تحسينات التنسيق:</h3>
                <ul>
                    <li><strong>رأس التقرير:</strong> خلفية متدرجة مع تنسيق مضغوط</li>
                    <li><strong>بطاقات الإحصائيات:</strong> حجم أصغر مع تنسيق أنيق</li>
                    <li><strong>الجدول:</strong> عرض محدد للأعمدة وتنسيق محسن</li>
                    <li><strong>النافذة:</strong> حجم مناسب مع تمرير محدود</li>
                    <li><strong>الألوان:</strong> نظام ألوان متناسق</li>
                    <li><strong>الاستجابة:</strong> تنسيق متجاوب للشاشات الصغيرة</li>
                </ul>
            </div>
        </div>

        <!-- عرض توضيحي للتنسيق الجديد -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للتنسيق الجديد</h2>
            <div class="demo-layout">
                <div class="demo-header">
                    <h3>المشتريات اليومية</h3>
                    <p>الفترة: 2025/01/14 حتى في 2025/01/14</p>
                </div>
                
                <div class="demo-summary">
                    <div class="demo-card">
                        <h4>الفواتير</h4>
                        <p class="value">3</p>
                    </div>
                    <div class="demo-card">
                        <h4>المبلغ الإجمالي</h4>
                        <p class="value">14892.50 ر.س</p>
                    </div>
                    <div class="demo-card">
                        <h4>متوسط الفاتورة</h4>
                        <p class="value">4964.17 ر.س</p>
                    </div>
                </div>

                <table class="demo-table">
                    <thead>
                        <tr>
                            <th style="width: 15%;">رقم الفاتورة</th>
                            <th style="width: 12%;">التاريخ</th>
                            <th style="width: 25%;">المورد</th>
                            <th style="width: 10%;">الأصناف</th>
                            <th style="width: 18%;">المبلغ</th>
                            <th style="width: 20%;">طريقة الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>PUR-001</td>
                            <td>14/01/2025</td>
                            <td>شركة الأغذية المتحدة</td>
                            <td>5</td>
                            <td>5,250.00 ر.س</td>
                            <td>نقداً</td>
                        </tr>
                        <tr>
                            <td>PUR-002</td>
                            <td>14/01/2025</td>
                            <td>مؤسسة الخضار الطازجة</td>
                            <td>3</td>
                            <td>3,890.50 ر.س</td>
                            <td>تحويل بنكي</td>
                        </tr>
                        <tr>
                            <td>PUR-003</td>
                            <td>14/01/2025</td>
                            <td>شركة المواد الغذائية</td>
                            <td>7</td>
                            <td>5,752.00 ر.س</td>
                            <td>آجل</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- الاختبار -->
        <div class="test-section">
            <h2>🧪 اختبار التحسينات</h2>
            <div class="highlight">
                <h3>🎯 اختبار التنسيق المحسن:</h3>
                <p>سنختبر التقرير المحسن للتأكد من التنسيق الجديد</p>
            </div>
            
            <button class="btn" onclick="startLayoutTest()">🚀 بدء اختبار التنسيق</button>
            <div id="test-result"></div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openReportsPage()">📊 فتح صفحة التقارير</button>
            <button class="btn" onclick="addTestData()">📝 إضافة بيانات تجريبية</button>
            <button class="btn" onclick="testResponsive()">📱 اختبار التجاوب</button>
            <button class="btn" onclick="compareLayouts()">🔍 مقارنة التنسيقات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد التحسين يجب أن:</h3>
                <ul>
                    <li><strong>المساحة:</strong> مضغوطة ومنظمة بدون إفراط</li>
                    <li><strong>النافذة:</strong> حجم مناسب مع تمرير محدود</li>
                    <li><strong>الإحصائيات:</strong> بطاقات صغيرة وأنيقة</li>
                    <li><strong>الجدول:</strong> أعمدة منظمة مع عرض محدد</li>
                    <li><strong>الألوان:</strong> نظام متناسق وجذاب</li>
                    <li><strong>التجاوب:</strong> يعمل على جميع أحجام الشاشات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // بدء اختبار التنسيق
        function startLayoutTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار التنسيق المحسن!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة التقارير<br>
                    2️⃣ اختر "تقارير المشتريات"<br>
                    3️⃣ افتح "المشتريات اليومية"<br>
                    4️⃣ لاحظ التنسيق الجديد المضغوط<br>
                    5️⃣ تحقق من سهولة القراءة<br><br>
                    
                    <strong>🎯 اضغط "فتح صفحة التقارير" للاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة التقارير
        function openReportsPage() {
            window.open('reports.html', '_blank');
            showResult('📊 تم فتح صفحة التقارير<br>💡 اختر "تقارير المشتريات" ← "المشتريات اليومية" لرؤية التنسيق الجديد', 'info');
        }

        // إضافة بيانات تجريبية
        function addTestData() {
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            
            const testPurchases = [
                {
                    id: 'PUR-001',
                    date: '14/01/2025',
                    supplier: 'شركة الأغذية المتحدة',
                    items: [
                        { product: 'أرز بسمتي', quantity: 50, price: 45, total: 2250 },
                        { product: 'زيت زيتون', quantity: 20, price: 85, total: 1700 },
                        { product: 'سكر أبيض', quantity: 30, price: 25, total: 750 }
                    ],
                    subtotal: 4700,
                    tax: 705,
                    total: 5405,
                    payment: 'نقداً',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'PUR-002',
                    date: '14/01/2025',
                    supplier: 'مؤسسة الخضار الطازجة',
                    items: [
                        { product: 'طماطم', quantity: 100, price: 15, total: 1500 },
                        { product: 'خيار', quantity: 80, price: 12, total: 960 },
                        { product: 'جزر', quantity: 60, price: 18, total: 1080 }
                    ],
                    subtotal: 3540,
                    tax: 531,
                    total: 4071,
                    payment: 'تحويل بنكي',
                    createdAt: new Date().toISOString()
                }
            ];

            // إضافة البيانات إذا لم تكن موجودة
            testPurchases.forEach(testPurchase => {
                if (!purchases.find(p => p.id === testPurchase.id)) {
                    purchases.push(testPurchase);
                }
            });

            localStorage.setItem('monjizPurchases', JSON.stringify(purchases));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة بيانات تجريبية!</strong><br><br>
                    📊 <strong>تم إضافة:</strong> ${testPurchases.length} فاتورة شراء<br>
                    💰 <strong>إجمالي المبلغ:</strong> ${testPurchases.reduce((sum, p) => sum + p.total, 0).toFixed(2)} ر.س<br><br>
                    💡 <strong>افتح تقرير المشتريات اليومية لرؤية البيانات!</strong>
                </div>
            `);
        }

        // اختبار التجاوب
        function testResponsive() {
            showResult(`
                <div class="info">
                    📱 <strong>اختبار التجاوب:</strong><br><br>
                    
                    <strong>تم تحسين التقرير ليعمل على:</strong><br>
                    🖥️ <strong>الشاشات الكبيرة:</strong> تنسيق كامل مع 3 بطاقات<br>
                    💻 <strong>الشاشات المتوسطة:</strong> تنسيق متكيف<br>
                    📱 <strong>الشاشات الصغيرة:</strong> بطاقات أصغر وجدول مضغوط<br><br>
                    
                    💡 <strong>جرب تصغير وتكبير نافذة المتصفح لرؤية التجاوب!</strong>
                </div>
            `);
        }

        // مقارنة التنسيقات
        function compareLayouts() {
            showResult(`
                <div class="info">
                    🔍 <strong>مقارنة التنسيقات:</strong><br><br>
                    
                    <strong>📏 المساحة:</strong><br>
                    • قبل: مساحة كبيرة جداً<br>
                    • بعد: مساحة مضغوطة ومنظمة<br><br>
                    
                    <strong>🎨 التنسيق:</strong><br>
                    • قبل: ألوان غير متناسقة<br>
                    • بعد: نظام ألوان موحد<br><br>
                    
                    <strong>📊 الجدول:</strong><br>
                    • قبل: أعمدة غير منظمة<br>
                    • بعد: عرض محدد لكل عمود<br><br>
                    
                    <strong>✅ النتيجة: تحسن كبير في التنسيق والمساحة!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🎨 <strong>تم تحسين تنسيق التقارير!</strong><br><br>
                    ✅ مساحة مضغوطة ومنظمة<br>
                    ✅ نافذة بحجم مناسب<br>
                    ✅ بطاقات إحصائيات أنيقة<br>
                    ✅ جدول منظم مع عرض محدد<br>
                    ✅ ألوان متناسقة وجذابة<br>
                    ✅ تجاوب مع جميع الشاشات<br><br>
                    🧪 <strong>اضغط "بدء اختبار التنسيق" للتحقق من التحسينات!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
