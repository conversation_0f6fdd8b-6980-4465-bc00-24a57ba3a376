@echo off
rem هذا الملف يساعد على تشغيل ملف VBS بدون مشاكل الحرف العربي

setlocal

rem تحديد المسار الكامل للملف
set "VBS_FILE=%~dp0open_launcher.vbs"

rem التأكد من وجود الملف
if not exist "%VBS_FILE%" (
    echo File not found: %VBS_FILE%
    pause
    exit /b 1
)

rem محاولة تشغيل الملف باستخدام cscript
echo Attempting to run VBS file using cscript...
cscript //nologo "%VBS_FILE%"

rem التحقق من نجاح التشغيل
if %ERRORLEVEL% neq 0 (
    echo Failed to run with cscript. Trying wscript...
    wscript "%VBS_FILE%"
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to run with wscript. Trying direct launcher.html...
        start "" "%~dp0launcher.html"
    )
)

endlocal
exit /b 0