<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المشتريات الشامل - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .feature-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .feature-card ul {
            margin: 0;
            padding-right: 20px;
        }
        .feature-card li {
            margin: 8px 0;
            color: #555;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 اختبار نظام المشتريات الشامل</h1>

        <!-- الميزات الجديدة -->
        <div class="test-section">
            <h2>✨ الميزات الجديدة المضافة</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>📋 فاتورة شراء شاملة</h3>
                    <ul>
                        <li>رقم فاتورة تسلسلي (PUR-001, PUR-002...)</li>
                        <li>اختيار المورد من القائمة</li>
                        <li>إضافة أصناف متعددة</li>
                        <li>حساب تلقائي للمجاميع والضريبة</li>
                        <li>ملاحظات إضافية</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>📦 تحديث المخزون</h3>
                    <ul>
                        <li>إضافة الكميات المشتراة للمخزون</li>
                        <li>تحديث أسعار الشراء</li>
                        <li>إضافة منتجات جديدة تلقائياً</li>
                        <li>حساب سعر البيع (سعر الشراء + 20%)</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>📊 التقارير والإحصائيات</h3>
                    <ul>
                        <li>تقارير المشتريات</li>
                        <li>تقارير الموردين</li>
                        <li>تقارير المخزون</li>
                        <li>إحصائيات شاملة</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>💾 الحفظ والاستمرارية</h3>
                    <ul>
                        <li>حفظ في localStorage</li>
                        <li>تحميل تلقائي عند بدء الصفحة</li>
                        <li>عدم فقدان البيانات</li>
                        <li>ربط مع جميع أجزاء النظام</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- مقارنة قبل وبعد -->
        <div class="test-section">
            <h2>📊 مقارنة النظام قبل وبعد التطوير</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>قبل التطوير</th>
                        <th>بعد التطوير</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>فاتورة الشراء</td>
                        <td>بيانات أساسية فقط</td>
                        <td>فاتورة شاملة مع أصناف</td>
                        <td>✅ محسن</td>
                    </tr>
                    <tr>
                        <td>الأصناف</td>
                        <td>غير موجودة</td>
                        <td>إضافة أصناف متعددة</td>
                        <td>✅ جديد</td>
                    </tr>
                    <tr>
                        <td>تحديث المخزون</td>
                        <td>لا يحدث</td>
                        <td>تحديث تلقائي</td>
                        <td>✅ جديد</td>
                    </tr>
                    <tr>
                        <td>التقارير</td>
                        <td>لا تظهر البيانات</td>
                        <td>تقارير شاملة</td>
                        <td>✅ مصلح</td>
                    </tr>
                    <tr>
                        <td>رقم الفاتورة</td>
                        <td>ثابت</td>
                        <td>تسلسلي (PUR-001, PUR-002...)</td>
                        <td>✅ محسن</td>
                    </tr>
                    <tr>
                        <td>الحفظ</td>
                        <td>مؤقت</td>
                        <td>دائم في localStorage</td>
                        <td>✅ مصلح</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار كامل لنظام المشتريات:</h3>
                <p>سنختبر جميع الميزات الجديدة للتأكد من عمل النظام بشكل متكامل</p>
            </div>
            
            <button class="btn" onclick="startPurchasesTest()">🚀 بدء اختبار نظام المشتريات</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار الشامل:</h3>
                <ol>
                    <li><strong>اختبار إنشاء فاتورة شراء:</strong>
                        <ul>
                            <li>فتح صفحة المشتريات</li>
                            <li>إنشاء فاتورة شراء جديدة</li>
                            <li>التحقق من رقم التسلسل (PUR-001)</li>
                            <li>اختيار مورد</li>
                            <li>إضافة أصناف متعددة</li>
                            <li>التحقق من الحسابات التلقائية</li>
                        </ul>
                    </li>
                    <li><strong>اختبار تحديث المخزون:</strong>
                        <ul>
                            <li>حفظ فاتورة الشراء</li>
                            <li>فتح صفحة المنتجات/المخزون</li>
                            <li>التحقق من تحديث الكميات</li>
                            <li>التحقق من أسعار الشراء</li>
                        </ul>
                    </li>
                    <li><strong>اختبار التقارير:</strong>
                        <ul>
                            <li>فتح صفحة التقارير</li>
                            <li>التحقق من تقارير المشتريات</li>
                            <li>التحقق من تقارير الموردين</li>
                            <li>التحقق من تقارير المخزون</li>
                        </ul>
                    </li>
                    <li><strong>اختبار الاستمرارية:</strong>
                        <ul>
                            <li>إعادة تحميل صفحة المشتريات</li>
                            <li>التحقق من ظهور الفواتير</li>
                            <li>إنشاء فاتورة ثانية</li>
                            <li>التحقق من الرقم PUR-002</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="openInventoryPage()">📦 فتح صفحة المخزون</button>
            <button class="btn" onclick="openReportsPage()">📊 فتح صفحة التقارير</button>
            <button class="btn" onclick="checkPurchasesData()">💾 فحص بيانات المشتريات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد التطوير يجب أن:</h3>
                <ul>
                    <li><strong>فاتورة الشراء:</strong> تحتوي على أصناف متعددة مع حسابات تلقائية</li>
                    <li><strong>رقم الفاتورة:</strong> PUR-001, PUR-002, PUR-003... (تسلسلي)</li>
                    <li><strong>المخزون:</strong> يتحدث تلقائياً عند إضافة فاتورة شراء</li>
                    <li><strong>التقارير:</strong> تظهر بيانات المشتريات والموردين والمخزون</li>
                    <li><strong>الحفظ:</strong> الفواتير تُحفظ ولا تختفي</li>
                    <li><strong>التكامل:</strong> ربط مع جميع أجزاء النظام</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // بدء اختبار نظام المشتريات
        function startPurchasesTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار نظام المشتريات الشامل!</strong><br><br>
                    
                    <strong>المرحلة 1: اختبار إنشاء فاتورة شراء</strong><br>
                    1️⃣ افتح صفحة المشتريات<br>
                    2️⃣ اضغط "فاتورة جديدة"<br>
                    3️⃣ تحقق من رقم الفاتورة: PUR-001<br>
                    4️⃣ اختر مورد من القائمة<br>
                    5️⃣ أضف أصناف متعددة<br>
                    6️⃣ تحقق من الحسابات التلقائية<br><br>
                    
                    <strong>المرحلة 2: اختبار تحديث المخزون</strong><br>
                    7️⃣ احفظ فاتورة الشراء<br>
                    8️⃣ افتح صفحة المخزون<br>
                    9️⃣ تحقق من تحديث الكميات<br><br>
                    
                    <strong>المرحلة 3: اختبار التقارير</strong><br>
                    🔟 افتح صفحة التقارير<br>
                    1️⃣1️⃣ تحقق من ظهور بيانات المشتريات<br><br>
                    
                    <strong>🎯 اضغط الأزرار أدناه لبدء كل مرحلة!</strong>
                </div>
            `);
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 اضغط "فاتورة جديدة" واختبر النظام الجديد', 'info');
        }

        // فتح صفحة المخزون
        function openInventoryPage() {
            window.open('inventory.html', '_blank');
            showResult('📦 تم فتح صفحة المخزون<br>💡 تحقق من تحديث الكميات بعد إضافة فاتورة شراء', 'info');
        }

        // فتح صفحة التقارير
        function openReportsPage() {
            window.open('reports.html', '_blank');
            showResult('📊 تم فتح صفحة التقارير<br>💡 تحقق من تقارير المشتريات والموردين والمخزون', 'info');
        }

        // فحص بيانات المشتريات
        function checkPurchasesData() {
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    💾 <strong>البيانات المحفوظة في localStorage:</strong><br><br>
                    🛒 <strong>فواتير الشراء:</strong> ${purchases.length} فاتورة<br>
                    🏢 <strong>الموردين:</strong> ${suppliers.length} مورد<br>
                    📦 <strong>المنتجات:</strong> ${products.length} منتج<br><br>
                    ${purchases.length > 0 ? `<strong>آخر فاتورة شراء:</strong> ${purchases[purchases.length - 1].id}` : '<strong>لا توجد فواتير شراء محفوظة</strong>'}
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🛒 <strong>تم تطوير نظام المشتريات الشامل!</strong><br><br>
                    ✅ فاتورة شراء شاملة مع أصناف<br>
                    ✅ تحديث تلقائي للمخزون<br>
                    ✅ رقم تسلسلي للفواتير<br>
                    ✅ ربط مع التقارير<br>
                    ✅ حفظ دائم في localStorage<br><br>
                    🧪 <strong>اضغط "بدء اختبار نظام المشتريات" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
