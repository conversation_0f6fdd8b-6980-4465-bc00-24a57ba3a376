<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير لغة لوحة المفاتيح</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #3498db;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-right: 4px solid #2980b9;
            padding-right: 10px;
        }
        .step {
            background-color: #f8f9fa;
            border-right: 4px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step h3 {
            color: #2ecc71;
            margin-top: 0;
        }
        .tip {
            background-color: #e8f4f8;
            border-right: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .tip h3 {
            color: #3498db;
            margin-top: 0;
        }
        .keyboard-shortcut {
            display: inline-block;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 2px 5px;
            font-family: monospace;
            margin: 0 3px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .keyboard-shortcut.active {
            background-color: #3498db;
            color: white;
            transform: scale(1.1);
        }
        .language-display {
            display: none;
            padding: 10px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .language-display.arabic {
            background-color: #f8d7da;
            color: #721c24;
        }
        .language-display.english {
            background-color: #d4edda;
            color: #155724;
        }
        .toggle-btn {
            background-color: #f1c40f;
            color: #333;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .toggle-btn:hover {
            background-color: #f39c12;
        }
        .video-container {
            display: none;
            margin: 20px 0;
            text-align: center;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .image-container img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .caption {
            margin-top: 10px;
            font-style: italic;
            color: #7f8c8d;
        }
        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تغيير لغة لوحة المفاتيح في ويندوز</h1>
        
        <p>تعد مشكلة ظهور الحرف العربي (ؤ) قبل الأوامر في سطر الأوامر من المشاكل الشائعة عند استخدام لوحة مفاتيح باللغة العربية. يمكنك حل هذه المشكلة بتغيير لغة لوحة المفاتيح إلى الإنجليزية قبل كتابة الأوامر.</p>
        
        <h2>طرق تغيير لغة لوحة المفاتيح</h2>
        
        <div class="step">
            <h3>الطريقة 1: استخدام اختصارات لوحة المفاتيح</h3>
            <p>يمكنك استخدام أحد الاختصارات التالية للتبديل بين لغات لوحة المفاتيح:</p>
            <ul>
                <li><span class="keyboard-shortcut">Alt + Shift</span> - اضغط على هذين المفتاحين معًا للتبديل بين اللغات المثبتة.</li>
                <li><span class="keyboard-shortcut">Windows + Space</span> - اضغط على مفتاح الويندوز ومفتاح المسافة معًا للتبديل بين اللغات.</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>الطريقة 2: استخدام شريط اللغة في شريط المهام</h3>
            <p>يمكنك النقر على رمز اللغة في شريط المهام (عادة ما يكون في الجزء السفلي الأيمن من الشاشة) واختيار اللغة المطلوبة:</p>
            <ol>
                <li>ابحث عن رمز اللغة في شريط المهام (مثل "AR" للعربية أو "EN" للإنجليزية).</li>
                <li>انقر على الرمز لفتح قائمة اللغات المتاحة.</li>
                <li>اختر "English (United States)" أو أي لغة إنجليزية أخرى.</li>
            </ol>
        </div>
        
        <div class="tip">
            <h3>نصيحة</h3>
            <p>قبل فتح سطر الأوامر (Command Prompt) أو PowerShell، تأكد من تغيير لغة لوحة المفاتيح إلى الإنجليزية لتجنب مشكلة الحرف العربي (ؤ).</p>
        </div>
        
        <h2>التحقق من لغة لوحة المفاتيح الحالية</h2>
        <p>يمكنك التحقق من لغة لوحة المفاتيح الحالية من خلال النظر إلى رمز اللغة في شريط المهام:</p>
        <ul>
            <li>إذا كان الرمز يعرض "AR" أو "العربية"، فهذا يعني أن لوحة المفاتيح مضبوطة على اللغة العربية.</li>
            <li>إذا كان الرمز يعرض "EN" أو "ENG" أو "English"، فهذا يعني أن لوحة المفاتيح مضبوطة على اللغة الإنجليزية.</li>
        </ul>
        
        <button id="check-language-btn" class="toggle-btn">التحقق من لغة المتصفح الحالية</button>
        <div id="current-language" class="language-display"></div>

        <div class="step">
            <h3>التحقق من لغة لوحة المفاتيح الحالية</h3>
            <p>يمكنك التحقق من لغة لوحة المفاتيح الحالية من خلال النظر إلى رمز اللغة في شريط المهام:</p>
            <ul>
                <li>إذا كان الرمز يعرض "AR" أو "العربية"، فهذا يعني أن لوحة المفاتيح مضبوطة على اللغة العربية.</li>
                <li>إذا كان الرمز يعرض "EN" أو "ENG" أو "English"، فهذا يعني أن لوحة المفاتيح مضبوطة على اللغة الإنجليزية.</li>
            </ul>
            <button class="toggle-btn toggle-image-btn" data-target="taskbar-language-icon">عرض الصورة</button>
        </div>

        <div id="taskbar-language-icon" class="image-container" style="display: none;">
            <img src="https://i.imgur.com/JQpwmzl.png" alt="رمز اللغة في شريط المهام">
            <div class="caption">رمز اللغة في شريط المهام في ويندوز</div>
        </div>

        <div class="tip">
            <h3>طريقة تغيير لغة لوحة المفاتيح بالصور</h3>
            <p>اتبع الخطوات التالية لتغيير لغة لوحة المفاتيح من خلال إعدادات ويندوز:</p>
            <ol>
                <li>افتح إعدادات ويندوز (Windows Settings) بالضغط على <span class="keyboard-shortcut">Windows + I</span></li>
                <li>انقر على "الوقت واللغة" (Time & Language)</li>
                <li>اختر "اللغة" (Language) من القائمة الجانبية</li>
                <li>انقر على "لغات لوحة المفاتيح" (Keyboard languages) لإضافة أو إزالة لغات</li>
            </ol>
            <button class="toggle-btn toggle-image-btn" data-target="windows-language-settings">عرض الصورة</button>
        </div>

        <div id="windows-language-settings" class="image-container" style="display: none;">
            <img src="https://i.imgur.com/8XYZ1AB.png" alt="إعدادات اللغة في ويندوز">
            <div class="caption">إعدادات اللغة في ويندوز 10</div>
        </div>

        <div class="step">
            <h3>مشاهدة فيديو توضيحي</h3>
            <p>يمكنك مشاهدة الفيديو التوضيحي التالي لمعرفة كيفية تغيير لغة لوحة المفاتيح بسهولة:</p>
            <button id="show-video-btn" class="toggle-btn">عرض الفيديو</button>
        </div>

        <div id="video-tutorial" class="video-container">
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
        
        <div class="buttons">
            <a href="launcher.html" class="btn btn-green">العودة إلى مشغل النظام</a>
            <a href="fix-arabic-char.html" class="btn">العودة إلى صفحة حل المشكلة</a>
        </div>
    </div>
    <script src="js/keyboard-language.js"></script>
</body>
</html>