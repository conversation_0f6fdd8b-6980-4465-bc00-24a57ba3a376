<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة المنتجات الفارغة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .problem-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .problem-box h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .step-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .step-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .step-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشكلة المنتجات الفارغة</h1>

        <!-- المشكلة المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشكلة المكتشفة</h2>
            <div class="highlight">
                <h3>المشكلة الحالية:</h3>
                <p><strong>❌ "إجمالي المنتجات 25" لكن لا يوجد أي منتج في قوائم الصفحة</strong></p>
                <p><strong>❌ المنتجات الجديدة لا تُضاف ولا تظهر في القائمة</strong></p>
            </div>
            
            <div class="problem-box">
                <h3>السبب المحتمل:</h3>
                <ul>
                    <li><strong>تضارب في البيانات:</strong> البيانات التجريبية لا تتوافق مع النظام الجديد</li>
                    <li><strong>مشكلة في العرض:</strong> دالة updateProductsTableDisplay لا تعمل بشكل صحيح</li>
                    <li><strong>بيانات ناقصة:</strong> البيانات التجريبية تفتقد لحقول مطلوبة (minQuantity, cost)</li>
                    <li><strong>مشكلة في التحديث:</strong> الجدول لا يتحديث بعد إضافة البيانات</li>
                </ul>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="fixes-list">
                <h3>تم إصلاح:</h3>
                <ul>
                    <li>إضافة حقول ناقصة في البيانات التجريبية (minQuantity, cost)</li>
                    <li>تحسين دالة addSampleProductsIfEmpty مع console.log</li>
                    <li>إصلاح تنسيق البيانات (parseFloat للأسعار)</li>
                    <li>إضافة تتبع مفصل للعمليات</li>
                    <li>تحسين دالة updateProductsDataDisplay</li>
                </ul>
            </div>
        </div>

        <!-- خطوات الحل -->
        <div class="test-section">
            <h2>🔧 خطوات الحل</h2>
            
            <div class="steps-grid">
                <div class="step-card">
                    <div class="icon">🗑️</div>
                    <h3>الخطوة 1</h3>
                    <p>مسح البيانات القديمة</p>
                    <button class="btn btn-danger" onclick="clearAllProductData()">مسح البيانات</button>
                </div>
                <div class="step-card">
                    <div class="icon">➕</div>
                    <h3>الخطوة 2</h3>
                    <p>إضافة بيانات جديدة صحيحة</p>
                    <button class="btn btn-success" onclick="addCorrectSampleData()">إضافة بيانات صحيحة</button>
                </div>
                <div class="step-card">
                    <div class="icon">🔄</div>
                    <h3>الخطوة 3</h3>
                    <p>اختبار صفحة المنتجات</p>
                    <button class="btn" onclick="testProductsPage()">اختبار الصفحة</button>
                </div>
                <div class="step-card">
                    <div class="icon">📊</div>
                    <h3>الخطوة 4</h3>
                    <p>فحص النتائج</p>
                    <button class="btn" onclick="checkResults()">فحص النتائج</button>
                </div>
            </div>
        </div>

        <!-- اختبار شامل -->
        <div class="test-section">
            <h2>🧪 اختبار شامل</h2>
            <button class="btn" onclick="runCompleteTest()">🚀 تشغيل الاختبار الشامل</button>
            <button class="btn" onclick="checkProductData()">📊 فحص البيانات الحالية</button>
            <button class="btn" onclick="debugProductsIssue()">🔍 تشخيص المشكلة</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ 25 منتج تجريبي:</strong> يظهرون في الجدول</li>
                    <li><strong>✅ التنقل يعمل:</strong> 3 صفحات (10 منتجات لكل صفحة)</li>
                    <li><strong>✅ العداد صحيح:</strong> "عرض 1 - 10 من 25 منتج"</li>
                    <li><strong>✅ إضافة منتج جديد:</strong> يعمل ويظهر في القائمة</li>
                    <li><strong>✅ الأيقونات تعمل:</strong> عرض، تعديل، حذف</li>
                    <li><strong>✅ البيانات كاملة:</strong> جميع الحقول موجودة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // مسح جميع بيانات المنتجات
        function clearAllProductData() {
            localStorage.removeItem('monjizProducts');
            showResult(`
                <div class="success">
                    🗑️ <strong>تم مسح جميع بيانات المنتجات!</strong><br><br>
                    ✅ تم حذف البيانات القديمة من localStorage<br>
                    💡 <strong>الآن اضغط "إضافة بيانات صحيحة"</strong>
                </div>
            `);
        }

        // إضافة بيانات تجريبية صحيحة
        function addCorrectSampleData() {
            const sampleData = [];
            
            // إضافة 25 منتج مع جميع الحقول المطلوبة
            for (let i = 1; i <= 25; i++) {
                const quantity = Math.floor(Math.random() * 100) + 1;
                const minQuantity = Math.floor(quantity * 0.2) + 1;
                const price = parseFloat((Math.random() * 1000 + 50).toFixed(2));
                const cost = parseFloat((Math.random() * 500 + 25).toFixed(2));
                
                sampleData.push({
                    id: i,
                    code: `PRD-${String(i).padStart(3, '0')}`,
                    name: `منتج رقم ${i}`,
                    category: ['إلكترونيات', 'ملابس', 'أدوات منزلية', 'كتب'][Math.floor(Math.random() * 4)],
                    price: price,
                    cost: cost,
                    quantity: quantity,
                    minQuantity: minQuantity,
                    unit: ['قطعة', 'كيلو', 'متر', 'لتر'][Math.floor(Math.random() * 4)],
                    description: `وصف المنتج رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            
            localStorage.setItem('monjizProducts', JSON.stringify(sampleData));
            
            showResult(`
                <div class="success">
                    ➕ <strong>تم إضافة بيانات تجريبية صحيحة!</strong><br><br>
                    📦 عدد المنتجات: ${sampleData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(sampleData.length / 10)}<br>
                    💰 نطاق الأسعار: 50 - 1050 ر.س<br>
                    📊 جميع الحقول مكتملة: اسم، فئة، سعر، تكلفة، كمية، حد أدنى<br><br>
                    💡 <strong>الآن اختبر صفحة المنتجات!</strong>
                </div>
            `);
        }

        // اختبار صفحة المنتجات
        function testProductsPage() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="info">
                    📦 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>تحقق من:</strong><br>
                    ✅ ظهور 25 منتج في الجدول<br>
                    ✅ التنقل بين 3 صفحات<br>
                    ✅ العداد: "عرض 1 - 10 من 25 منتج"<br>
                    ✅ الأيقونات تعمل (عرض، تعديل، حذف)<br>
                    ✅ إضافة منتج جديد يعمل<br><br>
                    
                    💡 <strong>افتح Developer Tools (F12) لمراقبة Console!</strong>
                </div>
            `);
        }

        // فحص النتائج
        function checkResults() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            let status = 'success';
            let message = '';
            
            if (productsData.length === 0) {
                status = 'error';
                message = `
                    <div class="error">
                        ❌ <strong>لا توجد بيانات!</strong><br><br>
                        📦 عدد المنتجات: 0<br>
                        💡 <strong>اضغط "إضافة بيانات صحيحة" أولاً</strong>
                    </div>
                `;
            } else if (productsData.length < 25) {
                status = 'info';
                message = `
                    <div class="info">
                        ⚠️ <strong>بيانات ناقصة!</strong><br><br>
                        📦 عدد المنتجات: ${productsData.length}<br>
                        📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                        💡 <strong>يجب أن يكون 25 منتج</strong>
                    </div>
                `;
            } else {
                message = `
                    <div class="success">
                        ✅ <strong>البيانات صحيحة!</strong><br><br>
                        📦 عدد المنتجات: ${productsData.length}<br>
                        📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                        🔧 جميع الحقول موجودة<br><br>
                        💡 <strong>يجب أن تظهر المنتجات في الصفحة الآن!</strong>
                    </div>
                `;
            }
            
            showResult(message);
        }

        // تشغيل الاختبار الشامل
        function runCompleteTest() {
            showResult(`
                <div class="info">
                    🚀 <strong>بدء الاختبار الشامل!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ مسح البيانات القديمة<br>
                    2️⃣ إضافة بيانات جديدة صحيحة<br>
                    3️⃣ اختبار صفحة المنتجات<br>
                    4️⃣ فحص النتائج<br><br>
                    
                    💡 <strong>اتبع الخطوات بالترتيب!</strong>
                </div>
            `);
        }

        // فحص بيانات المنتجات
        function checkProductData() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            if (productsData.length > 0) {
                const sampleProduct = productsData[0];
                const hasAllFields = sampleProduct.hasOwnProperty('minQuantity') && 
                                   sampleProduct.hasOwnProperty('cost') && 
                                   sampleProduct.hasOwnProperty('price') && 
                                   sampleProduct.hasOwnProperty('quantity');
                
                showResult(`
                    <div class="info">
                        📊 <strong>فحص بيانات المنتجات:</strong><br><br>
                        
                        📦 عدد المنتجات: ${productsData.length}<br>
                        📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                        🔧 الحقول مكتملة: ${hasAllFields ? '✅ نعم' : '❌ لا'}<br><br>
                        
                        <strong>عينة من البيانات:</strong><br>
                        ${productsData.slice(0, 3).map((p, i) => 
                            `${i + 1}. ${p.name} - ${p.price} ر.س`
                        ).join('<br>')}<br><br>
                        
                        ${hasAllFields ? 
                            '✅ <strong>البيانات صحيحة!</strong>' :
                            '❌ <strong>البيانات ناقصة - أضف بيانات صحيحة</strong>'
                        }
                    </div>
                `);
            } else {
                showResult(`
                    <div class="error">
                        ❌ <strong>لا توجد بيانات منتجات!</strong><br><br>
                        💡 <strong>اضغط "إضافة بيانات صحيحة"</strong>
                    </div>
                `);
            }
        }

        // تشخيص المشكلة
        function debugProductsIssue() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    🔍 <strong>تشخيص مشكلة المنتجات:</strong><br><br>
                    
                    <strong>فحص localStorage:</strong><br>
                    📦 مفتاح التخزين: monjizProducts<br>
                    📊 عدد المنتجات: ${productsData.length}<br>
                    💾 حجم البيانات: ${JSON.stringify(productsData).length} حرف<br><br>
                    
                    <strong>التشخيص:</strong><br>
                    ${productsData.length === 0 ? 
                        '❌ المشكلة: لا توجد بيانات في localStorage' :
                        productsData.length === 25 ?
                        '✅ البيانات موجودة - المشكلة في العرض' :
                        '⚠️ البيانات ناقصة - يجب أن تكون 25 منتج'
                    }<br><br>
                    
                    💡 <strong>الحل المقترح:</strong><br>
                    ${productsData.length === 0 ? 
                        'اضغط "إضافة بيانات صحيحة"' :
                        'افتح صفحة المنتجات مع Developer Tools'
                    }
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>أداة إصلاح مشكلة المنتجات الفارغة!</strong><br><br>
                    
                    <strong>المشكلة:</strong><br>
                    ❌ إجمالي المنتجات 25 لكن لا يظهر أي منتج<br>
                    ❌ المنتجات الجديدة لا تُضاف<br><br>
                    
                    <strong>الحل:</strong><br>
                    1️⃣ مسح البيانات القديمة<br>
                    2️⃣ إضافة بيانات صحيحة<br>
                    3️⃣ اختبار النتائج<br><br>
                    
                    🚀 <strong>ابدأ بـ "تشغيل الاختبار الشامل"!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
