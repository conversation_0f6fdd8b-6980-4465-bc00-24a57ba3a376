<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات الموردين - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .table-demo {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .table-demo th,
        .table-demo td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .table-demo th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .table-demo tr:hover {
            background: #f8f9fa;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge.info {
            background: #17a2b8;
            color: white;
        }
        .form-demo {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 30px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        .btn-primary, .btn-secondary {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاحات الموردين</h1>

        <!-- المشاكل المصلحة -->
        <div class="test-section">
            <h2>🎯 المشاكل المصلحة</h2>
            <div class="highlight">
                <h3>المشاكل:</h3>
                <ol>
                    <li><strong>فئة المورد لا قيمة لها</strong> - حقل غير مفيد في النموذج</li>
                    <li><strong>عدم تناسب الصفوف مع العناوين</strong> - الجدول يحتوي على عمود فئة بدون بيانات</li>
                </ol>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>نموذج يحتوي على حقل فئة المورد</li>
                        <li>جدول بـ 7 أعمدة مع عمود فئة فارغ</li>
                        <li>عدم تناسق بين العناوين والبيانات</li>
                        <li>حقول غير مفيدة تشوش المستخدم</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>نموذج مبسط بدون حقل الفئة</li>
                        <li>جدول بـ 5 أعمدة متناسقة</li>
                        <li>تناسق كامل بين العناوين والبيانات</li>
                        <li>واجهة نظيفة ومركزة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- النموذج المحسن -->
        <div class="test-section">
            <h2>📝 النموذج المحسن</h2>
            <div class="form-demo">
                <h3>نموذج إضافة مورد (بعد الإصلاح)</h3>
                <form onsubmit="testImprovedForm(event)">
                    <div class="form-group">
                        <label for="testSupplierName">اسم المورد *</label>
                        <input type="text" id="testSupplierName" placeholder="أدخل اسم المورد" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierPhone">رقم الهاتف *</label>
                        <input type="tel" id="testSupplierPhone" placeholder="مثال: +966501234567" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierEmail">البريد الإلكتروني</label>
                        <input type="email" id="testSupplierEmail" placeholder="مثال: <EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="testSupplierType">نوع المورد</label>
                        <select id="testSupplierType">
                            <option value="شركة">شركة</option>
                            <option value="مؤسسة">مؤسسة</option>
                            <option value="فرد">فرد</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="clearImprovedForm()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
                <div id="form-result"></div>
            </div>
        </div>

        <!-- الجدول المحسن -->
        <div class="test-section">
            <h2>📊 الجدول المحسن</h2>
            <div class="highlight">
                <h3>الجدول الجديد (5 أعمدة متناسقة):</h3>
            </div>
            <table class="table-demo">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>النوع</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>شركة التقنية المتقدمة</td>
                        <td><span class="badge info">شركة</span></td>
                        <td>+966112345678</td>
                        <td><EMAIL></td>
                        <td>تعديل | عرض | حذف</td>
                    </tr>
                    <tr>
                        <td>مؤسسة الإمداد الشامل</td>
                        <td><span class="badge info">مؤسسة</span></td>
                        <td>+966501234567</td>
                        <td><EMAIL></td>
                        <td>تعديل | عرض | حذف</td>
                    </tr>
                    <tr>
                        <td>محمد أحمد التجاري</td>
                        <td><span class="badge info">فرد</span></td>
                        <td>+966551234567</td>
                        <td><EMAIL></td>
                        <td>تعديل | عرض | حذف</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار الإصلاحات:</h3>
                <p>سنختبر النموذج والجدول للتأكد من الإصلاحات</p>
            </div>
            
            <button class="btn" onclick="startFixesTest()">🚀 بدء اختبار الإصلاحات</button>
            <div id="test-result"></div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="testSuppliersPage()">👥 اختبار صفحة الموردين</button>
            <button class="btn" onclick="testPurchasesPage()">🛒 اختبار صفحة المشتريات</button>
            <button class="btn" onclick="testFormFields()">📝 اختبار حقول النموذج</button>
            <button class="btn" onclick="testTableColumns()">📊 اختبار أعمدة الجدول</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاحات يجب أن:</h3>
                <ul>
                    <li><strong>النموذج:</strong> 4 حقول فقط (اسم، هاتف، بريد، نوع)</li>
                    <li><strong>الجدول:</strong> 5 أعمدة متناسقة مع البيانات</li>
                    <li><strong>التناسق:</strong> كل عمود له بيانات مطابقة</li>
                    <li><strong>البساطة:</strong> واجهة نظيفة بدون حقول غير مفيدة</li>
                    <li><strong>التوحيد:</strong> نفس التحسينات في الموردين والمشتريات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار النموذج المحسن
        function testImprovedForm(event) {
            event.preventDefault();
            
            const supplierName = document.getElementById('testSupplierName').value.trim();
            const supplierPhone = document.getElementById('testSupplierPhone').value.trim();
            const supplierEmail = document.getElementById('testSupplierEmail').value.trim();
            const supplierType = document.getElementById('testSupplierType').value;
            
            if (!supplierName) {
                document.getElementById('form-result').innerHTML = `
                    <div style="color: #dc3545; margin-top: 15px;">❌ يرجى إدخال اسم المورد</div>
                `;
                return;
            }
            
            if (!supplierPhone) {
                document.getElementById('form-result').innerHTML = `
                    <div style="color: #dc3545; margin-top: 15px;">❌ يرجى إدخال رقم الهاتف</div>
                `;
                return;
            }

            document.getElementById('form-result').innerHTML = `
                <div style="color: #28a745; margin-top: 15px; padding: 15px; background: #d4edda; border-radius: 8px;">
                    ✅ <strong>تم الحفظ بنجاح!</strong><br><br>
                    <strong>البيانات المحفوظة:</strong><br>
                    📝 الاسم: ${supplierName}<br>
                    📞 الهاتف: ${supplierPhone}<br>
                    📧 البريد: ${supplierEmail || 'غير محدد'}<br>
                    🏢 النوع: ${supplierType}<br>
                    ⏰ الوقت: ${new Date().toLocaleString()}<br><br>
                    💡 <strong>لاحظ: لا يوجد حقل فئة المورد!</strong>
                </div>
            `;
            
            clearImprovedForm();
        }

        function clearImprovedForm() {
            document.getElementById('testSupplierName').value = '';
            document.getElementById('testSupplierPhone').value = '';
            document.getElementById('testSupplierEmail').value = '';
            document.getElementById('testSupplierType').value = 'شركة';
        }

        // بدء اختبار الإصلاحات
        function startFixesTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار الإصلاحات!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة الموردين<br>
                    2️⃣ اضغط "مورد جديد" وتحقق من عدم وجود حقل الفئة<br>
                    3️⃣ تحقق من أن الجدول يحتوي على 5 أعمدة فقط<br>
                    4️⃣ افتح صفحة المشتريات وكرر نفس الاختبار<br>
                    5️⃣ أضف مورد جديد وتحقق من ظهوره بشكل صحيح<br><br>
                    
                    <strong>🎯 اضغط الأزرار أدناه للاختبار!</strong>
                </div>
            `);
        }

        // اختبار صفحة الموردين
        function testSuppliersPage() {
            window.open('suppliers.html', '_blank');
            showResult('👥 تم فتح صفحة الموردين<br>💡 تحقق من النموذج المحسن والجدول المتناسق!', 'info');
        }

        // اختبار صفحة المشتريات
        function testPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 اضغط "مورد جديد" لرؤية النموذج المحسن!', 'info');
        }

        // اختبار حقول النموذج
        function testFormFields() {
            showResult(`
                <div class="info">
                    📝 <strong>اختبار حقول النموذج:</strong><br><br>
                    
                    <strong>📋 الحقول المتبقية (4 حقول):</strong><br>
                    1️⃣ <strong>اسم المورد:</strong> حقل نص مطلوب ✅<br>
                    2️⃣ <strong>رقم الهاتف:</strong> حقل هاتف مطلوب ✅<br>
                    3️⃣ <strong>البريد الإلكتروني:</strong> حقل بريد اختياري ✅<br>
                    4️⃣ <strong>نوع المورد:</strong> قائمة منسدلة (3 خيارات) ✅<br><br>
                    
                    <strong>❌ الحقول المحذوفة:</strong><br>
                    • فئة المورد (كانت بلا قيمة)<br><br>
                    
                    ✅ <strong>النموذج أصبح أبسط وأكثر فعالية!</strong>
                </div>
            `);
        }

        // اختبار أعمدة الجدول
        function testTableColumns() {
            showResult(`
                <div class="info">
                    📊 <strong>اختبار أعمدة الجدول:</strong><br><br>
                    
                    <strong>📋 الأعمدة الحالية (5 أعمدة):</strong><br>
                    1️⃣ <strong>الاسم:</strong> اسم المورد ✅<br>
                    2️⃣ <strong>النوع:</strong> نوع المورد مع badge ملون ✅<br>
                    3️⃣ <strong>رقم الهاتف:</strong> رقم الهاتف ✅<br>
                    4️⃣ <strong>البريد الإلكتروني:</strong> البريد الإلكتروني ✅<br>
                    5️⃣ <strong>الإجراءات:</strong> أزرار التعديل والعرض والحذف ✅<br><br>
                    
                    <strong>❌ الأعمدة المحذوفة:</strong><br>
                    • الرقم (غير ضروري)<br>
                    • الفئة (بلا بيانات)<br><br>
                    
                    ✅ <strong>الجدول أصبح متناسق ومنظم!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح مشاكل الموردين بنجاح!</strong><br><br>
                    ✅ حذف حقل فئة المورد غير المفيد<br>
                    ✅ إصلاح تناسق أعمدة الجدول<br>
                    ✅ تبسيط النموذج (4 حقول بدلاً من 5)<br>
                    ✅ تحسين تخطيط الجدول (5 أعمدة بدلاً من 7)<br>
                    ✅ توحيد التحسينات في الموردين والمشتريات<br>
                    ✅ واجهة أنظف وأكثر تركيزاً<br><br>
                    🧪 <strong>اضغط "بدء اختبار الإصلاحات" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
