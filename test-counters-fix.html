<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح العدادات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .counter-demo {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .counter-demo h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .counter-text {
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .counter-correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .counter-wrong {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔢 اختبار إصلاح العدادات</h1>

        <!-- المشكلة المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشكلة المكتشفة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>❌ العدادات تظهر النصوص الخاطئة:</strong></p>
                <ul>
                    <li><strong>الموردين:</strong> يظهر "فاتورة" بدلاً من "مورد"</li>
                    <li><strong>المنتجات:</strong> يظهر "مورد" بدلاً من "منتج"</li>
                </ul>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <div class="counter-text counter-wrong">عرض 1 - 10 من 11 فاتورة</div>
                    <p><small>في صفحة الموردين (خطأ!)</small></p>
                    <div class="counter-text counter-wrong">عرض 1 - 9 من 9 مورد</div>
                    <p><small>في صفحة المنتجات (خطأ!)</small></p>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <div class="counter-text counter-correct">عرض 1 - 10 من 25 مورد</div>
                    <p><small>في صفحة الموردين (صحيح!)</small></p>
                    <div class="counter-text counter-correct">عرض 1 - 10 من 25 منتج</div>
                    <p><small>في صفحة المنتجات (صحيح!)</small></p>
                </div>
            </div>
        </div>

        <!-- عرض توضيحي للعدادات -->
        <div class="test-section">
            <h2>📊 عرض توضيحي للعدادات الصحيحة</h2>
            
            <div class="counter-demo">
                <h3>🏢 عداد الموردين</h3>
                <div class="counter-text counter-correct">
                    عرض <span style="color: #667eea;">1</span> - <span style="color: #667eea;">10</span> من <span style="color: #667eea;">25</span> مورد
                </div>
                <p>✅ يجب أن يقول "مورد" وليس "فاتورة"</p>
            </div>
            
            <div class="counter-demo">
                <h3>📦 عداد المنتجات</h3>
                <div class="counter-text counter-correct">
                    عرض <span style="color: #667eea;">1</span> - <span style="color: #667eea;">10</span> من <span style="color: #667eea;">25</span> منتج
                </div>
                <p>✅ يجب أن يقول "منتج" وليس "مورد"</p>
            </div>
        </div>

        <!-- الإصلاح المطبق -->
        <div class="test-section">
            <h2>🛠️ الإصلاح المطبق</h2>
            <div class="highlight">
                <h3>ما تم إصلاحه:</h3>
                <ul>
                    <li><strong>✅ تحسين دالة updateSuppliersResultsInfo</strong> - إضافة فحص العناصر</li>
                    <li><strong>✅ تحسين دالة updateProductsResultsInfo</strong> - إضافة فحص العناصر</li>
                    <li><strong>✅ التأكد من وجود العناصر</strong> قبل تحديث النصوص</li>
                    <li><strong>✅ منع الأخطاء</strong> في حالة عدم وجود العناصر</li>
                </ul>
            </div>
        </div>

        <!-- اختبار العدادات -->
        <div class="test-section">
            <h2>🧪 اختبار العدادات المصلحة</h2>
            <div class="highlight">
                <h3>🎯 اختبر الآن:</h3>
                <p>افتح الصفحات وتحقق من العدادات أسفل الجداول</p>
            </div>
            
            <button class="btn" onclick="testSuppliersCounters()">🏢 اختبار عدادات الموردين</button>
            <button class="btn" onclick="testProductsCounters()">📦 اختبار عدادات المنتجات</button>
            <button class="btn" onclick="testAllCounters()">📊 اختبار جميع العدادات</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>🏢 الموردين:</strong> "عرض 1 - 10 من 25 مورد" (وليس فاتورة)</li>
                    <li><strong>📦 المنتجات:</strong> "عرض 1 - 10 من 25 منتج" (وليس مورد)</li>
                    <li><strong>🛒 المبيعات:</strong> "عرض 1 - 10 من 25 فاتورة" (صحيح)</li>
                    <li><strong>🛍️ المشتريات:</strong> "عرض 1 - 10 من 25 فاتورة" (صحيح)</li>
                    <li><strong>📊 التنقل:</strong> يعمل بشكل صحيح في جميع الصفحات</li>
                    <li><strong>🎨 الأيقونات:</strong> ملونة ووظيفية في جميع الصفحات</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار عدادات الموردين
        function testSuppliersCounters() {
            window.open('suppliers.html', '_blank');
            showResult(`
                <div class="success">
                    🏢 <strong>تم فتح صفحة الموردين!</strong><br><br>
                    
                    <strong>تحقق من العداد أسفل الجدول:</strong><br>
                    ✅ يجب أن يقول: "عرض 1 - 10 من 25 مورد"<br>
                    ❌ وليس: "عرض 1 - 10 من 25 فاتورة"<br><br>
                    
                    💡 <strong>انتقل لأسفل الجدول لرؤية العداد!</strong>
                </div>
            `);
        }

        // اختبار عدادات المنتجات
        function testProductsCounters() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="success">
                    📦 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>تحقق من العداد أسفل الجدول:</strong><br>
                    ✅ يجب أن يقول: "عرض 1 - 10 من 25 منتج"<br>
                    ❌ وليس: "عرض 1 - 9 من 9 مورد"<br><br>
                    
                    💡 <strong>انتقل لأسفل الجدول لرؤية العداد!</strong>
                </div>
            `);
        }

        // اختبار جميع العدادات
        function testAllCounters() {
            window.open('suppliers.html', '_blank');
            window.open('products.html', '_blank');
            window.open('sales.html', '_blank');
            window.open('purchases.html', '_blank');
            
            showResult(`
                <div class="success">
                    📊 <strong>تم فتح جميع الصفحات!</strong><br><br>
                    
                    <strong>تحقق من العدادات في كل صفحة:</strong><br>
                    🏢 الموردين: "عرض 1 - 10 من 25 مورد"<br>
                    📦 المنتجات: "عرض 1 - 10 من 25 منتج"<br>
                    🛒 المبيعات: "عرض 1 - 10 من 25 فاتورة"<br>
                    🛍️ المشتريات: "عرض 1 - 10 من 25 فاتورة"<br><br>
                    
                    💡 <strong>العدادات أسفل كل جدول!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔢 <strong>تم إصلاح مشكلة العدادات!</strong><br><br>
                    
                    <strong>المشكلة كانت:</strong><br>
                    ❌ الموردين يظهر "فاتورة" بدلاً من "مورد"<br>
                    ❌ المنتجات يظهر "مورد" بدلاً من "منتج"<br><br>
                    
                    <strong>الإصلاح:</strong><br>
                    ✅ تحسين دوال تحديث العدادات<br>
                    ✅ إضافة فحص وجود العناصر<br>
                    ✅ منع الأخطاء في JavaScript<br><br>
                    
                    🧪 <strong>اختبر الصفحات الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
