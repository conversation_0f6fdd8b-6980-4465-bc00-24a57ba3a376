<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل النظام مباشرة</title>
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        header {
            background-color: #3498db;
            color: white;
            padding: 20px 0;
            text-align: center;
            border-radius: 5px 5px 0 0;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0;
            font-size: 2.2rem;
        }
        h2 {
            color: #2980b9;
            border-bottom: 2px solid #f1c40f;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        p {
            margin-bottom: 15px;
        }
        .launch-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        .launch-option {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 200px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .launch-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        .launch-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #3498db;
        }
        .launch-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .launch-desc {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            font-weight: bold;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-green {
            background-color: #2ecc71;
        }
        .btn-green:hover {
            background-color: #27ae60;
        }
        .btn-orange {
            background-color: #e67e22;
        }
        .btn-orange:hover {
            background-color: #d35400;
        }
        .btn-purple {
            background-color: #9b59b6;
        }
        .btn-purple:hover {
            background-color: #8e44ad;
        }
        .btn-red {
            background-color: #e74c3c;
        }
        .btn-red:hover {
            background-color: #c0392b;
        }
        .note {
            background-color: #f8f9fa;
            border-right: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .warning {
            background-color: #fff3cd;
            border-right: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #7f8c8d;
        }
        @media (max-width: 768px) {
            .launch-options {
                flex-direction: column;
                align-items: center;
            }
            .launch-option {
                width: 80%;
            }
        }
    </style>
    <script>
        // هذه الدالة تقوم بفتح ملف launcher.html مباشرة
        function openLauncher() {
            window.location.href = 'launcher.html';
        }
        
        // هذه الدالة تقوم بفتح صفحة المساعدة المناسبة
        function openHelpPage(page) {
            window.location.href = page;
        }
    </script>
</head>
<body>
    <header>
        <div class="container">
            <h1>تشغيل النظام مباشرة</h1>
        </div>
    </header>

    <div class="container">
        <div class="note">
            <h3>تشغيل سريع ومباشر</h3>
            <p>هذه الصفحة تتيح لك تشغيل النظام مباشرة بدون الحاجة إلى استخدام سطر الأوامر أو ملفات التشغيل الخارجية، مما يتجنب مشكلة الحرف العربي (ؤ) تمامًا.</p>
        </div>

        <div class="launch-options">
            <div class="launch-option">
                <div class="launch-icon">🚀</div>
                <div class="launch-title">تشغيل مباشر</div>
                <div class="launch-desc">فتح صفحة المشغل الرئيسية مباشرة</div>
                <button onclick="openLauncher()" class="btn btn-green">تشغيل النظام</button>
            </div>
            
            <div class="launch-option">
                <div class="launch-icon">🏠</div>
                <div class="launch-title">الصفحة الرئيسية</div>
                <div class="launch-desc">العودة إلى الصفحة الرئيسية للنظام</div>
                <button onclick="openHelpPage('index.html')" class="btn">الصفحة الرئيسية</button>
            </div>
            
            <div class="launch-option">
                <div class="launch-icon">❓</div>
                <div class="launch-title">المساعدة</div>
                <div class="launch-desc">عرض صفحات المساعدة المختلفة</div>
                <button onclick="openHelpPage('manual-launch.html')" class="btn btn-orange">تعليمات التشغيل</button>
            </div>
        </div>

        <h2>طرق تشغيل النظام</h2>
        <p>يمكنك تشغيل النظام بعدة طرق مختلفة:</p>
        
        <h3>1. التشغيل المباشر من المتصفح</h3>
        <p>أسهل طريقة هي النقر على زر "تشغيل النظام" أعلاه، والذي سيقوم بفتح صفحة المشغل الرئيسية مباشرة.</p>
        
        <h3>2. فتح ملف launcher.html مباشرة</h3>
        <p>يمكنك فتح ملف <strong>launcher.html</strong> مباشرة من مستكشف الملفات بالنقر المزدوج عليه.</p>
        
        <h3>3. استخدام ملفات التشغيل المباشر</h3>
        <p>يمكنك استخدام أي من ملفات التشغيل التالية:</p>
        <ul>
            <li><strong>open_launcher.vbs</strong> - الحل المفضل والأكثر موثوقية</li>
            <li><strong>run_vbs.bat</strong> - ملف بات لتشغيل ملف VBS</li>
            <li><strong>run_ps1.bat</strong> - ملف بات لتشغيل ملف PowerShell</li>
            <li><strong>run_py.bat</strong> - ملف بات لتشغيل ملف Python</li>
            <li><strong>run_js.bat</strong> - ملف بات لتشغيل ملف JavaScript</li>
            <li><strong>start_server.bat</strong> - ملف بات لتشغيل الخادم مباشرة</li>
        </ul>
        
        <div class="warning">
            <h3>ملاحظة هامة</h3>
            <p>إذا كنت تواجه مشكلة في تشغيل النظام، تأكد من تغيير لغة لوحة المفاتيح إلى اللغة الإنجليزية قبل محاولة تشغيل أي ملف.</p>
            <p>للمزيد من المعلومات حول حل مشكلة الحرف العربي، يمكنك زيارة <a href="fix-arabic-char.html">صفحة حل مشكلة الحرف العربي</a>.</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openLauncher()" class="btn btn-green" style="font-size: 18px; padding: 15px 30px;">تشغيل النظام الآن</button>
        </div>
        
        <footer>
            <p>نظام إدارة الأعمال - جميع الحقوق محفوظة &copy; 2023</p>
        </footer>
    </div>
</body>
</html>