<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للإضافة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 15px 25px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn.large {
            padding: 20px 30px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .step {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #fff;
        }
        
        .step h3 {
            margin-top: 0;
            color: #fff;
        }
        
        .navigation {
            text-align: center;
            margin: 30px 0;
        }
        
        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 12px 24px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .navigation a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .status {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            font-size: 18px;
        }
        
        .instructions {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions ol {
            padding-right: 20px;
        }
        
        .instructions li {
            margin: 10px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-rocket"></i> اختبار سريع للإضافة والتكامل</h1>
        
        <div class="status">
            <i class="fas fa-info-circle"></i>
            هذه الصفحة ستساعدك في اختبار إضافة العملاء والموردين والتأكد من ظهورهم في دليل الحسابات
        </div>

        <div class="navigation">
            <a href="customers.html" target="_blank">
                <i class="fas fa-users"></i> صفحة العملاء
            </a>
            <a href="suppliers.html" target="_blank">
                <i class="fas fa-truck"></i> صفحة الموردين
            </a>
            <a href="accounting.html" target="_blank">
                <i class="fas fa-calculator"></i> دليل الحسابات
            </a>
        </div>

        <!-- تعليمات الاختبار -->
        <div class="test-card">
            <h2><i class="fas fa-list-ol"></i> خطوات الاختبار</h2>
            
            <div class="step">
                <h3>الخطوة 1: اختبار إضافة عميل</h3>
                <div class="instructions">
                    <ol>
                        <li>اضغط على <strong>"صفحة العملاء"</strong> أعلاه (ستفتح في تبويب جديد)</li>
                        <li>اضغط على <strong>"إضافة عميل جديد"</strong></li>
                        <li>املأ البيانات:
                            <ul>
                                <li>الاسم: عميل تجريبي</li>
                                <li>النوع: شركة</li>
                                <li>الهاتف: +966501234567</li>
                                <li>البريد: <EMAIL></li>
                            </ul>
                        </li>
                        <li>اضغط <strong>"حفظ"</strong></li>
                        <li>انتظر رسالة النجاح</li>
                    </ol>
                </div>
            </div>

            <div class="step">
                <h3>الخطوة 2: التحقق من دليل الحسابات</h3>
                <div class="instructions">
                    <ol>
                        <li>اضغط على <strong>"دليل الحسابات"</strong> أعلاه</li>
                        <li>انتقل إلى تبويب <strong>"دليل الحسابات"</strong></li>
                        <li>ابحث عن العميل الجديد في الجدول</li>
                        <li>يجب أن يظهر برقم حساب يبدأ بـ <code>11030</code></li>
                        <li>جرب أيضاً <strong>"النظام الاحترافي"</strong> لرؤية الشجرة</li>
                    </ol>
                </div>
            </div>

            <div class="step">
                <h3>الخطوة 3: اختبار إضافة مورد</h3>
                <div class="instructions">
                    <ol>
                        <li>اضغط على <strong>"صفحة الموردين"</strong> أعلاه</li>
                        <li>اضغط على <strong>"إضافة مورد جديد"</strong></li>
                        <li>املأ البيانات:
                            <ul>
                                <li>الاسم: مورد تجريبي</li>
                                <li>النوع: شركة</li>
                                <li>الهاتف: +966502345678</li>
                                <li>البريد: <EMAIL></li>
                            </ul>
                        </li>
                        <li>اضغط <strong>"حفظ"</strong></li>
                        <li>انتظر رسالة النجاح</li>
                    </ol>
                </div>
            </div>

            <div class="step">
                <h3>الخطوة 4: التحقق النهائي</h3>
                <div class="instructions">
                    <ol>
                        <li>ارجع إلى <strong>"دليل الحسابات"</strong></li>
                        <li>اضغط على <strong>"مزامنة شاملة"</strong> إذا لم تظهر الحسابات</li>
                        <li>ابحث عن المورد الجديد برقم حساب يبدأ بـ <code>21030</code></li>
                        <li>جرب <strong>"تشخيص التكامل"</strong> لرؤية الإحصائيات</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- أدوات مساعدة -->
        <div class="test-card">
            <h2><i class="fas fa-tools"></i> أدوات مساعدة</h2>
            
            <button class="btn" onclick="openAllPages()">
                <i class="fas fa-external-link-alt"></i> فتح جميع الصفحات
            </button>
            
            <button class="btn" onclick="checkCurrentData()">
                <i class="fas fa-search"></i> فحص البيانات الحالية
            </button>
            
            <button class="btn" onclick="clearTestData()">
                <i class="fas fa-broom"></i> مسح البيانات التجريبية
            </button>
        </div>

        <!-- نتائج الفحص -->
        <div class="test-card">
            <h2><i class="fas fa-clipboard-check"></i> نتائج الفحص</h2>
            <div id="checkResults" style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 8px; font-family: monospace;">
                اضغط على "فحص البيانات الحالية" لرؤية النتائج...
            </div>
        </div>

        <!-- نصائح -->
        <div class="test-card">
            <h2><i class="fas fa-lightbulb"></i> نصائح مهمة</h2>
            <ul>
                <li><strong>إذا لم تظهر الحسابات:</strong> اضغط على "مزامنة شاملة" في دليل الحسابات</li>
                <li><strong>للتحقق من التكامل:</strong> استخدم "تشخيص التكامل" في دليل الحسابات</li>
                <li><strong>لرؤية الشجرة:</strong> جرب "النظام الاحترافي" في دليل الحسابات</li>
                <li><strong>في حالة المشاكل:</strong> افتح وحدة التحكم (F12) وراقب الرسائل</li>
            </ul>
        </div>
    </div>

    <script>
        function openAllPages() {
            window.open('customers.html', '_blank');
            window.open('suppliers.html', '_blank');
            window.open('accounting.html', '_blank');
            alert('تم فتح جميع الصفحات في تبويبات جديدة');
        }

        function checkCurrentData() {
            const results = document.getElementById('checkResults');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                const customerAccounts = accounts.filter(acc => acc.category === 'customers');
                const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
                
                let html = `
                    <div style="color: #87ceeb;">📊 إحصائيات البيانات الحالية:</div><br>
                    <div style="color: #90ee90;">العملاء: ${customers.length}</div>
                    <div style="color: #90ee90;">الموردين: ${suppliers.length}</div>
                    <div style="color: #90ee90;">إجمالي الحسابات: ${accounts.length}</div>
                    <div style="color: #90ee90;">حسابات العملاء: ${customerAccounts.length}</div>
                    <div style="color: #90ee90;">حسابات الموردين: ${supplierAccounts.length}</div><br>
                `;
                
                if (customers.length > 0) {
                    html += `<div style="color: #ffeb3b;">آخر العملاء:</div>`;
                    customers.slice(-3).forEach(customer => {
                        const expectedCode = `11030${String(customer.id).padStart(3, '0')}`;
                        const hasAccount = accounts.find(acc => acc.code === expectedCode);
                        const status = hasAccount ? '✅' : '❌';
                        html += `<div style="color: #fff;">  ${status} ${customer.name} (${expectedCode})</div>`;
                    });
                    html += '<br>';
                }
                
                if (suppliers.length > 0) {
                    html += `<div style="color: #ffeb3b;">آخر الموردين:</div>`;
                    suppliers.slice(-3).forEach(supplier => {
                        const expectedCode = `21030${String(supplier.id).padStart(3, '0')}`;
                        const hasAccount = accounts.find(acc => acc.code === expectedCode);
                        const status = hasAccount ? '✅' : '❌';
                        html += `<div style="color: #fff;">  ${status} ${supplier.name} (${expectedCode})</div>`;
                    });
                }
                
                results.innerHTML = html;
                
            } catch (error) {
                results.innerHTML = `<div style="color: #ffb3b3;">❌ خطأ في فحص البيانات: ${error.message}</div>`;
            }
        }

        function clearTestData() {
            if (confirm('هل تريد مسح جميع البيانات التجريبية؟\n\nسيتم حذف:\n- جميع العملاء\n- جميع الموردين\n- جميع الحسابات')) {
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizSuppliers');
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('professionalChartOfAccounts');
                
                alert('تم مسح جميع البيانات التجريبية');
                checkCurrentData();
            }
        }

        // فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkCurrentData, 1000);
        });
    </script>
</body>
</html>
