<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - منجز</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 اختبار سريع لوظائف منجز</h1>

    <div class="test-box">
        <h2>📦 اختبار إضافة منتج "جهاز عرض محمول"</h2>
        <button class="btn" onclick="testAddProjector()">إضافة جهاز عرض محمول</button>
        <button class="btn" onclick="showProducts()">عرض المنتجات المحفوظة</button>
        <div id="product-result"></div>
    </div>

    <div class="test-box">
        <h2>🏢 اختبار إضافة عميل "مطعم توباز"</h2>
        <button class="btn" onclick="testAddTopaz()">إضافة مطعم توباز</button>
        <button class="btn" onclick="showCustomers()">عرض العملاء المحفوظين</button>
        <div id="customer-result"></div>
    </div>

    <div class="test-box">
        <h2>🔗 اختبار الربط بين الصفحات</h2>
        <button class="btn" onclick="testLinking()">فحص الربط</button>
        <button class="btn" onclick="openProducts()">فتح صفحة المنتجات</button>
        <button class="btn" onclick="openCustomers()">فتح صفحة العملاء</button>
        <button class="btn" onclick="openSales()">فتح صفحة المبيعات</button>
        <div id="linking-result"></div>
    </div>

    <div class="test-box">
        <h2>🗑️ إدارة البيانات</h2>
        <button class="btn" onclick="clearAllData()">مسح جميع البيانات</button>
        <button class="btn" onclick="showAllData()">عرض جميع البيانات</button>
        <div id="data-result"></div>
    </div>

    <script>
        // اختبار إضافة جهاز عرض محمول
        function testAddProjector() {
            const projector = {
                id: Date.now(),
                name: 'جهاز عرض محمول',
                category: 'electronics',
                code: 'PROJ-001',
                price: 2500,
                cost: 2000,
                quantity: 5,
                minStock: 1,
                description: 'جهاز عرض محمول عالي الدقة للعروض التقديمية'
            };

            // حفظ في localStorage
            let products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            products.push(projector);
            localStorage.setItem('monjizProducts', JSON.stringify(products));

            // إشعار التحديث
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('product-result', `✅ تم إضافة جهاز عرض محمول بنجاح!<br>الرمز: ${projector.code}<br>السعر: ${projector.price} ر.س<br>إجمالي المنتجات: ${products.length}`, 'success');
        }

        // اختبار إضافة مطعم توباز
        function testAddTopaz() {
            const topaz = {
                id: Date.now(),
                name: 'مطعم توباز',
                type: 'company',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'الرياض، حي الملك فهد، المملكة العربية السعودية',
                createdAt: new Date().toLocaleDateString('ar-SA')
            };

            // حفظ في localStorage
            let customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            customers.push(topaz);
            localStorage.setItem('monjizCustomers', JSON.stringify(customers));

            // إشعار التحديث
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('customer-result', `✅ تم إضافة مطعم توباز بنجاح!<br>النوع: ${topaz.type}<br>الهاتف: ${topaz.phone}<br>إجمالي العملاء: ${customers.length}`, 'success');
        }

        // عرض المنتجات
        function showProducts() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            let html = `<div class="info">📦 عدد المنتجات المحفوظة: ${products.length}</div>`;
            
            if (products.length > 0) {
                html += '<ul>';
                products.forEach((product, index) => {
                    html += `<li><strong>${index + 1}.</strong> ${product.name} (${product.code}) - ${product.price} ر.س</li>`;
                });
                html += '</ul>';
            } else {
                html += '<div class="error">❌ لا توجد منتجات محفوظة</div>';
            }
            
            document.getElementById('product-result').innerHTML = html;
        }

        // عرض العملاء
        function showCustomers() {
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            let html = `<div class="info">👥 عدد العملاء المحفوظين: ${customers.length}</div>`;
            
            if (customers.length > 0) {
                html += '<ul>';
                customers.forEach((customer, index) => {
                    html += `<li><strong>${index + 1}.</strong> ${customer.name} (${customer.type}) - ${customer.phone}</li>`;
                });
                html += '</ul>';
            } else {
                html += '<div class="error">❌ لا يوجد عملاء محفوظين</div>';
            }
            
            document.getElementById('customer-result').innerHTML = html;
        }

        // اختبار الربط
        function testLinking() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let html = '<div class="info">🔍 فحص حالة البيانات:</div>';
            html += `<p>📦 المنتجات: ${products.length} عنصر</p>`;
            html += `<p>👥 العملاء: ${customers.length} عنصر</p>`;
            
            // البحث عن البيانات المطلوبة
            const hasProjector = products.some(p => p.name.includes('جهاز عرض'));
            const hasTopaz = customers.some(c => c.name.includes('توباز'));
            
            html += `<p>🎯 جهاز عرض محمول: ${hasProjector ? '✅ موجود' : '❌ غير موجود'}</p>`;
            html += `<p>🏢 مطعم توباز: ${hasTopaz ? '✅ موجود' : '❌ غير موجود'}</p>`;
            
            if (hasProjector && hasTopaz) {
                html += '<div class="success">🎉 ممتاز! البيانات جاهزة للربط بين الصفحات</div>';
                html += '<p>يمكنك الآن فتح صفحة المبيعات وستجد:</p>';
                html += '<ul><li>مطعم توباز في قائمة العملاء</li><li>جهاز عرض محمول في قائمة المنتجات</li></ul>';
            } else {
                html += '<div class="error">⚠️ يرجى إضافة جهاز عرض محمول ومطعم توباز أولاً</div>';
            }
            
            document.getElementById('linking-result').innerHTML = html;
        }

        // عرض جميع البيانات
        function showAllData() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            let html = '<div class="info">📊 إحصائيات شاملة للبيانات:</div>';
            html += `<p>📦 المنتجات: ${products.length}</p>`;
            html += `<p>👥 العملاء: ${customers.length}</p>`;
            html += `<p>🧾 الفواتير: ${invoices.length}</p>`;
            html += `<p>🏭 الموردين: ${suppliers.length}</p>`;
            
            if (products.length > 0) {
                html += `<p><strong>آخر منتج:</strong> ${products[products.length - 1].name}</p>`;
            }
            if (customers.length > 0) {
                html += `<p><strong>آخر عميل:</strong> ${customers[customers.length - 1].name}</p>`;
            }
            
            document.getElementById('data-result').innerHTML = html;
        }

        // مسح جميع البيانات
        function clearAllData() {
            localStorage.removeItem('monjizProducts');
            localStorage.removeItem('monjizCustomers');
            localStorage.removeItem('monjizInvoices');
            localStorage.removeItem('monjizSuppliers');
            localStorage.removeItem('monjizDataUpdate');
            
            showResult('data-result', '🗑️ تم مسح جميع البيانات من localStorage', 'info');
            
            // تحديث العرض
            setTimeout(() => {
                showProducts();
                showCustomers();
                testLinking();
                showAllData();
            }, 100);
        }

        // فتح الصفحات
        function openProducts() { window.open('products.html', '_blank'); }
        function openCustomers() { window.open('customers.html', '_blank'); }
        function openSales() { window.open('sales.html', '_blank'); }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            showProducts();
            showCustomers();
            testLinking();
            showAllData();
        });
    </script>
</body>
</html>
