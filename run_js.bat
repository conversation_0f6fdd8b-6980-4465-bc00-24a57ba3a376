@echo off
rem هذا الملف يساعد على تشغيل ملف JavaScript بدون مشاكل الحرف العربي

setlocal

rem تحديد المسار الكامل للملف
set "JS_FILE=%~dp0open_launcher.js"

rem التأكد من وجود الملف
if not exist "%JS_FILE%" (
    echo File not found: %JS_FILE%
    pause
    exit /b 1
)

rem محاولة تشغيل الملف باستخدام Node.js
echo Attempting to run JavaScript file with Node.js...
node "%JS_FILE%"

rem التحقق من نجاح التشغيل
if %ERRORLEVEL% neq 0 (
    echo Failed to run with Node.js. Trying direct launcher.html...
    start "" "%~dp0launcher.html"
)

endlocal
exit /b 0