<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح المنتجات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .problems-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .problems-list h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .problems-list ul {
            list-style: none;
            padding: 0;
        }
        .problems-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .problems-list li:before {
            content: "❌ ";
            color: #dc3545;
            font-weight: bold;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .steps-list {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .steps-list h3 {
            color: #0c5460;
            margin-bottom: 15px;
        }
        .steps-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .steps-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشاكل المنتجات</h1>

        <!-- المشاكل المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشاكل المكتشفة</h2>
            <div class="highlight">
                <h3>المشاكل الحالية:</h3>
                <p><strong>1. التنقل لا يعمل</strong> - نفس مشكلة الموردين</p>
                <p><strong>2. إضافة منتج جديد</strong> - النافذة تغلق عند الوصول للكمية</p>
                <p><strong>3. أيقونات الإجراءات</strong> - العرض والتعديل والحذف لا تفتح</p>
            </div>
            
            <div class="problems-list">
                <h3>تفاصيل المشاكل:</h3>
                <ul>
                    <li>دالة saveProduct تستخدم النظام القديم (productsData بدلاً من productsAllData)</li>
                    <li>دالة getProductData تحتوي على بيانات ثابتة بدلاً من البيانات الحقيقية</li>
                    <li>دوال العرض والتعديل والحذف معقدة ولا تعمل مع النظام الجديد</li>
                    <li>مشكلة في إغلاق النافذة المبكر</li>
                    <li>عدم تحديث العرض بعد الإضافة أو الحذف</li>
                </ul>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="fixes-list">
                <h3>تم إصلاح:</h3>
                <ul>
                    <li>تحديث دالة saveProduct لاستخدام النظام الجديد</li>
                    <li>إصلاح دالة getProductData للعمل مع البيانات الحقيقية</li>
                    <li>تحديث دالة confirmDeleteProduct في deleteProduct</li>
                    <li>إضافة تحديث العرض بعد كل عملية</li>
                    <li>إصلاح تحديث productsTotalItems</li>
                </ul>
            </div>
            
            <div class="problems-list">
                <h3>يحتاج إصلاح:</h3>
                <ul>
                    <li>دالة editProduct - معقدة جداً وتحتاج تبسيط</li>
                    <li>دالة viewProduct - معقدة جداً وتحتاج تبسيط</li>
                    <li>مشكلة إغلاق النافذة المبكر</li>
                </ul>
            </div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>🧪 خطوات اختبار الإصلاح</h2>
            
            <div class="steps-list">
                <h3>اختبر إضافة منتج جديد:</h3>
                <ol>
                    <li><strong>افتح صفحة المنتجات</strong></li>
                    <li><strong>اضغط "منتج جديد"</strong></li>
                    <li><strong>أدخل البيانات:</strong>
                        <ul>
                            <li>اسم المنتج: "جهاز اختبار"</li>
                            <li>الفئة: "إلكترونيات"</li>
                            <li>الكود: "TEST-001"</li>
                            <li>السعر: "500"</li>
                            <li>التكلفة: "400"</li>
                            <li>الكمية: "10"</li>
                        </ul>
                    </li>
                    <li><strong>اضغط "حفظ"</strong></li>
                    <li><strong>يجب أن يظهر:</strong> "تم إضافة المنتج بنجاح"</li>
                    <li><strong>تحقق من الجدول:</strong> المنتج الجديد يظهر في القائمة</li>
                </ol>
            </div>
            
            <div class="steps-list">
                <h3>اختبر الأيقونات:</h3>
                <ol>
                    <li><strong>اختبر العرض:</strong> اضغط أيقونة العرض (زرقاء)</li>
                    <li><strong>اختبر التعديل:</strong> اضغط أيقونة التعديل (صفراء)</li>
                    <li><strong>اختبر الحذف:</strong> اضغط أيقونة الحذف (حمراء)</li>
                    <li><strong>اختبر التنقل:</strong> تأكد من عمل التنقل بين الصفحات</li>
                </ol>
            </div>
        </div>

        <!-- اختبار سريع -->
        <div class="test-section">
            <h2>⚡ اختبار سريع</h2>
            <button class="btn" onclick="testAddProduct()">📦 اختبار إضافة المنتج</button>
            <button class="btn" onclick="testProductIcons()">🎨 اختبار الأيقونات</button>
            <button class="btn" onclick="checkProductData()">📊 فحص بيانات المنتجات</button>
            <button class="btn" onclick="resetProductData()">🔄 إعادة تعيين البيانات</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ إضافة المنتج:</strong> تعمل بدون إغلاق مبكر للنافذة</li>
                    <li><strong>✅ رسالة النجاح:</strong> "تم إضافة المنتج بنجاح"</li>
                    <li><strong>✅ ظهور في الجدول:</strong> المنتج الجديد يظهر فوراً</li>
                    <li><strong>✅ تحديث العداد:</strong> "عرض 1 - 10 من X منتج"</li>
                    <li><strong>✅ التنقل:</strong> يعمل مع البيانات الجديدة</li>
                    <li><strong>✅ أيقونة العرض:</strong> تفتح نافذة تفاصيل المنتج</li>
                    <li><strong>✅ أيقونة التعديل:</strong> تفتح نافذة تعديل المنتج</li>
                    <li><strong>✅ أيقونة الحذف:</strong> تفتح نافذة تأكيد الحذف</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار إضافة المنتج
        function testAddProduct() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="success">
                    📦 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    1️⃣ اضغط "منتج جديد"<br>
                    2️⃣ أدخل البيانات (اسم، فئة، كود، سعر، تكلفة، كمية)<br>
                    3️⃣ تأكد أن النافذة لا تغلق عند إدخال الكمية<br>
                    4️⃣ اضغط "حفظ"<br>
                    5️⃣ يجب أن تظهر رسالة "تم إضافة المنتج بنجاح"<br>
                    6️⃣ تحقق من ظهور المنتج في الجدول<br><br>
                    
                    💡 <strong>إذا أغلقت النافذة مبكراً، أعلمني!</strong>
                </div>
            `);
        }

        // اختبار الأيقونات
        function testProductIcons() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="info">
                    🎨 <strong>تم فتح صفحة المنتجات لاختبار الأيقونات!</strong><br><br>
                    
                    <strong>اختبر كل أيقونة:</strong><br>
                    🔵 أيقونة العرض (زرقاء) - يجب أن تفتح نافذة تفاصيل<br>
                    🟡 أيقونة التعديل (صفراء) - يجب أن تفتح نافذة تعديل<br>
                    🔴 أيقونة الحذف (حمراء) - يجب أن تفتح نافذة تأكيد<br><br>
                    
                    💡 <strong>إذا لم تفتح أي نافذة، أعلمني!</strong>
                </div>
            `);
        }

        // فحص بيانات المنتجات
        function checkProductData() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات المنتجات:</strong><br><br>
                    
                    📦 عدد المنتجات: ${productsData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                    📝 تنسيق البيانات: JSON<br><br>
                    
                    <strong>آخر 3 منتجات:</strong><br>
                    ${productsData.slice(-3).map((p, i) => 
                        `${productsData.length - 2 + i}. ${p.name} (${p.category})`
                    ).join('<br>')}<br><br>
                    
                    ${productsData.length > 0 ? 
                        '✅ <strong>البيانات موجودة ومحفوظة!</strong>' :
                        '❌ <strong>لا توجد بيانات - أضف منتج جديد</strong>'
                    }
                </div>
            `);
        }

        // إعادة تعيين بيانات المنتجات
        function resetProductData() {
            const productsData = [];
            for (let i = 1; i <= 25; i++) {
                productsData.push({
                    id: i,
                    code: `PRD-${String(i).padStart(3, '0')}`,
                    name: `منتج رقم ${i}`,
                    category: ['إلكترونيات', 'ملابس', 'أدوات منزلية', 'كتب'][Math.floor(Math.random() * 4)],
                    price: (Math.random() * 1000 + 50).toFixed(2),
                    cost: (Math.random() * 500 + 25).toFixed(2),
                    quantity: Math.floor(Math.random() * 100) + 1,
                    minQuantity: Math.floor(Math.random() * 10) + 1,
                    description: `وصف المنتج رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            showResult(`
                <div class="success">
                    🔄 <strong>تم إعادة تعيين بيانات المنتجات!</strong><br><br>
                    📊 تم إنشاء 25 منتج تجريبي<br>
                    📄 عدد الصفحات: 3<br><br>
                    💡 <strong>افتح صفحة المنتجات لرؤية البيانات الجديدة!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>جاري إصلاح مشاكل المنتجات!</strong><br><br>
                    
                    <strong>المشاكل المكتشفة:</strong><br>
                    ❌ التنقل لا يعمل<br>
                    ❌ إضافة منتج - النافذة تغلق مبكراً<br>
                    ❌ أيقونات الإجراءات لا تفتح<br><br>
                    
                    <strong>الإصلاحات المطبقة:</strong><br>
                    ✅ تحديث دالة saveProduct<br>
                    ✅ إصلاح دالة getProductData<br>
                    ✅ تحديث دالة الحذف<br><br>
                    
                    🧪 <strong>اختبر المنتجات الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
