<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل المبيعات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .fix-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .btn.warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            box-shadow: 0 2px 5px rgba(255,193,7,0.3);
        }
        .btn.danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            box-shadow: 0 2px 5px rgba(220,53,69,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #28a745;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .data-table th {
            background: #28a745;
            color: white;
            font-weight: bold;
        }
        .data-table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشاكل المبيعات</h1>

        <!-- فحص البيانات الحالية -->
        <div class="fix-section">
            <h2>📊 فحص البيانات الحالية</h2>
            <button class="btn primary" onclick="checkCurrentData()">فحص البيانات</button>
            <button class="btn" onclick="restoreCustomers()">استعادة العملاء المفقودين</button>
            <div id="data-status"></div>
        </div>

        <!-- إصلاح المشاكل -->
        <div class="fix-section">
            <h2>🛠️ إصلاح المشاكل</h2>
            <button class="btn" onclick="fixAllIssues()">إصلاح جميع المشاكل</button>
            <button class="btn warning" onclick="resetAllData()">إعادة تعيين البيانات</button>
            <button class="btn primary" onclick="addCompleteTestData()">إضافة بيانات اختبار كاملة</button>
            <div id="fix-result"></div>
        </div>

        <!-- عرض البيانات -->
        <div class="fix-section">
            <h2>📋 عرض البيانات</h2>
            <button class="btn primary" onclick="showCustomersTable()">عرض جدول العملاء</button>
            <button class="btn primary" onclick="showProductsTable()">عرض جدول المنتجات</button>
            <div id="data-display"></div>
        </div>

        <!-- اختبار المبيعات -->
        <div class="fix-section">
            <h2>🧪 اختبار المبيعات</h2>
            <button class="btn" onclick="testSalesSystem()">اختبار النظام</button>
            <button class="btn primary" onclick="openSalesPage()">فتح صفحة المبيعات</button>
            <div id="test-result"></div>
        </div>
    </div>

    <script>
        // فحص البيانات الحالية
        function checkCurrentData() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            let result = '<div class="info">📊 حالة البيانات الحالية:</div>';
            result += `<p><strong>المنتجات:</strong> ${products.length} عنصر</p>`;
            result += `<p><strong>العملاء:</strong> ${customers.length} عنصر</p>`;
            result += `<p><strong>الفواتير:</strong> ${invoices.length} عنصر</p>`;
            
            if (customers.length < 4) {
                result += '<div class="error">⚠️ عدد العملاء قليل - قد تكون هناك مشكلة</div>';
            }
            
            if (products.length < 3) {
                result += '<div class="error">⚠️ عدد المنتجات قليل - قد تكون هناك مشكلة</div>';
            }
            
            if (customers.length >= 4 && products.length >= 3) {
                result += '<div class="success">✅ البيانات تبدو جيدة</div>';
            }
            
            document.getElementById('data-status').innerHTML = result;
        }

        // استعادة العملاء المفقودين
        function restoreCustomers() {
            const currentCustomers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            // العملاء الأساسيين الذين يجب أن يكونوا موجودين
            const essentialCustomers = [
                { id: 'cust-1', name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') },
                { id: 'cust-2', name: 'شركة الأنوار للتجارة', type: 'company', phone: '+966502345678', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') },
                { id: 'cust-3', name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') },
                { id: 'cust-4', name: 'مؤسسة النجاح التجارية', type: 'company', phone: '+966504567890', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') },
                { id: 'cust-5', name: 'فاطمة السعد', type: 'individual', phone: '+966505678901', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') },
                { id: 'cust-6', name: 'مكتب الاستشارات القانونية', type: 'company', phone: '+966506789012', email: '<EMAIL>', address: 'الرياض، المملكة العربية السعودية', createdAt: new Date().toLocaleDateString('ar-SA') }
            ];

            // دمج العملاء الحاليين مع الأساسيين
            const allCustomers = [...currentCustomers];
            let addedCount = 0;

            essentialCustomers.forEach(essential => {
                const exists = currentCustomers.find(c => c.id === essential.id || c.name === essential.name);
                if (!exists) {
                    allCustomers.push(essential);
                    addedCount++;
                }
            });

            localStorage.setItem('monjizCustomers', JSON.stringify(allCustomers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('data-status', `✅ تم استعادة العملاء!<br>تم إضافة ${addedCount} عميل جديد<br>إجمالي العملاء: ${allCustomers.length}`, 'success');
        }

        // إصلاح جميع المشاكل
        function fixAllIssues() {
            console.log('🔧 بدء إصلاح جميع المشاكل...');
            
            // استعادة العملاء
            restoreCustomers();
            
            // إضافة منتجات إذا كانت قليلة
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            if (products.length < 3) {
                addCompleteTestData();
            }
            
            // إرسال إشعار التحديث
            localStorage.setItem('monjizDataUpdate', Date.now().toString());
            
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.postMessage({ type: 'data-updated', timestamp: Date.now() });
            }

            setTimeout(() => {
                checkCurrentData();
                showResult('fix-result', '✅ تم إصلاح جميع المشاكل بنجاح!<br>🔄 تم إرسال إشعار التحديث<br>💡 يمكنك الآن فتح صفحة المبيعات', 'success');
            }, 500);
        }

        // إعادة تعيين البيانات
        function resetAllData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟\nسيتم حذف جميع المنتجات والعملاء والفواتير الحالية')) {
                localStorage.removeItem('monjizProducts');
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizInvoices');
                
                addCompleteTestData();
                
                showResult('fix-result', '🔄 تم إعادة تعيين البيانات بنجاح', 'info');
                setTimeout(checkCurrentData, 500);
            }
        }

        // إضافة بيانات اختبار كاملة
        function addCompleteTestData() {
            // إضافة منتجات
            const testProducts = [
                { id: 'prod-1', name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000, quantity: 10, minStock: 2, description: 'جهاز عرض محمول عالي الدقة' },
                { id: 'prod-2', name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2, description: 'لابتوب Dell XPS 15 بمعالج Intel Core i7' },
                { id: 'prod-3', name: 'هاتف آيفون 15 Pro', category: 'phones', code: 'IPHONE-15P', price: 5200, cost: 4500, quantity: 5, minStock: 1, description: 'هاتف آيفون 15 Pro بذاكرة 256 جيجا' },
                { id: 'prod-4', name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5, description: 'كيبورد ميكانيكي للألعاب' },
                { id: 'prod-5', name: 'شاشة سامسونج 32 بوصة', category: 'electronics', code: 'SAM-32', price: 1800, cost: 1400, quantity: 6, minStock: 2, description: 'شاشة سامسونج 32 بوصة 4K' }
            ];

            localStorage.setItem('monjizProducts', JSON.stringify(testProducts));

            // إضافة عملاء
            restoreCustomers();

            localStorage.setItem('monjizDataUpdate', Date.now().toString());
            
            showResult('fix-result', '✅ تم إضافة بيانات اختبار كاملة!<br>📦 5 منتجات<br>👥 6 عملاء', 'success');
        }

        // عرض جدول العملاء
        function showCustomersTable() {
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let html = `<h3>👥 جدول العملاء (${customers.length} عميل)</h3>`;
            
            if (customers.length > 0) {
                html += '<table class="data-table">';
                html += '<thead><tr><th>المعرف</th><th>الاسم</th><th>النوع</th><th>الهاتف</th><th>البريد الإلكتروني</th></tr></thead>';
                html += '<tbody>';
                
                customers.forEach(customer => {
                    html += `<tr>
                        <td>${customer.id}</td>
                        <td>${customer.name}</td>
                        <td>${customer.type === 'company' ? 'شركة' : 'فرد'}</td>
                        <td>${customer.phone}</td>
                        <td>${customer.email}</td>
                    </tr>`;
                });
                
                html += '</tbody></table>';
            } else {
                html += '<div class="error">❌ لا يوجد عملاء</div>';
            }
            
            document.getElementById('data-display').innerHTML = html;
        }

        // عرض جدول المنتجات
        function showProductsTable() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            let html = `<h3>📦 جدول المنتجات (${products.length} منتج)</h3>`;
            
            if (products.length > 0) {
                html += '<table class="data-table">';
                html += '<thead><tr><th>المعرف</th><th>الاسم</th><th>الفئة</th><th>الرمز</th><th>السعر</th><th>الكمية</th></tr></thead>';
                html += '<tbody>';
                
                products.forEach(product => {
                    html += `<tr>
                        <td>${product.id}</td>
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td>${product.code}</td>
                        <td>${product.price} ر.س</td>
                        <td>${product.quantity}</td>
                    </tr>`;
                });
                
                html += '</tbody></table>';
            } else {
                html += '<div class="error">❌ لا يوجد منتجات</div>';
            }
            
            document.getElementById('data-display').innerHTML = html;
        }

        // اختبار النظام
        function testSalesSystem() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">🧪 نتائج اختبار النظام:</div>';
            
            if (products.length >= 3 && customers.length >= 4) {
                result += '<div class="success">✅ النظام جاهز للاستخدام</div>';
                result += `<p>📦 المنتجات: ${products.length} (كافية)</p>`;
                result += `<p>👥 العملاء: ${customers.length} (كافية)</p>`;
                result += '<p>💡 يمكنك الآن إنشاء فواتير بدون مشاكل</p>';
            } else {
                result += '<div class="error">❌ النظام يحتاج إصلاح</div>';
                result += `<p>📦 المنتجات: ${products.length} (يحتاج 3 على الأقل)</p>`;
                result += `<p>👥 العملاء: ${customers.length} (يحتاج 4 على الأقل)</p>`;
                result += '<p>💡 اضغط "إصلاح جميع المشاكل" أولاً</p>';
            }
            
            document.getElementById('test-result').innerHTML = result;
        }

        // فتح صفحة المبيعات
        function openSalesPage() {
            window.open('sales.html', '_blank');
            showResult('test-result', '🚀 تم فتح صفحة المبيعات<br>💡 جرب إنشاء فاتورة جديدة الآن', 'info');
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            checkCurrentData();
        });
    </script>
</body>
</html>
