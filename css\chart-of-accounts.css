/* تنسيقات دليل الحسابات المحسن */

/* الحاوي الرئيسي */
.chart-of-accounts-modern {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* رأس القسم المحسن */
.section-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* الأزرار المحسنة */
.btn-modern {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-modern.primary {
    background: #4CAF50;
    color: white;
}

.btn-modern.primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-modern.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-modern.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* شريط الأدوات */
.accounts-toolbar {
    padding: 24px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.search-section {
    flex: 1;
    max-width: 400px;
}

.search-box-modern {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box-modern i {
    position: absolute;
    right: 15px;
    color: #6c757d;
    z-index: 2;
}

.search-box-modern input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.search-box-modern input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-section {
    display: flex;
    gap: 12px;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* عرض الحسابات */
.accounts-display {
    padding: 30px;
}

/* تم حذف CSS الخاص بالمربعات الكبيرة حسب طلب المستخدم */

/* جدول الحسابات المحسن */
.accounts-table-modern {
    background: white;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    overflow: hidden;
}

.table-header {
    background: #f8f9fa;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.table-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    background: #e9ecef;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.table-container {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.modern-table th {
    background: #f8f9fa;
    padding: 16px 20px;
    text-align: right;
    font-weight: 700;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
}

.modern-table td {
    padding: 16px 20px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
    background: #f8f9fa;
}

.account-name-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.account-type-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-assets { background: #e8f5e8; color: #2e7d32; }
.badge-liabilities { background: #ffebee; color: #c62828; }
.badge-equity { background: #f3e5f5; color: #7b1fa2; }
.badge-revenue { background: #e3f2fd; color: #1565c0; }
.badge-expenses { background: #fff3e0; color: #ef6c00; }

.balance-cell {
    font-weight: 700;
    text-align: left;
}

.balance-positive { color: #2e7d32; }
.balance-negative { color: #c62828; }
.balance-zero { color: #6c757d; }

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
}

.status-active {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-inactive {
    background: #ffebee;
    color: #c62828;
}

.actions-cell {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.action-btn.edit {
    background: #e3f2fd;
    color: #1565c0;
}

.action-btn.edit:hover {
    background: #1565c0;
    color: white;
}

.action-btn.delete {
    background: #ffebee;
    color: #c62828;
}

.action-btn.delete:hover {
    background: #c62828;
    color: white;
}

/* النافذة المنبثقة المحسنة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
}

.modal-overlay.active {
    display: flex;
}

.modal-container.modern {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.modal-body {
    padding: 30px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

/* النموذج المحسن */
.modern-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.form-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* تجاوبية */
@media (max-width: 768px) {
    .accounts-toolbar {
        flex-direction: column;
        gap: 16px;
    }

    .search-section {
        max-width: none;
    }

    .filter-section {
        width: 100%;
        justify-content: space-between;
    }



    .section-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .modal-container.modern {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .modal-body {
        padding: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }
}
