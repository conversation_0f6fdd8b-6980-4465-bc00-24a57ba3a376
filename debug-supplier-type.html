<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة نوع المورد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .debug-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #dc3545;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(220,53,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #dc3545;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .debug-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .critical-box {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 تشخيص مشكلة نوع المورد</h1>

        <!-- المشكلة الحرجة -->
        <div class="debug-section">
            <h2>🔥 المشكلة الحرجة</h2>
            <div class="critical-box">
                <h3>المشكلة المستمرة:</h3>
                <p><strong>"بعد إكمال التسجيل تأتي رسالة يرجى اختيار نوع المورد من القائمة المنسدلة"</strong></p>
                
                <h4>هذا يعني:</h4>
                <ul>
                    <li>❌ المستخدم يختار نوع المورد لكن النظام لا يقرأ القيمة</li>
                    <li>❌ هناك مشكلة في JavaScript في قراءة قيمة select</li>
                    <li>❌ قد تكون مشكلة في timing أو DOM</li>
                    <li>❌ قد تكون مشكلة في event handling</li>
                </ul>
            </div>
        </div>

        <!-- التشخيص المفصل -->
        <div class="debug-section">
            <h2>🔍 التشخيص المفصل</h2>
            <button class="btn" onclick="openPurchasesWithDebug()">فتح المشتريات مع التشخيص المفصل</button>
            <button class="btn primary" onclick="showDebugInstructions()">عرض تعليمات التشخيص</button>
            <div id="debug-result"></div>
            
            <div class="step-list">
                <h3>خطوات التشخيص الدقيق:</h3>
                <ol>
                    <li><strong>افتح صفحة المشتريات</strong></li>
                    <li><strong>افتح Developer Tools (F12)</strong></li>
                    <li><strong>اذهب لتبويب Console</strong></li>
                    <li><strong>اضغط "مورد جديد"</strong></li>
                    <li><strong>املأ الحقول بالترتيب:</strong>
                        <ul>
                            <li>اختر نوع المورد: "شركة"</li>
                            <li>أدخل اسم المورد: "شركة الاختبار"</li>
                            <li>أدخل رقم الهاتف: "0501234567"</li>
                        </ul>
                    </li>
                    <li><strong>اضغط "حفظ المورد"</strong></li>
                    <li><strong>راقب Console للرسائل التالية:</strong>
                        <ul>
                            <li>"=== بدء معالجة إرسال نموذج المورد ==="</li>
                            <li>"فحص وجود العناصر:"</li>
                            <li>"قيم العناصر الفعلية:"</li>
                            <li>"خيارات نوع المورد:"</li>
                            <li>"التحقق من صحة البيانات:"</li>
                            <li>"قيمة نوع المورد:"</li>
                        </ul>
                    </li>
                    <li><strong>إذا ظهرت المشكلة، انسخ جميع الرسائل من Console</strong></li>
                </ol>
            </div>
        </div>

        <!-- الحلول المحتملة -->
        <div class="debug-section">
            <h2>💡 الحلول المحتملة</h2>
            <div class="highlight">
                <h3>إذا كانت المشكلة في قراءة القيمة:</h3>
                <ul>
                    <li><strong>مشكلة Timing:</strong> النموذج يُرسل قبل تحديث القيمة</li>
                    <li><strong>مشكلة Event:</strong> event listener لا يعمل بشكل صحيح</li>
                    <li><strong>مشكلة DOM:</strong> العنصر غير موجود أو ID خاطئ</li>
                    <li><strong>مشكلة Validation:</strong> التحقق يحدث على القيمة القديمة</li>
                </ul>
                
                <h3>الحلول المقترحة:</h3>
                <ol>
                    <li><strong>إضافة delay قبل التحقق</strong></li>
                    <li><strong>استخدام event delegation</strong></li>
                    <li><strong>التحقق من selectedIndex بدلاً من value</strong></li>
                    <li><strong>إضافة event listener للتغيير</strong></li>
                </ol>
            </div>
        </div>

        <!-- اختبار سريع -->
        <div class="debug-section">
            <h2>⚡ اختبار سريع</h2>
            <button class="btn success" onclick="createTestForm()">إنشاء نموذج اختبار</button>
            <button class="btn primary" onclick="testFormValidation()">اختبار التحقق</button>
            <div id="test-form-container"></div>
            <div id="test-result"></div>
        </div>

        <!-- سجل التشخيص -->
        <div class="debug-section">
            <h2>📋 سجل التشخيص</h2>
            <button class="btn primary" onclick="clearDebugLog()">مسح السجل</button>
            <div id="debug-log" class="debug-log">انتظار عمليات التشخيص...</div>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            updateDebugLog();
            console.log(logEntry);
        }

        function updateDebugLog() {
            document.getElementById('debug-log').textContent = debugLog.join('\n');
        }

        function clearDebugLog() {
            debugLog = [];
            updateDebugLog();
        }

        // فتح المشتريات مع التشخيص
        function openPurchasesWithDebug() {
            log('🚀 فتح صفحة المشتريات للتشخيص المفصل');
            window.open('purchases.html', '_blank');
            showResult('debug-result', '🚀 تم فتح صفحة المشتريات<br>💡 افتح Developer Tools (F12) واتبع خطوات التشخيص<br>🔍 راقب Console للرسائل التشخيصية المفصلة', 'info');
        }

        // عرض تعليمات التشخيص
        function showDebugInstructions() {
            let instructions = `
🔍 تعليمات التشخيص المفصل:

1. افتح صفحة المشتريات
2. اضغط F12 لفتح Developer Tools
3. اذهب لتبويب Console
4. اضغط "مورد جديد"
5. املأ الحقول:
   - نوع المورد: شركة
   - اسم المورد: شركة الاختبار
   - رقم الهاتف: 0501234567
6. اضغط "حفظ المورد"
7. راقب الرسائل في Console

الرسائل المتوقعة:
- "=== بدء معالجة إرسال نموذج المورد ==="
- "فحص وجود العناصر:"
- "قيم العناصر الفعلية:"
- "نوع المورد: "شركة""
- "خيارات نوع المورد:"
- "التحقق من صحة البيانات:"

إذا ظهرت رسالة خطأ، انسخ جميع الرسائل من Console.
            `;
            
            showResult('debug-result', `<pre style="text-align: right; white-space: pre-wrap;">${instructions}</pre>`, 'info');
        }

        // إنشاء نموذج اختبار
        function createTestForm() {
            log('📝 إنشاء نموذج اختبار محلي');
            
            const formHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 2px solid #007bff;">
                    <h4>نموذج اختبار نوع المورد</h4>
                    <form id="test-supplier-form">
                        <div style="margin: 10px 0;">
                            <label for="test-supplier-type">نوع المورد:</label>
                            <select id="test-supplier-type" style="width: 200px; padding: 5px; margin: 5px;">
                                <option value="">اختر نوع المورد</option>
                                <option value="individual">فرد</option>
                                <option value="company">شركة</option>
                            </select>
                        </div>
                        <div style="margin: 10px 0;">
                            <label for="test-supplier-name">اسم المورد:</label>
                            <input type="text" id="test-supplier-name" style="width: 200px; padding: 5px; margin: 5px;" placeholder="أدخل اسم المورد">
                        </div>
                        <div style="margin: 10px 0;">
                            <label for="test-supplier-phone">رقم الهاتف:</label>
                            <input type="tel" id="test-supplier-phone" style="width: 200px; padding: 5px; margin: 5px;" placeholder="0501234567">
                        </div>
                        <button type="button" onclick="testFormValidation()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 10px 5px;">
                            اختبار التحقق
                        </button>
                        <button type="button" onclick="fillTestData()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 10px 5px;">
                            ملء بيانات تجريبية
                        </button>
                    </form>
                </div>
            `;
            
            document.getElementById('test-form-container').innerHTML = formHTML;
            log('✅ تم إنشاء نموذج الاختبار');
        }

        // ملء بيانات تجريبية
        function fillTestData() {
            log('📝 ملء بيانات تجريبية');
            
            const typeSelect = document.getElementById('test-supplier-type');
            const nameInput = document.getElementById('test-supplier-name');
            const phoneInput = document.getElementById('test-supplier-phone');
            
            if (typeSelect && nameInput && phoneInput) {
                typeSelect.value = 'company';
                nameInput.value = 'شركة الاختبار';
                phoneInput.value = '0501234567';
                
                log('✅ تم ملء البيانات التجريبية');
                log(`نوع المورد: "${typeSelect.value}"`);
                log(`اسم المورد: "${nameInput.value}"`);
                log(`رقم الهاتف: "${phoneInput.value}"`);
            } else {
                log('❌ لم يتم العثور على عناصر النموذج');
            }
        }

        // اختبار التحقق من النموذج
        function testFormValidation() {
            log('🧪 بدء اختبار التحقق من النموذج');
            
            const typeElement = document.getElementById('test-supplier-type');
            const nameElement = document.getElementById('test-supplier-name');
            const phoneElement = document.getElementById('test-supplier-phone');
            
            if (!typeElement || !nameElement || !phoneElement) {
                log('❌ لم يتم العثور على عناصر النموذج');
                showResult('test-result', '❌ يرجى إنشاء نموذج الاختبار أولاً', 'error');
                return;
            }
            
            log('فحص وجود العناصر:');
            log(`- typeElement: ${typeElement ? 'موجود' : 'غير موجود'}`);
            log(`- nameElement: ${nameElement ? 'موجود' : 'غير موجود'}`);
            log(`- phoneElement: ${phoneElement ? 'موجود' : 'غير موجود'}`);
            
            log('قيم العناصر الفعلية:');
            log(`- نوع المورد: "${typeElement.value}" (selectedIndex: ${typeElement.selectedIndex})`);
            log(`- اسم المورد: "${nameElement.value}"`);
            log(`- رقم الهاتف: "${phoneElement.value}"`);
            
            log('خيارات نوع المورد:');
            for (let i = 0; i < typeElement.options.length; i++) {
                const option = typeElement.options[i];
                log(`  ${i}: value="${option.value}", text="${option.text}", selected=${option.selected}`);
            }
            
            // جمع البيانات
            const supplierData = {
                type: typeElement.value,
                name: nameElement.value,
                phone: phoneElement.value
            };
            
            log('البيانات المجمعة:');
            log(JSON.stringify(supplierData, null, 2));
            
            // التحقق من صحة البيانات
            log('التحقق من صحة البيانات:');
            log(`قيمة نوع المورد: "${supplierData.type}"`);
            log(`طول قيمة نوع المورد: ${supplierData.type ? supplierData.type.length : 'null/undefined'}`);
            log(`نوع البيانات: ${typeof supplierData.type}`);
            
            if (!supplierData.type || supplierData.type === '' || supplierData.type.trim() === '') {
                log('❌ خطأ: لم يتم اختيار نوع المورد');
                log(`القيمة الفعلية: "${supplierData.type}"`);
                showResult('test-result', '❌ يرجى اختيار نوع المورد من القائمة المنسدلة', 'error');
                return;
            }
            
            log('✅ نوع المورد صحيح');
            
            if (!supplierData.name || !supplierData.name.trim()) {
                log('❌ خطأ: لم يتم إدخال اسم المورد');
                showResult('test-result', '❌ يرجى إدخال اسم المورد', 'error');
                return;
            }
            
            log('✅ اسم المورد صحيح');
            
            if (!supplierData.phone || !supplierData.phone.trim()) {
                log('❌ خطأ: لم يتم إدخال رقم الهاتف');
                showResult('test-result', '❌ يرجى إدخال رقم الهاتف', 'error');
                return;
            }
            
            log('✅ رقم الهاتف صحيح');
            log('🎉 جميع البيانات صحيحة!');
            
            showResult('test-result', '✅ نجح الاختبار! جميع البيانات صحيحة<br>📊 راجع سجل التشخيص للتفاصيل', 'success');
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            log('🔧 بدء تشخيص مشكلة نوع المورد');
            createTestForm();
        });
    </script>
</body>
</html>
