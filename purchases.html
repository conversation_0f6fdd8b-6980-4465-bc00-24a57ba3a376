<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المشتريات - نظام إدارة الأعمال</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accounting.css">
    <link rel="stylesheet" href="css/accounting-fix.css">
    <link rel="stylesheet" href="css/chart-of-accounts.css">
    <link rel="stylesheet" href="css/purchases.css">
    <link rel="stylesheet" href="css/action-buttons-horizontal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        /* إجبار الأيقونات على أن تكون في صف واحد أفقي */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
            width: 100% !important;
            min-width: 120px !important;
            box-sizing: border-box !important;
        }

        .action-buttons-horizontal .action-btn {
            width: 30px !important;
            height: 30px !important;
            min-width: 30px !important;
            max-width: 30px !important;
            border: none !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s ease !important;
            font-size: 12px !important;
            margin: 0 !important;
            padding: 0 !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
            float: none !important;
            position: static !important;
        }

        .action-buttons-horizontal .action-btn.edit {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.edit:hover {
            background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(255, 193, 7, 0.4) !important;
        }

        .action-buttons-horizontal .action-btn.view {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.view:hover {
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(23, 162, 184, 0.4) !important;
        }

        .action-buttons-horizontal .action-btn.delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.delete:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4) !important;
        }

        /* تحسينات إضافية للجدول - مسافات مقللة */
        .modern-table {
            font-size: 0.9rem;
            border-collapse: collapse;
        }

        .modern-table td {
            padding: 8px 6px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }

        .modern-table tbody tr {
            height: auto;
            min-height: 45px;
        }

        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            min-width: 50px;
            display: inline-block;
        }

        .badge.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .badge.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .badge.danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* تحسين زر فاتورة جديدة */
        .add-purchase-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .add-purchase-btn:hover {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        /* أنماط أيقونات الإجراءات */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
            align-items: center;
        }

        .btn-action {
            border: none;
            padding: 8px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-view {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-view:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
        }

        .btn-edit {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #e0a800, #d39e00);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #c82333, #bd2130);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        /* تحسينات إضافية للمسافات */
        .table-container-modern {
            margin-top: 15px;
        }

        .modern-table th {
            padding: 10px 6px;
            font-size: 0.85rem;
            font-weight: 600;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .modern-table strong {
            font-weight: 600;
            color: #495057;
        }

        /* تحسين عرض المبالغ */
        .modern-table td:nth-child(4) {
            font-weight: 600;
            color: #28a745;
        }

        /* تحسينات إضافية للمظهر المدمج */
        .purchases-table {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .purchases-table thead tr {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .purchases-table tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .purchases-table tbody tr:hover {
            background-color: #e3f2fd !important;
            transform: scale(1.001);
            transition: all 0.2s ease;
        }

        /* أنماط القائمة المنسدلة */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 8px;
            border: 1px solid #ddd;
            top: 100%;
            right: 0;
        }

        .dropdown-menu a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }

        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }

        .dropdown-menu a i {
            margin-left: 8px;
            width: 16px;
        }

        .dropdown-toggle {
            cursor: pointer;
        }

        /* أنماط القائمة المنسدلة */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 8px;
            border: 1px solid #ddd;
            top: 100%;
            right: 0;
        }

        .dropdown-menu a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }

        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }

        .dropdown-menu a i {
            margin-left: 8px;
            width: 16px;
        }

        .dropdown-toggle {
            cursor: pointer;
        }

        /* أنماط التنقل بين الصفحات */
        .table-footer-modern {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            border-radius: 0 0 12px 12px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .results-info-modern {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
        }

        .pagination-modern {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .page-btn {
            background: white;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        .page-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .page-btn.disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .page-btn.disabled:hover {
            background: #f8f9fa;
            transform: none;
        }

        .page-btn.prev,
        .page-btn.next {
            padding: 8px 16px;
            font-weight: 600;
        }

        .page-btn i {
            margin: 0 4px;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .table-footer-modern {
                flex-direction: column;
                gap: 15px;
            }

            .pagination-modern {
                justify-content: center;
            }

            .page-btn {
                min-width: 35px;
                height: 35px;
                padding: 6px 10px;
                font-size: 13px;
            }
        }

        /* أنماط النافذة المنبثقة الموحدة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group select {
            background: white;
            cursor: pointer;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-primary, .btn-secondary {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }

        /* تحسين الأيقونات مع تأثيرات أفضل */
        .action-btn:hover {
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        /* تقليل المسافات في الخلايا */
        .modern-table td:first-child,
        .modern-table th:first-child {
            padding-right: 12px;
        }

        .modern-table td:last-child,
        .modern-table th:last-child {
            padding-left: 12px;
            width: 120px;
        }

        /* تحسين عرض الأرقام */
        .modern-table td:first-child strong {
            color: #007bff;
            font-size: 0.9rem;
        }

        /* CSS إضافي قوي جداً لضمان الأيقونات الأفقية */
        table .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
        }

        table .action-buttons-horizontal .action-btn {
            display: inline-block !important;
            float: left !important;
            margin-left: 5px !important;
        }

        /* إزالة أي CSS قد يسبب العرض العمودي */
        .action-btn {
            display: inline-block !important;
            vertical-align: middle !important;
        }

        /* طريقة بديلة لضمان الترتيب الأفقي */
        .action-buttons-horizontal .action-btn:nth-child(1) { order: 1; }
        .action-buttons-horizontal .action-btn:nth-child(2) { order: 2; }
        .action-buttons-horizontal .action-btn:nth-child(3) { order: 3; }

        /* إجبار الـ flexbox */
        .action-buttons-horizontal {
            white-space: nowrap !important;
        }

        .action-buttons-horizontal .action-btn {
            white-space: nowrap !important;
            min-width: 30px !important;
        }

        /* CSS إضافي قوي لمنع الانكسار */
        .action-buttons-horizontal {
            overflow: visible !important;
            min-height: 40px !important;
        }

        /* إجبار عرض الخلية */
        .modern-table td:last-child {
            width: 130px !important;
            min-width: 130px !important;
            max-width: 130px !important;
        }

        /* أنماط النوافذ المنبثقة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content-modern {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header-modern h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body-modern {
            padding: 20px;
        }

        .form-modern {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .input-modern, .select-modern, .textarea-modern {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.2s;
        }

        .input-modern:focus, .select-modern:focus, .textarea-modern:focus {
            outline: none;
            border-color: #007bff;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .btn-modern {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-modern.btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-modern.btn-primary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
        }

        .btn-modern.btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-modern.btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        /* تفاصيل الفاتورة */
        .invoice-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .invoice-details h4 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-row .label {
            font-weight: 600;
            color: #666;
        }

        .detail-row .value {
            color: #333;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html" class="active"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="suppliers.html"><i class="fas fa-user-tie"></i> الموردين</a></li>
                    <li><a href="products.html"><i class="fas fa-boxes"></i> المخزون</a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- محتوى الصفحة -->
    <main class="main-content">
        <div class="container">
            <!-- قسم إدارة المشتريات -->
            <div class="products-management-modern">
                <!-- رأس القسم -->
                <div class="section-header-modern">
                    <div class="header-title">
                        <h3><i class="fas fa-truck"></i> إدارة المشتريات</h3>
                        <p>إدارة فواتير المشتريات والموردين والمخزون</p>
                    </div>
                    <div class="header-actions">
                        <div class="dropdown">
                            <button class="btn-modern btn-secondary dropdown-toggle" onclick="toggleDropdown('print-export-dropdown')">
                                <i class="fas fa-print"></i>
                                طباعة وتصدير
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="print-export-dropdown">
                                <a href="#" onclick="printPurchases()"><i class="fas fa-print"></i> طباعة التقرير</a>
                                <a href="#" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> تصدير Excel</a>
                                <a href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf"></i> تصدير PDF</a>
                            </div>
                        </div>
                        <button class="btn-modern btn-success add-supplier-btn" onclick="showAddSupplierModal()">
                            <i class="fas fa-user-plus"></i>
                            مورد جديد
                        </button>
                        <button class="btn-modern btn-primary add-purchase-btn" onclick="showAddPurchaseModal()">
                            <i class="fas fa-plus"></i>
                            فاتورة جديدة
                        </button>
                    </div>
                </div>

                <!-- البطاقات الإحصائية -->
                <div class="stats-section-modern">
                    <div class="stats-grid-modern">
                        <div class="stat-card-modern primary">
                            <div class="stat-icon-modern">
                                <i class="fas fa-file-invoice-dollar"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>إجمالي المشتريات</h4>
                                <div class="stat-value-modern">54,320 <span>ر.س</span></div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +15% هذا الشهر
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern success">
                            <div class="stat-icon-modern">
                                <i class="fas fa-truck-loading"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>عدد الفواتير</h4>
                                <div class="stat-value-modern">28</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +8 هذا الشهر
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern info">
                            <div class="stat-icon-modern">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>عدد الموردين</h4>
                                <div class="stat-value-modern">12</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +2 موردين جدد
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern warning">
                            <div class="stat-icon-modern">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>المنتجات المشتراة</h4>
                                <div class="stat-value-modern">156</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +24 هذا الشهر
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات البحث والتصفية -->
                <div class="search-filters-modern">
                    <div class="search-box-modern">
                        <i class="fas fa-search"></i>
                        <input type="text" id="purchase-search" placeholder="البحث في الفواتير..." class="input-modern">
                        <div class="search-suggestions" id="search-suggestions"></div>
                    </div>
                    <div class="filters-modern">
                        <select id="date-filter" class="filter-select-modern">
                            <option value="all">جميع التواريخ</option>
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                        </select>
                        <select id="supplier-filter" class="filter-select-modern">
                            <option value="all">جميع الموردين</option>
                            <!-- سيتم ملء هذه القائمة ديناميكياً -->
                        </select>
                        <select id="status-filter" class="filter-select-modern">
                            <option value="all">جميع الحالات</option>
                            <option value="paid">مدفوع</option>
                            <option value="pending">معلق</option>
                        </select>
                        <button id="reset-filter" class="btn-modern btn-outline">
                            <i class="fas fa-times"></i> مسح التصفية
                        </button>
                    </div>
                </div>

            <!-- جدول المشتريات -->
            <div class="table-container-modern">
                <table class="modern-table purchases-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-hashtag"></i> رقم الفاتورة</th>
                            <th><i class="fas fa-calendar"></i> التاريخ</th>
                            <th><i class="fas fa-user-tie"></i> المورد</th>
                            <th><i class="fas fa-money-bill-wave"></i> المبلغ</th>
                            <th><i class="fas fa-info-circle"></i> الحالة</th>
                            <th><i class="fas fa-cogs"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="purchases-list">
                        <tr>
                            <td><strong>PUR-001</strong></td>
                            <td>2023-12-15</td>
                            <td>شركة الأغذية المتحدة</td>
                            <td><strong>12,450 ر.س</strong></td>
                            <td><span class="badge success">مدفوع</span></td>
                            <td>
                                <div class="action-buttons-horizontal">
                                    <button class="action-btn edit" onclick="editPurchase('PUR-001')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="viewPurchase('PUR-001')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="deletePurchase('PUR-001')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>PUR-002</strong></td>
                            <td>2023-12-14</td>
                            <td>مؤسسة الخضار الطازجة</td>
                            <td><strong>8,750 ر.س</strong></td>
                            <td><span class="badge warning">معلق</span></td>
                            <td>
                                <div class="action-buttons-horizontal">
                                    <button class="action-btn edit" onclick="editPurchase('PUR-002')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="viewPurchase('PUR-002')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="deletePurchase('PUR-002')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- تذييل الجدول -->
                <div class="table-footer-modern">
                    <div class="results-info-modern">
                        عرض <span id="start-result">1</span> - <span id="end-result">10</span> من <span id="total-results">0</span> نتيجة
                    </div>
                    <div class="pagination-modern" id="pagination-container">
                        <button class="page-btn prev" id="prev-btn" onclick="changePage('prev')">
                            <i class="fas fa-chevron-right"></i> السابق
                        </button>
                        <button class="page-btn active" onclick="changePage(1)">1</button>
                        <button class="page-btn" onclick="changePage(2)">2</button>
                        <button class="page-btn" onclick="changePage(3)">3</button>
                        <button class="page-btn next" id="next-btn" onclick="changePage('next')">
                            التالي <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- تذييل الصفحة -->
    <footer class="main-footer">
        <p>جميع الحقوق محفوظة &copy; 2023 نظام إدارة الأعمال</p>
    </footer>

    <!-- ربط ملف JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/purchases.js"></script>

    <script>
        // إجبار الأيقونات على أن تكون أفقية
        document.addEventListener('DOMContentLoaded', function() {
            function forceHorizontalButtons() {
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    // إجبار الـ flexbox في صف واحد
                    container.style.setProperty('display', 'flex', 'important');
                    container.style.setProperty('flex-direction', 'row', 'important');
                    container.style.setProperty('flex-wrap', 'nowrap', 'important');
                    container.style.setProperty('gap', '6px', 'important');
                    container.style.setProperty('justify-content', 'center', 'important');
                    container.style.setProperty('align-items', 'center', 'important');
                    container.style.setProperty('padding', '3px', 'important');
                    container.style.setProperty('width', '100%', 'important');
                    container.style.setProperty('min-width', '120px', 'important');

                    // تطبيق الأنماط على الأزرار
                    const buttons = container.querySelectorAll('.action-btn');
                    buttons.forEach((btn, index) => {
                        btn.style.setProperty('display', 'flex', 'important');
                        btn.style.setProperty('width', '30px', 'important');
                        btn.style.setProperty('height', '30px', 'important');
                        btn.style.setProperty('min-width', '30px', 'important');
                        btn.style.setProperty('max-width', '30px', 'important');
                        btn.style.setProperty('margin', '0', 'important');
                        btn.style.setProperty('padding', '0', 'important');
                        btn.style.setProperty('border-radius', '6px', 'important');
                        btn.style.setProperty('align-items', 'center', 'important');
                        btn.style.setProperty('justify-content', 'center', 'important');
                        btn.style.setProperty('border', 'none', 'important');
                        btn.style.setProperty('cursor', 'pointer', 'important');
                        btn.style.setProperty('font-size', '12px', 'important');
                        btn.style.setProperty('flex-shrink', '0', 'important');
                        btn.style.setProperty('flex-grow', '0', 'important');

                        // الألوان
                        if (btn.classList.contains('edit')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('view')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('delete')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        }
                    });
                });
            }

            // تطبيق التنسيق فوراً
            forceHorizontalButtons();

            // إعادة تطبيق التنسيق كل ثانية للتأكد
            setInterval(forceHorizontalButtons, 1000);
        });
    </script>

    <script>
        // وظائف إدارة المشتريات

        // وظيفة للحصول على بيانات فاتورة الشراء من الجدول
        function getPurchaseDataFromTable(purchaseId) {
            const tableRows = document.querySelectorAll('.purchases-table tbody tr');

            for (let row of tableRows) {
                const invoiceCell = row.cells[0];
                const rowPurchaseId = invoiceCell.querySelector('strong') ?
                    invoiceCell.querySelector('strong').textContent.trim() :
                    invoiceCell.textContent.trim();

                if (rowPurchaseId === purchaseId) {
                    const statusCell = row.cells[4];
                    const statusText = statusCell.querySelector('.badge') ?
                        statusCell.querySelector('.badge').textContent.trim() :
                        statusCell.textContent.trim();

                    const amountCell = row.cells[3];
                    const amountText = amountCell.querySelector('strong') ?
                        amountCell.querySelector('strong').textContent.trim() :
                        amountCell.textContent.trim();

                    return {
                        id: rowPurchaseId,
                        date: row.cells[1].textContent.trim(),
                        supplier: row.cells[2].textContent.trim(),
                        amount: amountText,
                        status: statusText,
                        rowElement: row
                    };
                }
            }

            return {
                id: purchaseId,
                date: '2023-12-15',
                supplier: 'غير محدد',
                amount: '0 ر.س',
                status: 'معلق',
                rowElement: null
            };
        }

        // وظيفة تعديل فاتورة الشراء
        function editPurchase(purchaseId) {
            console.log('تعديل فاتورة الشراء:', purchaseId);

            const purchaseData = getPurchaseDataFromTable(purchaseId);

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal edit-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 85vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-edit"></i>
                            تعديل فاتورة الشراء
                        </h3>
                        <button onclick="closePurchaseEditModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px; max-height: 60vh; overflow-y: auto;">
                        <form id="editPurchaseForm" class="form-modern">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-hashtag" style="color: #ffc107;"></i>
                                        رقم الفاتورة
                                    </label>
                                    <input type="text" value="${purchaseData.id}" readonly style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        background: #f8f9fa;
                                        color: #6c757d;
                                    ">
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-calendar" style="color: #ffc107;"></i>
                                        تاريخ الفاتورة
                                    </label>
                                    <input type="date" id="editPurchaseDate" value="${convertDateToInput(purchaseData.date)}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-truck" style="color: #ffc107;"></i>
                                        المورد
                                    </label>
                                    <input type="text" id="editPurchaseSupplier" value="${purchaseData.supplier}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-money-bill-wave" style="color: #ffc107;"></i>
                                        المبلغ
                                    </label>
                                    <input type="text" id="editPurchaseAmount" value="${purchaseData.amount}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-info-circle" style="color: #ffc107;"></i>
                                    حالة الفاتورة
                                </label>
                                <select id="editPurchaseStatus" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    background: white;
                                    cursor: pointer;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                    <option value="مدفوع" ${purchaseData.status.includes('مدفوع') ? 'selected' : ''}>مدفوع</option>
                                    <option value="معلق" ${purchaseData.status.includes('معلق') ? 'selected' : ''}>معلق</option>
                                    <option value="ملغي" ${purchaseData.status.includes('ملغي') ? 'selected' : ''}>ملغي</option>
                                </select>
                            </div>

                            <div style="
                                display: flex;
                                gap: 12px;
                                justify-content: flex-end;
                                padding-top: 20px;
                                border-top: 1px solid #e9ecef;
                                margin-top: 20px;
                            ">
                                <button type="button" onclick="closePurchaseEditModal()" style="
                                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>

                                <button type="submit" id="updatePurchaseBtn" style="
                                    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    <i class="fas fa-save"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            const form = document.getElementById('editPurchaseForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                updatePurchase(purchaseId);
            });

            window.closePurchaseEditModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePurchaseEditModal();
                }
            });
        }

        function updatePurchase(purchaseId) {
            const updateBtn = document.getElementById('updatePurchaseBtn');
            const originalText = updateBtn.innerHTML;

            // إظهار رسالة التحميل
            updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            updateBtn.disabled = true;

            // جمع البيانات المحدثة
            const updatedData = {
                date: document.getElementById('editPurchaseDate').value,
                supplier: document.getElementById('editPurchaseSupplier').value,
                amount: document.getElementById('editPurchaseAmount').value,
                status: document.getElementById('editPurchaseStatus').value
            };

            // محاكاة عملية التحديث
            setTimeout(() => {
                try {
                    // تحديث الصف في الجدول
                    updatePurchaseInTable(purchaseId, updatedData);

                    // إغلاق النافذة
                    closePurchaseEditModal();

                    // عرض رسالة نجاح
                    alert(`تم تحديث فاتورة الشراء "${purchaseId}" بنجاح!`);

                } catch (error) {
                    console.error('خطأ في التحديث:', error);
                    updateBtn.innerHTML = originalText;
                    updateBtn.disabled = false;
                    alert('حدث خطأ أثناء تحديث البيانات. يرجى المحاولة مرة أخرى.');
                }
            }, 1000);
        }

        function updatePurchaseInTable(purchaseId, updatedData) {
            // العثور على الصف في الجدول
            const rows = document.querySelectorAll('tbody tr');
            for (let row of rows) {
                const editBtn = row.querySelector(`button[onclick*="editPurchase('${purchaseId}')"]`);
                if (editBtn) {
                    // تحديث محتوى الصف
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 5) {
                        cells[1].textContent = updatedData.date;
                        cells[2].textContent = updatedData.supplier;
                        cells[3].innerHTML = `<strong>${updatedData.amount}</strong>`;

                        // تحديث شارة الحالة
                        const statusClass = updatedData.status === 'مدفوع' ? 'success' :
                                          updatedData.status === 'معلق' ? 'warning' : 'danger';
                        cells[4].innerHTML = `<span class="badge ${statusClass}">${updatedData.status}</span>`;

                        // إضافة تأثير بصري للتحديث
                        row.style.background = '#fff3cd';
                        setTimeout(() => {
                            row.style.background = '';
                        }, 2000);
                    }
                    break;
                }
            }
        }

        // وظيفة عرض فاتورة الشراء
        function viewPurchase(purchaseId) {
            console.log('عرض فاتورة الشراء:', purchaseId);

            const purchaseData = getPurchaseDataFromTable(purchaseId);

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal view-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-eye"></i>
                            تفاصيل فاتورة الشراء
                        </h3>
                        <button onclick="closePurchaseViewModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">رقم الفاتورة</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${purchaseData.id}</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">تاريخ الفاتورة</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${purchaseData.date}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">المورد</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem;">${purchaseData.supplier}</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">المبلغ الإجمالي</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.2rem; font-weight: 600; color: #28a745;">${purchaseData.amount}</p>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">حالة الفاتورة</label>
                            <p style="margin: 5px 0 0 0;">
                                <span class="badge ${purchaseData.status === 'مدفوع' ? 'success' : purchaseData.status === 'معلق' ? 'warning' : 'danger'}">
                                    ${purchaseData.status}
                                </span>
                            </p>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; padding-top: 20px; border-top: 1px solid #e9ecef;">
                            <button onclick="closePurchaseViewModal(); editPurchase('${purchaseData.id}')" style="
                                background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button onclick="closePurchaseViewModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة أنماط CSS للانيميشن
            if (!document.querySelector('#modal-animations')) {
                const style = document.createElement('style');
                style.id = 'modal-animations';
                style.textContent = `
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    @keyframes slideIn {
                        from { opacity: 0; transform: translateY(-50px) scale(0.9); }
                        to { opacity: 1; transform: translateY(0) scale(1); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(modal);

            window.closePurchaseViewModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePurchaseViewModal();
                }
            });
        }

        // وظيفة حذف فاتورة الشراء
        function deletePurchase(purchaseId) {
            console.log('حذف فاتورة الشراء:', purchaseId);

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const purchaseData = getPurchaseDataFromTable(purchaseId);

            const modal = document.createElement('div');
            modal.className = 'modal delete-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-radius: 12px 12px 0 0;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            تأكيد الحذف
                        </h3>
                        <button onclick="closePurchaseDeleteModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="
                                width: 80px;
                                height: 80px;
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 15px auto;
                                color: white;
                                font-size: 2rem;
                            ">
                                <i class="fas fa-trash"></i>
                            </div>
                            <h4 style="margin: 0 0 10px 0; color: #dc3545;">هل أنت متأكد من حذف هذه الفاتورة؟</h4>
                            <p style="margin: 0; color: #6c757d;">هذا الإجراء لا يمكن التراجع عنه!</p>
                        </div>

                        <div style="
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border-left: 4px solid #dc3545;
                        ">
                            <h5 style="margin: 0 0 10px 0; color: #dc3545;">تفاصيل الفاتورة:</h5>
                            <p style="margin: 5px 0; color: #333;"><strong>رقم الفاتورة:</strong> ${purchaseData.id}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>التاريخ:</strong> ${purchaseData.date}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>المورد:</strong> ${purchaseData.supplier}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>المبلغ:</strong> ${purchaseData.amount}</p>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button onclick="closePurchaseDeleteModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>

                            <button onclick="confirmDeletePurchase('${purchaseId}')" style="
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-trash"></i>
                                نعم، احذف الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closePurchaseDeleteModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePurchaseDeleteModal();
                }
            });
        }

        // وظائف مساعدة
        function convertDateToInput(dateStr) {
            if (!dateStr) return new Date().toISOString().split('T')[0];

            const parts = dateStr.split('-');
            if (parts.length === 3) {
                return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
            }
            return new Date().toISOString().split('T')[0];
        }

        function formatDateForDisplay(dateStr) {
            if (!dateStr) return new Date().toLocaleDateString('ar-SA');

            const date = new Date(dateStr);
            return date.toLocaleDateString('ar-SA');
        }

        function saveEditedPurchase(purchaseId) {
            const date = document.getElementById('edit-purchase-date').value;
            const supplier = document.getElementById('edit-purchase-supplier').value;
            const amount = document.getElementById('edit-purchase-amount').value;
            const status = document.getElementById('edit-purchase-status').value;

            if (!supplier.trim()) {
                alert('يرجى إدخال اسم المورد');
                return;
            }

            // تحديث البيانات في الجدول
            updatePurchaseInTable(purchaseId, {
                date: date,
                supplier: supplier,
                amount: amount,
                status: status
            });

            // إغلاق النافذة
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                closeModal(modal.querySelector('.close-btn'));
            }

            // إظهار رسالة نجاح
            showSuccessMessage('تم تحديث فاتورة الشراء بنجاح');
        }

        function updatePurchaseInTable(purchaseId, updatedData) {
            const tableRows = document.querySelectorAll('.purchases-table tbody tr');

            for (let row of tableRows) {
                const invoiceCell = row.cells[0];
                const rowPurchaseId = invoiceCell.querySelector('strong') ?
                    invoiceCell.querySelector('strong').textContent.trim() :
                    invoiceCell.textContent.trim();

                if (rowPurchaseId === purchaseId) {
                    row.cells[1].textContent = formatDateForDisplay(updatedData.date);
                    row.cells[2].textContent = updatedData.supplier;
                    row.cells[3].innerHTML = `<strong>${updatedData.amount}</strong>`;

                    // تحديث الحالة مع الحفاظ على التنسيق
                    const statusCell = row.cells[4];
                    let statusClass = 'success';
                    if (updatedData.status === 'معلق') statusClass = 'warning';
                    if (updatedData.status === 'ملغي') statusClass = 'danger';

                    statusCell.innerHTML = `<span class="badge ${statusClass}">${updatedData.status}</span>`;

                    console.log('تم تحديث فاتورة الشراء في الجدول:', purchaseId);
                    break;
                }
            }
        }

        function confirmDeletePurchase(purchaseId) {
            // حذف الصف من الجدول
            const tableRows = document.querySelectorAll('.purchases-table tbody tr');

            for (let row of tableRows) {
                const invoiceCell = row.cells[0];
                const rowPurchaseId = invoiceCell.querySelector('strong') ?
                    invoiceCell.querySelector('strong').textContent.trim() :
                    invoiceCell.textContent.trim();

                if (rowPurchaseId === purchaseId) {
                    // إضافة تأثير حركي للحذف
                    row.style.animation = 'slideOutRow 0.5s ease';
                    row.style.background = '#ffebee';
                    setTimeout(() => {
                        row.remove();
                    }, 500);
                    break;
                }
            }

            // إغلاق النافذة
            closePurchaseDeleteModal();

            // إظهار رسالة نجاح
            setTimeout(() => {
                alert(`تم حذف فاتورة الشراء "${purchaseId}" بنجاح`);
            }, 600);
        }

        function printPurchase(purchaseId) {
            console.log('طباعة فاتورة الشراء:', purchaseId);

            const purchaseData = getPurchaseDataFromTable(purchaseId);

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>فاتورة الشراء ${purchaseId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .details { margin-bottom: 20px; }
                        .detail-row { margin: 10px 0; }
                        .label { font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>فاتورة الشراء</h2>
                        <h3>${purchaseId}</h3>
                    </div>
                    <div class="details">
                        <div class="detail-row"><span class="label">رقم الفاتورة:</span> ${purchaseData.id}</div>
                        <div class="detail-row"><span class="label">التاريخ:</span> ${purchaseData.date}</div>
                        <div class="detail-row"><span class="label">المورد:</span> ${purchaseData.supplier}</div>
                        <div class="detail-row"><span class="label">المبلغ:</span> ${purchaseData.amount}</div>
                        <div class="detail-row"><span class="label">الحالة:</span> ${purchaseData.status}</div>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // دالة توليد رقم فاتورة شراء بسيط
        function generatePurchaseInvoiceNumber() {
            const savedPurchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            let maxNumber = 0;

            savedPurchases.forEach(purchase => {
                // التحقق من وجود invoiceNumber أو id
                let invoiceId = purchase.invoiceNumber || purchase.id;

                // تحويل إلى نص إذا كان رقم
                if (typeof invoiceId === 'number') {
                    maxNumber = Math.max(maxNumber, invoiceId);
                } else if (typeof invoiceId === 'string' && invoiceId.startsWith('PUR-')) {
                    const numberPart = invoiceId.replace('PUR-', '');
                    const number = parseInt(numberPart);
                    if (!isNaN(number) && number > maxNumber) {
                        maxNumber = number;
                    }
                }
            });

            const nextNumber = maxNumber + 1;
            return 'PUR-' + nextNumber.toString().padStart(3, '0');
        }

        // وظيفة إضافة فاتورة شراء جديدة
        function showAddPurchaseModal() {
            console.log('showAddPurchaseModal: بدء فتح نافذة إضافة فاتورة شراء جديدة...');

            try {
                // منع التمرير في الخلفية
                document.body.style.overflow = 'hidden';
                console.log('showAddPurchaseModal: تم منع التمرير');

            // توليد رقم فاتورة جديد
            const newInvoiceNumber = generatePurchaseInvoiceNumber();

            // تنسيق التاريخ الحالي
            const currentDate = new Date();
            const formattedDate = currentDate.toISOString().split('T')[0];

                // إنشاء النافذة
                console.log('showAddPurchaseModal: إنشاء عنصر النافذة...');
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                console.log('showAddPurchaseModal: تم إنشاء عنصر النافذة');
            modal.innerHTML = `
                <div class="modal-content-modern" style="max-width: 1000px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header-modern">
                        <h3><i class="fas fa-plus"></i> إضافة فاتورة شراء جديدة</h3>
                        <button class="close-btn" onclick="closePurchaseModal(this)">&times;</button>
                    </div>
                    <div class="modal-body-modern">
                        <form class="form-modern" id="new-purchase-form">
                            <!-- معلومات الفاتورة الأساسية -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label>رقم الفاتورة</label>
                                    <input type="text" class="input-modern" value="${newInvoiceNumber}" readonly id="purchase-invoice-number">
                                </div>
                                <div class="form-group">
                                    <label>التاريخ</label>
                                    <input type="date" class="input-modern" value="${formattedDate}" id="new-purchase-date">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>المورد *</label>
                                    <select class="select-modern" id="new-purchase-supplier" required>
                                        <option value="">اختر المورد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>طريقة الدفع</label>
                                    <select class="select-modern" id="new-purchase-payment">
                                        <option value="نقداً">نقداً</option>
                                        <option value="تحويل بنكي">تحويل بنكي</option>
                                        <option value="آجل">آجل</option>
                                        <option value="شيك">شيك</option>
                                    </select>
                                </div>
                            </div>

                            <!-- قسم الأصناف -->
                            <div class="form-section" style="margin-top: 30px;">
                                <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <h4 style="margin: 0; color: #333;"><i class="fas fa-boxes"></i> أصناف الفاتورة</h4>
                                    <button type="button" class="btn-modern btn-success" onclick="addPurchaseItem()" style="padding: 8px 15px; font-size: 14px;">
                                        <i class="fas fa-plus"></i> إضافة صنف
                                    </button>
                                </div>

                                <div class="items-container" id="purchase-items-container">
                                    <div class="items-table-header" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 60px; gap: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-weight: bold; margin-bottom: 10px;">
                                        <div>الصنف</div>
                                        <div>الكمية</div>
                                        <div>سعر الوحدة</div>
                                        <div>المجموع</div>
                                        <div>حذف</div>
                                    </div>
                                    <div id="purchase-items-list">
                                        <!-- سيتم إضافة الأصناف هنا -->
                                    </div>
                                </div>

                                <!-- ملخص الفاتورة -->
                                <div class="invoice-summary" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                        <span>المجموع الفرعي:</span>
                                        <span id="purchase-subtotal">0.00 ر.س</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                        <span>الضريبة (15%):</span>
                                        <span id="purchase-tax">0.00 ر.س</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 18px; border-top: 2px solid #ddd; padding-top: 10px;">
                                        <span>المجموع الكلي:</span>
                                        <span id="purchase-total">0.00 ر.س</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group" style="margin-top: 20px;">
                                <label>ملاحظات</label>
                                <textarea class="textarea-modern" rows="3" placeholder="ملاحظات إضافية..." id="new-purchase-notes"></textarea>
                            </div>

                            <div class="form-actions" style="margin-top: 30px;">
                                <button type="button" class="btn-modern btn-primary" onclick="savePurchaseInvoice()">
                                    <i class="fas fa-save"></i> حفظ الفاتورة
                                </button>
                                <button type="button" class="btn-modern btn-secondary" onclick="closePurchaseModal(this)">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

                console.log('showAddPurchaseModal: إضافة النافذة للصفحة...');
                document.body.appendChild(modal);
                console.log('showAddPurchaseModal: تم إضافة النافذة للصفحة');

                // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePurchaseModal(modal.querySelector('.close-btn'));
                }
            });

                // تحميل الموردين في القائمة
                console.log('showAddPurchaseModal: تحميل الموردين...');
                loadSuppliersInPurchaseModal();

                // إضافة صنف افتراضي
                console.log('showAddPurchaseModal: إضافة صنف افتراضي...');
                addPurchaseItem();

                console.log('showAddPurchaseModal: تم فتح النافذة بنجاح!');

            } catch (error) {
                console.error('showAddPurchaseModal: خطأ في فتح النافذة:', error);
                alert('حدث خطأ في فتح نافذة الفاتورة الجديدة: ' + error.message);
                document.body.style.overflow = 'auto'; // استعادة التمرير في حالة الخطأ
            }
        }

        // دالة تحميل الموردين في نافذة الشراء
        function loadSuppliersInPurchaseModal() {
            const supplierSelect = document.getElementById('new-purchase-supplier');
            if (!supplierSelect) return;

            // مسح الخيارات الموجودة عدا الأول
            while (supplierSelect.children.length > 1) {
                supplierSelect.removeChild(supplierSelect.lastChild);
            }

            // تحميل الموردين من localStorage
            const savedSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];

            savedSuppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.name;
                option.textContent = supplier.name;
                supplierSelect.appendChild(option);
            });

            console.log('تم تحميل', savedSuppliers.length, 'مورد في نافذة الشراء');
        }

        // دالة إضافة صنف جديد
        function addPurchaseItem() {
            const itemsList = document.getElementById('purchase-items-list');
            if (!itemsList) return;

            const itemIndex = itemsList.children.length;
            const itemDiv = document.createElement('div');
            itemDiv.className = 'purchase-item';
            itemDiv.style.cssText = 'display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 60px; gap: 10px; margin-bottom: 10px; align-items: center;';

            itemDiv.innerHTML = `
                <select class="select-modern item-product" onchange="updatePurchaseItemPrice(this)" style="font-size: 14px;">
                    <option value="">اختر الصنف</option>
                </select>
                <input type="number" class="input-modern item-quantity" placeholder="الكمية" min="1" value="1" onchange="calculatePurchaseItemTotal(this)" style="font-size: 14px;">
                <input type="number" class="input-modern item-price" placeholder="السعر" step="0.01" min="0" onchange="calculatePurchaseItemTotal(this)" style="font-size: 14px;">
                <input type="text" class="input-modern item-total" placeholder="المجموع" readonly style="font-size: 14px; background: #f8f9fa;">
                <button type="button" class="btn-modern btn-danger" onclick="removePurchaseItem(this)" style="padding: 8px; font-size: 12px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;

            itemsList.appendChild(itemDiv);

            // تحميل المنتجات في القائمة
            loadProductsInItem(itemDiv.querySelector('.item-product'));
        }

        // دالة تحميل المنتجات في صنف
        function loadProductsInItem(selectElement) {
            if (!selectElement) return;

            // تحميل المنتجات من localStorage
            const savedProducts = JSON.parse(localStorage.getItem('monjizProducts')) || [];

            savedProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.name;
                option.textContent = product.name;
                option.setAttribute('data-price', product.price || 0);
                selectElement.appendChild(option);
            });
        }

        // دالة تحديث سعر الصنف عند الاختيار
        function updatePurchaseItemPrice(selectElement) {
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const priceInput = selectElement.parentElement.querySelector('.item-price');

            if (selectedOption && selectedOption.getAttribute('data-price')) {
                priceInput.value = selectedOption.getAttribute('data-price');
                calculatePurchaseItemTotal(priceInput);
            }
        }

        // دالة حساب مجموع الصنف
        function calculatePurchaseItemTotal(element) {
            const itemRow = element.parentElement;
            const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
            const total = quantity * price;

            itemRow.querySelector('.item-total').value = total.toFixed(2);

            // تحديث مجموع الفاتورة
            calculatePurchaseInvoiceTotal();
        }

        // دالة حساب مجموع الفاتورة
        function calculatePurchaseInvoiceTotal() {
            const itemTotals = document.querySelectorAll('.item-total');
            let subtotal = 0;

            itemTotals.forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });

            const tax = subtotal * 0.15; // ضريبة 15%
            const total = subtotal + tax;

            document.getElementById('purchase-subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
            document.getElementById('purchase-tax').textContent = tax.toFixed(2) + ' ر.س';
            document.getElementById('purchase-total').textContent = total.toFixed(2) + ' ر.س';
        }

        // دالة حذف صنف
        function removePurchaseItem(button) {
            const itemRow = button.parentElement;
            itemRow.remove();
            calculatePurchaseInvoiceTotal();
        }

        // دالة إغلاق نافذة الشراء
        function closePurchaseModal(element) {
            const modal = element.closest('.modal-overlay');
            if (modal) {
                document.body.style.overflow = 'auto';
                modal.remove();
            }
        }

        // دالة حفظ فاتورة الشراء الجديدة
        function savePurchaseInvoice() {
            console.log('بدء حفظ فاتورة الشراء...');

            // جمع البيانات الأساسية
            const invoiceNumber = document.getElementById('purchase-invoice-number').value;
            const date = document.getElementById('new-purchase-date').value;
            const supplier = document.getElementById('new-purchase-supplier').value;
            const payment = document.getElementById('new-purchase-payment').value;
            const notes = document.getElementById('new-purchase-notes').value;

            // التحقق من البيانات الأساسية
            if (!supplier) {
                alert('يرجى اختيار المورد');
                return;
            }

            // جمع بيانات الأصناف
            const items = [];
            const itemRows = document.querySelectorAll('.purchase-item');

            if (itemRows.length === 0) {
                alert('يرجى إضافة صنف واحد على الأقل');
                return;
            }

            let hasValidItems = false;
            itemRows.forEach((row, index) => {
                const product = row.querySelector('.item-product').value;
                const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.item-price').value) || 0;
                const total = parseFloat(row.querySelector('.item-total').value) || 0;

                if (product && quantity > 0 && price > 0) {
                    items.push({
                        product: product,
                        quantity: quantity,
                        price: price,
                        total: total
                    });
                    hasValidItems = true;
                }
            });

            if (!hasValidItems) {
                alert('يرجى إضافة صنف صحيح واحد على الأقل');
                return;
            }

            // حساب المجاميع
            const subtotal = items.reduce((sum, item) => sum + item.total, 0);
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            // تنسيق التاريخ
            const formattedDate = new Date(date).toLocaleDateString('en-GB');

            // إنشاء فاتورة الشراء
            const purchaseInvoice = {
                id: invoiceNumber,
                date: formattedDate,
                supplier: supplier,
                payment: payment,
                notes: notes,
                items: items,
                subtotal: subtotal,
                tax: tax,
                total: total,
                status: 'مدفوع',
                createdAt: new Date().toISOString()
            };

            console.log('فاتورة الشراء:', purchaseInvoice);

            // حفظ الفاتورة
            const savedPurchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            savedPurchases.push(purchaseInvoice);
            localStorage.setItem('monjizPurchases', JSON.stringify(savedPurchases));

            // تحديث المخزون
            updateInventoryFromPurchase(items);

            // إضافة الفاتورة للجدول
            addPurchaseToTable(purchaseInvoice);

            // إغلاق النافذة
            closePurchaseModal(document.querySelector('.modal-overlay .close-btn'));

            // عرض رسالة نجاح
            alert('تم حفظ فاتورة الشراء بنجاح: ' + invoiceNumber);

            console.log('تم حفظ فاتورة الشراء بنجاح');
        }

        // دالة تحديث المخزون من فاتورة الشراء
        function updateInventoryFromPurchase(items) {
            console.log('تحديث المخزون من فاتورة الشراء...');

            // الحصول على المنتجات الحالية
            const savedProducts = JSON.parse(localStorage.getItem('monjizProducts')) || [];

            items.forEach(item => {
                // البحث عن المنتج في المخزون
                const productIndex = savedProducts.findIndex(p => p.name === item.product);

                if (productIndex !== -1) {
                    // تحديث الكمية الموجودة
                    const currentQuantity = parseInt(savedProducts[productIndex].quantity) || 0;
                    savedProducts[productIndex].quantity = currentQuantity + item.quantity;

                    // تحديث سعر الشراء
                    savedProducts[productIndex].purchasePrice = item.price;

                    console.log(`تم تحديث ${item.product}: الكمية من ${currentQuantity} إلى ${savedProducts[productIndex].quantity}`);
                } else {
                    // إضافة منتج جديد إذا لم يكن موجوداً
                    const newProduct = {
                        id: Date.now(),
                        name: item.product,
                        category: 'عام',
                        quantity: item.quantity,
                        price: item.price * 1.2, // سعر البيع = سعر الشراء + 20%
                        purchasePrice: item.price,
                        minStock: 10,
                        createdAt: new Date().toISOString()
                    };

                    savedProducts.push(newProduct);
                    console.log(`تم إضافة منتج جديد: ${item.product}`);
                }
            });

            // حفظ المنتجات المحدثة
            localStorage.setItem('monjizProducts', JSON.stringify(savedProducts));
            console.log('تم تحديث المخزون بنجاح');
        }

        function savePurchase() {
            const date = document.getElementById('new-purchase-date').value;
            const supplier = document.getElementById('new-purchase-supplier').value;
            const amount = document.getElementById('new-purchase-amount').value;
            const payment = document.getElementById('new-purchase-payment').value;
            const status = document.getElementById('new-purchase-status').value;
            const notes = document.getElementById('new-purchase-notes').value;

            if (!supplier) {
                alert('يرجى اختيار المورد');
                return;
            }

            if (!amount || amount <= 0) {
                alert('يرجى إدخال مبلغ صحيح');
                return;
            }

            // إضافة الفاتورة الجديدة إلى الجدول
            const tableBody = document.querySelector('.purchases-table tbody');
            const newRow = document.createElement('tr');

            let statusClass = 'success';
            if (status === 'معلق') statusClass = 'warning';
            if (status === 'ملغي') statusClass = 'danger';

            newRow.innerHTML = `
                <td><strong>PUR-003</strong></td>
                <td>${formatDateForDisplay(date)}</td>
                <td>${supplier}</td>
                <td><strong>${amount} ر.س</strong></td>
                <td><span class="badge ${statusClass}">${status}</span></td>
                <td>
                    <div class="action-buttons-horizontal">
                        <button class="action-btn edit" onclick="editPurchase('PUR-003')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view" onclick="viewPurchase('PUR-003')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete" onclick="deletePurchase('PUR-003')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(newRow);

            // إغلاق النافذة
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                closeModal(modal.querySelector('.close-btn'));
            }

            // إظهار رسالة نجاح
            showSuccessMessage('تم إضافة فاتورة الشراء بنجاح');
        }

        // وظائف مساعدة إضافية
        function closeModal(closeBtn) {
            const modal = closeBtn.closest('.modal-overlay');
            if (modal) {
                document.body.style.overflow = 'auto';
                modal.remove();
            }
        }

        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `
                <div style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                    z-index: 10000;
                    font-weight: 600;
                ">
                    <i class="fas fa-check-circle"></i> ${message}
                </div>
            `;

            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // متغيرات التنقل
        let currentPage = 1;
        let itemsPerPage = 10;
        let totalItems = 0;
        let allData = [];

        // دالة تحديث عرض البيانات حسب الصفحة
        function updateDataDisplay() {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = allData.slice(startIndex, endIndex);

            // تحديث الجدول
            updateTableDisplay(pageData);

            // تحديث معلومات النتائج
            updateResultsInfo();

            // تحديث أزرار التنقل
            updatePaginationButtons();
        }

        // دالة تحديث عرض الجدول
        function updateTableDisplay(data) {
            const tbody = document.querySelector('.purchases-table tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                            لا توجد بيانات للعرض
                        </td>
                    </tr>
                `;
                return;
            }

            data.forEach((item, index) => {
                const globalIndex = (currentPage - 1) * itemsPerPage + index + 1;
                const row = `
                    <tr>
                        <td>${globalIndex}</td>
                        <td><strong>${item.invoiceNumber || `PUR-${globalIndex.toString().padStart(3, '0')}`}</strong></td>
                        <td>${item.date || new Date().toISOString().split('T')[0]}</td>
                        <td>${item.supplier || `مورد رقم ${globalIndex}`}</td>
                        <td><strong class="positive">${item.amount || (Math.random() * 5000 + 1000).toFixed(0)} ر.س</strong></td>
                        <td><span class="badge ${item.payment === 'نقدي' ? 'success' : item.payment === 'تحويل بنكي' ? 'info' : 'warning'}">${item.payment || 'نقدي'}</span></td>
                        <td><span class="badge ${item.status === 'مكتمل' ? 'primary' : 'secondary'}">${item.status || 'مكتمل'}</span></td>
                        <td>
                            <div class="action-buttons-horizontal">
                                <button class="action-btn view" onclick="viewPurchase(${item.id || globalIndex})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit" onclick="editPurchase(${item.id || globalIndex})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="deletePurchase(${item.id || globalIndex})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // دالة تحديث معلومات النتائج
        function updateResultsInfo() {
            const startResult = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
            const endResult = Math.min(currentPage * itemsPerPage, totalItems);

            document.getElementById('start-result').textContent = startResult;
            document.getElementById('end-result').textContent = endResult;
            document.getElementById('total-results').textContent = totalItems;
        }

        // دالة تحديث أزرار التنقل
        function updatePaginationButtons() {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const paginationContainer = document.getElementById('pagination-container');

            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }

            paginationContainer.style.display = 'flex';

            let paginationHTML = '';

            // زر السابق
            const prevDisabled = currentPage === 1 ? 'disabled' : '';
            paginationHTML += `
                <button class="page-btn prev ${prevDisabled}" onclick="changePage('prev')">
                    <i class="fas fa-chevron-right"></i> السابق
                </button>
            `;

            // أزرار الصفحات
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="changePage(1)">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="page-dots">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHTML += `<button class="page-btn ${activeClass}" onclick="changePage(${i})">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `<span class="page-dots">...</span>`;
                }
                paginationHTML += `<button class="page-btn" onclick="changePage(${totalPages})">${totalPages}</button>`;
            }

            // زر التالي
            const nextDisabled = currentPage === totalPages ? 'disabled' : '';
            paginationHTML += `
                <button class="page-btn next ${nextDisabled}" onclick="changePage('next')">
                    التالي <i class="fas fa-chevron-left"></i>
                </button>
            `;

            paginationContainer.innerHTML = paginationHTML;
        }

        // دالة تغيير الصفحة
        function changePage(page) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);

            if (page === 'prev') {
                if (currentPage > 1) {
                    currentPage--;
                }
            } else if (page === 'next') {
                if (currentPage < totalPages) {
                    currentPage++;
                }
            } else if (typeof page === 'number') {
                if (page >= 1 && page <= totalPages) {
                    currentPage = page;
                }
            }

            updateDataDisplay();
        }

        // إضافة بعض البيانات التجريبية للمشتريات إذا لم تكن موجودة
        function addSamplePurchasesIfEmpty() {
            const existingData = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            console.log('addSamplePurchasesIfEmpty: البيانات الموجودة:', existingData.length);
            if (existingData.length === 0) {
                console.log('addSamplePurchasesIfEmpty: إضافة بيانات تجريبية...');
                const sampleData = [];

                // إضافة 25 فاتورة شراء للاختبار
                for (let i = 1; i <= 25; i++) {
                    sampleData.push({
                        id: i,
                        invoiceNumber: `PUR-${String(i).padStart(3, '0')}`,
                        date: new Date().toISOString().split('T')[0],
                        supplier: `مورد رقم ${i}`,
                        amount: (Math.random() * 5000 + 1000).toFixed(0),
                        payment: ['نقدي', 'تحويل بنكي', 'شيك'][Math.floor(Math.random() * 3)],
                        status: ['مكتمل', 'قيد المعالجة'][Math.floor(Math.random() * 2)],
                        notes: `ملاحظات للفاتورة رقم ${i}`,
                        createdAt: new Date().toISOString()
                    });
                }

                localStorage.setItem('monjizPurchases', JSON.stringify(sampleData));
                console.log('addSamplePurchasesIfEmpty: تم حفظ', sampleData.length, 'فاتورة تجريبية');
            } else {
                console.log('addSamplePurchasesIfEmpty: البيانات موجودة مسبقاً');
            }
        }

        // دالة تحميل البيانات
        function loadData() {
            console.log('loadData: بدء تحميل البيانات...');

            // إضافة البيانات التجريبية أولاً
            addSamplePurchasesIfEmpty();

            // تحميل بيانات المشتريات من localStorage
            allData = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            totalItems = allData.length;
            currentPage = 1;

            console.log('loadData: تم تحميل', totalItems, 'فاتورة');

            updateDataDisplay();
            console.log('loadData: تم تحديث العرض');
        }

        // دالة إظهار/إخفاء القائمة المنسدلة
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        }

        // إغلاق القائمة المنسدلة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach(dropdown => {
                if (!dropdown.parentElement.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });
        });

        // وظائف التصدير والطباعة
        function printPurchases() {
            console.log('طباعة تقرير المشتريات...');

            // الحصول على بيانات المشتريات
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];

            if (purchases.length === 0) {
                alert('لا توجد بيانات مشتريات للطباعة');
                return;
            }

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');

            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير المشتريات</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                            font-size: 24px;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: right;
                            font-size: 12px;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                            color: #333;
                        }
                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                        .footer {
                            margin-top: 30px;
                            text-align: center;
                            color: #666;
                            font-size: 10px;
                            border-top: 1px solid #ddd;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير المشتريات</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p>عدد المشتريات: ${purchases.length}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            purchases.forEach((purchase, index) => {
                htmlContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${purchase.invoiceNumber || '-'}</td>
                        <td>${new Date(purchase.date).toLocaleDateString('ar-SA') || '-'}</td>
                        <td>${purchase.supplier || '-'}</td>
                        <td>${purchase.amount || '-'}</td>
                        <td>${purchase.payment || '-'}</td>
                        <td>${purchase.status || '-'}</td>
                        <td>${purchase.notes || '-'}</td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام منجز لإدارة الأعمال</p>
                        <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };

            console.log('تم إنشاء تقرير طباعة للمشتريات');
        }

        function exportToExcel() {
            console.log('تصدير المشتريات إلى Excel...');

            // الحصول على بيانات المشتريات
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];

            if (purchases.length === 0) {
                alert('لا توجد بيانات مشتريات للتصدير');
                return;
            }

            // إنشاء محتوى CSV
            let csvContent = '\uFEFFرقم الفاتورة,التاريخ,المورد,المبلغ,طريقة الدفع,الحالة,الملاحظات\n';

            purchases.forEach(purchase => {
                const row = [
                    purchase.invoiceNumber || '',
                    new Date(purchase.date).toLocaleDateString('ar-SA') || '',
                    purchase.supplier || '',
                    purchase.amount || '',
                    purchase.payment || '',
                    purchase.status || '',
                    purchase.notes || ''
                ].join(',');
                csvContent += row + '\n';
            });

            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `مشتريات_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('تم تصدير', purchases.length, 'مشترى إلى Excel');
        }

        function exportToPDF() {
            console.log('تصدير المشتريات إلى PDF...');

            // الحصول على بيانات المشتريات
            const purchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];

            if (purchases.length === 0) {
                alert('لا توجد بيانات مشتريات للتصدير');
                return;
            }

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');

            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير المشتريات</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 12px;
                            text-align: right;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                            color: #333;
                        }
                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                        .footer {
                            margin-top: 30px;
                            text-align: center;
                            color: #666;
                            font-size: 12px;
                            border-top: 1px solid #ddd;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير المشتريات</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p>عدد المشتريات: ${purchases.length}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            purchases.forEach((purchase, index) => {
                htmlContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${purchase.invoiceNumber || '-'}</td>
                        <td>${new Date(purchase.date).toLocaleDateString('ar-SA') || '-'}</td>
                        <td>${purchase.supplier || '-'}</td>
                        <td>${purchase.amount || '-'}</td>
                        <td>${purchase.payment || '-'}</td>
                        <td>${purchase.status || '-'}</td>
                        <td>${purchase.notes || '-'}</td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام منجز لإدارة الأعمال</p>
                        <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };

            console.log('تم إنشاء تقرير PDF للمشتريات');
        }

        // نظام إدارة الموردين
        // تحميل الموردين من localStorage أو استخدام البيانات الافتراضية
        let suppliersData = JSON.parse(localStorage.getItem('monjizSuppliers')) || [
            {
                id: 1,
                name: 'شركة الأغذية المتحدة',
                type: 'company',
                phone: '+966112345678',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية'
            },
            {
                id: 2,
                name: 'مؤسسة الخضار الطازجة',
                type: 'company',
                phone: '+966112345679',
                email: '<EMAIL>',
                address: 'جدة، المملكة العربية السعودية'
            },
            {
                id: 3,
                name: 'شركة المواد الغذائية',
                type: 'company',
                phone: '+966112345680',
                email: '<EMAIL>',
                address: 'الدمام، المملكة العربية السعودية'
            },
            {
                id: 4,
                name: 'مؤسسة التوريدات العامة',
                type: 'company',
                phone: '+966112345681',
                email: '<EMAIL>',
                address: 'مكة المكرمة، المملكة العربية السعودية'
            }
        ];

        // دالة حفظ الموردين في localStorage
        function saveSuppliersToStorage() {
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliersData));
            console.log('تم حفظ الموردين في localStorage:', suppliersData.length);
        }

        // دالة تحميل الموردين في القوائم
        function loadSuppliersFromStorage() {
            const supplierSelects = document.querySelectorAll('#new-purchase-supplier, #supplier-filter');

            supplierSelects.forEach(select => {
                // مسح الخيارات الموجودة عدا الخيار الأول
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }

                // إضافة الموردين للقائمة
                suppliersData.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.name;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                });
            });

            console.log('تم تحميل', suppliersData.length, 'مورد في قوائم المشتريات');
        }

        // دالة إضافة فاتورة الشراء للجدول
        function addPurchaseToTable(purchase) {
            const tbody = document.querySelector('.purchases-table tbody') ||
                         document.querySelector('#purchases-table tbody') ||
                         document.querySelector('table tbody');

            if (!tbody) return;

            // إزالة صف "لا توجد فواتير" إذا كان موجوداً
            const noDataRow = tbody.querySelector('td[colspan]');
            if (noDataRow) {
                noDataRow.parentElement.remove();
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${purchase.id}</td>
                <td>${purchase.date}</td>
                <td>${purchase.supplier}</td>
                <td>${purchase.items.length}</td>
                <td>${purchase.total.toFixed(2)} ر.س</td>
                <td>
                    <div class="action-buttons-horizontal">
                        <button class="action-btn view" onclick="viewPurchase('${purchase.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="editPurchase('${purchase.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deletePurchase('${purchase.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            tbody.appendChild(row);
            console.log('تم إضافة فاتورة الشراء للجدول:', purchase.id);
        }

        // دالة تحميل فواتير الشراء من localStorage
        function loadPurchasesFromStorage() {
            console.log('تحميل فواتير الشراء من localStorage...');
            const savedPurchases = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            console.log('عدد فواتير الشراء المحفوظة:', savedPurchases.length);

            const tbody = document.querySelector('.purchases-table tbody') ||
                         document.querySelector('#purchases-table tbody') ||
                         document.querySelector('table tbody');

            if (tbody) {
                // مسح الجدول الحالي
                tbody.innerHTML = '';

                if (savedPurchases.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد فواتير شراء</td></tr>';
                } else {
                    // إضافة كل فاتورة للجدول
                    savedPurchases.forEach(purchase => {
                        addPurchaseToTable(purchase);
                    });
                }
            }
        }

        // تفعيل زر إضافة فاتورة جديدة وتحميل البيانات
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded - تفعيل أزرار المشتريات...');

            // لا حاجة لتفعيل الزر هنا لأنه مفعل بـ onclick في HTML
            const addPurchaseBtn = document.querySelector('.add-purchase-btn');
            if (addPurchaseBtn) {
                console.log('تم العثور على زر فاتورة جديدة');
            } else {
                console.error('لم يتم العثور على زر فاتورة جديدة');
            }

            // تحميل البيانات مع التنقل
            setTimeout(loadData, 100);

            // تحميل فواتير الشراء من localStorage
            setTimeout(loadPurchasesFromStorage, 300);

            // تحميل الموردين في المرشحات
            setTimeout(updateSupplierFiltersFromStorage, 400);

            // تحميل الموردين في الجدول
            setTimeout(loadSuppliersFromStorage, 500);

            // تحميل الموردين من localStorage
            setTimeout(loadSuppliersFromStorage, 100);

            // إضافة أنماط CSS للانيميشن
            if (!document.querySelector('#purchase-animations')) {
                const style = document.createElement('style');
                style.id = 'purchase-animations';
                style.textContent = `
                    @keyframes slideOutRow {
                        from { opacity: 1; transform: translateX(0); }
                        to { opacity: 0; transform: translateX(-50px); }
                    }
                `;
                document.head.appendChild(style);
            }
        });

        // دالة إضافة مورد جديد
        function showAddSupplierModal() {
            console.log('فتح نافذة إضافة مورد جديد...');

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3 id="modalTitle">إضافة مورد جديد</h3>
                        <button class="modal-close" onclick="closeSupplierModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="supplierForm" onsubmit="saveNewSupplier(event)">
                            <div class="form-group">
                                <label for="supplierName">اسم المورد *</label>
                                <input type="text" id="supplierName" name="supplierName" required placeholder="أدخل اسم المورد">
                            </div>

                            <div class="form-group">
                                <label for="supplierPhone">رقم الهاتف *</label>
                                <input type="tel" id="supplierPhone" name="supplierPhone" required placeholder="مثال: +966501234567">
                            </div>

                            <div class="form-group">
                                <label for="supplierEmail">البريد الإلكتروني</label>
                                <input type="email" id="supplierEmail" name="supplierEmail" placeholder="مثال: <EMAIL>">
                            </div>

                            <div class="form-group">
                                <label for="supplierType">نوع المورد</label>
                                <select id="supplierType" name="supplierType">
                                    <option value="شركة">شركة</option>
                                    <option value="مؤسسة">مؤسسة</option>
                                    <option value="فرد">فرد</option>
                                </select>
                            </div>



                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="closeSupplierModal(this)">إلغاء</button>
                                <button type="submit" class="btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeSupplierModal(modal.querySelector('.modal-close'));
                }
            });
        }

        // دالة حفظ المورد الجديد
        function saveNewSupplier() {
            console.log('بدء حفظ المورد الجديد...');

            // جمع البيانات
            const name = document.getElementById('supplierName').value.trim();
            const phone = document.getElementById('supplierPhone').value.trim();
            const email = document.getElementById('supplierEmail').value.trim();
            const type = document.getElementById('supplierType').value;

            // التحقق من البيانات الأساسية
            if (!name) {
                alert('يرجى إدخال اسم المورد');
                return;
            }

            if (!phone) {
                alert('يرجى إدخال رقم الهاتف');
                return;
            }

            // إنشاء المورد الجديد
            const newSupplier = {
                id: Date.now(),
                name: name,
                type: type,
                phone: phone,
                email: email || name.toLowerCase().replace(/\s+/g, '') + '@example.com',
                createdAt: new Date().toISOString()
            };

            console.log('المورد الجديد:', newSupplier);

            // حفظ المورد
            const savedSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            savedSuppliers.push(newSupplier);
            localStorage.setItem('monjizSuppliers', JSON.stringify(savedSuppliers));

            // إضافة المورد للجدول
            addSupplierToTable(newSupplier);

            // تحديث قوائم الموردين في نافذة الشراء
            updateSupplierDropdowns();

            // إغلاق النافذة
            closeSupplierModal(document.querySelector('.modal-overlay .close-btn'));

            // عرض رسالة نجاح
            alert('تم حفظ المورد بنجاح: ' + name);

            console.log('تم حفظ المورد بنجاح');
        }

        // دالة إغلاق نافذة المورد
        function closeSupplierModal(element) {
            const modal = element.closest('.modal-overlay');
            if (modal) {
                document.body.style.overflow = 'auto';
                modal.remove();
            }
        }

        // دالة تحديث قوائم الموردين في جميع النوافذ
        function updateSupplierDropdowns() {
            console.log('تحديث قوائم الموردين...');

            // تحديث قائمة الموردين في نافذة الشراء
            const supplierSelect = document.getElementById('new-purchase-supplier');
            if (supplierSelect) {
                // مسح الخيارات الموجودة عدا الأول
                while (supplierSelect.children.length > 1) {
                    supplierSelect.removeChild(supplierSelect.lastChild);
                }

                // تحميل الموردين من localStorage
                const savedSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];

                savedSuppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.name;
                    option.textContent = supplier.name;
                    supplierSelect.appendChild(option);
                });

                console.log('تم تحديث قائمة الموردين في نافذة الشراء:', savedSuppliers.length, 'مورد');
            }
        }

        // دالة تحميل الموردين في الجدول
        function loadSuppliersFromStorage() {
            console.log('تحميل الموردين من localStorage...');
            const savedSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            console.log('عدد الموردين المحفوظين:', savedSuppliers.length);

            const tbody = document.querySelector('.suppliers-table tbody') ||
                         document.querySelector('#suppliers-table tbody') ||
                         document.querySelector('table tbody');

            if (tbody) {
                // مسح الجدول الحالي
                tbody.innerHTML = '';

                if (savedSuppliers.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: #666;">لا توجد موردين</td></tr>';
                } else {
                    // إضافة كل مورد للجدول
                    savedSuppliers.forEach(supplier => {
                        addSupplierToTable(supplier);
                    });
                }
            }
        }

        // دالة إضافة مورد افتراضي للاختبار
        function addDefaultSuppliers() {
            const savedSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];

            if (savedSuppliers.length === 0) {
                const defaultSuppliers = [
                    {
                        id: 1,
                        name: 'شركة الأغذية المتحدة',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        category: 'أغذية',
                        address: 'الرياض، المملكة العربية السعودية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        name: 'مؤسسة الخضار الطازجة',
                        phone: '0507654321',
                        email: '<EMAIL>',
                        category: 'أغذية',
                        address: 'جدة، المملكة العربية السعودية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        name: 'شركة المواد الغذائية',
                        phone: '0509876543',
                        email: '<EMAIL>',
                        category: 'أغذية',
                        address: 'الدمام، المملكة العربية السعودية',
                        createdAt: new Date().toISOString()
                    }
                ];

                localStorage.setItem('monjizSuppliers', JSON.stringify(defaultSuppliers));
                console.log('تم إضافة موردين افتراضيين للاختبار');

                // تحميل الموردين في الجدول
                setTimeout(loadSuppliersFromStorage, 100);
            }
        }

        // إضافة موردين افتراضيين عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(addDefaultSuppliers, 600);
        });
    </script>
</body>
</html>