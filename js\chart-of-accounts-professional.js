/**
 * نظام دليل الحسابات الاحترافي
 * Professional Chart of Accounts System
 */

class ChartOfAccountsProfessional {
    constructor() {
        this.accounts = [];
        this.currentEditId = null;
        this.storageKey = 'professionalChartOfAccounts';
        this.init();
    }

    init() {
        this.loadAccounts();
        this.setupEventListeners();
        this.renderAccountsTree();
        this.updateStats();
        this.setupAccountsUpdateListener();
    }

    // إعداد مستمع تحديث الحسابات
    setupAccountsUpdateListener() {
        // مستمع تحديث الحسابات من النظام الأساسي
        document.addEventListener('accountsUpdated', () => {
            console.log('🔄 تحديث النظام الاحترافي بعد تحديث الحسابات...');
            this.refreshFromBasicSystem();
        });

        // مستمع إضافة العملاء
        document.addEventListener('customerAdded', () => {
            console.log('👤 تحديث النظام الاحترافي بعد إضافة عميل...');
            setTimeout(() => this.refreshFromBasicSystem(), 500);
        });

        // مستمع إضافة الموردين
        document.addEventListener('supplierAdded', () => {
            console.log('🚚 تحديث النظام الاحترافي بعد إضافة مورد...');
            setTimeout(() => this.refreshFromBasicSystem(), 500);
        });
    }

    // إعادة تحميل من النظام الأساسي
    refreshFromBasicSystem() {
        try {
            console.log('🔄 إعادة تحميل النظام الاحترافي من النظام الأساسي...');
            this.loadProfessionalAccounts();
            this.renderAccountsTree();
            this.updateStats();
            console.log('✅ تم تحديث النظام الاحترافي بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحديث النظام الاحترافي:', error);
        }
    }

    setupEventListeners() {
        // البحث الفوري
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // البحث عند الكتابة (فوري)
            searchInput.addEventListener('input', (e) => {
                this.searchAccounts(e.target.value);
            });

            // البحث عند الضغط على مفاتيح
            searchInput.addEventListener('keyup', (e) => {
                this.searchAccounts(e.target.value);
            });

            // مسح البحث عند مسح النص
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' || e.key === 'Delete') {
                    setTimeout(() => {
                        this.searchAccounts(e.target.value);
                    }, 10);
                }
            });
        }

        // نموذج إضافة/تعديل الحساب
        document.getElementById('accountForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAccount();
        });

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('accountModal').addEventListener('click', (e) => {
            if (e.target.id === 'accountModal') {
                this.closeModal();
            }
        });
    }

    loadAccounts() {
        try {
            // أولاً: محاولة تحميل من النظام الأساسي
            this.loadProfessionalAccounts();

            // ثانياً: إذا لم توجد حسابات، استخدم الافتراضية
            if (this.accounts.length === 0) {
                this.accounts = this.getDefaultAccounts();
                this.saveToStorage();
            }
        } catch (error) {
            console.error('خطأ في تحميل الحسابات:', error);
            this.accounts = this.getDefaultAccounts();
        }
    }

    // دالة تحميل الحسابات من النظام الأساسي وتحويلها للنظام الاحترافي
    loadProfessionalAccounts() {
        try {
            // تحميل من النظام الأساسي
            const basicAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) ||
                                JSON.parse(localStorage.getItem('monjizAccounts')) || [];

            if (basicAccounts.length > 0) {
                console.log('🔄 تحويل الحسابات من النظام الأساسي للاحترافي...', basicAccounts.length);
                this.accounts = this.convertBasicToProfessional(basicAccounts);
                console.log('✅ تم تحويل الحسابات للنظام الاحترافي:', this.accounts.length);
                return this.accounts;
            }

            // إذا لم توجد حسابات أساسية، جرب التحميل من النظام الاحترافي
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                this.accounts = JSON.parse(saved);
            } else {
                this.accounts = [];
            }

            return this.accounts;
        } catch (error) {
            console.error('خطأ في تحميل الحسابات الاحترافية:', error);
            this.accounts = [];
            return this.accounts;
        }
    }

    // دالة تحويل الحسابات من النظام الأساسي للاحترافي
    convertBasicToProfessional(basicAccounts) {
        const professionalAccounts = [];
        const accountMap = new Map();

        // إنشاء خريطة للحسابات لسهولة البحث
        basicAccounts.forEach(account => {
            accountMap.set(account.code, account);
        });

        // تحويل الحسابات الرئيسية أولاً
        const rootAccounts = basicAccounts.filter(acc => !acc.parentCode || acc.level === 1);

        rootAccounts.forEach(rootAccount => {
            const professionalAccount = {
                id: rootAccount.id || Date.now() + Math.random(),
                code: rootAccount.code,
                name: rootAccount.name,
                type: rootAccount.type || 'assets',
                parentId: null,
                balance: rootAccount.balance || 0,
                level: 0,
                category: rootAccount.category,
                linkedType: rootAccount.linkedType,
                linkedId: rootAccount.linkedId,
                children: this.buildProfessionalChildren(rootAccount.code, basicAccounts, 1)
            };
            professionalAccounts.push(professionalAccount);
        });

        return professionalAccounts;
    }

    // بناء الأطفال للنظام الاحترافي
    buildProfessionalChildren(parentCode, basicAccounts, level) {
        const children = basicAccounts.filter(acc => acc.parentCode === parentCode);

        return children.map(child => ({
            id: child.id || Date.now() + Math.random(),
            code: child.code,
            name: child.name,
            type: child.type || 'assets',
            parentId: parentCode,
            balance: child.balance || 0,
            level: level,
            category: child.category,
            linkedType: child.linkedType,
            linkedId: child.linkedId,
            children: this.buildProfessionalChildren(child.code, basicAccounts, level + 1)
        }));
    }

    saveToStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.accounts));
            console.log('تم حفظ الحسابات بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ الحسابات:', error);
        }
    }

    getDefaultAccounts() {
        return [
            {
                id: 1,
                code: '1',
                name: 'الأصول',
                type: 'assets',
                parentId: null,
                balance: 0,
                level: 0,
                children: [
                    {
                        id: 11,
                        code: '11',
                        name: 'الأصول المتداولة',
                        type: 'assets',
                        parentId: 1,
                        balance: 0,
                        level: 1,
                        children: [
                            {
                                id: 111,
                                code: '1101',
                                name: 'النقدية والبنوك',
                                type: 'assets',
                                parentId: 11,
                                balance: 0,
                                level: 2,
                                children: []
                            },
                            {
                                id: 112,
                                code: '1102',
                                name: 'العملاء والذمم المدينة',
                                type: 'assets',
                                parentId: 11,
                                balance: 0,
                                level: 2,
                                children: []
                            }
                        ]
                    },
                    {
                        id: 12,
                        code: '12',
                        name: 'الأصول الثابتة',
                        type: 'assets',
                        parentId: 1,
                        balance: 0,
                        level: 1,
                        children: [
                            {
                                id: 121,
                                code: '1201',
                                name: 'الأراضي والمباني',
                                type: 'assets',
                                parentId: 12,
                                balance: 0,
                                level: 2,
                                children: []
                            }
                        ]
                    }
                ]
            },
            {
                id: 2,
                code: '2',
                name: 'الخصوم',
                type: 'liabilities',
                parentId: null,
                balance: 0,
                level: 0,
                children: [
                    {
                        id: 21,
                        code: '21',
                        name: 'الخصوم المتداولة',
                        type: 'liabilities',
                        parentId: 2,
                        balance: 0,
                        level: 1,
                        children: [
                            {
                                id: 211,
                                code: '2101',
                                name: 'الموردون والذمم الدائنة',
                                type: 'liabilities',
                                parentId: 21,
                                balance: 0,
                                level: 2,
                                children: []
                            }
                        ]
                    }
                ]
            },
            {
                id: 3,
                code: '3',
                name: 'حقوق الملكية',
                type: 'equity',
                parentId: null,
                balance: 0,
                level: 0,
                children: [
                    {
                        id: 31,
                        code: '3101',
                        name: 'رأس المال',
                        type: 'equity',
                        parentId: 3,
                        balance: 0,
                        level: 1,
                        children: []
                    }
                ]
            },
            {
                id: 4,
                code: '4',
                name: 'الإيرادات',
                type: 'revenue',
                parentId: null,
                balance: 0,
                level: 0,
                children: [
                    {
                        id: 41,
                        code: '4101',
                        name: 'إيرادات المبيعات',
                        type: 'revenue',
                        parentId: 4,
                        balance: 0,
                        level: 1,
                        children: []
                    }
                ]
            },
            {
                id: 5,
                code: '5',
                name: 'المصروفات',
                type: 'expenses',
                parentId: null,
                balance: 0,
                level: 0,
                children: [
                    {
                        id: 51,
                        code: '5101',
                        name: 'تكلفة البضاعة المباعة',
                        type: 'expenses',
                        parentId: 5,
                        balance: 0,
                        level: 1,
                        children: []
                    },
                    {
                        id: 52,
                        code: '5201',
                        name: 'المصروفات الإدارية',
                        type: 'expenses',
                        parentId: 5,
                        balance: 0,
                        level: 1,
                        children: []
                    }
                ]
            }
        ];
    }

    renderAccountsTree() {
        const container = document.getElementById('accountsTree');
        
        if (this.accounts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>لا توجد حسابات</h3>
                    <p>ابدأ بإضافة حساب جديد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        this.accounts.forEach(account => {
            if (account.parentId === null) {
                container.appendChild(this.createAccountNode(account));
            }
        });
    }

    createAccountNode(account) {
        const node = document.createElement('div');
        node.className = `account-node level-${account.level}`;
        node.dataset.accountId = account.id;

        const hasChildren = account.children && account.children.length > 0;
        const typeClass = `type-${account.type}`;
        const balanceFormatted = this.formatCurrency(account.balance || 0);

        node.innerHTML = `
            <div class="account-header" onclick="toggleAccount(${account.id})">
                <div class="account-info">
                    ${hasChildren ? `<i class="fas fa-chevron-right expand-icon"></i>` : '<span style="width: 16px;"></span>'}
                    <div class="account-code">${account.code}</div>
                    <div class="account-name">${account.name}</div>
                    <div class="account-type ${typeClass}">${this.getTypeLabel(account.type)}</div>
                </div>
                <div class="account-balance">${balanceFormatted}</div>
                <div class="account-actions" onclick="event.stopPropagation()">
                    <button class="action-btn view" onclick="viewAccount(${account.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editAccount(${account.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn add" onclick="addSubAccount(${account.id})" title="إضافة حساب فرعي">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteAccount(${account.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        if (hasChildren) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'children';
            childrenContainer.id = `children-${account.id}`;
            
            account.children.forEach(child => {
                childrenContainer.appendChild(this.createAccountNode(child));
            });
            
            node.appendChild(childrenContainer);
        }

        return node;
    }

    getTypeLabel(type) {
        const labels = {
            'assets': 'أصول',
            'liabilities': 'خصوم',
            'equity': 'حقوق ملكية',
            'revenue': 'إيرادات',
            'expenses': 'مصروفات'
        };
        return labels[type] || type;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2
        }).format(amount);
    }

    toggleAccount(accountId) {
        const node = document.querySelector(`[data-account-id="${accountId}"]`);
        const children = node.querySelector('.children');
        const icon = node.querySelector('.expand-icon');
        
        if (children) {
            children.classList.toggle('show');
            node.classList.toggle('expanded');
        }
    }

    expandAll() {
        document.querySelectorAll('.children').forEach(children => {
            children.classList.add('show');
        });
        document.querySelectorAll('.account-node').forEach(node => {
            node.classList.add('expanded');
        });
    }

    collapseAll() {
        document.querySelectorAll('.children').forEach(children => {
            children.classList.remove('show');
        });
        document.querySelectorAll('.account-node').forEach(node => {
            node.classList.remove('expanded');
        });
    }

    showAddAccountModal(parentId = null) {
        this.currentEditId = null;
        document.getElementById('modalTitle').textContent = parentId ? 'إضافة حساب فرعي' : 'إضافة حساب جديد';
        document.getElementById('accountForm').reset();
        
        this.populateParentOptions();
        if (parentId) {
            document.getElementById('parentAccount').value = parentId;
        }
        
        document.getElementById('accountModal').classList.add('show');
        document.getElementById('accountCode').focus();
    }

    populateParentOptions() {
        const select = document.getElementById('parentAccount');
        select.innerHTML = '<option value="">حساب رئيسي</option>';
        
        this.flattenAccounts(this.accounts).forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.code} - ${account.name}`;
            select.appendChild(option);
        });
    }

    flattenAccounts(accounts, result = []) {
        accounts.forEach(account => {
            result.push(account);
            if (account.children && account.children.length > 0) {
                this.flattenAccounts(account.children, result);
            }
        });
        return result;
    }

    saveAccount() {
        const form = document.getElementById('accountForm');
        const formData = new FormData(form);
        
        const accountData = {
            code: formData.get('accountCode').trim(),
            name: formData.get('accountName').trim(),
            type: formData.get('accountType'),
            parentId: formData.get('parentAccount') ? parseInt(formData.get('parentAccount')) : null,
            balance: parseFloat(formData.get('accountBalance')) || 0,
            description: formData.get('accountDescription') || ''
        };

        // التحقق من صحة البيانات
        if (!accountData.code || !accountData.name || !accountData.type) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // التحقق من عدم تكرار رقم الحساب
        const existingAccount = this.flattenAccounts(this.accounts).find(acc => 
            acc.code === accountData.code && acc.id !== this.currentEditId
        );
        
        if (existingAccount) {
            alert('رقم الحساب موجود بالفعل');
            return;
        }

        if (this.currentEditId) {
            this.updateAccount(this.currentEditId, accountData);
        } else {
            this.addNewAccount(accountData);
        }

        this.closeModal();
        this.renderAccountsTree();
        this.updateStats();
        this.saveToStorage();
    }

    addNewAccount(accountData) {
        const newAccount = {
            id: Date.now(),
            ...accountData,
            level: accountData.parentId ? this.getAccountLevel(accountData.parentId) + 1 : 0,
            children: []
        };

        if (accountData.parentId) {
            const parent = this.findAccountById(accountData.parentId);
            if (parent) {
                parent.children.push(newAccount);
            }
        } else {
            this.accounts.push(newAccount);
        }
    }

    updateAccount(accountId, accountData) {
        const account = this.findAccountById(accountId);
        if (account) {
            Object.assign(account, accountData);
        }
    }

    findAccountById(id, accounts = this.accounts) {
        for (const account of accounts) {
            if (account.id === id) {
                return account;
            }
            if (account.children) {
                const found = this.findAccountById(id, account.children);
                if (found) return found;
            }
        }
        return null;
    }

    getAccountLevel(parentId) {
        const parent = this.findAccountById(parentId);
        return parent ? parent.level : 0;
    }

    closeModal() {
        document.getElementById('accountModal').classList.remove('show');
        this.currentEditId = null;
    }

    deleteAccountById(accountId, accounts = this.accounts) {
        for (let i = 0; i < accounts.length; i++) {
            if (accounts[i].id === accountId) {
                accounts.splice(i, 1);
                return true;
            }
            if (accounts[i].children && this.deleteAccountById(accountId, accounts[i].children)) {
                return true;
            }
        }
        return false;
    }

    updateStats() {
        const flatAccounts = this.flattenAccounts(this.accounts);
        const mainAccounts = this.accounts.length;
        const subAccounts = flatAccounts.length - mainAccounts;
        const totalBalance = flatAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);

        document.getElementById('totalAccounts').textContent = flatAccounts.length;
        document.getElementById('mainAccounts').textContent = mainAccounts;
        document.getElementById('subAccounts').textContent = subAccounts;
        document.getElementById('totalBalance').textContent = this.formatCurrency(totalBalance);
    }

    searchAccounts(query) {
        const container = document.getElementById('accountsTree');

        // إذا كان البحث فارغاً، أعرض جميع الحسابات
        if (!query || !query.trim()) {
            this.renderAccountsTree();
            return;
        }

        const searchTerm = query.trim().toLowerCase();
        const flatAccounts = this.flattenAccounts(this.accounts);

        // البحث الفوري من أول حرف
        const filteredAccounts = flatAccounts.filter(account => {
            const codeMatch = account.code.toLowerCase().includes(searchTerm);
            const nameMatch = account.name.toLowerCase().includes(searchTerm);
            const categoryMatch = account.category && account.category.toLowerCase().includes(searchTerm);

            return codeMatch || nameMatch || categoryMatch;
        });

        if (filteredAccounts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد نتائج</h3>
                    <p>لم يتم العثور على حسابات تطابق البحث "${query}"</p>
                    <small>جرب البحث برقم الحساب أو الاسم</small>
                </div>
            `;
            return;
        }

        // عرض النتائج مع تمييز النص المطابق
        container.innerHTML = '';
        filteredAccounts.forEach(account => {
            const accountNode = this.createSearchResultNode(account, searchTerm);
            container.appendChild(accountNode);
        });

        // تحديث عداد النتائج
        console.log(`🔍 تم العثور على ${filteredAccounts.length} حساب مطابق للبحث: "${query}"`);
    }

    // إنشاء عقدة نتيجة البحث مع تمييز النص
    createSearchResultNode(account, searchTerm) {
        const div = document.createElement('div');
        div.className = 'account-item search-result';

        // تمييز النص المطابق
        const highlightedName = this.highlightSearchTerm(account.name, searchTerm);
        const highlightedCode = this.highlightSearchTerm(account.code, searchTerm);

        div.innerHTML = `
            <div class="account-info">
                <div class="account-header">
                    <span class="account-code">${highlightedCode}</span>
                    <span class="account-name">${highlightedName}</span>
                </div>
                <div class="account-details">
                    <span class="account-type">${this.getTypeLabel(account.type)}</span>
                    <span class="account-balance">${this.formatCurrency(account.balance)}</span>
                </div>
            </div>
        `;

        return div;
    }

    // تمييز النص المطابق في البحث
    highlightSearchTerm(text, searchTerm) {
        if (!searchTerm) return text;

        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // الحصول على تسمية النوع
    getTypeLabel(type) {
        const typeLabels = {
            'assets': 'أصول',
            'liabilities': 'خصوم',
            'equity': 'حقوق ملكية',
            'revenue': 'إيرادات',
            'expenses': 'مصروفات',
            'income': 'دخل',
            'cost': 'تكلفة'
        };
        return typeLabels[type] || type;
    }
}

// الوظائف العامة
let chartOfAccounts;

document.addEventListener('DOMContentLoaded', () => {
    chartOfAccounts = new ChartOfAccountsProfessional();
});

function toggleAccount(accountId) {
    chartOfAccounts.toggleAccount(accountId);
}

function showAddAccountModal() {
    chartOfAccounts.showAddAccountModal();
}

function addSubAccount(parentId) {
    chartOfAccounts.showAddAccountModal(parentId);
}

function editAccount(accountId) {
    const account = chartOfAccounts.findAccountById(accountId);
    if (account) {
        chartOfAccounts.currentEditId = accountId;
        document.getElementById('modalTitle').textContent = 'تعديل الحساب';
        
        document.getElementById('accountCode').value = account.code;
        document.getElementById('accountName').value = account.name;
        document.getElementById('accountType').value = account.type;
        document.getElementById('parentAccount').value = account.parentId || '';
        document.getElementById('accountBalance').value = account.balance || 0;
        document.getElementById('accountDescription').value = account.description || '';
        
        chartOfAccounts.populateParentOptions();
        document.getElementById('accountModal').classList.add('show');
    }
}

function viewAccount(accountId) {
    const account = chartOfAccounts.findAccountById(accountId);
    if (account) {
        alert(`معلومات الحساب:
        
رقم الحساب: ${account.code}
اسم الحساب: ${account.name}
النوع: ${chartOfAccounts.getTypeLabel(account.type)}
الرصيد: ${chartOfAccounts.formatCurrency(account.balance || 0)}
المستوى: ${account.level + 1}
${account.description ? 'الوصف: ' + account.description : ''}`);
    }
}

function deleteAccount(accountId) {
    if (confirm('هل أنت متأكد من حذف هذا الحساب؟\nسيتم حذف جميع الحسابات الفرعية أيضاً.')) {
        chartOfAccounts.deleteAccountById(accountId);
        chartOfAccounts.renderAccountsTree();
        chartOfAccounts.updateStats();
        chartOfAccounts.saveToStorage();
    }
}

function expandAll() {
    chartOfAccounts.expandAll();
}

function collapseAll() {
    chartOfAccounts.collapseAll();
}

function closeModal() {
    chartOfAccounts.closeModal();
}

function exportAccounts() {
    const data = JSON.stringify(chartOfAccounts.accounts, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chart-of-accounts-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function resetAccounts() {
    if (confirm('هل أنت متأكد من إعادة تعيين دليل الحسابات؟\nسيتم فقدان جميع البيانات الحالية.')) {
        chartOfAccounts.accounts = chartOfAccounts.getDefaultAccounts();
        chartOfAccounts.renderAccountsTree();
        chartOfAccounts.updateStats();
        chartOfAccounts.saveToStorage();
    }
}

// دالة إضافة البنك الفرنسي للاختبار
function addFrenchBank() {
    const frenchBankData = {
        code: '1105',
        name: 'البنك الفرنسي',
        type: 'assets',
        parentId: null, // سيتم البحث عن الحساب الأب المناسب
        balance: 50000,
        description: 'حساب البنك الفرنسي - اختبار'
    };

    // البحث عن حساب النقدية والبنوك كحساب أب
    const flatAccounts = chartOfAccounts.flattenAccounts(chartOfAccounts.accounts);
    const bankParent = flatAccounts.find(acc => acc.code === '1101' || acc.name.includes('النقدية'));

    if (bankParent) {
        frenchBankData.parentId = bankParent.id;
    }

    // التحقق من عدم وجود الحساب مسبقاً
    const existingAccount = flatAccounts.find(acc => acc.code === frenchBankData.code);
    if (existingAccount) {
        alert('البنك الفرنسي موجود بالفعل في دليل الحسابات');
        return;
    }

    // إضافة الحساب
    chartOfAccounts.addNewAccount(frenchBankData);
    chartOfAccounts.renderAccountsTree();
    chartOfAccounts.updateStats();
    chartOfAccounts.saveToStorage();

    // توسيع الشجرة لإظهار الحساب الجديد
    if (bankParent) {
        setTimeout(() => {
            chartOfAccounts.toggleAccount(bankParent.id);
        }, 100);
    }

    alert('تم إضافة البنك الفرنسي بنجاح إلى دليل الحسابات!');
}

// دالة اختبار شاملة للنظام
function testProfessionalSystem() {
    console.log('🧪 اختبار النظام الاحترافي...');

    const stats = {
        totalAccounts: chartOfAccounts.flattenAccounts(chartOfAccounts.accounts).length,
        mainAccounts: chartOfAccounts.accounts.length,
        storageWorking: false
    };

    // اختبار التخزين
    try {
        const testData = { test: 'value' };
        localStorage.setItem('test_professional', JSON.stringify(testData));
        const retrieved = JSON.parse(localStorage.getItem('test_professional'));
        localStorage.removeItem('test_professional');
        stats.storageWorking = retrieved.test === 'value';
    } catch (error) {
        console.error('خطأ في التخزين:', error);
    }

    console.log('📊 إحصائيات النظام:', stats);

    alert(`نتائج اختبار النظام الاحترافي:

✅ إجمالي الحسابات: ${stats.totalAccounts}
✅ الحسابات الرئيسية: ${stats.mainAccounts}
${stats.storageWorking ? '✅' : '❌'} التخزين المحلي: ${stats.storageWorking ? 'يعمل' : 'لا يعمل'}

النظام ${stats.storageWorking && stats.totalAccounts > 0 ? 'جاهز للاستخدام' : 'يحتاج إلى مراجعة'}!`);
}
