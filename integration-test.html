<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التكامل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #fff;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn.primary { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
        .btn.secondary { background: rgba(108, 117, 125, 0.3); border-color: #6c757d; }
        .btn.success { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
        .btn.danger { background: rgba(220, 53, 69, 0.3); border-color: #dc3545; }
        .btn.warning { background: rgba(255, 193, 7, 0.3); border-color: #ffc107; }
        .btn.info { background: rgba(23, 162, 184, 0.3); border-color: #17a2b8; }
        
        .result {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .log {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
            border-left: 3px solid;
        }
        
        .log.success { 
            background: rgba(40, 167, 69, 0.2); 
            border-color: #28a745;
            color: #90ee90;
        }
        .log.error { 
            background: rgba(220, 53, 69, 0.2); 
            border-color: #dc3545;
            color: #ffb3b3;
        }
        .log.info { 
            background: rgba(23, 162, 184, 0.2); 
            border-color: #17a2b8;
            color: #87ceeb;
        }
        .log.warning {
            background: rgba(255, 193, 7, 0.2);
            border-color: #ffc107;
            color: #ffeb3b;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .navigation {
            text-align: center;
            margin: 30px 0;
        }

        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .navigation a:hover {
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-link"></i> اختبار نظام التكامل التلقائي</h1>
        
        <div class="navigation">
            <a href="accounting.html"><i class="fas fa-calculator"></i> دليل الحسابات</a>
            <a href="customers.html"><i class="fas fa-users"></i> العملاء</a>
            <a href="suppliers.html"><i class="fas fa-truck"></i> الموردين</a>
        </div>

        <!-- إحصائيات النظام -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> إحصائيات النظام</h2>
            <div class="stats" id="systemStats">
                <div class="stat-card">
                    <div class="stat-number" id="customersCount">-</div>
                    <div class="stat-label">العملاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="suppliersCount">-</div>
                    <div class="stat-label">الموردين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="accountsCount">-</div>
                    <div class="stat-label">الحسابات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="missingCount">-</div>
                    <div class="stat-label">الحسابات المفقودة</div>
                </div>
            </div>
            <button class="btn info" onclick="updateStats()">
                <i class="fas fa-sync"></i> تحديث الإحصائيات
            </button>
        </div>

        <!-- اختبارات التكامل -->
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> اختبارات التكامل</h2>
            <button class="btn primary" onclick="testFullSync()">
                <i class="fas fa-sync-alt"></i> اختبار المزامنة الشاملة
            </button>
            <button class="btn success" onclick="testAddCustomer()">
                <i class="fas fa-user-plus"></i> اختبار إضافة عميل
            </button>
            <button class="btn success" onclick="testAddSupplier()">
                <i class="fas fa-truck-loading"></i> اختبار إضافة مورد
            </button>
            <button class="btn warning" onclick="diagnoseSystem()">
                <i class="fas fa-stethoscope"></i> تشخيص النظام
            </button>
            <button class="btn secondary" onclick="clearResults()">
                <i class="fas fa-broom"></i> مسح النتائج
            </button>
        </div>

        <!-- إدارة البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> إدارة البيانات</h2>
            <button class="btn info" onclick="viewStorageData()">
                <i class="fas fa-eye"></i> عرض بيانات التخزين
            </button>
            <button class="btn warning" onclick="createSampleData()">
                <i class="fas fa-plus-circle"></i> إنشاء بيانات تجريبية
            </button>
            <button class="btn danger" onclick="clearAllData()">
                <i class="fas fa-trash"></i> مسح جميع البيانات
            </button>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h2>
            <div class="result" id="testResults">
                <div class="log info">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="js/integration-system.js"></script>
    <script>
        let testCount = 0;
        let passedTests = 0;

        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-SA')}:</strong> ${message}`;
            results.appendChild(logDiv);
            results.scrollTop = results.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStats() {
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                const customerAccounts = accounts.filter(acc => acc.category === 'customers');
                const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
                
                const missingCustomers = customers.length - customerAccounts.length;
                const missingSuppliers = suppliers.length - supplierAccounts.length;
                const totalMissing = Math.max(0, missingCustomers) + Math.max(0, missingSuppliers);
                
                document.getElementById('customersCount').textContent = customers.length;
                document.getElementById('suppliersCount').textContent = suppliers.length;
                document.getElementById('accountsCount').textContent = accounts.length;
                document.getElementById('missingCount').textContent = totalMissing;
                
                log(`تم تحديث الإحصائيات: ${customers.length} عميل، ${suppliers.length} مورد، ${accounts.length} حساب، ${totalMissing} مفقود`, 'success');
            } catch (error) {
                log(`خطأ في تحديث الإحصائيات: ${error.message}`, 'error');
            }
        }

        function testFullSync() {
            log('🔄 بدء اختبار المزامنة الشاملة...', 'info');
            testCount++;
            
            try {
                if (window.integrationSystem) {
                    const result = window.integrationSystem.fullSync();
                    
                    if (result.total > 0) {
                        log(`✅ المزامنة الشاملة نجحت: ${result.customers} عميل، ${result.suppliers} مورد`, 'success');
                        passedTests++;
                    } else {
                        log('ℹ️ المزامنة الشاملة مكتملة: لا توجد حسابات جديدة', 'info');
                        passedTests++;
                    }
                    
                    updateStats();
                } else {
                    log('❌ نظام التكامل غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في المزامنة الشاملة: ${error.message}`, 'error');
            }
        }

        function testAddCustomer() {
            log('👤 اختبار إضافة عميل جديد...', 'info');
            testCount++;
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const newId = Math.max(...customers.map(c => c.id || 0), 0) + 1;
                
                const testCustomer = {
                    id: newId,
                    name: `عميل اختبار ${newId}`,
                    type: 'شركة',
                    phone: `+966501234${String(newId).padStart(3, '0')}`,
                    email: `test${newId}@example.com`,
                    address: `عنوان اختبار ${newId}`,
                    createdAt: new Date().toISOString()
                };
                
                customers.push(testCustomer);
                localStorage.setItem('monjizCustomers', JSON.stringify(customers));
                
                // إرسال حدث إضافة العميل
                const event = new CustomEvent('customerAdded', { detail: testCustomer });
                document.dispatchEvent(event);
                
                log(`✅ تم إضافة العميل: ${testCustomer.name}`, 'success');
                passedTests++;
                
                setTimeout(() => {
                    updateStats();
                    // التحقق من إضافة الحساب
                    const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                    const customerAccount = accounts.find(acc => acc.linkedId === testCustomer.id);
                    
                    if (customerAccount) {
                        log(`✅ تم إنشاء حساب العميل تلقائياً: ${customerAccount.code}`, 'success');
                    } else {
                        log(`⚠️ لم يتم إنشاء حساب العميل تلقائياً`, 'warning');
                    }
                }, 500);
                
            } catch (error) {
                log(`❌ خطأ في اختبار إضافة العميل: ${error.message}`, 'error');
            }
        }

        function testAddSupplier() {
            log('🚚 اختبار إضافة مورد جديد...', 'info');
            testCount++;
            
            try {
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const newId = Math.max(...suppliers.map(s => s.id || 0), 0) + 1;
                
                const testSupplier = {
                    id: newId,
                    name: `مورد اختبار ${newId}`,
                    type: 'شركة',
                    phone: `+*********${String(newId).padStart(3, '0')}`,
                    email: `supplier${newId}@example.com`,
                    address: `عنوان مورد ${newId}`,
                    createdAt: new Date().toISOString()
                };
                
                suppliers.push(testSupplier);
                localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));
                
                // إرسال حدث إضافة المورد
                const event = new CustomEvent('supplierAdded', { detail: testSupplier });
                document.dispatchEvent(event);
                
                log(`✅ تم إضافة المورد: ${testSupplier.name}`, 'success');
                passedTests++;
                
                setTimeout(() => {
                    updateStats();
                    // التحقق من إضافة الحساب
                    const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                    const supplierAccount = accounts.find(acc => acc.linkedId === testSupplier.id);
                    
                    if (supplierAccount) {
                        log(`✅ تم إنشاء حساب المورد تلقائياً: ${supplierAccount.code}`, 'success');
                    } else {
                        log(`⚠️ لم يتم إنشاء حساب المورد تلقائياً`, 'warning');
                    }
                }, 500);
                
            } catch (error) {
                log(`❌ خطأ في اختبار إضافة المورد: ${error.message}`, 'error');
            }
        }

        function diagnoseSystem() {
            log('🔍 تشخيص النظام...', 'info');
            
            try {
                if (window.integrationSystem) {
                    const diagnosis = window.integrationSystem.diagnose();
                    
                    log(`📊 تشخيص النظام:`, 'info');
                    log(`• العملاء: ${diagnosis.customers.total} (${diagnosis.customers.withAccounts} لديهم حسابات، ${diagnosis.customers.missing} مفقود)`, 'info');
                    log(`• الموردين: ${diagnosis.suppliers.total} (${diagnosis.suppliers.withAccounts} لديهم حسابات، ${diagnosis.suppliers.missing} مفقود)`, 'info');
                    log(`• الحسابات: ${diagnosis.accounts.total} (${diagnosis.accounts.customers} عملاء، ${diagnosis.accounts.suppliers} موردين)`, 'info');
                    
                    if (diagnosis.customers.missing > 0 || diagnosis.suppliers.missing > 0) {
                        log(`⚠️ يوجد ${diagnosis.customers.missing + diagnosis.suppliers.missing} حساب مفقود`, 'warning');
                    } else {
                        log(`✅ جميع الحسابات متزامنة`, 'success');
                    }
                } else {
                    log('❌ نظام التكامل غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في التشخيص: ${error.message}`, 'error');
            }
        }

        function viewStorageData() {
            log('👁️ عرض بيانات التخزين...', 'info');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                log(`📦 بيانات التخزين:`, 'info');
                log(`• العملاء: ${customers.length} عنصر`, 'info');
                log(`• الموردين: ${suppliers.length} عنصر`, 'info');
                log(`• الحسابات: ${accounts.length} عنصر`, 'info');
                
                // عرض عينة من البيانات
                if (customers.length > 0) {
                    log(`• آخر عميل: ${customers[customers.length - 1].name}`, 'info');
                }
                if (suppliers.length > 0) {
                    log(`• آخر مورد: ${suppliers[suppliers.length - 1].name}`, 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ في عرض البيانات: ${error.message}`, 'error');
            }
        }

        function createSampleData() {
            log('🎲 إنشاء بيانات تجريبية...', 'info');
            
            try {
                // إنشاء عملاء تجريبيين
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                
                const sampleCustomers = [
                    { name: 'شركة الأمل للتجارة', type: 'شركة', phone: '+966501111111' },
                    { name: 'مؤسسة النور', type: 'مؤسسة', phone: '+966502222222' },
                    { name: 'أحمد محمد علي', type: 'فرد', phone: '+966503333333' }
                ];
                
                const sampleSuppliers = [
                    { name: 'مورد المواد الخام', type: 'شركة', phone: '+966504444444' },
                    { name: 'شركة التوريدات المتقدمة', type: 'شركة', phone: '+966505555555' }
                ];
                
                let addedCount = 0;
                
                sampleCustomers.forEach((customer, index) => {
                    const newId = Math.max(...customers.map(c => c.id || 0), 0) + index + 1;
                    const newCustomer = {
                        id: newId,
                        ...customer,
                        email: `customer${newId}@example.com`,
                        address: `عنوان ${customer.name}`,
                        createdAt: new Date().toISOString()
                    };
                    
                    customers.push(newCustomer);
                    addedCount++;
                });
                
                sampleSuppliers.forEach((supplier, index) => {
                    const newId = Math.max(...suppliers.map(s => s.id || 0), 0) + index + 1;
                    const newSupplier = {
                        id: newId,
                        ...supplier,
                        email: `supplier${newId}@example.com`,
                        address: `عنوان ${supplier.name}`,
                        createdAt: new Date().toISOString()
                    };
                    
                    suppliers.push(newSupplier);
                    addedCount++;
                });
                
                localStorage.setItem('monjizCustomers', JSON.stringify(customers));
                localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));
                
                log(`✅ تم إنشاء ${addedCount} عنصر تجريبي`, 'success');
                updateStats();
                
            } catch (error) {
                log(`❌ خطأ في إنشاء البيانات التجريبية: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizSuppliers');
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('professionalChartOfAccounts');
                
                log('🗑️ تم حذف جميع البيانات', 'warning');
                updateStats();
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<div class="log info">تم مسح النتائج...</div>';
            testCount = 0;
            passedTests = 0;
        }

        // تشغيل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة اختبار التكامل', 'success');
            updateStats();
            
            // اختبار وجود نظام التكامل
            if (window.integrationSystem) {
                log('✅ نظام التكامل متاح ويعمل', 'success');
            } else {
                log('❌ نظام التكامل غير متاح', 'error');
            }
        });
    </script>
</body>
</html>
