<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح القائمة المنسدلة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .demo-dropdown {
            position: relative;
            display: inline-block;
            margin: 20px;
        }
        .demo-dropdown .dropdown-toggle {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .demo-dropdown .dropdown-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .demo-dropdown .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 8px;
            border: 1px solid #ddd;
            top: 100%;
            right: 0;
        }
        .demo-dropdown .dropdown-menu a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }
        .demo-dropdown .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
        .demo-dropdown .dropdown-menu a i {
            margin-left: 8px;
            width: 16px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح القائمة المنسدلة</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>أيقونات الطباعة والتصدير في المشتريات والعملاء لا تعمل - القائمة المنسدلة لا تظهر</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>دالة toggleDropdown غير موجودة</li>
                        <li>أنماط CSS للقائمة المنسدلة غير موجودة</li>
                        <li>القائمة لا تظهر عند النقر</li>
                        <li>الأيقونات غير قابلة للنقر</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>دالة toggleDropdown مضافة</li>
                        <li>أنماط CSS كاملة للقائمة المنسدلة</li>
                        <li>القائمة تظهر وتختفي بالنقر</li>
                        <li>جميع الأيقونات تعمل بشكل صحيح</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- عرض توضيحي للقائمة المنسدلة -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للقائمة المنسدلة</h2>
            <div class="highlight">
                <h3>جرب القائمة المنسدلة التجريبية:</h3>
            </div>
            
            <div class="demo-dropdown">
                <button class="dropdown-toggle" onclick="toggleDemoDropdown()">
                    <i class="fas fa-print"></i>
                    طباعة وتصدير
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="dropdown-menu" id="demo-dropdown">
                    <a href="#" onclick="testPrint()"><i class="fas fa-print"></i> طباعة التقرير</a>
                    <a href="#" onclick="testExcel()"><i class="fas fa-file-excel"></i> تصدير Excel</a>
                    <a href="#" onclick="testPDF()"><i class="fas fa-file-pdf"></i> تصدير PDF</a>
                </div>
            </div>
            
            <div id="demo-result"></div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            <div class="step-list">
                <h3>التحسينات المضافة:</h3>
                <ol>
                    <li><strong>دالة toggleDropdown:</strong> إظهار/إخفاء القائمة المنسدلة</li>
                    <li><strong>أنماط CSS:</strong> تنسيق احترافي للقائمة المنسدلة</li>
                    <li><strong>إغلاق تلقائي:</strong> إغلاق القائمة عند النقر خارجها</li>
                    <li><strong>تأثيرات بصرية:</strong> ظلال وانتقالات سلسة</li>
                    <li><strong>دوال الطباعة والتصدير:</strong> دوال كاملة وفعالة</li>
                </ol>
            </div>
        </div>

        <!-- الصفحات المصلحة -->
        <div class="test-section">
            <h2>📄 الصفحات المصلحة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>صفحة المشتريات</h3>
                    <p>تم إصلاح القائمة المنسدلة ودوال الطباعة والتصدير</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>صفحة العملاء</h3>
                    <p>تم إصلاح القائمة المنسدلة ودوال الطباعة والتصدير</p>
                    <button class="btn" onclick="openCustomersPage()">فتح الصفحة</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار القائمة المنسدلة والوظائف:</h3>
                <p>سنختبر القائمة المنسدلة ووظائف الطباعة والتصدير في كلا الصفحتين</p>
            </div>
            
            <button class="btn" onclick="startDropdownTest()">🚀 بدء اختبار القائمة المنسدلة</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار الوظائف:</h3>
                <ol>
                    <li><strong>اختبار القائمة التجريبية:</strong> اضغط على القائمة أعلاه</li>
                    <li><strong>فتح صفحة المشتريات:</strong> اضغط "فتح الصفحة"</li>
                    <li><strong>اختبار القائمة:</strong> اضغط "طباعة وتصدير"</li>
                    <li><strong>اختبار الوظائف:</strong> جرب كل خيار في القائمة</li>
                    <li><strong>فتح صفحة العملاء:</strong> كرر نفس الاختبار</li>
                    <li><strong>التحقق:</strong> تأكد من عمل جميع الوظائف</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="openCustomersPage()">👥 فتح صفحة العملاء</button>
            <button class="btn" onclick="addTestData()">📊 إضافة بيانات تجريبية</button>
            <button class="btn" onclick="testAllFunctions()">🧪 اختبار جميع الوظائف</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>القائمة المنسدلة:</strong> تظهر وتختفي بالنقر على الزر</li>
                    <li><strong>الطباعة:</strong> تطبع تقرير مخصص للصفحة</li>
                    <li><strong>تصدير Excel:</strong> ينزل ملف CSV للبيانات</li>
                    <li><strong>تصدير PDF:</strong> يفتح نافذة جديدة بتقرير PDF</li>
                    <li><strong>الإغلاق التلقائي:</strong> تغلق القائمة عند النقر خارجها</li>
                    <li><strong>التنسيق:</strong> قائمة منسقة ومتجاوبة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // دالة إظهار/إخفاء القائمة التجريبية
        function toggleDemoDropdown() {
            const dropdown = document.getElementById('demo-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('demo-dropdown');
            const demoDropdown = document.querySelector('.demo-dropdown');
            if (dropdown && !demoDropdown.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

        // اختبار الطباعة
        function testPrint() {
            document.getElementById('demo-dropdown').style.display = 'none';
            showDemoResult('🖨️ تم اختبار وظيفة الطباعة!', 'success');
        }

        // اختبار Excel
        function testExcel() {
            document.getElementById('demo-dropdown').style.display = 'none';
            showDemoResult('📊 تم اختبار وظيفة تصدير Excel!', 'success');
        }

        // اختبار PDF
        function testPDF() {
            document.getElementById('demo-dropdown').style.display = 'none';
            showDemoResult('📄 تم اختبار وظيفة تصدير PDF!', 'success');
        }

        // بدء اختبار القائمة المنسدلة
        function startDropdownTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار القائمة المنسدلة!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ جرب القائمة التجريبية أعلاه<br>
                    2️⃣ افتح صفحة المشتريات<br>
                    3️⃣ اضغط زر "طباعة وتصدير"<br>
                    4️⃣ جرب كل خيار في القائمة<br>
                    5️⃣ افتح صفحة العملاء وكرر الاختبار<br><br>
                    
                    <strong>🎯 اضغط أزرار فتح الصفحات للاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 ابحث عن زر "طباعة وتصدير" واختبر القائمة المنسدلة!', 'info');
        }

        // فتح صفحة العملاء
        function openCustomersPage() {
            window.open('customers.html', '_blank');
            showResult('👥 تم فتح صفحة العملاء<br>💡 ابحث عن زر "طباعة وتصدير" واختبر القائمة المنسدلة!', 'info');
        }

        // إضافة بيانات تجريبية
        function addTestData() {
            // إضافة بيانات مشتريات
            const purchases = [
                { id: 1, invoiceNumber: 'INV-001', date: new Date().toISOString(), supplier: 'مورد تجريبي', amount: '1000', payment: 'نقدي', status: 'مكتمل', notes: 'اختبار' }
            ];
            localStorage.setItem('monjizPurchases', JSON.stringify(purchases));

            // إضافة بيانات عملاء
            const customers = [
                { id: 1, name: 'عميل تجريبي', type: 'شركة', phone: '+966501234567', email: '<EMAIL>', address: 'الرياض', createdAt: new Date().toISOString() }
            ];
            localStorage.setItem('monjizCustomers', JSON.stringify(customers));

            showResult('📊 تم إضافة بيانات تجريبية للمشتريات والعملاء<br>💡 الآن يمكنك اختبار الطباعة والتصدير!', 'info');
        }

        // اختبار جميع الوظائف
        function testAllFunctions() {
            showResult(`
                <div class="info">
                    🧪 <strong>اختبار جميع الوظائف:</strong><br><br>
                    
                    <strong>✅ الوظائف المصلحة:</strong><br>
                    • دالة toggleDropdown() تعمل<br>
                    • أنماط CSS للقائمة المنسدلة مضافة<br>
                    • دوال الطباعة والتصدير كاملة<br>
                    • إغلاق تلقائي للقائمة<br>
                    • تأثيرات بصرية سلسة<br><br>
                    
                    <strong>📋 الصفحات المصلحة:</strong><br>
                    • صفحة المشتريات ✅<br>
                    • صفحة العملاء ✅<br><br>
                    
                    🎯 <strong>جميع الوظائف جاهزة للاختبار!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // عرض نتائج العرض التوضيحي
        function showDemoResult(message, type = 'info') {
            document.getElementById('demo-result').innerHTML = `<div class="${type}" style="margin-top: 15px; padding: 15px; border-radius: 8px; text-align: center;">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح القائمة المنسدلة بنجاح!</strong><br><br>
                    ✅ دالة toggleDropdown مضافة<br>
                    ✅ أنماط CSS للقائمة المنسدلة<br>
                    ✅ إغلاق تلقائي عند النقر خارجها<br>
                    ✅ دوال الطباعة والتصدير كاملة<br>
                    ✅ تنسيق احترافي ومتجاوب<br>
                    ✅ تأثيرات بصرية سلسة<br><br>
                    🧪 <strong>اضغط "بدء اختبار القائمة المنسدلة" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
