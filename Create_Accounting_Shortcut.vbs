' VBScript to create a shortcut to accounting.html on the desktop
' Created to fix accounting management functions issue

Option Explicit

Dim objShell, objFSO, strCurrentDir, strAccountingPath, strDesktopPath, strShortcutPath

' Create file system object and shell object
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objShell = CreateObject("WScript.Shell")

' Get the current directory (where this script is located)
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Build the path to accounting.html
strAccountingPath = objFSO.BuildPath(strCurrentDir, "accounting.html")

' Get the desktop path
strDesktopPath = objShell.SpecialFolders("Desktop")

' Build the shortcut path
strShortcutPath = objFSO.BuildPath(strDesktopPath, "إدارة الحسابات - Monjiz.lnk")

' Check if the accounting.html file exists
If objFSO.FileExists(strAccountingPath) Then
    ' Create the shortcut
    Dim objShortcut
    Set objShortcut = objShell.CreateShortcut(strShortcutPath)
    
    ' Set shortcut properties
    objShortcut.TargetPath = strAccountingPath
    objShortcut.WorkingDirectory = strCurrentDir
    objShortcut.Description = "فتح صفحة إدارة الحسابات في Monjiz"
    objShortcut.IconLocation = strCurrentDir & "\favicon.ico, 0"
    
    ' Save the shortcut
    objShortcut.Save
    
    ' Display success message
    MsgBox "تم إنشاء اختصار لصفحة إدارة الحسابات على سطح المكتب بنجاح!", vbInformation, "تم الإنشاء"
Else
    ' Display error message if the file doesn't exist
    MsgBox "لم يتم العثور على ملف صفحة الحسابات في: " & strAccountingPath, vbExclamation, "خطأ"
End If

' Clean up
Set objShell = Nothing
Set objFSO = Nothing