@echo off
rem هذا الملف يساعد على تشغيل ملف PowerShell بدون مشاكل الحرف العربي

setlocal

rem تحديد المسار الكامل للملف
set "PS1_FILE=%~dp0open_launcher.ps1"

rem التأكد من وجود الملف
if not exist "%PS1_FILE%" (
    echo File not found: %PS1_FILE%
    pause
    exit /b 1
)

rem محاولة تشغيل الملف باستخدام PowerShell
echo Attempting to run PowerShell file...
powershell -ExecutionPolicy Bypass -File "%PS1_FILE%"

rem التحقق من نجاح التشغيل
if %ERRORLEVEL% neq 0 (
    echo Failed to run with PowerShell. Trying direct launcher.html...
    start "" "%~dp0launcher.html"
)

endlocal
exit /b 0