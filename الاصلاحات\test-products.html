<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المنتجات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-btn { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 10px;
        }
        .modal-modern {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }
        .modal-content-modern {
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
        }
        .modal-header-modern {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .close-modal-modern {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .form-group-modern {
            margin-bottom: 15px;
        }
        .form-group-modern label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-modern {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn-modern {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
    </style>
</head>
<body>
    <h1>اختبار وظائف المنتجات</h1>
    
    <button class="test-btn" onclick="testShowModal()">اختبار فتح النافذة</button>
    <button class="test-btn" onclick="testConsole()">اختبار Console</button>
    
    <div id="results"></div>

    <!-- نافذة إضافة منتج جديد -->
    <div class="modal-modern" id="add-product-modal">
        <div class="modal-content-modern">
            <div class="modal-header-modern">
                <h3><i class="fas fa-plus-circle"></i> إضافة منتج جديد</h3>
                <button class="close-modal-modern" onclick="closeModal('add-product-modal')">×</button>
            </div>
            <div class="modal-body-modern">
                <form id="add-product-form">
                    <div class="form-group-modern">
                        <label for="product-name">اسم المنتج *</label>
                        <input type="text" id="product-name" name="product-name" required class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-category">الفئة *</label>
                        <select id="product-category" name="product-category" required class="input-modern">
                            <option value="">اختر الفئة</option>
                            <option value="food">مواد غذائية</option>
                            <option value="electronics">إلكترونيات</option>
                        </select>
                    </div>
                    <div class="form-group-modern">
                        <label for="product-sku">رمز المنتج *</label>
                        <input type="text" id="product-sku" name="product-sku" required class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-price">سعر البيع *</label>
                        <input type="number" id="product-price" name="product-price" required step="0.01" min="0" class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-cost">سعر التكلفة *</label>
                        <input type="number" id="product-cost" name="product-cost" required step="0.01" min="0" class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-quantity">الكمية *</label>
                        <input type="number" id="product-quantity" name="product-quantity" required min="0" class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-min-quantity">الحد الأدنى *</label>
                        <input type="number" id="product-min-quantity" name="product-min-quantity" required min="0" class="input-modern">
                    </div>
                    <div class="form-group-modern">
                        <label for="product-description">الوصف</label>
                        <textarea id="product-description" name="product-description" class="input-modern" rows="3"></textarea>
                    </div>
                    <div class="form-actions-modern">
                        <button type="submit" class="btn-modern btn-primary">حفظ المنتج</button>
                        <button type="button" class="btn-modern btn-outline" onclick="closeModal('add-product-modal')">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // وظائف الاختبار
        function testShowModal() {
            console.log('اختبار فتح النافذة');
            showAddProductModal();
        }
        
        function testConsole() {
            console.log('اختبار Console يعمل!');
            document.getElementById('results').innerHTML += '<p>تم اختبار Console - انظر إلى Developer Tools</p>';
        }
        
        function showAddProductModal() {
            console.log('محاولة فتح نافذة إضافة منتج');
            const modal = document.getElementById('add-product-modal');
            if (modal) {
                console.log('تم العثور على النافذة، فتحها الآن');
                modal.style.display = 'flex';
                document.getElementById('results').innerHTML += '<p>تم فتح النافذة بنجاح!</p>';
            } else {
                console.log('لم يتم العثور على النافذة!');
                document.getElementById('results').innerHTML += '<p>خطأ: لم يتم العثور على النافذة!</p>';
            }
        }
        
        function closeModal(modalId) {
            console.log('إغلاق النافذة:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.getElementById('results').innerHTML += '<p>تم إغلاق النافذة</p>';
            }
        }
        
        // معالج النموذج
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل DOM');
            const form = document.getElementById('add-product-form');
            if (form) {
                console.log('تم العثور على النموذج');
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log('تم إرسال النموذج!');
                    
                    const formData = new FormData(this);
                    const name = formData.get('product-name');
                    const category = formData.get('product-category');
                    
                    console.log('بيانات المنتج:', { name, category });
                    document.getElementById('results').innerHTML += `<p>تم إرسال المنتج: ${name} - ${category}</p>`;
                    
                    closeModal('add-product-modal');
                });
            } else {
                console.log('لم يتم العثور على النموذج');
            }
        });
    </script>
</body>
</html>
