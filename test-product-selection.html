<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اختيار المنتجات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #6f42c1;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #6f42c1, #007bff);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(111,66,193,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(111,66,193,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #6f42c1;
            border-bottom: 3px solid #6f42c1;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .problem-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .solution-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار مشكلة اختفاء المنتجات</h1>

        <!-- وصف المشكلة -->
        <div class="test-section">
            <h2>❌ وصف المشكلة</h2>
            <div class="problem-box">
                <h3>المشكلة المبلغ عنها:</h3>
                <p><strong>"في فاتورة مبيعات جديدة بعد اختيار المنتج الثاني يختفي من النافذة اسم المنتج الأول لكن يبقى السعر"</strong></p>
                
                <h4>تفاصيل المشكلة:</h4>
                <ul>
                    <li>عند اختيار منتج في الصف الأول - يعمل بشكل طبيعي</li>
                    <li>عند إضافة صف ثاني واختيار منتج فيه - يختفي اسم المنتج من الصف الأول</li>
                    <li>السعر يبقى موجود في الصف الأول</li>
                    <li>هذا يسبب مشكلة في عرض الفاتورة</li>
                </ul>
            </div>
        </div>

        <!-- الحل المطبق -->
        <div class="test-section">
            <h2>✅ الحل المطبق</h2>
            <div class="solution-box">
                <h3>السبب الجذري:</h3>
                <p>دالة <code>loadProductsFromStorage()</code> كانت تمسح جميع قوائم المنتجات وتعيد إنشاؤها، مما يؤدي لفقدان الاختيارات السابقة.</p>
                
                <h4>الإصلاحات المطبقة:</h4>
                <ol>
                    <li><strong>حفظ الاختيارات الحالية:</strong> قبل إعادة تحميل القوائم</li>
                    <li><strong>استعادة الاختيارات:</strong> بعد إعادة إنشاء القوائم</li>
                    <li><strong>تحديث انتقائي:</strong> تحديث الصفوف الجديدة فقط عند الإمكان</li>
                    <li><strong>دالة منفصلة:</strong> لتحميل المنتجات في قوائم محددة</li>
                </ol>
            </div>
        </div>

        <!-- إعداد الاختبار -->
        <div class="test-section">
            <h2>🛠️ إعداد الاختبار</h2>
            <button class="btn success" onclick="setupTestData()">إعداد بيانات الاختبار</button>
            <button class="btn primary" onclick="checkCurrentData()">فحص البيانات الحالية</button>
            <div id="setup-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>🧪 خطوات الاختبار</h2>
            <button class="btn" onclick="openSalesForTest()">فتح المبيعات للاختبار</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار اليدوي:</h3>
                <ol>
                    <li><strong>اضغط "إعداد بيانات الاختبار"</strong> لضمان وجود منتجات كافية</li>
                    <li><strong>اضغط "فتح المبيعات للاختبار"</strong></li>
                    <li><strong>في صفحة المبيعات:</strong>
                        <ul>
                            <li>اضغط "فاتورة جديدة"</li>
                            <li>اختر عميل من القائمة</li>
                            <li><strong>اختر منتج في الصف الأول</strong> (مثل: جهاز عرض محمول)</li>
                            <li>تأكد من ظهور اسم المنتج والسعر</li>
                            <li><strong>اضغط "إضافة منتج آخر"</strong></li>
                            <li><strong>اختر منتج في الصف الثاني</strong> (مثل: لابتوب Dell)</li>
                            <li><strong>تحقق من الصف الأول:</strong> يجب أن يبقى اسم المنتج ظاهراً</li>
                            <li>إذا اختفى اسم المنتج الأول = المشكلة لم تُحل</li>
                            <li>إذا بقي اسم المنتج الأول = المشكلة تم حلها ✅</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="highlight">
                <h3>بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li>✅ <strong>اسم المنتج الأول يبقى ظاهراً</strong> حتى بعد اختيار منتج ثاني</li>
                    <li>✅ <strong>السعر يبقى صحيحاً</strong> في جميع الصفوف</li>
                    <li>✅ <strong>يمكن إضافة عدة منتجات</strong> دون فقدان الاختيارات السابقة</li>
                    <li>✅ <strong>حفظ الفاتورة يعمل</strong> مع جميع المنتجات المختارة</li>
                    <li>✅ <strong>أسماء المنتجات تظهر صحيحة</strong> في الفاتورة المحفوظة</li>
                </ul>
            </div>
        </div>

        <!-- تشخيص إضافي -->
        <div class="test-section">
            <h2>🔍 تشخيص إضافي</h2>
            <button class="btn primary" onclick="simulateProductSelection()">محاكاة اختيار المنتجات</button>
            <div id="diagnosis-result"></div>
        </div>
    </div>

    <script>
        // إعداد بيانات الاختبار
        function setupTestData() {
            const testProducts = [
                { id: 'test-1', name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000, quantity: 10, minStock: 2 },
                { id: 'test-2', name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2 },
                { id: 'test-3', name: 'هاتف آيفون 15 Pro', category: 'phones', code: 'IPHONE-15P', price: 5200, cost: 4500, quantity: 5, minStock: 1 },
                { id: 'test-4', name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5 },
                { id: 'test-5', name: 'شاشة سامسونج 32 بوصة', category: 'electronics', code: 'SAM-32', price: 1800, cost: 1400, quantity: 6, minStock: 2 }
            ];

            localStorage.setItem('monjizProducts', JSON.stringify(testProducts));

            const testCustomers = [
                { id: 'cust-1', name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { id: 'cust-2', name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' }
            ];

            localStorage.setItem('monjizCustomers', JSON.stringify(testCustomers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('setup-result', '✅ تم إعداد بيانات الاختبار بنجاح!<br>📦 5 منتجات متنوعة<br>👥 2 عميل<br>🔄 تم إرسال إشعار التحديث', 'success');
        }

        // فحص البيانات الحالية
        function checkCurrentData() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">📊 البيانات الحالية:</div>';
            result += `<p><strong>المنتجات:</strong> ${products.length} عنصر</p>`;
            result += `<p><strong>العملاء:</strong> ${customers.length} عنصر</p>`;
            
            if (products.length >= 3) {
                result += '<p>✅ عدد المنتجات كافي للاختبار</p>';
                result += '<p><strong>المنتجات المتوفرة:</strong></p><ul>';
                products.slice(0, 3).forEach(product => {
                    result += `<li>${product.name} - ${product.price} ر.س</li>`;
                });
                result += '</ul>';
            } else {
                result += '<p>❌ عدد المنتجات غير كافي - اضغط "إعداد بيانات الاختبار"</p>';
            }
            
            document.getElementById('setup-result').innerHTML = result;
        }

        // فتح المبيعات للاختبار
        function openSalesForTest() {
            window.open('sales.html', '_blank');
            showResult('test-result', '🚀 تم فتح صفحة المبيعات<br>💡 اتبع خطوات الاختبار المذكورة أعلاه<br>🔍 راقب ما إذا كان اسم المنتج الأول يختفي أم لا', 'info');
        }

        // محاكاة اختيار المنتجات
        function simulateProductSelection() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            if (products.length < 2) {
                showResult('diagnosis-result', '❌ تحتاج منتجين على الأقل للمحاكاة - اضغط "إعداد بيانات الاختبار"', 'error');
                return;
            }

            let result = '<div class="info">🧪 محاكاة اختيار المنتجات:</div>';
            
            // محاكاة الخطوات
            result += '<h4>الخطوة 1: اختيار المنتج الأول</h4>';
            result += `<p>✅ تم اختيار: ${products[0].name} - ${products[0].price} ر.س</p>`;
            result += '<p>✅ اسم المنتج ظاهر، السعر ظاهر</p>';
            
            result += '<h4>الخطوة 2: إضافة صف جديد</h4>';
            result += '<p>✅ تم إضافة صف ثاني للمنتجات</p>';
            
            result += '<h4>الخطوة 3: اختيار المنتج الثاني</h4>';
            result += `<p>✅ تم اختيار: ${products[1].name} - ${products[1].price} ر.س</p>`;
            
            result += '<h4>الخطوة 4: فحص الصف الأول</h4>';
            result += '<p><strong>النتيجة المتوقعة بعد الإصلاح:</strong></p>';
            result += `<p>✅ الصف الأول: ${products[0].name} - ${products[0].price} ر.س (يجب أن يبقى ظاهراً)</p>`;
            result += `<p>✅ الصف الثاني: ${products[1].name} - ${products[1].price} ر.س</p>`;
            
            result += '<div class="success">🎯 إذا كان كلا المنتجين ظاهرين = المشكلة تم حلها!</div>';
            
            document.getElementById('diagnosis-result').innerHTML = result;
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            checkCurrentData();
        });
    </script>
</body>
</html>
