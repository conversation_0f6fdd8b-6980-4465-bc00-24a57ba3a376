<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الأعمال - التقارير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* تخصيص منطقة التقارير */
        .reports-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: calc(100vh - 80px);
            padding: 20px;
            margin-top: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .report-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .report-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 32px;
            position: relative;
            z-index: 1;
        }

        .report-info-enhanced {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }

        .report-icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .report-main-icon {
            font-size: 24px;
            color: white;
            display: block;
        }

        .report-text-info h2 {
            font-size: 22px;
            font-weight: 700;
            margin: 0 0 8px 0;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .report-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .breadcrumb-separator {
            opacity: 0.6;
        }

        .report-actions-enhanced {
            display: flex;
            gap: 12px;
        }

        .btn-enhanced {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-family: 'Cairo', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .btn-back {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-back:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-generate {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.6);
        }

        .report-categories-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .category-card-modern {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .category-card-modern:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .category-icon-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            transition: all 0.3s ease;
        }

        .category-card-modern:hover .category-icon-modern {
            transform: scale(1.1);
        }

        .category-content-modern h3 {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .category-content-modern p {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        #selected-report {
            display: none;
        }

        .report-content-modern {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* تنسيق التقارير المتاحة */
        .available-reports-modern {
            margin-top: 20px;
        }

        .reports-grid-modern {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .report-card-modern {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .report-card-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .report-icon-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .report-details-modern {
            flex: 1;
        }

        .report-details-modern h5 {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .report-details-modern p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .report-action-modern {
            color: #667eea;
            font-size: 18px;
            flex-shrink: 0;
        }

        /* تنسيقات التقارير المفصلة */
        .report-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 20px;
        }

        .report-header h2 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .report-header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }

        .report-table th {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #495057;
        }

        .report-table td {
            border: 1px solid #dee2e6;
            padding: 10px 8px;
            text-align: center;
        }

        .report-table .subtotal {
            background: #e9ecef;
            font-weight: 600;
        }

        .report-table .total {
            background: #667eea;
            color: white;
            font-weight: 700;
        }

        .report-table .final-total {
            background: #28a745;
            color: white;
            font-weight: 700;
        }

        .report-table .profit {
            background: #d4edda;
            color: #155724;
            font-weight: 600;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .summary-card h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .summary-card .amount {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
        }

        .summary-card .count {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .summary-card .average {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .summary-card .warning {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
            color: #ffc107;
        }

        /* تحسين تنسيق التقارير */
        .report-container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .report-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .report-header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .report-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .report-summary .summary-card {
            background: white;
            color: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .report-summary .summary-card h3 {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .report-summary .summary-card .summary-value {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
        }

        .report-table-container {
            padding: 0;
            margin: 0;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            background: white;
        }

        .report-table th {
            background: #667eea;
            color: white;
            padding: 12px 8px;
            text-align: right;
            font-size: 14px;
            font-weight: 600;
        }

        .report-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }

        .report-table tr:hover {
            background: #f8f9fa;
        }

        .no-data-message {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            background: white;
        }

        .no-data-message i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 15px;
            display: block;
        }

        .no-data-message small {
            display: block;
            margin-top: 10px;
            color: #999;
            font-size: 12px;
        }

        /* تحسين النافذة المنبثقة للتقارير */
        .modal-overlay .modal-content {
            max-width: 95vw;
            max-height: 90vh;
            overflow-y: auto;
            margin: 2vh auto;
        }

        .modal-overlay .report-container {
            border-radius: 0;
            box-shadow: none;
        }

        /* تحسين الجدول للشاشات الصغيرة */
        @media (max-width: 768px) {
            .report-summary {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
                padding: 15px;
            }

            .report-summary .summary-card {
                padding: 10px;
            }

            .report-summary .summary-card h3 {
                font-size: 11px;
            }

            .report-summary .summary-card .summary-value {
                font-size: 16px;
            }

            .report-table th,
            .report-table td {
                padding: 8px 4px;
                font-size: 12px;
            }
        }

        /* تحسين ألوان الحالة */
        .status-paid {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .report-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .btn-print, .btn-excel, .btn-pdf {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-print {
            background: #6c757d;
            color: white;
        }

        .btn-excel {
            background: #28a745;
            color: white;
        }

        .btn-pdf {
            background: #dc3545;
            color: white;
        }

        .btn-print:hover, .btn-excel:hover, .btn-pdf:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* حالات الأصناف والعملاء */
        .status-paid { background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-pending { background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-good { background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-warning { background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-critical { background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-low { background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px; }

        /* عناصر التحكم في التقارير */
        .report-controls-modern {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-apply-filters {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }

        .btn-apply-filters:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-reset-filters {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }

        .btn-reset-filters:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* ========== شريط التنقل الرئيسي الموحد ========== */
        .main-header {
            background: linear-gradient(to left, #0078ff, #00c3ff);
            color: white;
            padding: 10px 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            width: 100%;
            height: 70px;
        }

        .main-header .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            height: 100%;
        }

        .main-header .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }

        .main-header .logo i {
            font-size: 28px;
            color: #f39c12;
        }

        .main-header .logo h1 {
            font-size: 24px;
            margin: 0;
            font-weight: 600;
            color: white;
        }

        .main-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 0;
        }

        .main-nav li {
            margin: 0;
        }

        .main-nav a {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            height: 50px;
            box-sizing: border-box;
        }

        .main-nav a:hover {
            background: rgba(255,255,255,0.1);
            border-bottom-color: #f39c12;
            transform: translateY(-1px);
        }

        .main-nav a.active {
            background: rgba(255,255,255,0.15);
            border-bottom-color: #f39c12;
            font-weight: 600;
        }

        .main-nav a i {
            font-size: 16px;
            width: 18px;
            text-align: center;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .main-header .container {
                flex-direction: column;
                height: auto;
                padding: 10px 20px;
            }

            .main-header .logo {
                margin-bottom: 10px;
            }

            .main-nav ul {
                flex-wrap: wrap;
                justify-content: center;
                gap: 5px;
            }

            .main-nav a {
                padding: 12px 15px;
                font-size: 13px;
                height: auto;
            }

            .main-header .logo h1 {
                font-size: 20px;
            }
        }

        @media (max-width: 480px) {
            .main-nav ul {
                flex-direction: column;
                width: 100%;
            }

            .main-nav a {
                justify-content: center;
                padding: 15px;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="suppliers.html"><i class="fas fa-user-tie"></i> الموردين</a></li>
                    <li><a href="products.html"><i class="fas fa-boxes"></i> المخزون</a></li>
                    <li><a href="reports.html" class="active"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- قسم التقارير -->
    <div class="reports-section">
        <div class="container">
        <!-- رأس التقرير -->
        <div class="report-header-modern" id="selected-report">
            <div class="report-header-content">
                <div class="report-info-enhanced">
                    <div class="report-icon-wrapper">
                        <i class="fas fa-chart-line report-main-icon"></i>
                    </div>
                    <div class="report-text-info">
                        <h2 id="report-title">تقرير مفصل</h2>
                        <div class="report-breadcrumb">
                            <span class="breadcrumb-item">
                                <i class="fas fa-home"></i> التقارير
                            </span>
                            <span class="breadcrumb-separator">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                            <span class="breadcrumb-item active" id="breadcrumb-category">
                                المالية
                            </span>
                        </div>
                    </div>
                </div>

                <div class="report-actions-enhanced">
                    <button class="btn-enhanced btn-back" onclick="goBack()">
                        <i class="fas fa-arrow-right"></i>
                        <span>العودة</span>
                    </button>
                    <button class="btn-enhanced btn-generate" onclick="generateDetailedReport()" id="generate-report-btn" style="display: none;">
                        <i class="fas fa-play"></i>
                        <span>إنشاء التقرير</span>
                    </button>
                    <button class="btn-enhanced btn-test" onclick="testReportsSystem()" style="background: linear-gradient(135deg, #28a745, #20c997); margin-left: 10px;">
                        <i class="fas fa-flask"></i>
                        <span>اختبار النظام</span>
                    </button>
                    <button class="btn-enhanced btn-add-test" onclick="addTestInvoice()" style="background: linear-gradient(135deg, #007bff, #0056b3); margin-left: 10px;">
                        <i class="fas fa-plus"></i>
                        <span>إضافة فاتورة تجريبية</span>
                    </button>
                    <button class="btn-enhanced btn-add-stock" onclick="addTestStockMovements()" style="background: linear-gradient(135deg, #6f42c1, #5a32a3); margin-left: 10px;">
                        <i class="fas fa-boxes"></i>
                        <span>إضافة حركات مخزون</span>
                    </button>
                </div>
            </div>

            <!-- عناصر التحكم في التقرير -->
            <div class="report-controls-modern" id="report-controls" style="display: none;">
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="report-date-from">من تاريخ:</label>
                        <input type="date" id="report-date-from" class="form-control">
                    </div>
                    <div class="control-group">
                        <label for="report-date-to">إلى تاريخ:</label>
                        <input type="date" id="report-date-to" class="form-control">
                    </div>
                    <div class="control-group">
                        <label for="report-account">الحساب:</label>
                        <select id="report-account" class="form-control">
                            <!-- سيتم ملء الخيارات ديناميكياً حسب نوع التقرير -->
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="report-branch">الفرع:</label>
                        <select id="report-branch" class="form-control">
                            <option value="">جميع الفروع</option>
                            <option value="main">الفرع الرئيسي</option>
                            <option value="branch1">فرع الرياض</option>
                            <option value="branch2">فرع جدة</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <button onclick="applyReportFilters()" class="btn-apply-filters">
                            <i class="fas fa-filter"></i> تطبيق المرشحات
                        </button>
                    </div>
                    <div class="control-group">
                        <button onclick="resetReportFilters()" class="btn-reset-filters">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- محتوى التقرير المحسن -->
            <div class="report-content-modern" id="report-content">
                <!-- سيتم ملء هذا القسم ديناميكياً حسب الفئة المختارة -->
            </div>
        </div>

        <!-- اختيار نوع التقرير -->
        <div class="report-categories-modern">
            <div class="category-card-modern" data-category="financial" onclick="selectCategory('financial')">
                <div class="category-icon-modern">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="category-content-modern">
                    <h3>التقارير المالية</h3>
                    <p>قوائم مالية وتقارير محاسبية شاملة</p>
                </div>
            </div>

            <div class="category-card-modern" data-category="sales" onclick="selectCategory('sales')">
                <div class="category-icon-modern">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="category-content-modern">
                    <h3>تقارير المبيعات</h3>
                    <p>تحليل المبيعات والأداء التجاري</p>
                </div>
            </div>

            <div class="category-card-modern" data-category="purchases" onclick="selectCategory('purchases')">
                <div class="category-icon-modern">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="category-content-modern">
                    <h3>تقارير المشتريات</h3>
                    <p>تحليل المشتريات وأداء الموردين</p>
                </div>
            </div>

            <div class="category-card-modern" data-category="inventory" onclick="selectCategory('inventory')">
                <div class="category-icon-modern">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="category-content-modern">
                    <h3>تقارير المخزون</h3>
                    <p>حركة المخزون والجرد والتقييم</p>
                </div>
            </div>

            <div class="category-card-modern" data-category="customers" onclick="selectCategory('customers')">
                <div class="category-icon-modern">
                    <i class="fas fa-users"></i>
                </div>
                <div class="category-content-modern">
                    <h3>تقارير العملاء</h3>
                    <p>تحليل العملاء والمديونيات</p>
                </div>
            </div>

            <div class="category-card-modern" data-category="suppliers" onclick="selectCategory('suppliers')">
                <div class="category-icon-modern">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="category-content-modern">
                    <h3>تقارير الموردين</h3>
                    <p>تحليل الموردين والمدفوعات</p>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // متغيرات عامة محسنة
        let currentCategory = null;
        let currentReportType = null;
        let navigationHistory = [];
        let currentView = 'categories'; // categories, reports, report-detail

        // إضافة event listener عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة بنجاح');

            // تهيئة الحالة الأولية
            initializeReportsPage();

            // إضافة event delegation للتقارير
            document.addEventListener('click', function(e) {
                const reportCard = e.target.closest('.report-card-modern');
                if (reportCard) {
                    const reportType = reportCard.getAttribute('data-report-type');
                    if (reportType) {
                        console.log('تم النقر على التقرير:', reportType);
                        currentReportType = reportType;
                        generateReportDirectly(reportType);
                    }
                }
            });

            // إضافة دعم للتنقل بالكيبورد
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    goBack();
                }
            });
        });

        // دالة تهيئة الصفحة
        function initializeReportsPage() {
            currentView = 'categories';
            navigationHistory = [];
            updateBreadcrumb();
        }

        // دالة اختيار فئة التقرير محسنة
        function selectCategory(category) {
            console.log('تم اختيار فئة:', category);

            // حفظ الحالة السابقة في التاريخ
            if (currentView === 'categories') {
                navigationHistory.push({
                    view: 'categories',
                    category: null,
                    reportType: null
                });
            }

            // تحديث الحالة الحالية
            currentCategory = category;
            currentReportType = null;
            currentView = 'reports';

            // إخفاء قسم الفئات وإظهار قسم التقرير المحدد مع تأثير انتقال
            const categoriesSection = document.querySelector('.report-categories-modern');
            const selectedSection = document.getElementById('selected-report');

            categoriesSection.style.opacity = '0';
            setTimeout(() => {
                categoriesSection.style.display = 'none';
                selectedSection.style.display = 'block';
                selectedSection.style.opacity = '0';
                setTimeout(() => {
                    selectedSection.style.opacity = '1';
                }, 50);
            }, 200);

            // تحديث عنوان التقرير
            const titles = {
                'financial': 'التقارير المالية',
                'sales': 'تقارير المبيعات',
                'purchases': 'تقارير المشتريات',
                'inventory': 'تقارير المخزون',
                'customers': 'تقارير العملاء',
                'suppliers': 'تقارير الموردين'
            };

            const categoryNames = {
                'financial': 'المالية',
                'sales': 'المبيعات',
                'purchases': 'المشتريات',
                'inventory': 'المخزون',
                'customers': 'العملاء',
                'suppliers': 'الموردين'
            };

            document.getElementById('report-title').textContent = titles[category] || 'تقرير مفصل';
            document.getElementById('breadcrumb-category').textContent = categoryNames[category] || 'تقرير مفصل';

            // تحديث breadcrumb
            updateBreadcrumb();

            // عرض التقارير المتاحة للفئة المختارة
            displayAvailableReports(category);
        }

        // دالة عرض التقارير المتاحة
        function displayAvailableReports(category) {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            const categoryOptions = {
                'financial': [
                    { value: 'balance-sheet', text: 'قائمة المركز المالي', icon: 'fas fa-balance-scale', description: 'الميزانية العمومية والأصول والخصوم' },
                    { value: 'profit-loss', text: 'قائمة الأرباح والخسائر', icon: 'fas fa-chart-line', description: 'الإيرادات والمصروفات وصافي الربح' },
                    { value: 'cash-flow', text: 'قائمة التدفقات النقدية', icon: 'fas fa-money-bill-wave', description: 'التدفقات النقدية الداخلة والخارجة' },
                    { value: 'trial-balance', text: 'ميزان المراجعة', icon: 'fas fa-calculator', description: 'أرصدة جميع الحسابات' }
                ],
                'sales': [
                    { value: 'daily', text: 'المبيعات اليومية', icon: 'fas fa-calendar-day', description: 'تقرير مبيعات اليوم' },
                    { value: 'monthly', text: 'المبيعات الشهرية', icon: 'fas fa-calendar-alt', description: 'تقرير مبيعات الشهر' },
                    { value: 'by-customer', text: 'المبيعات حسب العميل', icon: 'fas fa-user-tie', description: 'تحليل مبيعات كل عميل' },
                    { value: 'by-product', text: 'المبيعات حسب المنتج', icon: 'fas fa-box', description: 'تحليل مبيعات كل منتج' }
                ],
                'purchases': [
                    { value: 'purchases-daily', text: 'المشتريات اليومية', icon: 'fas fa-calendar-day', description: 'تقرير مشتريات اليوم' },
                    { value: 'purchases-monthly', text: 'المشتريات الشهرية', icon: 'fas fa-calendar-alt', description: 'تقرير مشتريات الشهر' },
                    { value: 'by-supplier', text: 'المشتريات حسب المورد', icon: 'fas fa-handshake', description: 'تحليل مشتريات كل مورد' },
                    { value: 'purchases-by-category', text: 'المشتريات حسب الفئة', icon: 'fas fa-tags', description: 'تحليل مشتريات كل فئة' }
                ],
                'inventory': [
                    { value: 'current-stock', text: 'المخزون الحالي', icon: 'fas fa-warehouse', description: 'كميات وقيم المخزون الحالي' },
                    { value: 'stock-movement', text: 'حركة المخزون', icon: 'fas fa-exchange-alt', description: 'حركات الدخول والخروج' },
                    { value: 'low-stock', text: 'تقرير النواقص', icon: 'fas fa-exclamation-triangle', description: 'الأصناف التي وصلت للحد الأدنى' },
                    { value: 'stock-valuation', text: 'تقييم المخزون', icon: 'fas fa-dollar-sign', description: 'تقييم المخزون بالتكلفة والسوق' }
                ],
                'customers': [
                    { value: 'customer-list', text: 'قائمة العملاء', icon: 'fas fa-list', description: 'جميع بيانات العملاء' },
                    { value: 'customer-balances', text: 'أرصدة العملاء', icon: 'fas fa-credit-card', description: 'المديونيات والمدفوعات' },
                    { value: 'customer-analysis', text: 'تحليل العملاء', icon: 'fas fa-chart-bar', description: 'تحليل سلوك وأداء العملاء' },
                    { value: 'customer-aging', text: 'أعمار الديون', icon: 'fas fa-clock', description: 'تحليل أعمار ديون العملاء' }
                ],
                'suppliers': [
                    { value: 'supplier-list', text: 'قائمة الموردين', icon: 'fas fa-user-tie', description: 'جميع بيانات الموردين' },
                    { value: 'supplier-balances', text: 'أرصدة الموردين', icon: 'fas fa-credit-card', description: 'المستحقات والمدفوعات' },
                    { value: 'supplier-analysis', text: 'تحليل الموردين', icon: 'fas fa-chart-bar', description: 'أداء الموردين والجودة' },
                    { value: 'supplier-evaluation', text: 'تقييم الموردين', icon: 'fas fa-star', description: 'تقييم أداء وموثوقية الموردين' }
                ]
            };

            const reports = categoryOptions[category] || [];

            if (reports.length === 0) {
                reportContent.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">لا توجد تقارير متاحة لهذه الفئة</div>';
                return;
            }

            reportContent.innerHTML = `
                <div class="available-reports-modern">
                    <div class="reports-grid-modern">
                        ${reports.map(report => `
                            <div class="report-card-modern" data-report-type="${report.value}">
                                <div class="report-icon-modern">
                                    <i class="${report.icon}"></i>
                                </div>
                                <div class="report-details-modern">
                                    <h5>${report.text}</h5>
                                    <p>${report.description}</p>
                                </div>
                                <div class="report-action-modern">
                                    <i class="fas fa-arrow-left"></i>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // دالة العودة المحسنة
        function goBack() {
            console.log('العودة من:', currentView);

            if (currentView === 'report-detail') {
                // العودة من تفاصيل التقرير إلى قائمة التقارير
                currentView = 'reports';
                currentReportType = null;

                // إخفاء عناصر التحكم
                const reportControls = document.getElementById('report-controls');
                if (reportControls) {
                    reportControls.style.display = 'none';
                }

                // إخفاء زر إنشاء التقرير
                const generateBtn = document.getElementById('generate-report-btn');
                if (generateBtn) {
                    generateBtn.style.display = 'none';
                }

                // عرض قائمة التقارير للفئة الحالية
                displayAvailableReports(currentCategory);

                // تحديث العنوان
                const titles = {
                    'financial': 'التقارير المالية',
                    'sales': 'تقارير المبيعات',
                    'purchases': 'تقارير المشتريات',
                    'inventory': 'تقارير المخزون',
                    'customers': 'تقارير العملاء',
                    'suppliers': 'تقارير الموردين'
                };

                document.getElementById('report-title').textContent = titles[currentCategory] || 'تقرير مفصل';

            } else if (currentView === 'reports') {
                // العودة من قائمة التقارير إلى الفئات الرئيسية
                currentView = 'categories';
                currentCategory = null;
                currentReportType = null;

                // إخفاء عناصر التحكم
                const reportControls = document.getElementById('report-controls');
                if (reportControls) {
                    reportControls.style.display = 'none';
                }

                // إخفاء قسم التقرير المحدد وإظهار الفئات مع تأثير انتقال
                const categoriesSection = document.querySelector('.report-categories-modern');
                const selectedSection = document.getElementById('selected-report');

                selectedSection.style.opacity = '0';
                setTimeout(() => {
                    selectedSection.style.display = 'none';
                    categoriesSection.style.display = 'grid';
                    categoriesSection.style.opacity = '0';
                    setTimeout(() => {
                        categoriesSection.style.opacity = '1';
                    }, 50);
                }, 200);

                // إخفاء زر إنشاء التقرير
                const generateBtn = document.getElementById('generate-report-btn');
                if (generateBtn) {
                    generateBtn.style.display = 'none';
                }
            }

            // تحديث breadcrumb
            updateBreadcrumb();
        }

        // دالة إنشاء التقرير مباشرة محسنة
        function generateReportDirectly(reportType) {
            console.log('إنشاء التقرير:', reportType);

            // تحديث الحالة
            currentReportType = reportType;
            currentView = 'report-detail';

            const reportContent = document.getElementById('report-content');
            const reportControls = document.getElementById('report-controls');

            if (reportContent) {
                // إظهار عناصر التحكم
                if (reportControls) {
                    reportControls.style.display = 'block';
                    // تعيين التواريخ الافتراضية
                    setDefaultDates();
                    // تحديث خيارات الحسابات حسب نوع التقرير
                    updateAccountOptions(reportType);
                }

                // إظهار مؤشر التحميل
                reportContent.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 20px; color: #666;">جاري تحميل التقرير...</p>
                    </div>
                `;

                // تأخير قصير لإظهار التحميل
                setTimeout(() => {
                    const reportData = getDetailedReportData(reportType);
                    reportContent.innerHTML = reportData.html;

                    // تحديث العنوان
                    document.getElementById('report-title').textContent = reportData.title;

                    // إظهار زر إنشاء التقرير
                    const generateBtn = document.getElementById('generate-report-btn');
                    if (generateBtn) {
                        generateBtn.style.display = 'flex';
                    }

                    // تحديث breadcrumb
                    updateBreadcrumb();

                    // تمرير سلس إلى أعلى التقرير
                    reportContent.scrollIntoView({ behavior: 'smooth', block: 'start' });

                }, 500);
            }
        }

        // دوال عناصر التحكم في التقارير
        function setDefaultDates() {
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            const dateFromInput = document.getElementById('report-date-from');
            const dateToInput = document.getElementById('report-date-to');

            if (dateFromInput) {
                dateFromInput.value = firstDayOfMonth.toISOString().split('T')[0];
            }
            if (dateToInput) {
                dateToInput.value = today.toISOString().split('T')[0];
            }
        }

        // متغير عام لخيارات التقارير
        window.reportFilters = {};

        // دالة تحديث خيارات الحسابات حسب نوع التقرير
        function updateAccountOptions(reportType) {
            const accountSelect = document.getElementById('report-account');
            if (!accountSelect) return;

            // تحديد الخيارات المناسبة لكل نوع تقرير
            const accountOptions = {
                // التقارير المالية
                'balance-sheet': [
                    { value: '', text: 'جميع الحسابات' },
                    { value: 'assets', text: 'الأصول' },
                    { value: 'liabilities', text: 'الخصوم' },
                    { value: 'equity', text: 'حقوق الملكية' },
                    { value: 'cash', text: 'النقدية' },
                    { value: 'bank', text: 'البنوك' }
                ],
                'profit-loss': [
                    { value: '', text: 'جميع الحسابات' },
                    { value: 'revenue', text: 'الإيرادات' },
                    { value: 'expenses', text: 'المصروفات' },
                    { value: 'sales', text: 'المبيعات' },
                    { value: 'cost-of-goods', text: 'تكلفة البضاعة المباعة' }
                ],
                'cash-flow': [
                    { value: '', text: 'جميع الحسابات النقدية' },
                    { value: 'cash', text: 'النقدية' },
                    { value: 'bank-main', text: 'البنك الرئيسي' },
                    { value: 'bank-secondary', text: 'البنك الثانوي' },
                    { value: 'petty-cash', text: 'النثرية' }
                ],
                'trial-balance': [
                    { value: '', text: 'جميع الحسابات' },
                    { value: 'assets', text: 'الأصول' },
                    { value: 'liabilities', text: 'الخصوم' },
                    { value: 'equity', text: 'حقوق الملكية' },
                    { value: 'revenue', text: 'الإيرادات' },
                    { value: 'expenses', text: 'المصروفات' }
                ],

                // تقارير المبيعات
                'daily': [
                    { value: '', text: 'جميع طرق الدفع' },
                    { value: 'cash', text: 'نقداً' },
                    { value: 'credit-card', text: 'بطاقة ائتمان' },
                    { value: 'bank-transfer', text: 'تحويل بنكي' },
                    { value: 'installment', text: 'تقسيط' }
                ],
                'monthly': [
                    { value: '', text: 'جميع طرق الدفع' },
                    { value: 'cash', text: 'نقداً' },
                    { value: 'credit-card', text: 'بطاقة ائتمان' },
                    { value: 'bank-transfer', text: 'تحويل بنكي' },
                    { value: 'installment', text: 'تقسيط' }
                ],
                'by-customer': [
                    { value: '', text: 'جميع العملاء' },
                    { value: 'ahmed-ali', text: 'أحمد محمد علي' },
                    { value: 'fatima-saad', text: 'فاطمة السعد' },
                    { value: 'mohammed-ahmed', text: 'محمد الأحمد' },
                    { value: 'sara-khalid', text: 'سارة خالد' },
                    { value: 'abdullah-otaibi', text: 'عبدالله العتيبي' },
                    { value: 'nora-zahrani', text: 'نورا الزهراني' },
                    { value: 'khalid-mutairi', text: 'خالد المطيري' },
                    { value: 'reem-shahri', text: 'ريم الشهري' },
                    { value: 'omar-salem', text: 'عمر سالم' },
                    { value: 'layla-ahmed', text: 'ليلى أحمد' },
                    { value: 'youssef-hassan', text: 'يوسف حسان' },
                    { value: 'maryam-ali', text: 'مريم علي' },
                    { value: 'hassan-ibrahim', text: 'حسان إبراهيم' },
                    { value: 'aisha-mohammed', text: 'عائشة محمد' },
                    { value: 'waleed-abdulla', text: 'وليد عبدالله' }
                ],
                'by-product': [
                    { value: '', text: 'جميع المنتجات' },
                    { value: 'laptop-dell-xps13', text: 'لابتوب ديل XPS 13' },
                    { value: 'iphone-15', text: 'هاتف آيفون 15' },
                    { value: 'wooden-desk', text: 'مكتب خشبي فاخر' },
                    { value: 'leather-chair', text: 'كرسي مكتبي جلد' },
                    { value: 'hp-printer', text: 'طابعة ليزر HP' },
                    { value: 'samsung-monitor', text: 'شاشة سامسونج 27 بوصة' },
                    { value: 'office-supplies', text: 'أدوات مكتبية متنوعة' },
                    { value: 'wireless-mouse', text: 'ماوس لاسلكي' },
                    { value: 'keyboard-mechanical', text: 'كيبورد ميكانيكي' },
                    { value: 'tablet-ipad', text: 'تابلت آيباد' },
                    { value: 'headphones-sony', text: 'سماعات سوني' },
                    { value: 'webcam-logitech', text: 'كاميرا ويب لوجيتك' }
                ],

                // تقارير المشتريات
                'purchases-daily': [
                    { value: '', text: 'جميع طرق الدفع' },
                    { value: 'cash', text: 'نقداً' },
                    { value: 'credit', text: 'آجل' },
                    { value: 'bank-transfer', text: 'تحويل بنكي' },
                    { value: 'check', text: 'شيك' }
                ],
                'monthly': [
                    { value: '', text: 'جميع طرق الدفع' },
                    { value: 'cash', text: 'نقداً' },
                    { value: 'credit', text: 'آجل' },
                    { value: 'bank-transfer', text: 'تحويل بنكي' },
                    { value: 'check', text: 'شيك' }
                ],
                'by-supplier': [
                    { value: '', text: 'جميع الموردين' },
                    { value: 'advanced-tech', text: 'شركة التقنية المتقدمة المحدودة' },
                    { value: 'modern-furniture', text: 'مؤسسة الأثاث الحديث' },
                    { value: 'office-solutions', text: 'شركة الحلول المكتبية' },
                    { value: 'electronics-world', text: 'عالم الإلكترونيات التجاري' },
                    { value: 'stationery-house', text: 'بيت القرطاسية والمكتبيات' },
                    { value: 'computer-center', text: 'مركز الحاسوب والتقنية' },
                    { value: 'mobile-plaza', text: 'مجمع الجوالات والاتصالات' },
                    { value: 'office-depot', text: 'مستودع المكاتب والأثاث' }
                ],
                'by-category': [
                    { value: '', text: 'جميع الفئات' },
                    { value: 'electronics', text: 'الإلكترونيات' },
                    { value: 'furniture', text: 'الأثاث' },
                    { value: 'stationery', text: 'القرطاسية' },
                    { value: 'accessories', text: 'الإكسسوارات' },
                    { value: 'computers', text: 'الحاسوب' },
                    { value: 'mobiles', text: 'الجوالات' }
                ],

                // تقارير المخزون
                'current-stock': [
                    { value: '', text: 'جميع الأصناف' },
                    { value: 'laptop-dell-xps13', text: 'لابتوب ديل XPS 13' },
                    { value: 'iphone-15', text: 'هاتف آيفون 15' },
                    { value: 'wooden-desk', text: 'مكتب خشبي فاخر' },
                    { value: 'leather-chair', text: 'كرسي مكتبي جلد' },
                    { value: 'hp-printer', text: 'طابعة ليزر HP' },
                    { value: 'samsung-monitor', text: 'شاشة سامسونج 27 بوصة' },
                    { value: 'low-stock-items', text: 'الأصناف المنخفضة' },
                    { value: 'out-of-stock', text: 'الأصناف المنتهية' }
                ],
                'stock-movement': [
                    { value: '', text: 'جميع أنواع الحركات' },
                    { value: 'purchase-receipt', text: 'استلام مشتريات' },
                    { value: 'sales-issue', text: 'صرف مبيعات' },
                    { value: 'transfer-in', text: 'تحويل وارد' },
                    { value: 'transfer-out', text: 'تحويل صادر' },
                    { value: 'adjustment-in', text: 'تسوية زيادة' },
                    { value: 'adjustment-out', text: 'تسوية نقص' },
                    { value: 'return-in', text: 'مرتجع وارد' },
                    { value: 'return-out', text: 'مرتجع صادر' }
                ],
                'low-stock': [
                    { value: '', text: 'جميع الأصناف' },
                    { value: 'critical-level', text: 'مستوى حرج (أقل من 5)' },
                    { value: 'warning-level', text: 'مستوى تحذير (أقل من 10)' },
                    { value: 'low-level', text: 'مستوى منخفض (أقل من 20)' },
                    { value: 'out-of-stock', text: 'منتهي الصلاحية' }
                ],
                'stock-valuation': [
                    { value: '', text: 'جميع طرق التقييم' },
                    { value: 'fifo', text: 'الوارد أولاً صادر أولاً (FIFO)' },
                    { value: 'lifo', text: 'الوارد أخيراً صادر أولاً (LIFO)' },
                    { value: 'average-cost', text: 'متوسط التكلفة' },
                    { value: 'market-value', text: 'القيمة السوقية' }
                ],

                // تقارير العملاء
                'customer-list': [
                    { value: '', text: 'جميع العملاء' },
                    { value: 'ahmed-ali', text: 'أحمد محمد علي' },
                    { value: 'fatima-saad', text: 'فاطمة السعد' },
                    { value: 'mohammed-ahmed', text: 'محمد الأحمد' },
                    { value: 'sara-khalid', text: 'سارة خالد' },
                    { value: 'abdullah-otaibi', text: 'عبدالله العتيبي' },
                    { value: 'nora-zahrani', text: 'نورا الزهراني' },
                    { value: 'khalid-mutairi', text: 'خالد المطيري' },
                    { value: 'reem-shahri', text: 'ريم الشهري' },
                    { value: 'omar-salem', text: 'عمر سالم' },
                    { value: 'layla-ahmed', text: 'ليلى أحمد' }
                ],
                'customer-balances': [
                    { value: '', text: 'جميع العملاء' },
                    { value: 'ahmed-ali', text: 'أحمد محمد علي' },
                    { value: 'fatima-saad', text: 'فاطمة السعد' },
                    { value: 'mohammed-ahmed', text: 'محمد الأحمد' },
                    { value: 'sara-khalid', text: 'سارة خالد' },
                    { value: 'abdullah-otaibi', text: 'عبدالله العتيبي' },
                    { value: 'nora-zahrani', text: 'نورا الزهراني' },
                    { value: 'khalid-mutairi', text: 'خالد المطيري' },
                    { value: 'reem-shahri', text: 'ريم الشهري' }
                ],
                'customer-analysis': [
                    { value: '', text: 'جميع العملاء' },
                    { value: 'high-value', text: 'عملاء عالي القيمة (أكثر من 20,000 ر.س)' },
                    { value: 'medium-value', text: 'عملاء متوسط القيمة (10,000 - 20,000 ر.س)' },
                    { value: 'low-value', text: 'عملاء منخفض القيمة (أقل من 10,000 ر.س)' },
                    { value: 'frequent-buyers', text: 'عملاء دائمين (أكثر من 10 مشتريات)' },
                    { value: 'new-customers', text: 'عملاء جدد (آخر 3 أشهر)' }
                ],
                'customer-aging': [
                    { value: '', text: 'جميع العملاء' },
                    { value: 'current', text: 'مستحق حالياً (0-30 يوم)' },
                    { value: 'overdue-30', text: 'متأخر 30 يوم (31-60 يوم)' },
                    { value: 'overdue-60', text: 'متأخر 60 يوم (61-90 يوم)' },
                    { value: 'overdue-90', text: 'متأخر 90 يوم (أكثر من 90 يوم)' }
                ],

                // تقارير الموردين
                'supplier-list': [
                    { value: '', text: 'جميع الموردين' },
                    { value: 'advanced-tech', text: 'شركة التقنية المتقدمة المحدودة' },
                    { value: 'modern-furniture', text: 'مؤسسة الأثاث الحديث' },
                    { value: 'office-solutions', text: 'شركة الحلول المكتبية' },
                    { value: 'electronics-world', text: 'عالم الإلكترونيات التجاري' },
                    { value: 'stationery-house', text: 'بيت القرطاسية والمكتبيات' },
                    { value: 'computer-center', text: 'مركز الحاسوب والتقنية' },
                    { value: 'mobile-plaza', text: 'مجمع الجوالات والاتصالات' },
                    { value: 'office-depot', text: 'مستودع المكاتب والأثاث' },
                    { value: 'tech-innovations', text: 'شركة الابتكارات التقنية' }
                ],
                'supplier-balances': [
                    { value: '', text: 'جميع الموردين' },
                    { value: 'advanced-tech', text: 'شركة التقنية المتقدمة المحدودة' },
                    { value: 'modern-furniture', text: 'مؤسسة الأثاث الحديث' },
                    { value: 'office-solutions', text: 'شركة الحلول المكتبية' },
                    { value: 'electronics-world', text: 'عالم الإلكترونيات التجاري' },
                    { value: 'stationery-house', text: 'بيت القرطاسية والمكتبيات' },
                    { value: 'computer-center', text: 'مركز الحاسوب والتقنية' },
                    { value: 'mobile-plaza', text: 'مجمع الجوالات والاتصالات' }
                ],
                'supplier-analysis': [
                    { value: '', text: 'جميع الموردين' },
                    { value: 'high-volume', text: 'موردين عالي الحجم (أكثر من 50,000 ر.س)' },
                    { value: 'medium-volume', text: 'موردين متوسط الحجم (20,000 - 50,000 ر.س)' },
                    { value: 'low-volume', text: 'موردين منخفض الحجم (أقل من 20,000 ر.س)' },
                    { value: 'reliable-suppliers', text: 'موردين موثوقين (تقييم ممتاز)' },
                    { value: 'new-suppliers', text: 'موردين جدد (آخر 6 أشهر)' }
                ],
                'supplier-evaluation': [
                    { value: '', text: 'جميع الموردين' },
                    { value: 'excellent-rating', text: 'تقييم ممتاز (90-100%)' },
                    { value: 'good-rating', text: 'تقييم جيد (80-89%)' },
                    { value: 'average-rating', text: 'تقييم متوسط (70-79%)' },
                    { value: 'poor-rating', text: 'تقييم ضعيف (أقل من 70%)' }
                ]
            };

            // الحصول على الخيارات المناسبة أو الافتراضية
            const options = accountOptions[reportType] || [
                { value: '', text: 'جميع الحسابات' },
                { value: 'cash', text: 'النقدية' },
                { value: 'bank', text: 'البنك' },
                { value: 'customers', text: 'العملاء' },
                { value: 'suppliers', text: 'الموردين' }
            ];

            // مسح الخيارات الحالية
            accountSelect.innerHTML = '';

            // إضافة الخيارات الجديدة
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                accountSelect.appendChild(optionElement);
            });

            // تهيئة reportFilters مع البيانات الحالية
            window.reportFilters = accountOptions;
        }

        function applyReportFilters() {
            const dateFrom = document.getElementById('report-date-from').value;
            const dateTo = document.getElementById('report-date-to').value;
            const account = document.getElementById('report-account').value;
            const branch = document.getElementById('report-branch').value;

            // التحقق من صحة التواريخ
            if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                showErrorMessage('تاريخ البداية يجب أن يكون قبل تاريخ النهاية!');
                return;
            }

            // إظهار مؤشر التحميل
            showLoadingMessage('جاري تطبيق المرشحات وإعادة إنشاء التقرير...');

            // محاكاة تطبيق المرشحات
            setTimeout(() => {
                hideLoadingMessage();

                // إعادة إنشاء التقرير بالمرشحات الجديدة
                const reportData = getDetailedReportData(currentReportType, {
                    dateFrom: dateFrom,
                    dateTo: dateTo,
                    account: account,
                    branch: branch
                });

                const reportContent = document.getElementById('report-content');
                if (reportContent) {
                    reportContent.innerHTML = reportData.html;
                }

                // إظهار رسالة نجاح مع تفاصيل المرشحات
                let filterDetails = [];
                if (dateFrom && dateTo) {
                    filterDetails.push(`الفترة: ${dateFrom} إلى ${dateTo}`);
                }
                if (account) {
                    const accountText = document.getElementById('report-account').selectedOptions[0].text;
                    filterDetails.push(`الحساب: ${accountText}`);
                }
                if (branch) {
                    const branchText = document.getElementById('report-branch').selectedOptions[0].text;
                    filterDetails.push(`الفرع: ${branchText}`);
                }

                const message = filterDetails.length > 0
                    ? `تم تطبيق المرشحات بنجاح! (${filterDetails.join(', ')})`
                    : 'تم تحديث التقرير بنجاح!';

                showSuccessMessage(message);
            }, 1000);
        }

        function resetReportFilters() {
            // إعادة تعيين جميع المرشحات
            document.getElementById('report-date-from').value = '';
            document.getElementById('report-date-to').value = '';
            document.getElementById('report-account').value = '';
            document.getElementById('report-branch').value = '';

            // تعيين التواريخ الافتراضية
            setDefaultDates();

            // إعادة تحميل التقرير
            applyReportFilters();
        }

        // دالة تحديث breadcrumb
        function updateBreadcrumb() {
            const breadcrumbCategory = document.getElementById('breadcrumb-category');

            if (currentView === 'categories') {
                breadcrumbCategory.textContent = 'اختيار نوع التقرير';
            } else if (currentView === 'reports') {
                const categoryNames = {
                    'financial': 'المالية',
                    'sales': 'المبيعات',
                    'purchases': 'المشتريات',
                    'inventory': 'المخزون',
                    'customers': 'العملاء',
                    'suppliers': 'الموردين'
                };
                breadcrumbCategory.textContent = categoryNames[currentCategory] || 'تقرير';
            } else if (currentView === 'report-detail') {
                const reportNames = {
                    'balance-sheet': 'قائمة المركز المالي',
                    'profit-loss': 'قائمة الأرباح والخسائر',
                    'daily': 'المبيعات اليومية',
                    'current-stock': 'المخزون الحالي'
                };
                breadcrumbCategory.textContent = reportNames[currentReportType] || 'تقرير مفصل';
            }
        }

        // دالة تحسين الأداء - تحميل مسبق للبيانات
        function preloadReportData() {
            // تحميل مسبق للتقارير الأكثر استخداماً
            const popularReports = ['balance-sheet', 'profit-loss', 'daily', 'current-stock'];
            popularReports.forEach(reportType => {
                setTimeout(() => {
                    getDetailedReportData(reportType);
                }, 100);
            });
        }

        // دالة إضافة تأثيرات بصرية محسنة
        function addVisualEnhancements() {
            // إضافة تأثير hover للبطاقات
            const categoryCards = document.querySelectorAll('.category-card-modern');
            categoryCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // دالة تنسيق التاريخ بالميلادي
        function formatDateArabic(date) {
            if (!date) return '';

            const dateObj = typeof date === 'string' ? new Date(date) : date;

            return dateObj.toLocaleDateString('ar', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                calendar: 'gregory'
            });
        }

        // دالة تنسيق التاريخ بصيغة أكثر وضوحاً
        function formatDateDetailed(date) {
            if (!date) return '';

            const dateObj = typeof date === 'string' ? new Date(date) : date;

            return dateObj.toLocaleDateString('ar', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            });
        }

        // دالة الحصول على بيانات التقرير المفصلة محسنة
        function getDetailedReportData(reportType, filters = {}) {
            // تنسيق التاريخ بالميلادي
            const currentDate = formatDateArabic(new Date());

            // تطبيق المرشحات على البيانات
            let dateRange = '';
            if (filters.dateFrom && filters.dateTo) {
                const fromDate = formatDateArabic(filters.dateFrom);
                const toDate = formatDateArabic(filters.dateTo);
                dateRange = `من ${fromDate} إلى ${toDate}`;
            } else {
                dateRange = `كما في ${currentDate}`;
            }

            const reportTemplates = {
                // التقارير المالية
                'balance-sheet': {
                    title: 'قائمة المركز المالي',
                    category: 'financial',
                    html: createBalanceSheetReport(dateRange, filters)
                },
                'profit-loss': {
                    title: 'قائمة الأرباح والخسائر',
                    category: 'financial',
                    html: createProfitLossReport(dateRange, filters)
                },
                'cash-flow': {
                    title: 'قائمة التدفقات النقدية',
                    category: 'financial',
                    html: createCashFlowReport(dateRange, filters)
                },
                'trial-balance': {
                    title: 'ميزان المراجعة',
                    category: 'financial',
                    html: createTrialBalanceReport(dateRange, filters)
                },

                // تقارير المبيعات
                'daily': {
                    title: 'تقرير المبيعات اليومية',
                    category: 'sales',
                    html: createDailySalesReport(dateRange, filters)
                },
                'monthly': {
                    title: 'تقرير المبيعات الشهرية',
                    category: 'sales',
                    html: createMonthlySalesReport(currentDate)
                },
                'by-customer': {
                    title: 'المبيعات حسب العميل',
                    category: 'sales',
                    html: createSalesByCustomerReport(dateRange, filters)
                },
                'by-product': {
                    title: 'المبيعات حسب المنتج',
                    category: 'sales',
                    html: createSalesByProductReport(dateRange, filters)
                },

                // تقارير المشتريات
                'purchases-daily': {
                    title: 'المشتريات اليومية',
                    category: 'purchases',
                    html: createPurchasesDailyReport(dateRange, filters)
                },
                'purchases-monthly': {
                    title: 'المشتريات الشهرية',
                    category: 'purchases',
                    html: createPurchasesMonthlyReport(dateRange, filters)
                },
                'by-supplier': {
                    title: 'المشتريات حسب المورد',
                    category: 'purchases',
                    html: createPurchasesBySupplierReport(dateRange, filters)
                },
                'purchases-by-category': {
                    title: 'المشتريات حسب الفئة',
                    category: 'purchases',
                    html: createPurchasesByCategoryReport(dateRange, filters)
                },

                // تقارير المخزون
                'current-stock': {
                    title: 'تقرير المخزون الحالي',
                    category: 'inventory',
                    html: createStockReport(currentDate)
                },
                'stock-movement': {
                    title: 'حركة المخزون',
                    category: 'inventory',
                    html: createStockMovementReport(currentDate)
                },
                'low-stock': {
                    title: 'تقرير النواقص',
                    category: 'inventory',
                    html: createLowStockReport(currentDate)
                },
                'stock-valuation': {
                    title: 'تقييم المخزون',
                    category: 'inventory',
                    html: createStockValuationReport(currentDate)
                },

                // تقارير العملاء
                'customer-list': {
                    title: 'قائمة العملاء',
                    category: 'customers',
                    html: createCustomerListReport(dateRange, filters)
                },
                'customer-balances': {
                    title: 'أرصدة العملاء',
                    category: 'customers',
                    html: createCustomerBalancesReport(dateRange, filters)
                },
                'customer-analysis': {
                    title: 'تحليل العملاء',
                    category: 'customers',
                    html: createCustomerAnalysisReport(dateRange, filters)
                },
                'customer-aging': {
                    title: 'أعمار الديون',
                    category: 'customers',
                    html: createCustomerAgingReport(dateRange, filters)
                },

                // تقارير الموردين
                'supplier-list': {
                    title: 'قائمة الموردين',
                    category: 'suppliers',
                    html: createSupplierListReport(dateRange, filters)
                },
                'supplier-balances': {
                    title: 'أرصدة الموردين',
                    category: 'suppliers',
                    html: createSupplierBalancesReport(dateRange, filters)
                },
                'supplier-analysis': {
                    title: 'تحليل الموردين',
                    category: 'suppliers',
                    html: createSupplierAnalysisReport(dateRange, filters)
                },
                'supplier-evaluation': {
                    title: 'تقييم الموردين',
                    category: 'suppliers',
                    html: createSupplierEvaluationReport(dateRange, filters)
                }
            };

            return reportTemplates[reportType] || {
                title: 'تقرير عام',
                category: 'general',
                html: createDefaultReport(reportType, currentDate)
            };
        }

        // دالة إنشاء تقرير الميزانية
        function createBalanceSheetReport(dateRange, filters = {}) {
            let filterInfo = '';
            if (filters.account && filters.account !== '') {
                const accountNames = {
                    'cash': 'النقدية',
                    'bank': 'البنك',
                    'customers': 'العملاء',
                    'suppliers': 'الموردين',
                    'inventory': 'المخزون'
                };
                filterInfo += ` - الحساب: ${accountNames[filters.account] || filters.account}`;
            }
            if (filters.branch && filters.branch !== '') {
                const branchNames = {
                    'main': 'الفرع الرئيسي',
                    'branch1': 'فرع الرياض',
                    'branch2': 'فرع جدة'
                };
                filterInfo += ` - الفرع: ${branchNames[filters.branch] || filters.branch}`;
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>قائمة المركز المالي (الميزانية العمومية)</h2>
                        <p>${dateRange}${filterInfo}</p>
                    </div>
                    <div class="report-content">
                        <table class="report-table">
                            <thead>
                                <tr><th>البند</th><th>المبلغ (ر.س)</th><th>النسبة %</th></tr>
                            </thead>
                            <tbody>
                                <tr><td><strong>الأصول المتداولة:</strong></td><td></td><td></td></tr>
                                <tr><td>النقدية وما في حكمها</td><td>125,000</td><td>15.6%</td></tr>
                                <tr><td>العملاء والذمم المدينة</td><td>180,000</td><td>22.5%</td></tr>
                                <tr><td>المخزون</td><td>95,000</td><td>11.9%</td></tr>
                                <tr class="subtotal"><td><strong>إجمالي الأصول المتداولة</strong></td><td><strong>400,000</strong></td><td><strong>50.0%</strong></td></tr>
                                <tr><td><strong>الأصول الثابتة:</strong></td><td></td><td></td></tr>
                                <tr><td>الأراضي والمباني</td><td>250,000</td><td>31.3%</td></tr>
                                <tr><td>المعدات والآلات</td><td>150,000</td><td>18.7%</td></tr>
                                <tr class="subtotal"><td><strong>إجمالي الأصول الثابتة</strong></td><td><strong>400,000</strong></td><td><strong>50.0%</strong></td></tr>
                                <tr class="total"><td><strong>إجمالي الأصول</strong></td><td><strong>800,000</strong></td><td><strong>100%</strong></td></tr>
                                <tr><td><strong>الخصوم والملكية:</strong></td><td></td><td></td></tr>
                                <tr><td>الموردون والذمم الدائنة</td><td>200,000</td><td>25.0%</td></tr>
                                <tr><td>القروض</td><td>200,000</td><td>25.0%</td></tr>
                                <tr><td>رأس المال</td><td>300,000</td><td>37.5%</td></tr>
                                <tr><td>الأرباح المحتجزة</td><td>100,000</td><td>12.5%</td></tr>
                                <tr class="total"><td><strong>إجمالي الخصوم والملكية</strong></td><td><strong>800,000</strong></td><td><strong>100%</strong></td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('balance-sheet')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('balance-sheet')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دالة إنشاء تقرير الأرباح والخسائر
        function createProfitLossReport(currentDate) {
            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>قائمة الأرباح والخسائر</h2>
                        <p>للفترة المنتهية في ${currentDate}</p>
                    </div>
                    <div class="report-content">
                        <table class="report-table">
                            <thead>
                                <tr><th>البند</th><th>المبلغ (ر.س)</th><th>النسبة %</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>إيرادات المبيعات</td><td>1,200,000</td><td>100.0%</td></tr>
                                <tr><td>تكلفة البضاعة المباعة</td><td>(720,000)</td><td>60.0%</td></tr>
                                <tr class="profit"><td><strong>إجمالي الربح</strong></td><td><strong>480,000</strong></td><td><strong>40.0%</strong></td></tr>
                                <tr><td>مصروفات البيع والتسويق</td><td>(120,000)</td><td>10.0%</td></tr>
                                <tr><td>المصروفات الإدارية</td><td>(85,000)</td><td>7.1%</td></tr>
                                <tr><td>مصروفات الإهلاك</td><td>(35,000)</td><td>2.9%</td></tr>
                                <tr class="profit"><td><strong>الربح التشغيلي</strong></td><td><strong>240,000</strong></td><td><strong>20.0%</strong></td></tr>
                                <tr><td>مصروفات الفوائد</td><td>(18,000)</td><td>1.5%</td></tr>
                                <tr><td>ضريبة الدخل</td><td>(33,000)</td><td>2.8%</td></tr>
                                <tr class="final-total"><td><strong>صافي الربح</strong></td><td><strong>189,000</strong></td><td><strong>15.7%</strong></td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('profit-loss')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('profit-loss')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دالة إنشاء تقرير المبيعات اليومية
        function createDailySalesReport(dateRange, filters = {}) {
            let filterInfo = '';
            if (filters.account && filters.account !== '') {
                const paymentMethods = {
                    'cash': 'نقداً',
                    'credit-card': 'بطاقة ائتمان',
                    'bank-transfer': 'تحويل بنكي',
                    'installment': 'تقسيط'
                };
                filterInfo += ` - طريقة الدفع: ${paymentMethods[filters.account] || filters.account}`;
            }
            if (filters.branch && filters.branch !== '') {
                const branchNames = {
                    'main': 'الفرع الرئيسي',
                    'branch1': 'فرع الرياض',
                    'branch2': 'فرع جدة'
                };
                filterInfo += ` - الفرع: ${branchNames[filters.branch] || filters.branch}`;
            }

            // الحصول على البيانات الحقيقية
            let salesData = [];
            if (window.dataManager) {
                salesData = window.dataManager.getSales();
            } else if (window.realSalesData) {
                salesData = window.realSalesData;
            } else {
                // البحث في localStorage
                const invoicesData = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
                const salesLocalData = JSON.parse(localStorage.getItem('monjizSales')) || [];
                salesData = [...invoicesData, ...salesLocalData];
            }

            console.log('📊 بيانات المبيعات للتقرير:', salesData.length, 'فاتورة');

            // تصفية البيانات حسب التاريخ (اليوم الحالي)
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD
            const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`; // DD/MM/YYYY

            const todaySales = salesData.filter(sale => {
                if (sale.date) {
                    // التحقق من تنسيقات التاريخ المختلفة
                    return sale.date === todayFormatted ||
                           sale.date === todayStr ||
                           (sale.createdAt && sale.createdAt.startsWith(todayStr));
                }
                return false;
            });

            console.log('📅 مبيعات اليوم:', todaySales.length, 'فاتورة');

            // حساب الإحصائيات
            const totalSales = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
            const totalInvoices = todaySales.length;
            const averageInvoice = totalInvoices > 0 ? totalSales / totalInvoices : 0;

            // بناء صفوف الجدول
            let tableRows = '';
            if (todaySales.length > 0) {
                todaySales.forEach(sale => {
                    const paymentMethod = sale.paymentMethod === 'cash' ? 'نقداً' :
                                        sale.paymentMethod === 'credit' ? 'آجل' :
                                        sale.paymentMethod === 'card' ? 'بطاقة' : 'تحويل';

                    const status = sale.status === 'paid' ? '<span class="status-paid">مدفوعة</span>' :
                                  sale.status === 'pending' ? '<span class="status-pending">معلقة</span>' :
                                  '<span class="status-overdue">متأخرة</span>';

                    tableRows += `
                        <tr>
                            <td>${sale.id}</td>
                            <td>${sale.customerName || 'غير محدد'}</td>
                            <td>${(sale.total || 0).toFixed(2)}</td>
                            <td>${paymentMethod}</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });

                tableRows += `<tr class="total"><td colspan="2"><strong>الإجمالي</strong></td><td><strong>${totalSales.toFixed(2)}</strong></td><td colspan="2"></td></tr>`;
            } else {
                tableRows = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: #666;">لا توجد مبيعات اليوم</td></tr>';
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>تقرير المبيعات اليومية</h2>
                        <p>${dateRange}${filterInfo}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي المبيعات</h4>
                                <p class="amount">${totalSales.toFixed(2)} ر.س</p>
                            </div>
                            <div class="summary-card">
                                <h4>عدد الفواتير</h4>
                                <p class="count">${totalInvoices} فاتورة</p>
                            </div>
                            <div class="summary-card">
                                <h4>متوسط الفاتورة</h4>
                                <p class="average">${averageInvoice.toFixed(2)} ر.س</p>
                            </div>
                        </div>
                        <table class="report-table">
                            <thead>
                                <tr><th>رقم الفاتورة</th><th>العميل</th><th>المبلغ (ر.س)</th><th>طريقة الدفع</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('daily-sales')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('daily-sales')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دالة إنشاء تقرير قائمة العملاء
        function createCustomerListReport(dateRange, filters = {}) {
            // الحصول على البيانات الحقيقية
            let customersData = [];
            if (window.dataManager) {
                customersData = window.dataManager.getCustomers();
            } else {
                customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            }

            console.log('👥 بيانات العملاء للتقرير:', customersData.length, 'عميل');

            // بناء صفوف الجدول
            let tableRows = '';
            if (customersData.length > 0) {
                customersData.forEach((customer, index) => {
                    const customerType = customer.type === 'individual' ? 'فرد' : 'شركة';
                    const status = customer.status === 'active' ? '<span class="status-paid">نشط</span>' : '<span class="status-pending">غير نشط</span>';

                    tableRows += `
                        <tr>
                            <td>${index + 1}</td>
                            <td><strong>${customer.name}</strong></td>
                            <td>${customerType}</td>
                            <td>${customer.phone || 'غير محدد'}</td>
                            <td>${customer.email || 'غير محدد'}</td>
                            <td>${customer.address || 'غير محدد'}</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });
            } else {
                tableRows = '<tr><td colspan="7" style="text-align: center; padding: 20px; color: #666;">لا توجد بيانات عملاء</td></tr>';
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>قائمة العملاء</h2>
                        <p>${dateRange}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي العملاء</h4>
                                <p class="count">${customersData.length} عميل</p>
                            </div>
                            <div class="summary-card">
                                <h4>العملاء النشطين</h4>
                                <p class="count">${customersData.filter(c => c.status !== 'inactive').length} عميل</p>
                            </div>
                            <div class="summary-card">
                                <h4>العملاء الجدد</h4>
                                <p class="count">${customersData.filter(c => c.createdAt && new Date(c.createdAt) > new Date(Date.now() - 30*24*60*60*1000)).length} عميل</p>
                            </div>
                        </div>
                        <table class="report-table">
                            <thead>
                                <tr><th>#</th><th>اسم العميل</th><th>النوع</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>العنوان</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('customer-list')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('customer-list')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دالة إنشاء تقرير أرصدة العملاء
        function createCustomerBalancesReport(dateRange, filters = {}) {
            // الحصول على بيانات العملاء والمبيعات
            let customersData = [];
            let salesData = [];

            if (window.dataManager) {
                customersData = window.dataManager.getCustomers();
                salesData = window.dataManager.getSales();
            } else {
                customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                salesData = JSON.parse(localStorage.getItem('monjizSales')) ||
                           JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            }

            console.log('👥 بيانات العملاء:', customersData.length);
            console.log('💰 بيانات المبيعات:', salesData.length);

            // حساب أرصدة العملاء
            const customerBalances = customersData.map(customer => {
                const customerSales = salesData.filter(sale =>
                    sale.customerId === customer.id ||
                    sale.customerName === customer.name
                );

                const totalSales = customerSales.reduce((sum, sale) => sum + (sale.total || 0), 0);
                const totalInvoices = customerSales.length;
                const lastSale = customerSales.length > 0 ?
                    customerSales.sort((a, b) => new Date(b.date || b.createdAt) - new Date(a.date || a.createdAt))[0] : null;

                return {
                    ...customer,
                    totalSales,
                    totalInvoices,
                    lastSaleDate: lastSale ? (lastSale.date || lastSale.createdAt) : 'لا توجد مبيعات',
                    averageSale: totalInvoices > 0 ? totalSales / totalInvoices : 0
                };
            });

            // ترتيب العملاء حسب إجمالي المبيعات
            customerBalances.sort((a, b) => b.totalSales - a.totalSales);

            // بناء صفوف الجدول
            let tableRows = '';
            let totalAllSales = 0;

            if (customerBalances.length > 0) {
                customerBalances.forEach((customer, index) => {
                    totalAllSales += customer.totalSales;

                    tableRows += `
                        <tr>
                            <td>${index + 1}</td>
                            <td><strong>${customer.name}</strong></td>
                            <td>${customer.totalSales.toFixed(2)}</td>
                            <td>${customer.totalInvoices}</td>
                            <td>${customer.averageSale.toFixed(2)}</td>
                            <td>${customer.lastSaleDate}</td>
                        </tr>
                    `;
                });

                tableRows += `<tr class="total"><td colspan="2"><strong>الإجمالي</strong></td><td><strong>${totalAllSales.toFixed(2)}</strong></td><td colspan="3"></td></tr>`;
            } else {
                tableRows = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد بيانات عملاء</td></tr>';
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>أرصدة العملاء</h2>
                        <p>${dateRange}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي المبيعات</h4>
                                <p class="amount">${totalAllSales.toFixed(2)} ر.س</p>
                            </div>
                            <div class="summary-card">
                                <h4>عدد العملاء</h4>
                                <p class="count">${customerBalances.length} عميل</p>
                            </div>
                            <div class="summary-card">
                                <h4>متوسط المبيعات للعميل</h4>
                                <p class="average">${customerBalances.length > 0 ? (totalAllSales / customerBalances.length).toFixed(2) : '0'} ر.س</p>
                            </div>
                        </div>
                        <table class="report-table">
                            <thead>
                                <tr><th>#</th><th>اسم العميل</th><th>إجمالي المبيعات (ر.س)</th><th>عدد الفواتير</th><th>متوسط الفاتورة (ر.س)</th><th>آخر عملية شراء</th></tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('customer-balances')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('customer-balances')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دالة إنشاء تقرير تحليل العملاء
        function createCustomerAnalysisReport(dateRange, filters = {}) {
            return createCustomerBalancesReport(dateRange, filters); // نفس التقرير مؤقتاً
        }

        // دالة إنشاء تقرير أعمار الديون
        function createCustomerAgingReport(dateRange, filters = {}) {
            return createCustomerBalancesReport(dateRange, filters); // نفس التقرير مؤقتاً
        }

        // دالة إنشاء تقرير المخزون
        function createStockReport(currentDate) {
            // الحصول على البيانات الحقيقية
            let productsData = [];
            if (window.dataManager) {
                productsData = window.dataManager.getProducts();
            } else {
                productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            }

            console.log('📦 بيانات المنتجات للتقرير:', productsData.length, 'منتج');

            // حساب الإحصائيات
            let totalValue = 0;
            let lowStockCount = 0;
            let outOfStockCount = 0;

            // بناء صفوف الجدول
            let tableRows = '';
            if (productsData.length > 0) {
                productsData.forEach((product, index) => {
                    const quantity = product.quantity || 0;
                    const price = product.price || 0;
                    const totalProductValue = quantity * price;
                    totalValue += totalProductValue;

                    // تحديد حالة المخزون
                    let status = '';
                    if (quantity === 0) {
                        status = '<span class="status-critical">نفد</span>';
                        outOfStockCount++;
                    } else if (quantity <= (product.minStock || 5)) {
                        status = '<span class="status-warning">منخفض</span>';
                        lowStockCount++;
                    } else {
                        status = '<span class="status-good">جيد</span>';
                    }

                    tableRows += `
                        <tr>
                            <td>${product.code || product.id}</td>
                            <td><strong>${product.name}</strong></td>
                            <td>${quantity}</td>
                            <td>${price.toFixed(2)}</td>
                            <td>${totalProductValue.toFixed(2)}</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });

                tableRows += `<tr class="total"><td colspan="4"><strong>الإجمالي</strong></td><td><strong>${totalValue.toFixed(2)}</strong></td><td></td></tr>`;
            } else {
                tableRows = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد منتجات في المخزون</td></tr>';
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>تقرير المخزون الحالي</h2>
                        <p>كما في ${currentDate}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي قيمة المخزون</h4>
                                <p class="amount">${totalValue.toFixed(2)} ر.س</p>
                            </div>
                            <div class="summary-card">
                                <h4>عدد الأصناف</h4>
                                <p class="count">${productsData.length} صنف</p>
                            </div>
                            <div class="summary-card">
                                <h4>الأصناف الناقصة</h4>
                                <p class="warning">${lowStockCount + outOfStockCount} صنف</p>
                            </div>
                        </div>
                        <table class="report-table">
                            <thead>
                                <tr><th>كود الصنف</th><th>اسم الصنف</th><th>الكمية</th><th>سعر الوحدة (ر.س)</th><th>إجمالي القيمة (ر.س)</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('current-stock')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('current-stock')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }



        // دالة إنشاء تقرير افتراضي
        function createDefaultReport(reportType, currentDate) {
            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>تقرير: ${reportType}</h2>
                        <p>تم إنشاء التقرير في ${currentDate}</p>
                    </div>
                    <div class="report-content">
                        <table class="report-table">
                            <thead>
                                <tr><th>البند</th><th>القيمة (ر.س)</th><th>النسبة %</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>إجمالي المبيعات</td><td>150,000</td><td>100%</td></tr>
                                <tr><td>التكاليف</td><td>90,000</td><td>60%</td></tr>
                                <tr class="profit"><td><strong>صافي الربح</strong></td><td><strong>60,000</strong></td><td><strong>40%</strong></td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('${reportType}')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('${reportType}')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        // دوال التقارير الإضافية المبسطة
        function createCashFlowReport(currentDate) {
            return createDefaultReport('قائمة التدفقات النقدية', currentDate);
        }

        function createTrialBalanceReport(currentDate) {
            return createDefaultReport('ميزان المراجعة', currentDate);
        }

        function createMonthlySalesReport(currentDate) {
            return createDefaultReport('المبيعات الشهرية', currentDate);
        }

        // دالة إرجاع فواتير العميل المحدد
        function getCustomerInvoices(customerId) {
            const customerInvoices = {
                'ahmed-ali': {
                    invoices: [
                        '<tr><td>INV-001</td><td>15/01/2024</td><td>3,500</td><td>نقداً</td><td>لابتوب ديل XPS 13</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-018</td><td>12/01/2024</td><td>650</td><td>نقداً</td><td>كرسي مكتبي جلد</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-032</td><td>10/01/2024</td><td>1,100</td><td>نقداً</td><td>شاشة سامسونج 27 بوصة</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '5,250',
                    count: '3'
                },
                'sara-khalid': {
                    invoices: [
                        '<tr><td>INV-005</td><td>14/01/2024</td><td>4,200</td><td>بطاقة ائتمان</td><td>هاتف آيفون 15</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-025</td><td>11/01/2024</td><td>1,200</td><td>بطاقة ائتمان</td><td>طابعة ليزر HP</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-038</td><td>09/01/2024</td><td>850</td><td>تحويل بنكي</td><td>ماوس لاسلكي + كيبورد</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-052</td><td>07/01/2024</td><td>2,800</td><td>بطاقة ائتمان</td><td>تابلت آيباد</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '9,050',
                    count: '4'
                },
                'mohammed-ahmed': {
                    invoices: [
                        '<tr><td>INV-012</td><td>13/01/2024</td><td>1,800</td><td>تحويل بنكي</td><td>مكتب خشبي فاخر</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-045</td><td>08/01/2024</td><td>12,100</td><td>تقسيط</td><td>مجموعة مكتبية كاملة</td><td><span class="status-pending">جاري التحصيل</span></td></tr>'
                    ],
                    total: '13,900',
                    count: '2'
                },
                'fatima-saad': {
                    invoices: [
                        '<tr><td>INV-029</td><td>10/01/2024</td><td>3,200</td><td>نقداً</td><td>كرسي مكتبي جلد - 5 قطع</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-041</td><td>08/01/2024</td><td>1,500</td><td>بطاقة ائتمان</td><td>أدوات مكتبية متنوعة</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-055</td><td>06/01/2024</td><td>950</td><td>نقداً</td><td>سماعات سوني</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '5,650',
                    count: '3'
                },
                'abdullah-otaibi': {
                    invoices: [
                        '<tr><td>INV-063</td><td>09/01/2024</td><td>2,400</td><td>تحويل بنكي</td><td>كيبورد ميكانيكي - 3 قطع</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-071</td><td>07/01/2024</td><td>1,800</td><td>نقداً</td><td>سماعات لاسلكية</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '4,200',
                    count: '2'
                },
                'nora-zahrani': {
                    invoices: [
                        '<tr><td>INV-078</td><td>10/01/2024</td><td>1,829</td><td>بطاقة ائتمان</td><td>أدوات مكتبية وقرطاسية</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-085</td><td>08/01/2024</td><td>3,200</td><td>تحويل بنكي</td><td>كرسي مكتبي متحرك</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-092</td><td>06/01/2024</td><td>950</td><td>نقداً</td><td>ماوس وكيبورد</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-099</td><td>05/01/2024</td><td>2,100</td><td>بطاقة ائتمان</td><td>شاشة كمبيوتر 24 بوصة</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-106</td><td>04/01/2024</td><td>1,650</td><td>تحويل بنكي</td><td>طابعة نافثة للحبر</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-113</td><td>03/01/2024</td><td>1,200</td><td>نقداً</td><td>كابلات وموصلات</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-120</td><td>02/01/2024</td><td>871</td><td>بطاقة ائتمان</td><td>إكسسوارات مكتبية</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '12,800',
                    count: '7'
                },
                'khalid-mutairi': {
                    invoices: [
                        '<tr><td>INV-127</td><td>11/01/2024</td><td>4,500</td><td>تقسيط</td><td>مكتب تنفيذي فاخر</td><td><span class="status-pending">جاري التحصيل</span></td></tr>',
                        '<tr><td>INV-134</td><td>09/01/2024</td><td>2,200</td><td>بطاقة ائتمان</td><td>كرسي مدير جلد</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-141</td><td>07/01/2024</td><td>1,100</td><td>نقداً</td><td>مصباح مكتبي LED</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '7,800',
                    count: '3'
                },
                'reem-shahri': {
                    invoices: [
                        '<tr><td>INV-148</td><td>12/01/2024</td><td>3,800</td><td>تحويل بنكي</td><td>تابلت سامسونج</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-155</td><td>10/01/2024</td><td>1,950</td><td>بطاقة ائتمان</td><td>سماعات بلوتوث</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '5,750',
                    count: '2'
                },
                'omar-salem': {
                    invoices: [
                        '<tr><td>INV-162</td><td>13/01/2024</td><td>2,650</td><td>نقداً</td><td>كاميرا ويب عالية الدقة</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-169</td><td>11/01/2024</td><td>1,400</td><td>بطاقة ائتمان</td><td>ميكروفون USB</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-176</td><td>09/01/2024</td><td>3,200</td><td>تحويل بنكي</td><td>شاشة منحنية 32 بوصة</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-183</td><td>07/01/2024</td><td>850</td><td>نقداً</td><td>حامل شاشة قابل للتعديل</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '8,100',
                    count: '4'
                },
                'layla-ahmed': {
                    invoices: [
                        '<tr><td>INV-190</td><td>14/01/2024</td><td>5,200</td><td>تقسيط</td><td>لابتوب HP EliteBook</td><td><span class="status-pending">جاري التحصيل</span></td></tr>',
                        '<tr><td>INV-197</td><td>12/01/2024</td><td>1,800</td><td>بطاقة ائتمان</td><td>حقيبة لابتوب فاخرة</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '7,000',
                    count: '2'
                },
                'youssef-hassan': {
                    invoices: [
                        '<tr><td>INV-204</td><td>15/01/2024</td><td>2,100</td><td>نقداً</td><td>طابعة متعددة الوظائف</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-211</td><td>13/01/2024</td><td>1,650</td><td>تحويل بنكي</td><td>ماسح ضوئي محمول</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-218</td><td>11/01/2024</td><td>950</td><td>بطاقة ائتمان</td><td>ورق طباعة عالي الجودة</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '4,700',
                    count: '3'
                },
                'maryam-ali': {
                    invoices: [
                        '<tr><td>INV-225</td><td>16/01/2024</td><td>3,400</td><td>بطاقة ائتمان</td><td>كرسي مريح للظهر</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-232</td><td>14/01/2024</td><td>2,800</td><td>تحويل بنكي</td><td>مكتب قابل للتعديل</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-239</td><td>12/01/2024</td><td>1,200</td><td>نقداً</td><td>منظم مكتبي خشبي</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-246</td><td>10/01/2024</td><td>750</td><td>بطاقة ائتمان</td><td>مجموعة أقلام فاخرة</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '8,150',
                    count: '4'
                },
                'hassan-ibrahim': {
                    invoices: [
                        '<tr><td>INV-253</td><td>17/01/2024</td><td>4,800</td><td>تقسيط</td><td>جهاز عرض محمول</td><td><span class="status-pending">جاري التحصيل</span></td></tr>',
                        '<tr><td>INV-260</td><td>15/01/2024</td><td>2,200</td><td>تحويل بنكي</td><td>شاشة عرض تفاعلية</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '7,000',
                    count: '2'
                },
                'aisha-mohammed': {
                    invoices: [
                        '<tr><td>INV-267</td><td>18/01/2024</td><td>1,950</td><td>بطاقة ائتمان</td><td>هاتف ذكي سامسونج</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-274</td><td>16/01/2024</td><td>1,400</td><td>نقداً</td><td>غطاء وحامي شاشة</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-281</td><td>14/01/2024</td><td>850</td><td>بطاقة ائتمان</td><td>شاحن لاسلكي سريع</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '4,200',
                    count: '3'
                },
                'waleed-abdulla': {
                    invoices: [
                        '<tr><td>INV-288</td><td>19/01/2024</td><td>6,500</td><td>تحويل بنكي</td><td>نظام صوتي للمكتب</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-295</td><td>17/01/2024</td><td>3,200</td><td>بطاقة ائتمان</td><td>مكبرات صوت ذكية</td><td><span class="status-paid">مدفوع</span></td></tr>',
                        '<tr><td>INV-302</td><td>15/01/2024</td><td>1,800</td><td>نقداً</td><td>سماعات إلغاء الضوضاء</td><td><span class="status-paid">مدفوع</span></td></tr>'
                    ],
                    total: '11,500',
                    count: '3'
                }
            };

            let customer = customerInvoices[customerId];

            // إذا لم يتم العثور على العميل، تحقق من العملاء الجدد في localStorage
            if (!customer && customerId.startsWith('customer-')) {
                const customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const customerIdNum = customerId.replace('customer-', '');
                const customerInfo = customersData.find(c => c.id == customerIdNum);

                if (customerInfo) {
                    // إنشاء فواتير وهمية للعميل الجديد
                    customer = {
                        invoices: [
                            `<tr><td>INV-NEW-001</td><td>${new Date().toLocaleDateString('ar-SA')}</td><td>1,500</td><td>نقداً</td><td>منتجات متنوعة</td><td><span class="status-paid">مدفوع</span></td></tr>`,
                            `<tr><td>INV-NEW-002</td><td>${new Date(Date.now() - 86400000).toLocaleDateString('ar-SA')}</td><td>2,300</td><td>بطاقة ائتمان</td><td>خدمات إضافية</td><td><span class="status-paid">مدفوع</span></td></tr>`
                        ],
                        total: '3,800',
                        count: '2'
                    };
                }
            }

            if (!customer) {
                return '<tr><td colspan="6">لا توجد فواتير لهذا العميل</td></tr>';
            }

            const invoicesHtml = customer.invoices.join('');
            const totalRow = `<tr class="total"><td colspan="2"><strong>الإجمالي</strong></td><td><strong>${customer.total}</strong></td><td colspan="3"></td></tr>`;

            return invoicesHtml + totalRow;
        }

        // دالة تحديث قوائم العملاء من localStorage
        function updateCustomerFiltersFromStorage() {
            const customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];

            if (customersData.length > 0) {
                // تحديث قائمة العملاء في المرشحات
                const customerFilters = [];
                customerFilters.push({ value: '', text: 'جميع العملاء' });

                customersData.forEach(customer => {
                    const customerId = `customer-${customer.id}`;
                    customerFilters.push({ value: customerId, text: customer.name });
                });

                // تحديث جميع قوائم العملاء في reportFilters
                if (window.reportFilters) {
                    window.reportFilters['by-customer'] = customerFilters;
                    window.reportFilters['customer-list'] = customerFilters;
                    window.reportFilters['customer-balances'] = customerFilters;
                    window.reportFilters['customer-analysis'] = customerFilters;
                }

                console.log('تم تحديث قوائم العملاء في التقارير:', customersData.length, 'عميل');
            }
        }

        // دالة تحديث قوائم المنتجات من localStorage
        function updateProductFiltersFromStorage() {
            console.log('🔄 تحديث قوائم المنتجات...');

            // الحصول على البيانات من النظام المركزي أولاً
            let productsData = [];

            if (window.dataManager) {
                productsData = window.dataManager.getProducts();
                console.log('✅ تم تحميل المنتجات من النظام المركزي:', productsData.length, 'منتج');
            } else {
                productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
                console.log('⚠️ تم تحميل المنتجات من localStorage:', productsData.length, 'منتج');
            }

            if (productsData.length > 0) {
                // تحديث قائمة المنتجات في المرشحات
                const productFilters = [];
                productFilters.push({ value: '', text: 'جميع المنتجات' });

                productsData.forEach(product => {
                    const productId = `product-${product.id}`;
                    productFilters.push({ value: productId, text: product.name });
                });

                // تحديث جميع قوائم المنتجات في reportFilters
                if (window.reportFilters) {
                    window.reportFilters['by-product'] = productFilters;
                    window.reportFilters['product-list'] = productFilters;
                    window.reportFilters['inventory-status'] = productFilters;
                    window.reportFilters['product-analysis'] = productFilters;
                    window.reportFilters['current-stock'] = productFilters;
                    window.reportFilters['stock-movement'] = productFilters;
                    window.reportFilters['low-stock'] = productFilters;
                }

                // تحديث البيانات العامة للمنتجات
                window.realProductsData = productsData;

                console.log('✅ تم تحديث قوائم المنتجات في التقارير:', productsData.length, 'منتج');

                // عرض تفاصيل المنتجات
                productsData.forEach((product, index) => {
                    console.log(`   ${index + 1}. ${product.name} - الكمية: ${product.quantity || 0} - السعر: ${product.price || 0}`);
                });
            } else {
                console.log('⚠️ لا توجد منتجات');
            }
        }

        // دالة تحديث قوائم الموردين من النظام المركزي
        function updateSupplierFiltersFromStorage() {
            console.log('تحديث قوائم الموردين في التقارير...');

            // التحقق من توفر النظام المركزي
            if (!window.dataManager) {
                console.error('النظام المركزي غير متاح في التقارير');
                return;
            }

            const suppliersData = window.dataManager.getSuppliers();
            console.log('الموردين المتاحين:', suppliersData);

            if (suppliersData.length > 0) {
                // تحديث قائمة الموردين في المرشحات
                const supplierFilters = [];
                supplierFilters.push({ value: '', text: 'جميع الموردين' });

                suppliersData.forEach(supplier => {
                    const supplierId = `supplier-${supplier.id}`;
                    supplierFilters.push({ value: supplierId, text: supplier.name });
                });

                // تحديث جميع قوائم الموردين في reportFilters
                if (window.reportFilters) {
                    window.reportFilters['by-supplier'] = supplierFilters;
                    window.reportFilters['supplier-list'] = supplierFilters;
                    window.reportFilters['supplier-balances'] = supplierFilters;
                    window.reportFilters['supplier-analysis'] = supplierFilters;
                    window.reportFilters['supplier-evaluation'] = supplierFilters;
                }

                console.log('تم تحديث قوائم الموردين في التقارير:', suppliersData.length, 'مورد');
            } else {
                console.log('لا توجد موردين في النظام');
            }
        }

        // دالة إرجاع ملخص العميل المحدد
        function getCustomerSummary(customerId) {
            const customerSummary = {
                'ahmed-ali': { total: '5,250', count: '3', average: '1,750' },
                'sara-khalid': { total: '9,050', count: '4', average: '2,263' },
                'mohammed-ahmed': { total: '13,900', count: '2', average: '6,950' },
                'fatima-saad': { total: '5,650', count: '3', average: '1,883' },
                'abdullah-otaibi': { total: '4,200', count: '2', average: '2,100' },
                'nora-zahrani': { total: '12,800', count: '7', average: '1,829' },
                'khalid-mutairi': { total: '7,800', count: '3', average: '2,600' },
                'reem-shahri': { total: '5,750', count: '2', average: '2,875' },
                'omar-salem': { total: '8,100', count: '4', average: '2,025' },
                'layla-ahmed': { total: '7,000', count: '2', average: '3,500' },
                'youssef-hassan': { total: '4,700', count: '3', average: '1,567' },
                'maryam-ali': { total: '8,150', count: '4', average: '2,038' },
                'hassan-ibrahim': { total: '7,000', count: '2', average: '3,500' },
                'aisha-mohammed': { total: '4,200', count: '3', average: '1,400' },
                'waleed-abdulla': { total: '11,500', count: '3', average: '3,833' }
            };

            let summary = customerSummary[customerId];

            // إذا لم يتم العثور على العميل، تحقق من العملاء الجدد في localStorage
            if (!summary && customerId.startsWith('customer-')) {
                const customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const customerIdNum = customerId.replace('customer-', '');
                const customerInfo = customersData.find(c => c.id == customerIdNum);

                if (customerInfo) {
                    // إرجاع ملخص افتراضي للعميل الجديد
                    summary = { total: '3,800', count: '2', average: '1,900' };
                }
            }

            return summary || { total: '0', count: '0', average: '0' };
        }

        function createSalesByCustomerReport(dateRange, filters = {}) {
            let filterInfo = '';
            let reportTitle = 'تقرير المبيعات حسب العميل';

            if (filters.account && filters.account !== '') {
                const customerNames = {
                    'ahmed-ali': 'أحمد محمد علي',
                    'fatima-saad': 'فاطمة السعد',
                    'mohammed-ahmed': 'محمد الأحمد',
                    'sara-khalid': 'سارة خالد',
                    'abdullah-otaibi': 'عبدالله العتيبي',
                    'nora-zahrani': 'نورا الزهراني',
                    'khalid-mutairi': 'خالد المطيري',
                    'reem-shahri': 'ريم الشهري',
                    'omar-salem': 'عمر سالم',
                    'layla-ahmed': 'ليلى أحمد',
                    'youssef-hassan': 'يوسف حسان',
                    'maryam-ali': 'مريم علي',
                    'hassan-ibrahim': 'حسان إبراهيم',
                    'aisha-mohammed': 'عائشة محمد',
                    'waleed-abdulla': 'وليد عبدالله'
                };
                const customerName = customerNames[filters.account] || filters.account;
                filterInfo += ` - العميل: ${customerName}`;
                reportTitle = `مبيعات العميل: ${customerName}`;
            }
            if (filters.branch && filters.branch !== '') {
                const branchNames = {
                    'main': 'الفرع الرئيسي',
                    'branch1': 'فرع الرياض',
                    'branch2': 'فرع جدة'
                };
                filterInfo += ` - الفرع: ${branchNames[filters.branch] || filters.branch}`;
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>${reportTitle}</h2>
                        <p>${dateRange}${filterInfo}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي المبيعات</h4>
                                <p class="amount">${filters.account ? getCustomerSummary(filters.account).total : '125,750'} ر.س</p>
                            </div>
                            <div class="summary-card">
                                <h4>${filters.account ? 'عدد الفواتير' : 'عدد العملاء'}</h4>
                                <p class="count">${filters.account ? getCustomerSummary(filters.account).count + ' فاتورة' : '15 عميل'}</p>
                            </div>
                            <div class="summary-card">
                                <h4>متوسط ${filters.account ? 'الفاتورة' : 'المبيعات للعميل'}</h4>
                                <p class="average">${filters.account ? getCustomerSummary(filters.account).average : '8,383'} ر.س</p>
                            </div>
                        </div>
                        ${filters.account ? `
                        <table class="report-table">
                            <thead>
                                <tr><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ (ر.س)</th><th>طريقة الدفع</th><th>المنتجات</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                ${getCustomerInvoices(filters.account)}
                            </tbody>
                        </table>
                        ` : `
                        <table class="report-table">
                            <thead>
                                <tr><th>اسم العميل</th><th>عدد الفواتير</th><th>إجمالي المبيعات (ر.س)</th><th>متوسط الفاتورة (ر.س)</th><th>آخر عملية شراء</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>أحمد محمد علي</td><td>3</td><td>5,250</td><td>1,750</td><td>15/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>فاطمة السعد</td><td>3</td><td>5,650</td><td>1,883</td><td>10/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>محمد الأحمد</td><td>2</td><td>13,900</td><td>6,950</td><td>13/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>سارة خالد</td><td>4</td><td>9,050</td><td>2,263</td><td>14/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>عبدالله العتيبي</td><td>2</td><td>4,200</td><td>2,100</td><td>09/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>نورا الزهراني</td><td>7</td><td>12,800</td><td>1,829</td><td>10/01/2024</td><td><span class="status-warning">متوسط</span></td></tr>
                                <tr><td>خالد المطيري</td><td>3</td><td>7,800</td><td>2,600</td><td>11/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>ريم الشهري</td><td>2</td><td>5,750</td><td>2,875</td><td>12/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>عمر سالم</td><td>4</td><td>8,100</td><td>2,025</td><td>13/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>ليلى أحمد</td><td>2</td><td>7,000</td><td>3,500</td><td>14/01/2024</td><td><span class="status-warning">متوسط</span></td></tr>
                                <tr><td>يوسف حسان</td><td>3</td><td>4,700</td><td>1,567</td><td>15/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>مريم علي</td><td>4</td><td>8,150</td><td>2,038</td><td>16/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>حسان إبراهيم</td><td>2</td><td>7,000</td><td>3,500</td><td>17/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr><td>عائشة محمد</td><td>3</td><td>4,200</td><td>1,400</td><td>18/01/2024</td><td><span class="status-warning">متوسط</span></td></tr>
                                <tr><td>وليد عبدالله</td><td>3</td><td>11,500</td><td>3,833</td><td>19/01/2024</td><td><span class="status-good">نشط</span></td></tr>
                                <tr class="total"><td><strong>الإجمالي</strong></td><td><strong>47</strong></td><td><strong>125,750</strong></td><td><strong>2,675</strong></td><td colspan="2"></td></tr>
                            </tbody>
                        </table>
                        `}
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('sales-by-customer')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('sales-by-customer')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        function createSalesByProductReport(dateRange, filters = {}) {
            let filterInfo = '';
            let reportTitle = 'تقرير المبيعات حسب المنتج';

            if (filters.account && filters.account !== '') {
                const productNames = {
                    'laptop-dell-xps13': 'لابتوب ديل XPS 13',
                    'iphone-15': 'هاتف آيفون 15',
                    'wooden-desk': 'مكتب خشبي فاخر',
                    'leather-chair': 'كرسي مكتبي جلد',
                    'hp-printer': 'طابعة ليزر HP',
                    'samsung-monitor': 'شاشة سامسونج 27 بوصة',
                    'office-supplies': 'أدوات مكتبية متنوعة',
                    'wireless-mouse': 'ماوس لاسلكي',
                    'keyboard-mechanical': 'كيبورد ميكانيكي',
                    'tablet-ipad': 'تابلت آيباد',
                    'headphones-sony': 'سماعات سوني',
                    'webcam-logitech': 'كاميرا ويب لوجيتك'
                };
                const productName = productNames[filters.account] || filters.account;
                filterInfo += ` - المنتج: ${productName}`;
                reportTitle = `مبيعات المنتج: ${productName}`;
            }
            if (filters.branch && filters.branch !== '') {
                const branchNames = {
                    'main': 'الفرع الرئيسي',
                    'branch1': 'فرع الرياض',
                    'branch2': 'فرع جدة'
                };
                filterInfo += ` - الفرع: ${branchNames[filters.branch] || filters.branch}`;
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>${reportTitle}</h2>
                        <p>${dateRange}${filterInfo}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي المبيعات</h4>
                                <p class="amount">${filters.account ? '42,000' : '89,450'} ر.س</p>
                            </div>
                            <div class="summary-card">
                                <h4>${filters.account ? 'الكمية المباعة' : 'عدد المنتجات المباعة'}</h4>
                                <p class="count">${filters.account ? '12 قطعة' : '156 قطعة'}</p>
                            </div>
                            <div class="summary-card">
                                <h4>${filters.account ? 'سعر الوحدة' : 'متوسط سعر البيع'}</h4>
                                <p class="average">${filters.account ? '3,500' : '574'} ر.س</p>
                            </div>
                        </div>
                        ${filters.account ? `
                        <table class="report-table">
                            <thead>
                                <tr><th>رقم الفاتورة</th><th>التاريخ</th><th>العميل</th><th>الكمية</th><th>سعر الوحدة (ر.س)</th><th>الإجمالي (ر.س)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>INV-001</td><td>15/01/2024</td><td>أحمد محمد علي</td><td>1</td><td>3,500</td><td>3,500</td></tr>
                                <tr><td>INV-012</td><td>14/01/2024</td><td>فاطمة السعد</td><td>2</td><td>3,500</td><td>7,000</td></tr>
                                <tr><td>INV-025</td><td>13/01/2024</td><td>محمد الأحمد</td><td>1</td><td>3,500</td><td>3,500</td></tr>
                                <tr><td>INV-038</td><td>12/01/2024</td><td>سارة خالد</td><td>3</td><td>3,500</td><td>10,500</td></tr>
                                <tr><td>INV-045</td><td>11/01/2024</td><td>عبدالله العتيبي</td><td>2</td><td>3,500</td><td>7,000</td></tr>
                                <tr><td>INV-052</td><td>10/01/2024</td><td>نورا الزهراني</td><td>1</td><td>3,500</td><td>3,500</td></tr>
                                <tr><td>INV-068</td><td>09/01/2024</td><td>خالد المطيري</td><td>2</td><td>3,500</td><td>7,000</td></tr>
                                <tr class="total"><td colspan="3"><strong>الإجمالي</strong></td><td><strong>12</strong></td><td></td><td><strong>42,000</strong></td></tr>
                            </tbody>
                        </table>
                        ` : `
                        <table class="report-table">
                            <thead>
                                <tr><th>اسم المنتج</th><th>الفئة</th><th>الكمية المباعة</th><th>سعر الوحدة (ر.س)</th><th>إجمالي المبيعات (ر.س)</th><th>النسبة %</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>لابتوب ديل XPS 13</td><td>إلكترونيات</td><td>12</td><td>3,500</td><td>42,000</td><td>46.9%</td></tr>
                                <tr><td>هاتف آيفون 15</td><td>إلكترونيات</td><td>5</td><td>4,200</td><td>21,000</td><td>23.5%</td></tr>
                                <tr><td>مكتب خشبي فاخر</td><td>أثاث</td><td>8</td><td>1,800</td><td>14,400</td><td>16.1%</td></tr>
                                <tr><td>كرسي مكتبي جلد</td><td>أثاث</td><td>15</td><td>650</td><td>9,750</td><td>10.9%</td></tr>
                                <tr><td>طابعة ليزر HP</td><td>إلكترونيات</td><td>3</td><td>1,200</td><td>3,600</td><td>4.0%</td></tr>
                                <tr><td>شاشة سامسونج 27 بوصة</td><td>إلكترونيات</td><td>2</td><td>1,100</td><td>2,200</td><td>2.5%</td></tr>
                                <tr><td>أدوات مكتبية متنوعة</td><td>قرطاسية</td><td>25</td><td>20</td><td>500</td><td>0.6%</td></tr>
                                <tr><td>ماوس لاسلكي</td><td>إلكترونيات</td><td>18</td><td>85</td><td>1,530</td><td>1.7%</td></tr>
                                <tr><td>كيبورد ميكانيكي</td><td>إلكترونيات</td><td>12</td><td>150</td><td>1,800</td><td>2.0%</td></tr>
                                <tr><td>تابلت آيباد</td><td>إلكترونيات</td><td>4</td><td>2,800</td><td>11,200</td><td>12.5%</td></tr>
                                <tr class="total"><td colspan="2"><strong>الإجمالي</strong></td><td><strong>99</strong></td><td></td><td><strong>107,480</strong></td><td><strong>100%</strong></td></tr>
                            </tbody>
                        </table>
                        `}
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('sales-by-product')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('sales-by-product')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        function createStockMovementReport(currentDate) {
            // الحصول على حركات المخزون الحقيقية
            let stockMovements = [];
            if (window.dataManager) {
                stockMovements = window.dataManager.getStockMovements();
            } else {
                stockMovements = JSON.parse(localStorage.getItem('monjizStockMovements')) || [];
            }

            console.log('📦 حركات المخزون للتقرير:', stockMovements.length, 'حركة');

            // ترتيب الحركات حسب التاريخ (الأحدث أولاً)
            stockMovements.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            // حساب الإحصائيات
            const totalMovements = stockMovements.length;
            const inMovements = stockMovements.filter(m => m.movementType === 'in');
            const outMovements = stockMovements.filter(m => m.movementType === 'out');
            const totalInQuantity = inMovements.reduce((sum, m) => sum + (m.quantity || 0), 0);
            const totalOutQuantity = outMovements.reduce((sum, m) => sum + (m.quantity || 0), 0);

            // بناء صفوف الجدول
            let tableRows = '';
            if (stockMovements.length > 0) {
                stockMovements.forEach((movement, index) => {
                    const movementTypeText = movement.movementType === 'in' ? 'دخول' : 'خروج';
                    const movementTypeClass = movement.movementType === 'in' ? 'status-paid' : 'status-warning';

                    const referenceTypeText = {
                        'sale': 'مبيعات',
                        'purchase': 'مشتريات',
                        'adjustment': 'تسوية',
                        'manual': 'يدوي',
                        'transfer': 'تحويل'
                    }[movement.referenceType] || movement.referenceType;

                    tableRows += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${movement.date} ${movement.time || ''}</td>
                            <td><strong>${movement.productName}</strong></td>
                            <td>${movement.productCode}</td>
                            <td><span class="${movementTypeClass}">${movementTypeText}</span></td>
                            <td>${movement.quantity}</td>
                            <td>${movement.oldQuantity || 0}</td>
                            <td>${movement.newQuantity || 0}</td>
                            <td>${referenceTypeText}</td>
                            <td>${movement.reference || 'غير محدد'}</td>
                        </tr>
                    `;
                });
            } else {
                tableRows = '<tr><td colspan="10" style="text-align: center; padding: 20px; color: #666;">لا توجد حركات مخزون</td></tr>';
            }

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>تقرير حركة المخزون</h2>
                        <p>كما في ${currentDate}</p>
                    </div>
                    <div class="report-content">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>إجمالي الحركات</h4>
                                <p class="count">${totalMovements} حركة</p>
                            </div>
                            <div class="summary-card">
                                <h4>حركات الدخول</h4>
                                <p class="count">${inMovements.length} حركة (${totalInQuantity} وحدة)</p>
                            </div>
                            <div class="summary-card">
                                <h4>حركات الخروج</h4>
                                <p class="count">${outMovements.length} حركة (${totalOutQuantity} وحدة)</p>
                            </div>
                        </div>
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>التاريخ والوقت</th>
                                    <th>اسم المنتج</th>
                                    <th>كود المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>الرصيد السابق</th>
                                    <th>الرصيد الجديد</th>
                                    <th>نوع العملية</th>
                                    <th>المرجع</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn-print">طباعة</button>
                        <button onclick="exportToExcel('stock-movement')" class="btn-excel">تصدير Excel</button>
                        <button onclick="exportToPDF('stock-movement')" class="btn-pdf">تصدير PDF</button>
                    </div>
                </div>
            `;
        }

        function createLowStockReport(currentDate) {
            return createDefaultReport('تقرير النواقص', currentDate);
        }

        function createStockValuationReport(currentDate) {
            return createDefaultReport('تقييم المخزون', currentDate);
        }

        // دوال التصدير محسنة
        function exportToExcel(reportType) {
            // إظهار مؤشر التحميل
            showLoadingMessage('جاري تصدير التقرير إلى Excel...');

            setTimeout(() => {
                hideLoadingMessage();
                showSuccessMessage(`تم تصدير تقرير ${getReportTitle(reportType)} إلى Excel بنجاح!`);
            }, 1500);
        }

        function exportToPDF(reportType) {
            // إظهار مؤشر التحميل
            showLoadingMessage('جاري تصدير التقرير إلى PDF...');

            setTimeout(() => {
                hideLoadingMessage();
                showSuccessMessage(`تم تصدير تقرير ${getReportTitle(reportType)} إلى PDF بنجاح!`);
            }, 1500);
        }

        function generateDetailedReport() {
            showSuccessMessage('تم إنشاء التقرير المفصل بنجاح!');
        }

        // دوال مساعدة للرسائل
        function getReportTitle(reportType) {
            const titles = {
                'balance-sheet': 'قائمة المركز المالي',
                'profit-loss': 'قائمة الأرباح والخسائر',
                'daily': 'المبيعات اليومية',
                'current-stock': 'المخزون الحالي'
            };
            return titles[reportType] || reportType;
        }

        function showLoadingMessage(message) {
            const existingMessage = document.getElementById('loading-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loading-message';
            loadingDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                text-align: center;
                min-width: 300px;
            `;

            loadingDiv.innerHTML = `
                <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20px;"></div>
                <p style="margin: 0; color: #333; font-weight: 600;">${message}</p>
            `;

            document.body.appendChild(loadingDiv);
        }

        function hideLoadingMessage() {
            const loadingMessage = document.getElementById('loading-message');
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }

        function showSuccessMessage(message) {
            const existingMessage = document.getElementById('success-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const successDiv = document.createElement('div');
            successDiv.id = 'success-message';
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(40,167,69,0.3);
                z-index: 10000;
                max-width: 400px;
                animation: slideIn 0.3s ease-out;
            `;

            successDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check-circle" style="font-size: 20px;"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (successDiv.parentNode) {
                        successDiv.remove();
                    }
                }, 300);
            }, 3000);
        }

        function showErrorMessage(message) {
            const existingMessage = document.getElementById('error-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const errorDiv = document.createElement('div');
            errorDiv.id = 'error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(220,53,69,0.3);
                z-index: 10000;
                max-width: 400px;
                animation: slideIn 0.3s ease-out;
            `;

            errorDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-exclamation-circle" style="font-size: 20px;"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(errorDiv);

            setTimeout(() => {
                errorDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 300);
            }, 4000);
        }

        // تهيئة التحسينات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة CSS للرسوم المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
                .report-categories-modern, #selected-report {
                    transition: opacity 0.3s ease;
                }
            `;
            document.head.appendChild(style);

            // تحديث قوائم العملاء والمنتجات والموردين من النظام المركزي
            setTimeout(() => {
                if (window.dataManager) {
                    updateCustomerFiltersFromStorage();
                    updateProductFiltersFromStorage();
                    updateSupplierFiltersFromStorage();
                    updateSalesDataFromStorage();
                    updatePurchasesDataFromStorage();
                    updateAccountsDataFromStorage();

                    // إعداد مراقبة التحديثات المباشرة
                    setupRealtimeDataMonitoring();
                } else {
                    console.error('النظام المركزي غير محمل في التقارير');
                }
            }, 500);

            // تحميل مسبق للبيانات
            setTimeout(preloadReportData, 1000);

            // إضافة تأثيرات بصرية
            setTimeout(addVisualEnhancements, 500);
        });

        // دالة تحديث بيانات المبيعات من localStorage
        function updateSalesDataFromStorage() {
            console.log('🔄 تحديث بيانات المبيعات...');

            // الحصول على البيانات من النظام المركزي أولاً
            let salesData = [];

            if (window.dataManager) {
                salesData = window.dataManager.getSales();
                console.log('✅ تم تحميل البيانات من النظام المركزي:', salesData.length, 'فاتورة');
            } else {
                // البحث في localStorage كبديل
                const invoicesData = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
                const salesLocalData = JSON.parse(localStorage.getItem('monjizSales')) || [];

                // دمج البيانات من المفتاحين
                salesData = [...invoicesData, ...salesLocalData];

                // إزالة المكررات
                salesData = salesData.filter((sale, index, self) =>
                    index === self.findIndex(s => s.id === sale.id)
                );

                console.log('⚠️ تم تحميل البيانات من localStorage:', salesData.length, 'فاتورة');
            }

            if (salesData.length > 0) {
                // تحديث البيانات الوهمية بالبيانات الحقيقية
                window.realSalesData = salesData;

                // تحديث إحصائيات المبيعات
                updateSalesStatistics(salesData);

                console.log('✅ تم تحديث بيانات المبيعات الحقيقية');

                // عرض تفاصيل البيانات
                salesData.forEach((sale, index) => {
                    console.log(`   ${index + 1}. ${sale.id} - ${sale.customerName} - ${sale.total} ر.س`);
                });
            } else {
                console.log('⚠️ لا توجد بيانات مبيعات');
            }
        }

        // دالة تحديث بيانات المشتريات من localStorage
        function updatePurchasesDataFromStorage() {
            const purchasesData = JSON.parse(localStorage.getItem('monjizPurchases')) || [];
            console.log('تحديث بيانات المشتريات من localStorage:', purchasesData.length, 'فاتورة شراء');

            if (purchasesData.length > 0) {
                // تحديث البيانات الوهمية بالبيانات الحقيقية
                window.realPurchasesData = purchasesData;

                console.log('تم تحديث بيانات المشتريات الحقيقية');
            }
        }

        // دالة تحديث بيانات الحسابات من localStorage
        function updateAccountsDataFromStorage() {
            const accountsData = JSON.parse(localStorage.getItem('chartOfAccounts')) ||
                                JSON.parse(localStorage.getItem('monjizAccounts')) || [];
            console.log('تحديث بيانات الحسابات من localStorage:', accountsData.length, 'حساب');

            if (accountsData.length > 0) {
                // تحديث البيانات الوهمية بالبيانات الحقيقية
                window.realAccountsData = accountsData;

                // تحديث التقارير المالية
                updateFinancialReportsData(accountsData);

                console.log('تم تحديث بيانات الحسابات الحقيقية');
            }
        }

        // دالة إعداد مراقبة التحديثات المباشرة
        function setupRealtimeDataMonitoring() {
            console.log('🔄 إعداد مراقبة التحديثات المباشرة للتقارير...');

            // مراقبة تغييرات localStorage
            window.addEventListener('storage', function(e) {
                console.log('🔔 تم اكتشاف تغيير في البيانات:', e.key);

                switch(e.key) {
                    case 'monjizSales':
                    case 'monjizInvoices':
                        updateSalesDataFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'monjizPurchases':
                        updatePurchasesDataFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'monjizCustomers':
                        updateCustomerFiltersFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'monjizSuppliers':
                        updateSupplierFiltersFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'monjizProducts':
                        updateProductFiltersFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'chartOfAccounts':
                    case 'monjizAccounts':
                        updateAccountsDataFromStorage();
                        refreshCurrentReport();
                        break;
                    case 'monjizStockMovements':
                        console.log('🔄 تحديث حركات المخزون...');
                        refreshCurrentReport();
                        break;
                }
            });

            // مراقبة BroadcastChannel إذا كان متاحاً
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.addEventListener('message', function(event) {
                    console.log('🔔 تم استلام تحديث عبر BroadcastChannel:', event.data);

                    switch(event.data.type) {
                        case 'sales-updated':
                        case 'invoice-added':
                            updateSalesDataFromStorage();
                            refreshCurrentReport();
                            break;
                        case 'purchase-added':
                            updatePurchasesDataFromStorage();
                            refreshCurrentReport();
                            break;
                        case 'customer-added':
                        case 'customers-updated':
                            updateCustomerFiltersFromStorage();
                            refreshCurrentReport();
                            break;
                        case 'supplier-added':
                        case 'suppliers-updated':
                            updateSupplierFiltersFromStorage();
                            refreshCurrentReport();
                            break;
                        case 'product-added':
                        case 'products-updated':
                            updateProductFiltersFromStorage();
                            refreshCurrentReport();
                            break;
                        case 'accounts-updated':
                            updateAccountsDataFromStorage();
                            refreshCurrentReport();
                            break;
                    }
                });
            }

            // مراقبة دورية كل 30 ثانية
            setInterval(() => {
                console.log('🔄 تحديث دوري للبيانات...');
                updateAllDataFromStorage();
            }, 30000);

            console.log('✅ تم إعداد مراقبة التحديثات المباشرة');
        }

        // دالة تحديث جميع البيانات
        function updateAllDataFromStorage() {
            updateSalesDataFromStorage();
            updatePurchasesDataFromStorage();
            updateCustomerFiltersFromStorage();
            updateSupplierFiltersFromStorage();
            updateProductFiltersFromStorage();
            updateAccountsDataFromStorage();
        }

        // دالة إعادة تحميل التقرير الحالي
        function refreshCurrentReport() {
            if (currentReportType && currentView === 'report-detail') {
                console.log('🔄 إعادة تحميل التقرير الحالي:', currentReportType);

                // تأخير قصير لضمان تحديث البيانات
                setTimeout(() => {
                    const reportData = getDetailedReportData(currentReportType);
                    const reportContent = document.getElementById('report-content');
                    if (reportContent) {
                        reportContent.innerHTML = reportData.html;
                        console.log('✅ تم تحديث التقرير الحالي');
                    }
                }, 500);
            }
        }

        // دالة تحديث إحصائيات المبيعات
        function updateSalesStatistics(invoicesData) {
            // حساب إجمالي المبيعات
            let totalSales = 0;
            let totalInvoices = invoicesData.length;

            invoicesData.forEach(invoice => {
                if (invoice.total) {
                    totalSales += parseFloat(invoice.total) || 0;
                }
            });

            // تحديث عناصر الإحصائيات في الصفحة
            const totalSalesElements = document.querySelectorAll('.total-sales, [data-stat="total-sales"]');
            totalSalesElements.forEach(element => {
                element.textContent = totalSales.toFixed(2) + ' ر.س';
            });

            const totalInvoicesElements = document.querySelectorAll('.total-invoices, [data-stat="total-invoices"]');
            totalInvoicesElements.forEach(element => {
                element.textContent = totalInvoices;
            });

            console.log('تم تحديث الإحصائيات:', { totalSales, totalInvoices });
        }

        // دالة تحديث بيانات التقارير المالية
        function updateFinancialReportsData(accountsData) {
            console.log('🏦 تحديث بيانات التقارير المالية...');

            // تصنيف الحسابات حسب النوع
            const assetAccounts = accountsData.filter(acc => acc.type === 'assets');
            const liabilityAccounts = accountsData.filter(acc => acc.type === 'liabilities');
            const equityAccounts = accountsData.filter(acc => acc.type === 'equity');
            const revenueAccounts = accountsData.filter(acc => acc.type === 'revenue');
            const expenseAccounts = accountsData.filter(acc => acc.type === 'expenses');

            // حساب الأرصدة
            const totalAssets = assetAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
            const totalLiabilities = liabilityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
            const totalEquity = equityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
            const totalRevenue = revenueAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
            const totalExpenses = expenseAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);

            // حساب صافي الربح
            const netProfit = totalRevenue - totalExpenses;

            // تحديث البيانات العامة للتقارير المالية
            window.financialData = {
                assets: {
                    accounts: assetAccounts,
                    total: totalAssets,
                    formatted: totalAssets.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                },
                liabilities: {
                    accounts: liabilityAccounts,
                    total: totalLiabilities,
                    formatted: totalLiabilities.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                },
                equity: {
                    accounts: equityAccounts,
                    total: totalEquity,
                    formatted: totalEquity.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                },
                revenue: {
                    accounts: revenueAccounts,
                    total: totalRevenue,
                    formatted: totalRevenue.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                },
                expenses: {
                    accounts: expenseAccounts,
                    total: totalExpenses,
                    formatted: totalExpenses.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                },
                netProfit: {
                    amount: netProfit,
                    formatted: netProfit.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' })
                }
            };

            console.log('✅ تم تحديث بيانات التقارير المالية:', {
                totalAssets,
                totalLiabilities,
                totalEquity,
                netProfit
            });
        }

        // دالة تقرير المشتريات اليومية
        function createPurchasesDailyReport(dateRange, filters = {}) {
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];

            if (purchasesData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>المشتريات اليومية</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-shopping-cart"></i>
                            <p>لا توجد فواتير شراء في هذه الفترة</p>
                            <small>قم بإضافة فواتير شراء جديدة لعرض التقرير</small>
                        </div>
                    </div>
                `;
            }

            let totalPurchases = 0;
            let totalAmount = 0;
            let purchasesHtml = '';

            purchasesData.forEach(purchase => {
                totalPurchases++;
                totalAmount += parseFloat(purchase.total) || 0;

                purchasesHtml += `
                    <tr>
                        <td>${purchase.id}</td>
                        <td>${purchase.date}</td>
                        <td>${purchase.supplier}</td>
                        <td>${purchase.items ? purchase.items.length : 0}</td>
                        <td>${purchase.total ? purchase.total.toFixed(2) : '0.00'} ر.س</td>
                        <td>${purchase.payment || 'نقداً'}</td>
                    </tr>
                `;
            });

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>المشتريات اليومية</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>الفواتير</h3>
                            <p class="summary-value">${totalPurchases}</p>
                        </div>
                        <div class="summary-card">
                            <h3>المبلغ الإجمالي</h3>
                            <p class="summary-value">${totalAmount.toFixed(2)} ر.س</p>
                        </div>
                        <div class="summary-card">
                            <h3>متوسط الفاتورة</h3>
                            <p class="summary-value">${totalPurchases > 0 ? (totalAmount / totalPurchases).toFixed(2) : '0.00'} ر.س</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th style="width: 15%;">رقم الفاتورة</th>
                                    <th style="width: 12%;">التاريخ</th>
                                    <th style="width: 25%;">المورد</th>
                                    <th style="width: 10%;">الأصناف</th>
                                    <th style="width: 18%;">المبلغ</th>
                                    <th style="width: 20%;">طريقة الدفع</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchasesHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تقرير المشتريات حسب المورد
        function createPurchasesBySupplierReport(dateRange, filters = {}) {
            // الحصول على البيانات من النظام المركزي
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];
            const suppliersData = window.dataManager ? window.dataManager.getSuppliers() : [];

            console.log('بيانات المشتريات:', purchasesData);
            console.log('بيانات الموردين:', suppliersData);

            if (purchasesData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>المشتريات حسب المورد</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد فواتير شراء في هذه الفترة</p>
                        </div>
                    </div>
                `;
            }

            // تجميع البيانات حسب المورد
            const supplierData = {};

            purchasesData.forEach(purchase => {
                const supplier = purchase.supplier || 'غير محدد';

                if (!supplierData[supplier]) {
                    supplierData[supplier] = {
                        name: supplier,
                        invoices: 0,
                        totalAmount: 0,
                        items: 0
                    };
                }

                supplierData[supplier].invoices++;
                supplierData[supplier].totalAmount += parseFloat(purchase.total) || 0;
                supplierData[supplier].items += purchase.items ? purchase.items.length : 0;
            });

            let suppliersHtml = '';
            Object.values(supplierData).forEach(supplier => {
                suppliersHtml += `
                    <tr>
                        <td>${supplier.name}</td>
                        <td>${supplier.invoices}</td>
                        <td>${supplier.items}</td>
                        <td>${supplier.totalAmount.toFixed(2)} ر.س</td>
                        <td>${supplier.invoices > 0 ? (supplier.totalAmount / supplier.invoices).toFixed(2) : '0.00'} ر.س</td>
                    </tr>
                `;
            });

            const totalSuppliers = Object.keys(supplierData).length;
            const totalAmount = Object.values(supplierData).reduce((sum, supplier) => sum + supplier.totalAmount, 0);

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>المشتريات حسب المورد</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>عدد الموردين</h3>
                            <p class="summary-value">${totalSuppliers}</p>
                        </div>
                        <div class="summary-card">
                            <h3>إجمالي المشتريات</h3>
                            <p class="summary-value">${totalAmount.toFixed(2)} ر.س</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>المورد</th>
                                    <th>عدد الفواتير</th>
                                    <th>عدد الأصناف</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط الفاتورة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${suppliersHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تقرير قائمة الموردين
        function createSupplierListReport(dateRange, filters = {}) {
            const suppliersData = window.dataManager ? window.dataManager.getSuppliers() : [];
            console.log('قائمة الموردين للتقرير:', suppliersData);

            if (suppliersData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>قائمة الموردين</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد موردين مسجلين في النظام</p>
                        </div>
                    </div>
                `;
            }

            let suppliersHtml = '';
            suppliersData.forEach(supplier => {
                suppliersHtml += `
                    <tr>
                        <td>${supplier.name}</td>
                        <td>${supplier.phone || 'غير محدد'}</td>
                        <td>${supplier.email || 'غير محدد'}</td>
                        <td>${supplier.address || 'غير محدد'}</td>
                        <td>${supplier.category || 'عام'}</td>
                    </tr>
                `;
            });

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>قائمة الموردين</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>إجمالي الموردين</h3>
                            <p class="summary-value">${suppliersData.length}</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>العنوان</th>
                                    <th>الفئة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${suppliersHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تقرير أرصدة الموردين
        function createSupplierBalancesReport(dateRange, filters = {}) {
            const suppliersData = window.dataManager ? window.dataManager.getSuppliers() : [];
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];

            if (suppliersData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>أرصدة الموردين</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد موردين مسجلين في النظام</p>
                        </div>
                    </div>
                `;
            }

            // حساب أرصدة الموردين من فواتير الشراء
            const supplierBalances = {};

            suppliersData.forEach(supplier => {
                supplierBalances[supplier.name] = {
                    name: supplier.name,
                    totalPurchases: 0,
                    totalAmount: 0,
                    invoicesCount: 0
                };
            });

            purchasesData.forEach(purchase => {
                const supplierName = purchase.supplier;
                if (supplierBalances[supplierName]) {
                    supplierBalances[supplierName].totalAmount += parseFloat(purchase.total) || 0;
                    supplierBalances[supplierName].invoicesCount++;
                }
            });

            let balancesHtml = '';
            Object.values(supplierBalances).forEach(supplier => {
                balancesHtml += `
                    <tr>
                        <td>${supplier.name}</td>
                        <td>${supplier.invoicesCount}</td>
                        <td>${supplier.totalAmount.toFixed(2)} ر.س</td>
                        <td>${supplier.invoicesCount > 0 ? (supplier.totalAmount / supplier.invoicesCount).toFixed(2) : '0.00'} ر.س</td>
                        <td><span class="status-paid">مدفوع</span></td>
                    </tr>
                `;
            });

            const totalAmount = Object.values(supplierBalances).reduce((sum, supplier) => sum + supplier.totalAmount, 0);

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>أرصدة الموردين</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>إجمالي المستحقات</h3>
                            <p class="summary-value">${totalAmount.toFixed(2)} ر.س</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>المورد</th>
                                    <th>عدد الفواتير</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط الفاتورة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${balancesHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تحليل الموردين
        function createSupplierAnalysisReport(dateRange, filters = {}) {
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];

            if (purchasesData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>تحليل الموردين</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد فواتير شراء للتحليل</p>
                        </div>
                    </div>
                `;
            }

            // تحليل أداء الموردين
            const supplierAnalysis = {};

            purchasesData.forEach(purchase => {
                const supplier = purchase.supplier || 'غير محدد';

                if (!supplierAnalysis[supplier]) {
                    supplierAnalysis[supplier] = {
                        name: supplier,
                        invoices: 0,
                        totalAmount: 0,
                        totalItems: 0,
                        avgDeliveryTime: 0,
                        performance: 'ممتاز'
                    };
                }

                supplierAnalysis[supplier].invoices++;
                supplierAnalysis[supplier].totalAmount += parseFloat(purchase.total) || 0;
                supplierAnalysis[supplier].totalItems += purchase.items ? purchase.items.length : 0;
            });

            let analysisHtml = '';
            Object.values(supplierAnalysis).forEach(supplier => {
                const avgInvoice = supplier.invoices > 0 ? (supplier.totalAmount / supplier.invoices) : 0;
                const performance = avgInvoice > 5000 ? 'ممتاز' : avgInvoice > 2000 ? 'جيد' : 'متوسط';

                analysisHtml += `
                    <tr>
                        <td>${supplier.name}</td>
                        <td>${supplier.invoices}</td>
                        <td>${supplier.totalAmount.toFixed(2)} ر.س</td>
                        <td>${avgInvoice.toFixed(2)} ر.س</td>
                        <td>${supplier.totalItems}</td>
                        <td><span class="performance-${performance.toLowerCase()}">${performance}</span></td>
                    </tr>
                `;
            });

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>تحليل الموردين</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>المورد</th>
                                    <th>عدد الفواتير</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط الفاتورة</th>
                                    <th>إجمالي الأصناف</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${analysisHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تقييم الموردين
        function createSupplierEvaluationReport(dateRange, filters = {}) {
            return createSupplierAnalysisReport(dateRange, filters);
        }

        // دالة تقرير المشتريات الشهرية
        function createPurchasesMonthlyReport(dateRange, filters = {}) {
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];

            if (purchasesData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>المشتريات الشهرية</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد فواتير شراء في هذه الفترة</p>
                        </div>
                    </div>
                `;
            }

            // تجميع البيانات حسب الشهر
            const monthlyData = {};

            purchasesData.forEach(purchase => {
                const date = new Date(purchase.createdAt || purchase.date);
                const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                const monthName = date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });

                if (!monthlyData[monthKey]) {
                    monthlyData[monthKey] = {
                        month: monthName,
                        invoices: 0,
                        totalAmount: 0,
                        suppliers: new Set()
                    };
                }

                monthlyData[monthKey].invoices++;
                monthlyData[monthKey].totalAmount += parseFloat(purchase.total) || 0;
                monthlyData[monthKey].suppliers.add(purchase.supplier);
            });

            let monthlyHtml = '';
            Object.values(monthlyData).forEach(month => {
                monthlyHtml += `
                    <tr>
                        <td>${month.month}</td>
                        <td>${month.invoices}</td>
                        <td>${month.suppliers.size}</td>
                        <td>${month.totalAmount.toFixed(2)} ر.س</td>
                        <td>${month.invoices > 0 ? (month.totalAmount / month.invoices).toFixed(2) : '0.00'} ر.س</td>
                    </tr>
                `;
            });

            const totalAmount = Object.values(monthlyData).reduce((sum, month) => sum + month.totalAmount, 0);
            const totalInvoices = Object.values(monthlyData).reduce((sum, month) => sum + month.invoices, 0);

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>المشتريات الشهرية</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>إجمالي الفواتير</h3>
                            <p class="summary-value">${totalInvoices}</p>
                        </div>
                        <div class="summary-card">
                            <h3>إجمالي المبلغ</h3>
                            <p class="summary-value">${totalAmount.toFixed(2)} ر.س</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>الشهر</th>
                                    <th>عدد الفواتير</th>
                                    <th>عدد الموردين</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط الفاتورة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${monthlyHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة تقرير المشتريات حسب الفئة
        function createPurchasesByCategoryReport(dateRange, filters = {}) {
            const purchasesData = window.dataManager ? window.dataManager.getPurchases() : [];
            const suppliersData = window.dataManager ? window.dataManager.getSuppliers() : [];

            if (purchasesData.length === 0) {
                return `
                    <div class="report-container">
                        <div class="report-header">
                            <h2>المشتريات حسب الفئة</h2>
                            <p>الفترة: ${dateRange}</p>
                        </div>
                        <div class="no-data-message">
                            <i class="fas fa-info-circle"></i>
                            <p>لا توجد فواتير شراء في هذه الفترة</p>
                        </div>
                    </div>
                `;
            }

            // إنشاء خريطة الموردين وفئاتهم
            const supplierCategories = {};
            suppliersData.forEach(supplier => {
                supplierCategories[supplier.name] = supplier.category || 'عام';
            });

            // تجميع البيانات حسب الفئة
            const categoryData = {};

            purchasesData.forEach(purchase => {
                const category = supplierCategories[purchase.supplier] || 'عام';

                if (!categoryData[category]) {
                    categoryData[category] = {
                        name: category,
                        invoices: 0,
                        totalAmount: 0,
                        suppliers: new Set(),
                        items: 0
                    };
                }

                categoryData[category].invoices++;
                categoryData[category].totalAmount += parseFloat(purchase.total) || 0;
                categoryData[category].suppliers.add(purchase.supplier);
                categoryData[category].items += purchase.items ? purchase.items.length : 0;
            });

            let categoryHtml = '';
            Object.values(categoryData).forEach(category => {
                categoryHtml += `
                    <tr>
                        <td>${category.name}</td>
                        <td>${category.invoices}</td>
                        <td>${category.suppliers.size}</td>
                        <td>${category.items}</td>
                        <td>${category.totalAmount.toFixed(2)} ر.س</td>
                        <td>${category.invoices > 0 ? (category.totalAmount / category.invoices).toFixed(2) : '0.00'} ر.س</td>
                    </tr>
                `;
            });

            const totalAmount = Object.values(categoryData).reduce((sum, category) => sum + category.totalAmount, 0);
            const totalCategories = Object.keys(categoryData).length;

            return `
                <div class="report-container">
                    <div class="report-header">
                        <h2>المشتريات حسب الفئة</h2>
                        <p>الفترة: ${dateRange}</p>
                    </div>

                    <div class="report-summary">
                        <div class="summary-card">
                            <h3>عدد الفئات</h3>
                            <p class="summary-value">${totalCategories}</p>
                        </div>
                        <div class="summary-card">
                            <h3>إجمالي المشتريات</h3>
                            <p class="summary-value">${totalAmount.toFixed(2)} ر.س</p>
                        </div>
                    </div>

                    <div class="report-table-container">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>عدد الفواتير</th>
                                    <th>عدد الموردين</th>
                                    <th>عدد الأصناف</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط الفاتورة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${categoryHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
        // دالة اختبار شاملة للتقارير
        function testReportsSystem() {
            console.log('🧪 بدء اختبار نظام التقارير الشامل...');

            // اختبار توفر النظام المركزي
            if (!window.dataManager) {
                console.error('❌ النظام المركزي غير متاح');
                alert('خطأ: النظام المركزي غير متاح!\nتأكد من تحميل ملف data-manager.js');
                return;
            }

            console.log('✅ النظام المركزي متاح');

            // اختبار البيانات
            const customers = window.dataManager.getCustomers();
            const suppliers = window.dataManager.getSuppliers();
            const products = window.dataManager.getProducts();
            const sales = window.dataManager.getSales();
            const purchases = window.dataManager.getPurchases();

            console.log('📊 إحصائيات البيانات:');
            console.log(`   العملاء: ${customers.length}`);
            console.log(`   الموردين: ${suppliers.length}`);
            console.log(`   المنتجات: ${products.length}`);
            console.log(`   المبيعات: ${sales.length}`);
            console.log(`   المشتريات: ${purchases.length}`);

            // اختبار البيانات من localStorage
            const localCustomers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const localSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            const localAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            console.log('💾 البيانات المحفوظة محلياً:');
            console.log(`   العملاء: ${localCustomers.length}`);
            console.log(`   الموردين: ${localSuppliers.length}`);
            console.log(`   الحسابات: ${localAccounts.length}`);

            // اختبار التقارير
            const reportTypes = [
                'balance-sheet',
                'profit-loss',
                'daily',
                'monthly',
                'by-customer',
                'by-product',
                'current-stock',
                'customer-list',
                'supplier-list'
            ];

            console.log('📋 اختبار التقارير:');
            reportTypes.forEach(reportType => {
                try {
                    const reportData = getDetailedReportData(reportType);
                    console.log(`   ✅ ${reportType}: ${reportData.title}`);
                } catch (error) {
                    console.error(`   ❌ ${reportType}: خطأ - ${error.message}`);
                }
            });

            // اختبار المرشحات
            console.log('🔍 اختبار المرشحات:');
            if (window.reportFilters) {
                Object.keys(window.reportFilters).forEach(filterType => {
                    const filterOptions = window.reportFilters[filterType];
                    console.log(`   ${filterType}: ${filterOptions ? filterOptions.length : 0} خيار`);
                });
            } else {
                console.log('   ⚠️ المرشحات غير محملة');
            }

            // تقرير النتائج
            const report = `
🧪 تقرير اختبار نظام التقارير:

📊 البيانات المتاحة:
- العملاء: ${customers.length} (محلي: ${localCustomers.length})
- الموردين: ${suppliers.length} (محلي: ${localSuppliers.length})
- المنتجات: ${products.length}
- المبيعات: ${sales.length}
- المشتريات: ${purchases.length}
- الحسابات: ${localAccounts.length}

📋 التقارير: ${reportTypes.length} تقرير متاح

🔄 النظام المركزي: ${window.dataManager ? 'متاح' : 'غير متاح'}
🔍 المرشحات: ${window.reportFilters ? 'محملة' : 'غير محملة'}

✅ النظام جاهز للاستخدام!
            `;

            console.log(report);
            alert(report);
        }

        // دالة إضافة فاتورة تجريبية للاختبار
        function addTestInvoice() {
            if (!window.dataManager) {
                alert('النظام المركزي غير متاح!');
                return;
            }

            const testInvoice = {
                id: 'INV-' + Date.now(),
                date: new Date().toLocaleDateString('ar-SA'),
                customerName: 'نور',
                customerId: 1,
                products: [
                    {
                        productId: 1,
                        name: 'منتج تجريبي',
                        quantity: 2,
                        price: 100,
                        total: 200
                    }
                ],
                total: 200,
                paymentMethod: 'cash',
                status: 'paid',
                createdAt: new Date().toISOString()
            };

            // إضافة الفاتورة
            const savedInvoice = window.dataManager.addSale(testInvoice);

            if (savedInvoice) {
                // إرسال إشعار
                if (window.BroadcastChannel) {
                    const channel = new BroadcastChannel('monjiz-updates');
                    channel.postMessage({
                        type: 'sales-updated',
                        data: savedInvoice
                    });
                }

                // تحديث البيانات في التقارير
                updateSalesDataFromStorage();

                alert(`تم إضافة فاتورة تجريبية بنجاح!\nرقم الفاتورة: ${savedInvoice.id}\nالعميل: ${savedInvoice.customerName}\nالمبلغ: ${savedInvoice.total} ر.س`);

                console.log('✅ تم إضافة فاتورة تجريبية:', savedInvoice);
            } else {
                alert('فشل في إضافة الفاتورة التجريبية!');
            }
        }

        // دالة إضافة حركات مخزون تجريبية
        function addTestStockMovements() {
            if (!window.dataManager) {
                alert('النظام المركزي غير متاح!');
                return;
            }

            // إضافة منتجات تجريبية أولاً
            const testProducts = [
                {
                    id: 1,
                    name: 'لابتوب ديل',
                    code: 'LAPTOP001',
                    price: 3500,
                    quantity: 10,
                    category: 'إلكترونيات'
                },
                {
                    id: 2,
                    name: 'هاتف آيفون',
                    code: 'PHONE001',
                    price: 4200,
                    quantity: 5,
                    category: 'إلكترونيات'
                }
            ];

            // حفظ المنتجات
            const existingProducts = window.dataManager.getProducts();
            testProducts.forEach(product => {
                const exists = existingProducts.find(p => p.id === product.id);
                if (!exists) {
                    window.dataManager.addProduct(product);
                }
            });

            // إضافة حركات مخزون تجريبية
            const testMovements = [
                {
                    productId: 1,
                    productName: 'لابتوب ديل',
                    productCode: 'LAPTOP001',
                    movementType: 'in',
                    quantity: 20,
                    oldQuantity: 0,
                    newQuantity: 20,
                    reference: 'فاتورة مشتريات PUR-001',
                    referenceType: 'purchase',
                    date: new Date().toLocaleDateString('ar-SA'),
                    time: new Date().toLocaleTimeString('ar-SA')
                },
                {
                    productId: 1,
                    productName: 'لابتوب ديل',
                    productCode: 'LAPTOP001',
                    movementType: 'out',
                    quantity: 3,
                    oldQuantity: 20,
                    newQuantity: 17,
                    reference: 'فاتورة مبيعات INV-001',
                    referenceType: 'sale',
                    date: new Date().toLocaleDateString('ar-SA'),
                    time: new Date().toLocaleTimeString('ar-SA')
                },
                {
                    productId: 2,
                    productName: 'هاتف آيفون',
                    productCode: 'PHONE001',
                    movementType: 'in',
                    quantity: 15,
                    oldQuantity: 0,
                    newQuantity: 15,
                    reference: 'فاتورة مشتريات PUR-002',
                    referenceType: 'purchase',
                    date: new Date().toLocaleDateString('ar-SA'),
                    time: new Date().toLocaleTimeString('ar-SA')
                }
            ];

            let addedCount = 0;
            testMovements.forEach(movement => {
                const added = window.dataManager.addStockMovement(movement);
                if (added) addedCount++;
            });

            // تحديث البيانات في التقارير
            updateProductFiltersFromStorage();

            alert(`تم إضافة حركات مخزون تجريبية!\n\nعدد الحركات المضافة: ${addedCount}\nالمنتجات: ${testProducts.length}\n\nيمكنك الآن مراجعة تقرير حركة المخزون.`);

            console.log('✅ تم إضافة حركات مخزون تجريبية:', addedCount, 'حركة');
        }

        // إضافة الدوال للنافذة العامة
        window.testReportsSystem = testReportsSystem;
        window.addTestInvoice = addTestInvoice;
        window.addTestStockMovements = addTestStockMovements;

        // رسالة في وحدة التحكم
        console.log('💡 أوامر الاختبار المتاحة:');
        console.log('   testReportsSystem() - اختبار نظام التقارير');
        console.log('   addTestInvoice() - إضافة فاتورة تجريبية');
        console.log('   addTestStockMovements() - إضافة حركات مخزون تجريبية');
    </script>
    <script src="js/data-manager.js"></script>
</body>
</html>
