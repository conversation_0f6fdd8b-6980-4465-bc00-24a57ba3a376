<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل المورد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #e74c3c;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(231,76,60,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231,76,60,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #e74c3c;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .problem-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .solution-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار حل مشكلة تسجيل المورد</h1>

        <!-- وصف المشكلة -->
        <div class="test-section">
            <h2>❌ المشكلة المبلغ عنها</h2>
            <div class="problem-box">
                <h3>المشكلة:</h3>
                <p><strong>"عند تسجيل مورد جديد وإكمال الاختيارات يقول لك يرجى اختيار عنصر من القائمة"</strong></p>
                
                <h4>السبب المحتمل:</h4>
                <ul>
                    <li>HTML5 validation على حقل select مع required attribute</li>
                    <li>الخيار الأول في القائمة له قيمة فارغة</li>
                    <li>المتصفح يرفض إرسال النموذج لأن القيمة المختارة فارغة</li>
                    <li>رسالة الخطأ تأتي من المتصفح وليس من JavaScript</li>
                </ul>
            </div>
        </div>

        <!-- الحل المطبق -->
        <div class="test-section">
            <h2>✅ الحل المطبق</h2>
            <div class="solution-box">
                <h3>التحسينات المطبقة:</h3>
                <ol>
                    <li><strong>إزالة HTML5 validation:</strong> حذف required attribute من حقول select و input</li>
                    <li><strong>إضافة JavaScript validation مخصص:</strong> تحقق أكثر دقة ووضوحاً</li>
                    <li><strong>تحسين رسائل الخطأ:</strong> رسائل واضحة باللغة العربية</li>
                    <li><strong>إضافة تمييز بصري:</strong> تلوين الحقول الخاطئة باللون الأحمر</li>
                    <li><strong>إضافة تشخيص مفصل:</strong> console.log لتتبع المشكلة</li>
                    <li><strong>التحقق من صحة البيانات:</strong> رقم الهاتف والبريد الإلكتروني</li>
                    <li><strong>التركيز التلقائي:</strong> على الحقل الخاطئ</li>
                </ol>
            </div>
        </div>

        <!-- اختبار الحل -->
        <div class="test-section">
            <h2>🧪 اختبار الحل</h2>
            <button class="btn success" onclick="openPurchasesForTest()">فتح صفحة المشتريات للاختبار</button>
            <button class="btn primary" onclick="simulateValidation()">محاكاة التحقق من البيانات</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار الشامل:</h3>
                <ol>
                    <li><strong>اختبار الحقول الفارغة:</strong>
                        <ul>
                            <li>افتح نافذة "مورد جديد"</li>
                            <li>اضغط "حفظ المورد" مباشرة (بدون ملء أي حقول)</li>
                            <li>✅ يجب أن تظهر رسالة "يرجى اختيار نوع المورد من القائمة المنسدلة"</li>
                            <li>✅ يجب أن يتم تمييز حقل نوع المورد باللون الأحمر</li>
                        </ul>
                    </li>
                    <li><strong>اختبار نوع المورد فقط:</strong>
                        <ul>
                            <li>اختر "فرد" أو "شركة"</li>
                            <li>اضغط "حفظ المورد"</li>
                            <li>✅ يجب أن تظهر رسالة "يرجى إدخال اسم المورد"</li>
                            <li>✅ يجب أن يتم تمييز حقل اسم المورد باللون الأحمر</li>
                        </ul>
                    </li>
                    <li><strong>اختبار نوع المورد + الاسم:</strong>
                        <ul>
                            <li>أدخل اسم المورد</li>
                            <li>اضغط "حفظ المورد"</li>
                            <li>✅ يجب أن تظهر رسالة "يرجى إدخال رقم الهاتف"</li>
                            <li>✅ يجب أن يتم تمييز حقل رقم الهاتف باللون الأحمر</li>
                        </ul>
                    </li>
                    <li><strong>اختبار رقم هاتف خاطئ:</strong>
                        <ul>
                            <li>أدخل رقم هاتف خاطئ (مثل: 123)</li>
                            <li>اضغط "حفظ المورد"</li>
                            <li>✅ يجب أن تظهر رسالة "يرجى إدخال رقم هاتف صحيح"</li>
                        </ul>
                    </li>
                    <li><strong>اختبار بريد إلكتروني خاطئ:</strong>
                        <ul>
                            <li>أدخل بريد إلكتروني خاطئ (مثل: test)</li>
                            <li>اضغط "حفظ المورد"</li>
                            <li>✅ يجب أن تظهر رسالة "يرجى إدخال بريد إلكتروني صحيح"</li>
                        </ul>
                    </li>
                    <li><strong>اختبار البيانات الصحيحة:</strong>
                        <ul>
                            <li>املأ جميع الحقول المطلوبة بشكل صحيح</li>
                            <li>اضغط "حفظ المورد"</li>
                            <li>✅ يجب أن تظهر رسالة النجاح</li>
                            <li>✅ يجب أن تُغلق النافذة</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="highlight">
                <h3>بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li>✅ <strong>لا تظهر رسالة "يرجى اختيار عنصر من القائمة"</strong> من المتصفح</li>
                    <li>✅ <strong>تظهر رسائل خطأ واضحة باللغة العربية</strong> من JavaScript</li>
                    <li>✅ <strong>يتم تمييز الحقول الخاطئة</strong> باللون الأحمر</li>
                    <li>✅ <strong>يتم التركيز على الحقل الخاطئ</strong> تلقائياً</li>
                    <li>✅ <strong>يتم التحقق من صحة رقم الهاتف</strong> والبريد الإلكتروني</li>
                    <li>✅ <strong>يعمل الحفظ بشكل طبيعي</strong> عند إدخال بيانات صحيحة</li>
                    <li>✅ <strong>تظهر رسالة النجاح</strong> مع تفاصيل المورد</li>
                </ul>
            </div>
        </div>

        <!-- تشخيص إضافي -->
        <div class="test-section">
            <h2>🔍 تشخيص إضافي</h2>
            <button class="btn primary" onclick="openConsoleInstructions()">تعليمات فتح Console</button>
            <div id="console-instructions" style="display: none;">
                <div class="info">
                    <h4>لمراقبة التشخيص المفصل:</h4>
                    <ol>
                        <li>اضغط F12 لفتح Developer Tools</li>
                        <li>اذهب لتبويب Console</li>
                        <li>افتح نافذة "مورد جديد"</li>
                        <li>جرب ملء الحقول والحفظ</li>
                        <li>راقب الرسائل في Console</li>
                    </ol>
                    <p><strong>ستجد رسائل مثل:</strong></p>
                    <ul>
                        <li>"=== بدء معالجة إرسال نموذج المورد ==="</li>
                        <li>"عناصر النموذج: ..."</li>
                        <li>"التحقق من صحة البيانات: ..."</li>
                        <li>"✅ تم التحقق من صحة جميع البيانات"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // فتح صفحة المشتريات للاختبار
        function openPurchasesForTest() {
            window.open('purchases.html', '_blank');
            showResult('test-result', '🚀 تم فتح صفحة المشتريات<br>💡 ابحث عن زر "مورد جديد" واختبر النافذة<br>🔍 جرب جميع سيناريوهات الاختبار المذكورة أعلاه', 'info');
        }

        // محاكاة التحقق من البيانات
        function simulateValidation() {
            let result = '<div class="info">🧪 محاكاة التحقق من البيانات:</div>';
            
            // محاكاة حالات مختلفة
            const testCases = [
                { type: '', name: '', phone: '', expected: 'خطأ: نوع المورد مطلوب' },
                { type: 'company', name: '', phone: '', expected: 'خطأ: اسم المورد مطلوب' },
                { type: 'company', name: 'شركة التجارة', phone: '', expected: 'خطأ: رقم الهاتف مطلوب' },
                { type: 'company', name: 'شركة التجارة', phone: '123', expected: 'خطأ: رقم الهاتف غير صحيح' },
                { type: 'company', name: 'شركة التجارة', phone: '0501234567', expected: '✅ البيانات صحيحة' }
            ];

            result += '<div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">';
            testCases.forEach((testCase, index) => {
                result += `
                    <div style="border-bottom: 1px solid #eee; padding: 10px 0; ${index === testCases.length - 1 ? 'border-bottom: none;' : ''}">
                        <strong>اختبار ${index + 1}:</strong><br>
                        نوع المورد: "${testCase.type || 'فارغ'}"<br>
                        اسم المورد: "${testCase.name || 'فارغ'}"<br>
                        رقم الهاتف: "${testCase.phone || 'فارغ'}"<br>
                        <span style="color: ${testCase.expected.includes('✅') ? '#28a745' : '#dc3545'};">
                            النتيجة المتوقعة: ${testCase.expected}
                        </span>
                    </div>
                `;
            });
            result += '</div>';
            
            document.getElementById('test-result').innerHTML = result;
        }

        // فتح تعليمات Console
        function openConsoleInstructions() {
            const instructions = document.getElementById('console-instructions');
            if (instructions.style.display === 'none') {
                instructions.style.display = 'block';
            } else {
                instructions.style.display = 'none';
            }
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            simulateValidation();
        });
    </script>
</body>
</html>
