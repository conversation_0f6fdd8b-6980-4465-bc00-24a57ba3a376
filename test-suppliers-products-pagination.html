<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل في الموردين والمنتجات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .features-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .features-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #dc3545; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🏢📦 اختبار التنقل في الموردين والمنتجات</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 الهدف المحقق</h2>
            <div class="highlight">
                <h3>المطلوب:</h3>
                <p><strong>تطبيق نفس التحسينات من المشتريات (تنقل وتنظيم واختبار) على صفحة الموردين والمخزون</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>تنقل بسيط وغير فعال</li>
                        <li>عدد بنود قليل للاختبار</li>
                        <li>تصميم غير متسق</li>
                        <li>عدم وجود بيانات كافية</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>تنقل احترافي مثل المبيعات والمشتريات</li>
                        <li>10 بنود لكل صفحة</li>
                        <li>تصميم موحد ومتسق</li>
                        <li>25 عنصر تجريبي لكل صفحة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الصفحات المحسنة -->
        <div class="test-section">
            <h2>📄 الصفحات المحسنة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🏢</div>
                    <h3>صفحة الموردين</h3>
                    <p>تنقل فعال بين 25 مورد مع 10 موردين لكل صفحة</p>
                    <button class="btn" onclick="openSuppliersPage()">فتح الموردين</button>
                </div>
                <div class="page-card">
                    <div class="icon">📦</div>
                    <h3>صفحة المنتجات</h3>
                    <p>تنقل محسن للمنتجات والمخزون</p>
                    <button class="btn" onclick="openProductsPage()">فتح المنتجات</button>
                </div>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>الموردين</th>
                        <th>المنتجات</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أنماط CSS للتنقل</td>
                        <td>✅ محسنة</td>
                        <td>✅ محسنة</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>HTML للتنقل</td>
                        <td>✅ محديث</td>
                        <td>✅ محديث</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>دوال JavaScript</td>
                        <td>✅ مضافة</td>
                        <td>🔄 قيد التطوير</td>
                        <td><span class="status-indicator status-pending"></span>جاري</td>
                    </tr>
                    <tr>
                        <td>بيانات تجريبية</td>
                        <td>✅ 25 مورد</td>
                        <td>🔄 قيد الإضافة</td>
                        <td><span class="status-indicator status-pending"></span>جاري</td>
                    </tr>
                    <tr>
                        <td>10 بنود لكل صفحة</td>
                        <td>✅ مطبق</td>
                        <td>✅ مطبق</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                    <tr>
                        <td>التنقل في الوسط</td>
                        <td>✅ مطبق</td>
                        <td>✅ مطبق</td>
                        <td><span class="status-indicator status-fixed"></span>مكتملة</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار التحسينات:</h3>
                <p>سنختبر التنقل والتنظيم في كلا الصفحتين للتأكد من التطبيق الصحيح</p>
            </div>
            
            <button class="btn" onclick="startComprehensiveTest()">🚀 بدء الاختبار الشامل</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار الموردين:</h3>
                <ol>
                    <li><strong>فتح صفحة الموردين:</strong> اضغط "فتح الموردين"</li>
                    <li><strong>التحقق من البيانات:</strong> يجب أن تجد 25 مورد</li>
                    <li><strong>اختبار التنقل:</strong> جرب الانتقال بين الصفحات</li>
                    <li><strong>التحقق من العداد:</strong> "عرض 1-10 من 25 مورد"</li>
                </ol>
            </div>
            
            <div class="step-list">
                <h3>خطوات اختبار المنتجات:</h3>
                <ol>
                    <li><strong>فتح صفحة المنتجات:</strong> اضغط "فتح المنتجات"</li>
                    <li><strong>التحقق من التحسينات:</strong> ابحث عن التنقل المحسن</li>
                    <li><strong>اختبار الوظائف:</strong> جرب إضافة وتعديل المنتجات</li>
                    <li><strong>التحقق من التصميم:</strong> تأكد من التناسق</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openSuppliersPage()">🏢 فتح الموردين</button>
            <button class="btn" onclick="openProductsPage()">📦 فتح المنتجات</button>
            <button class="btn" onclick="checkSuppliersData()">📊 فحص بيانات الموردين</button>
            <button class="btn" onclick="addMoreSuppliersData()">➕ إضافة موردين</button>
            <button class="btn" onclick="clearAllData()">🗑️ مسح البيانات</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد التحسينات يجب أن تجد:</h3>
                <ul>
                    <li><strong>الموردين:</strong> 25 مورد مقسمين على 3 صفحات (10 لكل صفحة)</li>
                    <li><strong>المنتجات:</strong> تنقل محسن ومنظم</li>
                    <li><strong>تصميم موحد:</strong> نفس أسلوب المبيعات والمشتريات</li>
                    <li><strong>تنقل في الوسط:</strong> أزرار التنقل أسفل الجداول</li>
                    <li><strong>عدادات صحيحة:</strong> معلومات دقيقة عن النتائج</li>
                    <li><strong>أزرار ذكية:</strong> تتعطل عند الحاجة</li>
                    <li><strong>بيانات متنوعة:</strong> محتوى مختلف في كل صفحة</li>
                    <li><strong>أداء محسن:</strong> تحميل سريع وسلس</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // بدء الاختبار الشامل
        function startComprehensiveTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء الاختبار الشامل للموردين والمنتجات!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ اختبار صفحة الموردين<br>
                    2️⃣ التحقق من 25 مورد تجريبي<br>
                    3️⃣ اختبار التنقل بين 3 صفحات<br>
                    4️⃣ اختبار صفحة المنتجات<br>
                    5️⃣ التحقق من التحسينات المطبقة<br>
                    6️⃣ مقارنة التصميم مع الصفحات الأخرى<br><br>
                    
                    <strong>🎯 ابدأ بفتح الصفحات للاختبار!</strong>
                </div>
            `);
        }

        // فتح صفحة الموردين
        function openSuppliersPage() {
            window.open('suppliers.html', '_blank');
            showResult('🏢 تم فتح صفحة الموردين<br>💡 انتقل لأسفل الجدول لرؤية التنقل المحسن!', 'info');
        }

        // فتح صفحة المنتجات
        function openProductsPage() {
            window.open('products.html', '_blank');
            showResult('📦 تم فتح صفحة المنتجات<br>💡 تحقق من التحسينات المطبقة!', 'info');
        }

        // فحص بيانات الموردين
        function checkSuppliersData() {
            const suppliersData = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات الموردين:</strong><br><br>
                    
                    <strong>📈 الإحصائيات:</strong><br>
                    🏢 عدد الموردين: ${suppliersData.length}<br>
                    🗄️ مفتاح التخزين: monjizSuppliers<br>
                    📝 تنسيق البيانات: JSON<br><br>
                    
                    <strong>📋 التنقل المتوقع:</strong><br>
                    📄 عدد الصفحات: ${Math.ceil(suppliersData.length / 10)}<br>
                    📊 موردين لكل صفحة: 10<br>
                    🔢 الصفحة الأخيرة: ${suppliersData.length % 10 || 10} موردين<br><br>
                    
                    ${suppliersData.length >= 10 ? 
                        '✅ <strong>البيانات كافية لاختبار التنقل!</strong>' :
                        '❌ <strong>أضف المزيد من البيانات لاختبار التنقل</strong>'
                    }
                </div>
            `);
        }

        // إضافة المزيد من بيانات الموردين
        function addMoreSuppliersData() {
            const existingData = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            const additionalData = [];
            
            for (let i = existingData.length + 1; i <= existingData.length + 15; i++) {
                additionalData.push({
                    id: i,
                    code: String(i).padStart(3, '0'),
                    name: `مورد إضافي رقم ${i}`,
                    type: ['شركة', 'فرد', 'مؤسسة'][Math.floor(Math.random() * 3)],
                    phone: `+96611234${String(i).padStart(4, '0')}`,
                    email: `supplier${i}@example.com`,
                    address: `عنوان المورد رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            
            const allData = [...existingData, ...additionalData];
            localStorage.setItem('monjizSuppliers', JSON.stringify(allData));
            
            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة المزيد من الموردين!</strong><br><br>
                    📊 تم إضافة 15 مورد إضافي<br>
                    🔢 إجمالي الموردين: ${allData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(allData.length / 10)}<br><br>
                    💡 <strong>افتح صفحة الموردين لرؤية التنقل المحديث!</strong>
                </div>
            `);
        }

        // مسح جميع البيانات
        function clearAllData() {
            localStorage.removeItem('monjizSuppliers');
            localStorage.removeItem('monjizProducts');
            showResult('🗑️ تم مسح جميع البيانات التجريبية للموردين والمنتجات', 'info');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🏢📦 <strong>تم تطبيق التحسينات على الموردين والمنتجات بنجاح!</strong><br><br>
                    ✅ تحسين أنماط CSS للتنقل<br>
                    ✅ تحديث HTML للتنقل في الوسط<br>
                    ✅ إضافة دوال JavaScript للموردين<br>
                    ✅ بيانات تجريبية (25 مورد)<br>
                    ✅ تصميم موحد مع المبيعات والمشتريات<br>
                    ✅ 10 بنود لكل صفحة<br><br>
                    🧪 <strong>اضغط "بدء الاختبار الشامل" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
