<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حلول إدارة الحسابات - Monjiz</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 18px;
        }
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .solution-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .solution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #3498db;
            color: white;
            padding: 15px 20px;
        }
        .card-header h2 {
            margin: 0;
            font-size: 20px;
        }
        .card-body {
            padding: 20px;
        }
        .card-body p {
            color: #34495e;
            margin-bottom: 20px;
        }
        .card-footer {
            background-color: #f9f9f9;
            padding: 15px 20px;
            border-top: 1px solid #ecf0f1;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .note {
            background-color: #f8f9fa;
            border-right: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
        }
        .direct-links {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .direct-links h2 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        .link-btn {
            display: block;
            text-align: center;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
            text-decoration: none;
            color: #34495e;
            font-weight: bold;
            transition: background-color 0.3s, transform 0.3s;
        }
        .link-btn:hover {
            background-color: #e8e8e8;
            transform: translateY(-2px);
        }
        .link-btn i {
            display: block;
            font-size: 24px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>حلول إدارة الحسابات - Monjiz</h1>
            <p>اختر الحل المناسب لإصلاح مشكلة وظائف إدارة الحسابات</p>
        </div>
        
        <div class="direct-links">
            <h2>روابط مباشرة</h2>
            <div class="links-grid">
                <a href="accounting.html" class="link-btn">صفحة الحسابات</a>
                <a href="launcher.html" class="link-btn">الصفحة الرئيسية</a>
                <a href="accounting-fix-guide.html" class="link-btn">دليل الإصلاح الشامل</a>
                <a href="accounting-redirect.html" class="link-btn">صفحة التوجيه التلقائي</a>
                <a href="README-accounting-fix.md" class="link-btn">ملف الإرشادات</a>
            </div>
        </div>
        
        <div class="solutions-grid">
            <div class="solution-card">
                <div class="card-header">
                    <h2>ملفات التشغيل المباشر</h2>
                </div>
                <div class="card-body">
                    <p>استخدم أحد ملفات التشغيل المباشر لفتح صفحة الحسابات بشكل صحيح.</p>
                    <ul>
                        <li>ملف VBScript (لجميع إصدارات Windows)</li>
                        <li>ملف Batch (لجميع إصدارات Windows)</li>
                        <li>ملف PowerShell (لـ Windows 7 وما بعده)</li>
                        <li>ملف Python (يتطلب تثبيت Python)</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <a href="open_accounting.vbs" class="btn">VBScript</a>
                    <a href="open_accounting.bat" class="btn">Batch</a>
                    <a href="open_accounting.ps1" class="btn">PowerShell</a>
                    <a href="open_accounting.py" class="btn">Python</a>
                </div>
            </div>
            
            <div class="solution-card">
                <div class="card-header">
                    <h2>اختصار سطح المكتب</h2>
                </div>
                <div class="card-body">
                    <p>قم بإنشاء اختصار لصفحة الحسابات على سطح المكتب للوصول السريع إليها في أي وقت.</p>
                    <p>سيتم إنشاء اختصار باسم "إدارة الحسابات - Monjiz" على سطح المكتب الخاص بك.</p>
                </div>
                <div class="card-footer">
                    <a href="Create_Accounting_Shortcut.vbs" class="btn">إنشاء اختصار</a>
                </div>
            </div>
            
            <div class="solution-card">
                <div class="card-header">
                    <h2>صفحة التوجيه التلقائي</h2>
                </div>
                <div class="card-body">
                    <p>استخدم صفحة التوجيه التلقائي التي ستنقلك إلى صفحة الحسابات بعد 5 ثوانٍ.</p>
                    <p>تحتوي هذه الصفحة أيضًا على روابط للصفحة الرئيسية وملفات الإرشادات.</p>
                </div>
                <div class="card-footer">
                    <a href="accounting-redirect.html" class="btn">فتح صفحة التوجيه</a>
                </div>
            </div>
            
            <div class="solution-card">
                <div class="card-header">
                    <h2>دليل الإصلاح الشامل</h2>
                </div>
                <div class="card-body">
                    <p>استخدم دليل الإصلاح الشامل الذي يحتوي على تعليمات مفصلة لإصلاح مشكلة وظائف إدارة الحسابات.</p>
                    <p>يتضمن الدليل شرحًا للمشكلة والحلول المتاحة وخطوات إضافية في حالة استمرار المشكلة.</p>
                </div>
                <div class="card-footer">
                    <a href="accounting-fix-guide.html" class="btn">فتح دليل الإصلاح</a>
                </div>
            </div>
        </div>
        
        <div class="note">
            <p><strong>ملاحظة هامة:</strong> تأكد من تغيير لغة لوحة المفاتيح إلى الإنجليزية قبل تشغيل أي من ملفات السكريبت لتجنب مشكلة الحرف العربي (ؤ) الذي يظهر قبل الأوامر في موجه الأوامر.</p>
        </div>
        
        <div class="footer">
            <p>تم إنشاء هذه الصفحة لمساعدتك في إصلاح مشكلة وظائف إدارة الحسابات في نظام Monjiz.</p>
            <p>© 2023 Monjiz - نظام إدارة الأعمال</p>
        </div>
    </div>
</body>
</html>