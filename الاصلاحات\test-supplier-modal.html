<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة المورد الجديد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #9b59b6 0%, #3498db 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #9b59b6;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #9b59b6, #3498db);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(155,89,182,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155,89,182,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #9b59b6;
            border-bottom: 3px solid #9b59b6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .comparison-table th {
            background: #9b59b6;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار نافذة المورد الجديد</h1>

        <!-- التحسينات المطبقة -->
        <div class="test-section">
            <h2>✅ التحسينات المطبقة</h2>
            <div class="highlight">
                <h3>التغييرات حسب الطلب:</h3>
                <ul>
                    <li>❌ <strong>تم حذف حقل "اسم الشركة"</strong> - لا قيمة له</li>
                    <li>❌ <strong>تم حذف "فئة المورد"</strong> - غير مطلوب</li>
                    <li>❌ <strong>تم حذف "شروط الدفع"</strong> - غير مطلوب</li>
                    <li>❌ <strong>تم حذف "ملاحظات"</strong> - غير مطلوب</li>
                    <li>✅ <strong>تم إصلاح مشكلة الإغلاق</strong> - إغلاق بضغطة واحدة</li>
                    <li>✅ <strong>تم تبسيط النافذة</strong> - الحقول الأساسية فقط</li>
                </ul>
            </div>
        </div>

        <!-- مقارنة الحقول -->
        <div class="test-section">
            <h2>📊 مقارنة الحقول</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الحقل</th>
                        <th>قبل التحديث</th>
                        <th>بعد التحديث</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>نوع المورد</td>
                        <td>موجود (مطلوب)</td>
                        <td>موجود (مطلوب)</td>
                        <td>✅ محتفظ به</td>
                    </tr>
                    <tr>
                        <td>اسم المورد</td>
                        <td>موجود (مطلوب)</td>
                        <td>موجود (مطلوب)</td>
                        <td>✅ محتفظ به</td>
                    </tr>
                    <tr>
                        <td>رقم الهاتف</td>
                        <td>موجود (مطلوب)</td>
                        <td>موجود (مطلوب)</td>
                        <td>✅ محتفظ به</td>
                    </tr>
                    <tr>
                        <td>البريد الإلكتروني</td>
                        <td>موجود (اختياري)</td>
                        <td>موجود (اختياري)</td>
                        <td>✅ محتفظ به</td>
                    </tr>
                    <tr>
                        <td>اسم الشركة</td>
                        <td>موجود (اختياري)</td>
                        <td>محذوف</td>
                        <td>❌ تم حذفه</td>
                    </tr>
                    <tr>
                        <td>الرقم الضريبي</td>
                        <td>موجود (اختياري)</td>
                        <td>موجود (اختياري)</td>
                        <td>✅ محتفظ به</td>
                    </tr>
                    <tr>
                        <td>العنوان</td>
                        <td>textarea كبير</td>
                        <td>input مبسط</td>
                        <td>🔄 تم تبسيطه</td>
                    </tr>
                    <tr>
                        <td>فئة المورد</td>
                        <td>قائمة منسدلة</td>
                        <td>محذوف</td>
                        <td>❌ تم حذفه</td>
                    </tr>
                    <tr>
                        <td>شروط الدفع</td>
                        <td>قائمة منسدلة</td>
                        <td>محذوف</td>
                        <td>❌ تم حذفه</td>
                    </tr>
                    <tr>
                        <td>ملاحظات</td>
                        <td>textarea</td>
                        <td>محذوف</td>
                        <td>❌ تم حذفه</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- اختبار النافذة -->
        <div class="test-section">
            <h2>🧪 اختبار النافذة</h2>
            <button class="btn success" onclick="openPurchasesPage()">فتح صفحة المشتريات</button>
            <button class="btn primary" onclick="testSupplierData()">اختبار بيانات الموردين</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار:</h3>
                <ol>
                    <li><strong>اضغط "فتح صفحة المشتريات"</strong></li>
                    <li><strong>في صفحة المشتريات:</strong>
                        <ul>
                            <li>ابحث عن زر "مورد جديد" أو "إضافة مورد"</li>
                            <li>اضغط على الزر</li>
                        </ul>
                    </li>
                    <li><strong>في النافذة المنبثقة:</strong>
                        <ul>
                            <li>✅ تحقق من وجود: نوع المورد، اسم المورد، رقم الهاتف، البريد الإلكتروني، الرقم الضريبي، العنوان</li>
                            <li>❌ تحقق من عدم وجود: اسم الشركة، فئة المورد، شروط الدفع، ملاحظات</li>
                            <li>املأ الحقول المطلوبة (نوع المورد، اسم المورد، رقم الهاتف)</li>
                            <li>اضغط "حفظ المورد"</li>
                        </ul>
                    </li>
                    <li><strong>اختبار الإغلاق:</strong>
                        <ul>
                            <li>افتح النافذة مرة أخرى</li>
                            <li>اضغط زر الإغلاق (×) مرة واحدة</li>
                            <li>✅ يجب أن تُغلق النافذة فوراً</li>
                            <li>اضغط "إلغاء" مرة واحدة</li>
                            <li>✅ يجب أن تُغلق النافذة فوراً</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="highlight">
                <h3>بعد التحديث يجب أن:</h3>
                <ul>
                    <li>✅ <strong>النافذة مبسطة:</strong> 6 حقول فقط بدلاً من 10</li>
                    <li>✅ <strong>الحقول الأساسية موجودة:</strong> نوع المورد، اسم المورد، هاتف، إيميل، رقم ضريبي، عنوان</li>
                    <li>✅ <strong>الحقول غير المطلوبة محذوفة:</strong> اسم الشركة، فئة المورد، شروط الدفع، ملاحظات</li>
                    <li>✅ <strong>الإغلاق يعمل بضغطة واحدة:</strong> لا يحتاج ضغطات متعددة</li>
                    <li>✅ <strong>حفظ البيانات يعمل:</strong> المورد يُضاف لقائمة الموردين</li>
                    <li>✅ <strong>رسالة النجاح تظهر:</strong> مع تفاصيل المورد المحفوظ</li>
                </ul>
            </div>
        </div>

        <!-- فحص البيانات -->
        <div class="test-section">
            <h2>📋 فحص البيانات المحفوظة</h2>
            <button class="btn primary" onclick="showSavedSuppliers()">عرض الموردين المحفوظين</button>
            <div id="suppliers-data"></div>
        </div>
    </div>

    <script>
        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('test-result', '🚀 تم فتح صفحة المشتريات<br>💡 ابحث عن زر "مورد جديد" واختبر النافذة<br>🔍 تأكد من أن الحقول المطلوبة موجودة والحقول غير المطلوبة محذوفة', 'info');
        }

        // اختبار بيانات الموردين
        function testSupplierData() {
            // إضافة مورد تجريبي
            const testSupplier = {
                id: Date.now(),
                type: 'company',
                name: 'شركة التجارة المتقدمة',
                phone: '+966501234567',
                email: '<EMAIL>',
                taxNumber: '*********',
                address: 'الرياض، المملكة العربية السعودية',
                company: '', // محذوف
                category: 'general', // قيمة افتراضية
                paymentTerms: 'cash', // قيمة افتراضية
                notes: '', // محذوف
                createdAt: new Date().toISOString()
            };

            // حفظ في localStorage
            let suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            suppliers.push(testSupplier);
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));

            showResult('test-result', '✅ تم إضافة مورد تجريبي بنجاح!<br>📊 اسم المورد: شركة التجارة المتقدمة<br>📞 الهاتف: +966501234567<br>💡 يمكنك الآن فتح صفحة المشتريات والتحقق من وجود المورد في القوائم', 'success');
        }

        // عرض الموردين المحفوظين
        function showSavedSuppliers() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            let html = `<h3>📋 الموردين المحفوظين (${suppliers.length} مورد)</h3>`;
            
            if (suppliers.length > 0) {
                html += '<div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">';
                suppliers.forEach((supplier, index) => {
                    html += `
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0; ${index === suppliers.length - 1 ? 'border-bottom: none;' : ''}">
                            <strong>${supplier.name}</strong> (${supplier.type === 'company' ? 'شركة' : 'فرد'})<br>
                            📞 ${supplier.phone}<br>
                            ${supplier.email ? `📧 ${supplier.email}<br>` : ''}
                            ${supplier.taxNumber ? `🏢 ${supplier.taxNumber}<br>` : ''}
                            ${supplier.address ? `📍 ${supplier.address}<br>` : ''}
                            <small style="color: #666;">تم الإنشاء: ${new Date(supplier.createdAt).toLocaleDateString('ar-SA')}</small>
                        </div>
                    `;
                });
                html += '</div>';
            } else {
                html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; color: #666;">لا يوجد موردين محفوظين</div>';
            }
            
            document.getElementById('suppliers-data').innerHTML = html;
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            showSavedSuppliers();
        });
    </script>
</body>
</html>
