/* إعدادات عامة */
:root {
    --primary-color: #0078ff;
    --secondary-color: #00c3ff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-color: #6c757d;
    --gray-light-color: #e9ecef;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* ========== شريط التنقل الرئيسي الموحد ========== */
.main-header {
    background: linear-gradient(to left, #0078ff, #00c3ff);
    color: white;
    padding: 10px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    height: 70px;
}

.main-header .container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    height: 100%;
}

.main-header .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.main-header .logo i {
    font-size: 28px;
    color: #f39c12;
}

.main-header .logo h1 {
    font-size: 24px;
    margin: 0;
    font-weight: 600;
    color: white;
}

.main-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 0;
}

.main-nav li {
    margin: 0;
}

.main-nav a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    height: 50px;
    box-sizing: border-box;
}

.main-nav a:hover {
    background: rgba(255,255,255,0.1);
    border-bottom-color: #f39c12;
    transform: translateY(-1px);
}

.main-nav a.active {
    background: rgba(255,255,255,0.15);
    border-bottom-color: #f39c12;
    font-weight: 600;
}

.main-nav a i {
    font-size: 16px;
    width: 18px;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo i {
    font-size: 24px;
    margin-left: 10px;
}

.logo h1 {
    font-size: 20px;
    font-weight: 700;
}

.main-nav ul {
    display: flex;
}

.main-nav li {
    margin: 0 10px;
}

.main-nav a {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.main-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.main-nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.main-nav i {
    margin-left: 5px;
}

/* لوحة التحكم */
.dashboard {
    padding: 30px 0;
}

.dashboard-header {
    margin-bottom: 20px;
}

.dashboard-header h2 {
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.dashboard-header h2 i {
    margin-left: 10px;
    color: var(--primary-color);
}

/* البطاقات الإحصائية */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-content h3 {
    color: var(--gray-color);
    font-size: 16px;
    margin-bottom: 10px;
}

.card-value {
    font-size: 28px;
    font-weight: 700;
}

.card-value span {
    font-size: 16px;
    font-weight: normal;
}

.card-icon {
    font-size: 40px;
    opacity: 0.8;
}

.products-card {
    border-top: 4px solid #ffc107;
}

.products-card .card-icon {
    color: #ffc107;
}

.customers-card {
    border-top: 4px solid #17a2b8;
}

.customers-card .card-icon {
    color: #17a2b8;
}

.monthly-sales-card {
    border-top: 4px solid #28a745;
}

.monthly-sales-card .card-icon {
    color: #28a745;
}

.daily-sales-card {
    border-top: 4px solid #dc3545;
}

.daily-sales-card .card-icon {
    color: #dc3545;
}

/* الإجراءات السريعة */
.quick-actions {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.quick-actions h3 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.actions-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: var(--light-color);
    text-align: center;
}

.action-btn i {
    font-size: 24px;
    margin-bottom: 10px;
}

.reports-btn {
    color: #6f42c1;
}

.reports-btn:hover {
    background-color: #6f42c1;
    color: white;
}

.new-product-btn {
    color: #fd7e14;
}

.new-product-btn:hover {
    background-color: #fd7e14;
    color: white;
}

.new-customer-btn {
    color: #20c997;
}

.new-customer-btn:hover {
    background-color: #20c997;
    color: white;
}

.new-invoice-btn {
    color: #e83e8c;
}

.new-invoice-btn:hover {
    background-color: #e83e8c;
    color: white;
}

/* القسم السفلي */
.dashboard-bottom {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.inventory-alerts,
.recent-invoices {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.section-header h3 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.view-all {
    color: var(--primary-color);
    font-size: 14px;
    transition: var(--transition);
}

.view-all:hover {
    text-decoration: underline;
}

.alerts-container {
    margin-bottom: 15px;
}

.alert {
    padding: 15px;
    background-color: #fff3cd;
    border-radius: var(--border-radius);
    color: #856404;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.alert i {
    margin-left: 10px;
    font-size: 18px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-align: center;
}

.btn:hover {
    background-color: var(--secondary-color);
}

.view-products-btn {
    width: 100%;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: var(--gray-color);
}

.empty-state i {
    font-size: 40px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* القدم */
.main-footer {
    background-color: var(--dark-color);
    color: white;
    padding: 15px 0;
    text-align: center;
    margin-top: 30px;
}

/* أنماط الصفحات الفرعية */
.main-content {
    padding: 30px 0;
    min-height: calc(100vh - 200px);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.page-title h2 {
    color: var(--dark-color);
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.page-title h2 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.page-title p {
    color: var(--gray-color);
    font-size: 14px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-btn:hover {
    background-color: var(--secondary-color);
}

.secondary-btn {
    background-color: var(--gray-light-color);
    color: var(--dark-color);
    border: 1px solid #ddd;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.secondary-btn:hover {
    background-color: #ddd;
}

/* أدوات البحث والتصفية */
.search-filters {
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
}

.search-box input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
}

.filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filters select,
.filters input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
    min-width: 120px;
}

/* الجداول */
.table-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.action-btn {
    background: none;
    border: none;
    padding: 5px 8px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
}

.view-btn {
    color: var(--primary-color);
}

.view-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.edit-btn {
    color: var(--warning-color);
}

.edit-btn:hover {
    background-color: var(--warning-color);
    color: white;
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

.print-btn {
    color: var(--gray-color);
}

.print-btn:hover {
    background-color: var(--gray-color);
    color: white;
}

/* حالات الجدول */
.low-stock {
    background-color: #fff3cd !important;
    color: #856404;
}

.positive-balance {
    color: var(--success-color);
    font-weight: 600;
}

.negative-balance {
    color: var(--danger-color);
    font-weight: 600;
}

.zero-balance {
    color: var(--gray-color);
}

/* حالات الفواتير */
.invoice-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* تذييل الجدول */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--light-color);
    border-top: 1px solid #eee;
}

.results-info {
    color: var(--gray-color);
    font-size: 14px;
}

.pagination {
    display: flex;
    gap: 5px;
}

.pagination a {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: var(--dark-color);
    transition: var(--transition);
}

.pagination a:hover,
.pagination a.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* التنبيهات */
.inventory-alerts {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    padding: 20px;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.alert-header h3 {
    color: var(--warning-color);
    display: flex;
    align-items: center;
}

.alert-header h3 i {
    margin-left: 10px;
}

.close-alerts-btn {
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    padding: 5px;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
        height: auto;
        padding: 10px 20px;
    }

    .main-header .logo {
        margin-bottom: 10px;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }

    .main-nav a {
        padding: 12px 15px;
        font-size: 13px;
        height: auto;
    }

    .main-header .logo h1 {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .main-nav ul {
        flex-direction: column;
        width: 100%;
    }

    .main-nav a {
        justify-content: center;
        padding: 15px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
}

    .main-nav li {
        margin-bottom: 10px;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filters {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .dashboard-bottom {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .data-table {
        font-size: 14px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 10px;
    }

    .table-footer {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }

    .actions-container {
        grid-template-columns: 1fr 1fr;
    }

    .card-value {
        font-size: 24px;
    }

    .page-actions {
        flex-direction: column;
        width: 100%;
    }

    .primary-btn,
    .secondary-btn {
        width: 100%;
        justify-content: center;
    }

    .filters {
        flex-direction: column;
    }

    .filters select,
    .filters input[type="date"] {
        width: 100%;
    }
}

/* تنسيق القوائم المنسدلة */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background-color: #6c757d;
    color: white;
    border: none;
    cursor: pointer;
}

.dropdown-toggle:hover {
    background-color: #5a6268;
}

.dropdown-menu {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    border-radius: 4px;
    border: 1px solid #ddd;
    top: 100%;
    right: 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    color: #333;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    transition: background-color 0.3s;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
}

.dropdown-menu a i {
    margin-left: 8px;
    width: 16px;
}

/* تنسيق مجموعة الفلاتر */
.filter-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    height: 32px;
}

/* تنسيق تعليمات الاستيراد */
.import-instructions {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.import-instructions h4 {
    color: #0066cc;
    margin-bottom: 10px;
}

.import-instructions ul {
    margin: 0;
    padding-right: 20px;
}

.import-instructions li {
    margin-bottom: 5px;
    color: #333;
}

/* تحسين تنسيق النوافذ المنبثقة */
.modal-content {
    max-width: 500px;
}

.form-help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    display: block;
}

/* حالات المخزون */
.in-stock {
    color: #28a745;
    font-weight: bold;
}

.low-stock {
    color: #ffc107;
    font-weight: bold;
}

.out-of-stock {
    color: #dc3545;
    font-weight: bold;
}

/* تحسين أزرار الصفحات */
.pagination a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
    color: #007bff;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.pagination a.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination a.active:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* أزرار الخطر */
.danger-btn {
    background-color: #dc3545;
    color: white;
    border: none;
}

.danger-btn:hover {
    background-color: #c82333;
}

/* تنسيقات صفحة المنتجات المحسنة */
.products-management-modern {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 30px;
}

/* رأس القسم المحسن */
.section-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* الأزرار المحسنة */
.btn-modern {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-family: 'Cairo', sans-serif;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* قسم الإحصائيات المحسن */
.stats-section {
    padding: 20px; /* تقليل المسافة الخارجية */
    background: #f8f9fa;
}

.stats-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* تقليل الحد الأدنى للعرض */
    gap: 15px; /* تقليل المسافة بين البطاقات */
}

.stat-card-modern {
    background: white;
    border-radius: 12px;
    padding: 15px; /* تقليل المسافة الداخلية */
    display: flex;
    align-items: center;
    gap: 12px; /* تقليل المسافة بين الأيقونة والمحتوى */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stat-card-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card-modern.stat-primary {
    border-left-color: #667eea;
}

.stat-card-modern.stat-warning {
    border-left-color: #f093fb;
}

.stat-card-modern.stat-success {
    border-left-color: #4facfe;
}

.stat-card-modern.stat-danger {
    border-left-color: #fa709a;
}

.stat-icon-modern {
    width: 45px; /* تقليل حجم الأيقونة */
    height: 45px; /* تقليل حجم الأيقونة */
    border-radius: 10px; /* تقليل نصف قطر الحواف */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px; /* تقليل حجم الخط */
    color: white;
}

.stat-primary .stat-icon-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-warning .stat-icon-modern {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-success .stat-icon-modern {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-danger .stat-icon-modern {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-content-modern {
    flex: 1;
}

.stat-content-modern h4 {
    font-size: 14px; /* تقليل حجم العنوان */
    margin: 0 0 5px 0; /* تقليل المسافة أسفل العنوان */
    color: #4a5568;
}

.stat-value-modern {
    font-size: 18px; /* تقليل حجم القيمة */
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 2px;
}

.stat-change-modern {
    font-size: 11px; /* تقليل حجم نسبة التغيير */
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 3px;
}

.stat-change-modern.positive {
    color: #38a169;
}

.stat-change-modern.negative {
    color: #e53e3e;
}

.stat-number {
    font-size: 22px; /* تقليل حجم الخط */
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 2px; /* تقليل المسافة السفلية */
}

.stat-label {
    font-size: 12px; /* تقليل حجم الخط */
    color: #718096;
    font-weight: 500;
}

/* قسم البحث والتصفية المحسن */
.filters-section {
    padding: 30px;
    background: white;
}

.search-filter-modern {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box-modern {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box-modern i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 16px;
}

.search-box-modern input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.search-box-modern input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-modern {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group-modern {
    display: flex;
    gap: 10px;
    align-items: center;
}

.select-modern {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: #f7fafc;
    color: #2d3748;
    transition: all 0.3s ease;
    min-width: 150px;
}

.select-modern:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* قسم الجدول المحسن */
.table-section {
    padding: 0 30px 30px;
    background: white;
}

.table-container-modern {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.table-modern {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table-modern th {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 16px 12px;
    text-align: right;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
    border-bottom: 2px solid #e2e8f0;
}

.table-modern td {
    padding: 14px 12px;
    text-align: right;
    border-bottom: 1px solid #f1f5f9;
    color: #4a5568;
    font-size: 14px;
}

.table-modern tbody tr {
    transition: all 0.2s ease;
}

.table-modern tbody tr:hover {
    background: #f8f9fa;
}

.table-footer-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e2e8f0;
}

.results-info-modern {
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

.pagination-modern {
    display: flex;
    gap: 8px;
    align-items: center;
}

.pagination-modern button {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
}

.pagination-modern button:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.pagination-modern button.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.pagination-modern button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* النوافذ المنبثقة المحسنة */
.modal-modern {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    justify-content: center;
    align-items: center;
}

.modal-content-modern {
    background-color: white;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header-modern h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal-modern {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-modal-modern:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body-modern {
    padding: 30px;
}

.form-group-modern {
    margin-bottom: 20px;
}

.form-group-modern label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.input-modern,
.textarea-modern {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f7fafc;
    font-family: 'Cairo', sans-serif;
}

.input-modern:focus,
.textarea-modern:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea-modern {
    resize: vertical;
    min-height: 80px;
}

.file-input-modern {
    width: 100%;
    padding: 10px;
    border: 2px dashed #e2e8f0;
    border-radius: 8px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-input-modern:hover {
    border-color: #cbd5e0;
    background: #edf2f7;
}

.form-help-modern {
    font-size: 12px;
    color: #718096;
    margin-top: 5px;
    display: block;
}

.form-actions-modern {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.import-instructions-modern {
    background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
    border: 1px solid #9ae6b4;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.import-instructions-modern h4 {
    color: #2f855a;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.import-instructions-modern ul {
    margin: 0;
    padding-right: 20px;
}

.import-instructions-modern li {
    margin-bottom: 8px;
    color: #2d3748;
    font-size: 14px;
}

/* التجاوب مع الشاشات الصغيرة للتصميم المحسن */
@media (max-width: 768px) {
    .section-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-grid-modern {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .search-filter-modern {
        flex-direction: column;
        gap: 15px;
    }

    .search-box-modern {
        min-width: auto;
        width: 100%;
    }

    .filters-modern {
        width: 100%;
        justify-content: center;
    }

    .filter-group-modern {
        flex-direction: column;
        width: 100%;
    }

    .select-modern {
        width: 100%;
    }

    .table-section {
        padding: 0 15px 20px;
    }

    .table-container-modern {
        overflow-x: auto;
    }

    .table-modern {
        min-width: 800px;
    }

    .table-footer-modern {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px;
    }

    .pagination-modern {
        justify-content: center;
        flex-wrap: wrap;
    }

    .modal-content-modern {
        width: 95%;
        margin: 10% auto;
    }

    .modal-body-modern {
        padding: 20px;
    }

    .form-actions-modern {
        flex-direction: column;
    }

    .btn-modern {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-title h3 {
        font-size: 20px;
    }

    .stat-card-modern {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon-modern {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .table-modern th,
    .table-modern td {
        padding: 10px 8px;
        font-size: 12px;
    }

    .btn-modern {
        padding: 10px 16px;
        font-size: 13px;
    }
}

/* حالات المخزون */
.in-stock {
    color: #28a745;
    font-weight: bold;
}

.low-stock {
    color: #ffc107;
    font-weight: bold;
}

.out-of-stock {
    color: #dc3545;
    font-weight: bold;
}

/* شارات الحالة */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* أزرار صغيرة */
.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
    margin: 0 2px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
}

/* تحسينات إضافية للنماذج والتحقق من الأخطاء */

/* تنسيق حقول الإدخال مع الأخطاء */
.input-modern.error,
.select-modern.error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
    background-color: #fff5f5;
}

.field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    font-weight: 500;
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين تنسيق النوافذ المنبثقة */
.modal-modern {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-content-modern {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسين تنسيق الأزرار */
.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease-in-out;
}

.btn-modern:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-modern:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* تحسين تركيز حقول الإدخال */
.input-modern:focus,
.select-modern:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    background-color: #f8f9ff;
}

/* تحسين عرض التنبيهات */
.alert {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيق جدول الفواتير مثل دليل الحسابات */
.invoices-table-modern {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 20px;
}

.invoices-table-modern .table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #5a67d8;
}

.invoices-table-modern .table-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.invoices-table-modern .table-header h4 i {
    font-size: 20px;
    opacity: 0.9;
}

.invoices-table-modern .table-actions {
    display: flex;
    gap: 10px;
}

.invoices-table-modern .table-container {
    overflow-x: auto;
    max-height: 600px;
}

.invoices-table-modern .modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.invoices-table-modern .modern-table thead th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 8px 6px; /* تقليل padding أكثر */
    text-align: center;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 13px; /* تقليل حجم الخط */
}

.invoices-table-modern .modern-table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
    height: 30px; /* تقليل الارتفاع أكثر ليكون مثل دليل الحسابات */
}

.invoices-table-modern .modern-table tbody tr:hover {
    background-color: #f8f9ff;
    transform: none; /* إزالة التحريك */
    box-shadow: none; /* إزالة الظل */
}

.invoices-table-modern .modern-table tbody td {
    padding: 2px 4px; /* تقليل padding أكثر */
    text-align: center;
    vertical-align: middle;
    border-right: 1px solid #f1f3f4;
    line-height: 1.1; /* تقليل المسافة بين الأسطر أكثر */
    font-size: 12px; /* تقليل حجم الخط أكثر */
}

.invoices-table-modern .modern-table tbody td:last-child {
    border-right: none;
}

/* تنسيق شارات طرق الدفع */
.payment-badge {
    display: inline-block;
    padding: 2px 8px; /* تقليل padding */
    border-radius: 12px; /* تقليل border-radius */
    font-size: 11px; /* تقليل حجم الخط */
    font-weight: 500;
    text-align: center;
    min-width: 50px; /* تقليل العرض الأدنى */
}

.payment-badge.cash {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.payment-badge.transfer {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
}

.payment-badge.credit {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.payment-badge.card {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

/* تنسيق شارات الحالة */
.status-badge {
    display: inline-block;
    padding: 2px 8px; /* تقليل padding */
    border-radius: 12px; /* تقليل border-radius */
    font-size: 11px; /* تقليل حجم الخط */
    font-weight: 500;
    text-align: center;
    min-width: 50px; /* تقليل العرض الأدنى */
}

.status-badge.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.status-badge.cancelled {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

/* تنسيق أزرار الإجراءات */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 3px; /* تقليل المسافة بين الأزرار */
    flex-wrap: wrap;
}

.action-buttons .btn-icon {
    width: 26px; /* تقليل العرض */
    height: 26px; /* تقليل الارتفاع */
    border-radius: 4px; /* تقليل border-radius */
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 10px; /* تقليل حجم الأيقونة */
}

.action-buttons .btn-icon.view {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.action-buttons .btn-icon.edit {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.action-buttons .btn-icon.print {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.action-buttons .btn-icon.delete {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.action-buttons .btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* تنسيق حقول التاريخ */
input[type="date"].filter-date-modern {
    position: relative;
    direction: rtl;
}

input[type="date"].filter-date-modern::-webkit-calendar-picker-indicator {
    position: absolute;
    right: auto;
    left: 10px;
}

input[type="date"].filter-date-modern:before {
    content: attr(data-formatted-date);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    pointer-events: none;
}

.filter-date-modern {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: #f7fafc;
    color: #2d3748;
    transition: all 0.3s ease;
    min-width: 150px;
    direction: rtl; /* تصحيح اتجاه النص للعربية */
}

/* تصحيح عرض حقول التاريخ للعربية */
input[type="date"].filter-date-modern {
    direction: rtl;
    position: relative;
    color: transparent; /* إخفاء التاريخ الأصلي */
}

/* تغيير موضع مؤشر التقويم إلى اليسار */
input[type="date"].filter-date-modern::-webkit-calendar-picker-indicator {
    position: absolute;
    left: 10px;
    right: auto;
}

/* عرض التاريخ المنسق باستخدام خاصية data-formatted-date */
input[type="date"].filter-date-modern[data-formatted-date]::before {
    content: attr(data-formatted-date);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 10px 35px 10px 15px;
    color: #2d3748;
    pointer-events: none; /* السماح بالنقر على حقل الإدخال */
    display: flex;
    align-items: center;
}

.action-buttons .btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-buttons .btn-icon:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons .btn-icon:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* تحسين تركيز حقول الإدخال */
.input-modern:focus,
.select-modern:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    background-color: #f8f9ff;
}

/* تحسين عرض التنبيهات */
.alert {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيق جدول الفواتير مثل دليل الحسابات */
.invoices-table-modern {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 20px;
}

.invoices-table-modern .table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #5a67d8;
}

.invoices-table-modern .table-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.invoices-table-modern .table-header h4 i {
    font-size: 20px;
    opacity: 0.9;
}

.invoices-table-modern .table-actions {
    display: flex;
    gap: 10px;
}

.invoices-table-modern .table-container {
    overflow-x: auto;
    max-height: 600px;
}

.invoices-table-modern .modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.invoices-table-modern .modern-table thead th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 8px 6px; /* تقليل padding أكثر */
    text-align: center;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 13px; /* تقليل حجم الخط */
}

.invoices-table-modern .modern-table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
    height: 30px; /* تقليل الارتفاع أكثر ليكون مثل دليل الحسابات */
}

.invoices-table-modern .modern-table tbody tr:hover {
    background-color: #f8f9ff;
    transform: none; /* إزالة التحريك */
    box-shadow: none; /* إزالة الظل */
}

.invoices-table-modern .modern-table tbody td {
    padding: 2px 4px; /* تقليل padding أكثر */
    text-align: center;
    vertical-align: middle;
    border-right: 1px solid #f1f3f4;
    line-height: 1.1; /* تقليل المسافة بين الأسطر أكثر */
    font-size: 12px; /* تقليل حجم الخط أكثر */
}

.invoices-table-modern .modern-table tbody td:last-child {
    border-right: none;
}

/* تنسيق شارات طرق الدفع */
.payment-badge {
    display: inline-block;
    padding: 2px 8px; /* تقليل padding */
    border-radius: 12px; /* تقليل border-radius */
    font-size: 11px; /* تقليل حجم الخط */
    font-weight: 500;
    text-align: center;
    min-width: 50px; /* تقليل العرض الأدنى */
}

.payment-badge.cash {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.payment-badge.transfer {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
}

.payment-badge.credit {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.payment-badge.card {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

/* تنسيق شارات الحالة */
.status-badge {
    display: inline-block;
    padding: 2px 8px; /* تقليل padding */
    border-radius: 12px; /* تقليل border-radius */
    font-size: 11px; /* تقليل حجم الخط */
    font-weight: 500;
    text-align: center;
    min-width: 50px; /* تقليل العرض الأدنى */
}

.status-badge.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.status-badge.cancelled {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

/* تنسيق أزرار الإجراءات */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 3px; /* تقليل المسافة بين الأزرار */
    flex-wrap: wrap;
}

.action-buttons .btn-icon {
    width: 26px; /* تقليل العرض */
    height: 26px; /* تقليل الارتفاع */
    border-radius: 4px; /* تقليل border-radius */
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 10px; /* تقليل حجم الأيقونة */
}

.action-buttons .btn-icon.view {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.action-buttons .btn-icon.edit {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.action-buttons .btn-icon.print {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.action-buttons .btn-icon.delete {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.action-buttons .btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-buttons .btn-icon:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons .btn-icon:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* تحسين تركيز حقول الإدخال */
.input-modern:focus,
.select-modern:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    background-color: #f8f9ff;
}

/* تحسين عرض التنبيهات */
.alert {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيق جدول الفواتير مثل دليل الحسابات */
.invoices-table-modern {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 20px;
}

.invoices-table-modern .table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #5a67d8;
}

.invoices-table-modern .table-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.invoices-table-modern .table-header h4 i {
    font-size: 20px;
    opacity: 0.9;
}

.invoices-table-modern .table-actions {
    display: flex;
    gap: 10px;
}

.invoices-table-modern .table-container {
    overflow-x: auto;
    max-height: 600px;
}

.invoices-table-modern .modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.invoices-table-modern .modern-table thead th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 8px 6px; /* تقليل padding أكثر */
    text-align: center;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 13px; /* تقليل حجم الخط */
}

.invoices-table-modern .modern-table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
    height: 30px; /* تقليل الارتفاع أكثر ليكون مثل دليل الحسابات */
}

.invoices-table-modern .modern-table tbody tr:hover {
    background-color: #f8f9ff;
    transform: none; /* إزالة التحريك */
    box-shadow: none; /* إزالة الظل */
}

.invoices-table-modern .modern-