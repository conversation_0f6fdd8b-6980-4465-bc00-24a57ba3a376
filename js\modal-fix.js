/**
 * ملف إصلاح مشكلة أزرار الإلغاء في النوافذ المنبثقة وعرض قيود اليومية
 * هذا الملف يعالج مشكلة عدم عمل أزرار الإلغاء في نوافذ قيد اليومية، سند القبض، وسند الصرف
 * ويضيف وظائف لعرض قيود اليومية وسندات القبض والصرف
 */

document.addEventListener('DOMContentLoaded', function() {
    // إصلاح مشكلة أزرار الإلغاء في النوافذ المنبثقة
    fixCancelButtons();
    
    // إصلاح مشكلة عرض قيود اليومية وسندات القبض والصرف
    fixJournalEntriesDisplay();
    fixReceiptVouchersDisplay();
    fixPaymentVouchersDisplay();
    
    // إضافة مستمعي الأحداث لأزرار الإضافة
    setupAddButtonsEventListeners();
    
    console.log('تم إصلاح المشاكل بنجاح');
});


/**
 * إصلاح مشكلة أزرار الإلغاء في النوافذ المنبثقة
 */
function fixCancelButtons() {
    // إعادة تعيين مستمعي الأحداث لأزرار الإلغاء
    
    // زر إلغاء قيد اليومية
    const cancelJournalBtn = document.getElementById('cancel-add-journal-entry');
    if (cancelJournalBtn) {
        // إزالة مستمعي الأحداث القديمة لتجنب التكرار
        const newCancelJournalBtn = cancelJournalBtn.cloneNode(true);
        cancelJournalBtn.parentNode.replaceChild(newCancelJournalBtn, cancelJournalBtn);
        
        // إضافة مستمع حدث جديد
        newCancelJournalBtn.addEventListener('click', function() {
            const modal = document.getElementById('add-journal-entry-modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                
                // إعادة تعيين النموذج
                const form = document.getElementById('add-journal-entry-form');
                if (form) {
                    form.reset();
                }
            }
        });
    }
    
    // زر إلغاء سند القبض
    const cancelReceiptBtn = document.getElementById('cancel-add-receipt-voucher');
    if (cancelReceiptBtn) {
        // إزالة مستمعي الأحداث القديمة لتجنب التكرار
        const newCancelReceiptBtn = cancelReceiptBtn.cloneNode(true);
        cancelReceiptBtn.parentNode.replaceChild(newCancelReceiptBtn, cancelReceiptBtn);
        
        // إضافة مستمع حدث جديد
        newCancelReceiptBtn.addEventListener('click', function() {
            const modal = document.getElementById('add-receipt-voucher-modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                
                // إعادة تعيين النموذج
                const form = document.getElementById('add-receipt-voucher-form');
                if (form) {
                    form.reset();
                }
            }
        });
    }
    
    // زر إلغاء سند الصرف
    const cancelPaymentBtn = document.getElementById('cancel-add-payment-voucher');
    if (cancelPaymentBtn) {
        // إزالة مستمعي الأحداث القديمة لتجنب التكرار
        const newCancelPaymentBtn = cancelPaymentBtn.cloneNode(true);
        cancelPaymentBtn.parentNode.replaceChild(newCancelPaymentBtn, cancelPaymentBtn);
        
        // إضافة مستمع حدث جديد
        newCancelPaymentBtn.addEventListener('click', function() {
            const modal = document.getElementById('add-payment-voucher-modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                
                // إعادة تعيين النموذج
                const form = document.getElementById('add-payment-voucher-form');
                if (form) {
                    form.reset();
                }
            }
        });
    }
    
    // إعادة تعيين مستمعي الأحداث لأزرار الإغلاق
    document.querySelectorAll('.close-modal').forEach(btn => {
        // إزالة مستمعي الأحداث القديمة لتجنب التكرار
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
        
        // إضافة مستمع حدث جديد
        newBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';

                // إعادة تعيين النموذج إذا كان موجوداً
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                }
            }
        });
    });
    
    console.log('تم إصلاح مشكلة أزرار الإلغاء بنجاح');
}

/**
 * إصلاح مشكلة عرض قيود اليومية
 */
function fixJournalEntriesDisplay() {
    // التحقق من وجود تبويب قيد اليومية
    const journalTab = document.getElementById('journal-entries-tab');
    if (!journalTab) {
        console.error('لم يتم العثور على تبويب قيد اليومية');
        return;
    }
    
    // إنشاء محتوى تبويب قيد اليومية إذا كان فارغًا
    if (journalTab.children.length === 0) {
        journalTab.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-journal-whills"></i> قيود اليومية</h3>
                <div class="header-actions">
                    <button class="btn secondary-btn add-entry-btn" onclick="showAddJournalEntryModal()">
                        <i class="fas fa-plus"></i> إضافة قيد
                    </button>
                    <button class="btn secondary-btn import-entries-btn" onclick="importJournalEntries()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="btn secondary-btn export-entries-btn" onclick="exportJournalEntries()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
            
            <div class="journal-entries-search-filter">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="journal-entries-search" placeholder="البحث في قيود اليومية...">
                </div>
                <div class="filter-options">
                    <div class="date-range">
                        <div class="date-input">
                            <label for="journal-start-date">من تاريخ:</label>
                            <input type="date" id="journal-start-date">
                        </div>
                        <div class="date-input">
                            <label for="journal-end-date">إلى تاريخ:</label>
                            <input type="date" id="journal-end-date">
                        </div>
                        <button class="apply-date-btn">تطبيق</button>
                    </div>
                </div>
            </div>
            
            <div class="journal-entries-table-container">
                <table class="journal-entries-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم القيد</th>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="journal-entries-table-body">
                        <!-- سيتم إضافة قيود اليومية هنا -->
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // تحميل بيانات قيود اليومية
    const journalEntries = [
        { id: 1, date: '2023-06-01', number: 'JE-2023-001', description: 'قيد افتتاحي', amount: 10000, status: 'مرحل' },
        { id: 2, date: '2023-06-05', number: 'JE-2023-002', description: 'مبيعات نقدية', amount: 5000, status: 'مرحل' },
        { id: 3, date: '2023-06-10', number: 'JE-2023-003', description: 'مصروفات تشغيلية', amount: 2500, status: 'معلق' },
        { id: 4, date: '2023-06-15', number: 'JE-2023-004', description: 'رواتب الموظفين', amount: 7500, status: 'معلق' },
        { id: 5, date: '2023-06-20', number: 'JE-2023-005', description: 'إيرادات خدمات', amount: 3500, status: 'مرحل' }
    ];
    
    const tableBody = document.getElementById('journal-entries-table-body');
    if (tableBody) {
        // مسح الجدول
        tableBody.innerHTML = '';
        
        // إضافة قيود اليومية إلى الجدول
        journalEntries.forEach(entry => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${entry.date}</td>
                <td>${entry.number}</td>
                <td>${entry.description}</td>
                <td>${formatCurrency(entry.amount)}</td>
                <td><span class="status-badge ${entry.status === 'مرحل' ? 'status-posted' : 'status-pending'}">${entry.status}</span></td>
                <td>
                    <div class="entry-actions">
                        <button class="action-btn view-btn" data-id="${entry.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-btn" data-id="${entry.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${entry.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // إضافة مستمعي الأحداث لأزرار الإجراءات
        setupJournalEntryActionButtons();
    }
    
    console.log('تم إصلاح مشكلة عرض قيود اليومية بنجاح');
}

/**
 * إصلاح مشكلة عرض سندات القبض
 */
function fixReceiptVouchersDisplay() {
    // التحقق من وجود تبويب سند القبض
    const receiptTab = document.getElementById('receipt-voucher-tab');
    if (!receiptTab) {
        console.error('لم يتم العثور على تبويب سند القبض');
        return;
    }
    
    // إنشاء محتوى تبويب سند القبض إذا كان فارغًا
    if (receiptTab.children.length === 0) {
        receiptTab.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-receipt"></i> سندات القبض</h3>
                <div class="header-actions">
                    <button class="btn secondary-btn add-receipt-btn" onclick="showAddReceiptVoucherModal()">
                        <i class="fas fa-plus"></i> إضافة سند قبض
                    </button>
                    <button class="btn secondary-btn import-receipts-btn" onclick="importReceiptVouchers()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="btn secondary-btn export-receipts-btn" onclick="exportReceiptVouchers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
            
            <div class="receipt-vouchers-search-filter">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="receipt-vouchers-search" placeholder="البحث في سندات القبض...">
                </div>
                <div class="filter-options">
                    <div class="date-range">
                        <div class="date-input">
                            <label for="receipt-start-date">من تاريخ:</label>
                            <input type="date" id="receipt-start-date">
                        </div>
                        <div class="date-input">
                            <label for="receipt-end-date">إلى تاريخ:</label>
                            <input type="date" id="receipt-end-date">
                        </div>
                        <button class="apply-date-btn">تطبيق</button>
                    </div>
                </div>
            </div>
            
            <div class="receipt-vouchers-table-container">
                <table class="receipt-vouchers-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم السند</th>
                            <th>المستلم من</th>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="receipt-vouchers-table-body">
                        <!-- سيتم إضافة سندات القبض هنا -->
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // تحميل بيانات سندات القبض
    const receiptVouchers = [
        { id: 1, date: '2023-06-01', number: 'RV-2023-001', from: 'شركة الأمل', description: 'دفعة مقدمة', amount: 5000, paymentMethod: 'نقدي' },
        { id: 2, date: '2023-06-05', number: 'RV-2023-002', from: 'مؤسسة النور', description: 'تسديد فاتورة', amount: 3500, paymentMethod: 'شيك' },
        { id: 3, date: '2023-06-10', number: 'RV-2023-003', from: 'محمد أحمد', description: 'دفعة جزئية', amount: 1500, paymentMethod: 'تحويل بنكي' },
        { id: 4, date: '2023-06-15', number: 'RV-2023-004', from: 'شركة الصفا', description: 'تسديد فاتورة', amount: 4200, paymentMethod: 'نقدي' },
        { id: 5, date: '2023-06-20', number: 'RV-2023-005', from: 'مؤسسة الرياض', description: 'دفعة مقدمة', amount: 2800, paymentMethod: 'شيك' }
    ];
    
    const tableBody = document.getElementById('receipt-vouchers-table-body');
    if (tableBody) {
        // مسح الجدول
        tableBody.innerHTML = '';
        
        // إضافة سندات القبض إلى الجدول
        receiptVouchers.forEach(voucher => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${voucher.date}</td>
                <td>${voucher.number}</td>
                <td>${voucher.from}</td>
                <td>${voucher.description}</td>
                <td>${formatCurrency(voucher.amount)}</td>
                <td>${voucher.paymentMethod}</td>
                <td>
                    <div class="voucher-actions">
                        <button class="action-btn view-btn" data-id="${voucher.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-btn" data-id="${voucher.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn print-btn" data-id="${voucher.id}" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${voucher.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // إضافة مستمعي الأحداث لأزرار الإجراءات
        setupReceiptVoucherActionButtons();
    }
    
    console.log('تم إصلاح مشكلة عرض سندات القبض بنجاح');
}

/**
 * إصلاح مشكلة عرض سندات الصرف
 */
function fixPaymentVouchersDisplay() {
    // التحقق من وجود تبويب سند الصرف
    const paymentTab = document.getElementById('payment-voucher-tab');
    if (!paymentTab) {
        console.error('لم يتم العثور على تبويب سند الصرف');
        return;
    }
    
    // إنشاء محتوى تبويب سند الصرف إذا كان فارغًا
    if (paymentTab.children.length === 0) {
        paymentTab.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-money-check"></i> سندات الصرف</h3>
                <div class="header-actions">
                    <button class="btn secondary-btn add-payment-btn" onclick="showAddPaymentVoucherModal()">
                        <i class="fas fa-plus"></i> إضافة سند صرف
                    </button>
                    <button class="btn secondary-btn import-payments-btn" onclick="importPaymentVouchers()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="btn secondary-btn export-payments-btn" onclick="exportPaymentVouchers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
            
            <div class="payment-vouchers-search-filter">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="payment-vouchers-search" placeholder="البحث في سندات الصرف...">
                </div>
                <div class="filter-options">
                    <div class="date-range">
                        <div class="date-input">
                            <label for="payment-start-date">من تاريخ:</label>
                            <input type="date" id="payment-start-date">
                        </div>
                        <div class="date-input">
                            <label for="payment-end-date">إلى تاريخ:</label>
                            <input type="date" id="payment-end-date">
                        </div>
                        <button class="apply-date-btn">تطبيق</button>
                    </div>
                </div>
            </div>
            
            <div class="payment-vouchers-table-container">
                <table class="payment-vouchers-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم السند</th>
                            <th>المدفوع له</th>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="payment-vouchers-table-body">
                        <!-- سيتم إضافة سندات الصرف هنا -->
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // تحميل بيانات سندات الصرف
    const paymentVouchers = [
        { id: 1, date: '2023-06-02', number: 'PV-2023-001', to: 'شركة التوريدات', description: 'دفعة مستلزمات مكتبية', amount: 1200, paymentMethod: 'نقدي' },
        { id: 2, date: '2023-06-07', number: 'PV-2023-002', to: 'مؤسسة الخدمات', description: 'صيانة دورية', amount: 800, paymentMethod: 'شيك' },
        { id: 3, date: '2023-06-12', number: 'PV-2023-003', to: 'أحمد محمد', description: 'رواتب موظفين', amount: 5000, paymentMethod: 'تحويل بنكي' },
        { id: 4, date: '2023-06-18', number: 'PV-2023-004', to: 'شركة الكهرباء', description: 'فاتورة كهرباء', amount: 750, paymentMethod: 'نقدي' },
        { id: 5, date: '2023-06-25', number: 'PV-2023-005', to: 'مؤسسة الاتصالات', description: 'فاتورة هاتف وإنترنت', amount: 500, paymentMethod: 'شيك' }
    ];
    
    const tableBody = document.getElementById('payment-vouchers-table-body');
    if (tableBody) {
        // مسح الجدول
        tableBody.innerHTML = '';
        
        // إضافة سندات الصرف إلى الجدول
        paymentVouchers.forEach(voucher => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${voucher.date}</td>
                <td>${voucher.number}</td>
                <td>${voucher.to}</td>
                <td>${voucher.description}</td>
                <td>${formatCurrency(voucher.amount)}</td>
                <td>${voucher.paymentMethod}</td>
                <td>
                    <div class="voucher-actions">
                        <button class="action-btn view-btn" data-id="${voucher.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-btn" data-id="${voucher.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn print-btn" data-id="${voucher.id}" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${voucher.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // إضافة مستمعي الأحداث لأزرار الإجراءات
        setupPaymentVoucherActionButtons();
    }
    
    console.log('تم إصلاح مشكلة عرض سندات الصرف بنجاح');
}

/**
 * تنسيق المبلغ بتنسيق العملة
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' }).format(amount);
}

/**
 * إعداد مستمعي الأحداث لأزرار الإجراءات في قيود اليومية
 */
function setupJournalEntryActionButtons() {
    // أزرار عرض التفاصيل
    document.querySelectorAll('#journal-entries-table-body .view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const entryId = this.getAttribute('data-id');
            alert(`عرض تفاصيل القيد رقم ${entryId}`);
            // هنا يمكن إضافة كود لعرض تفاصيل القيد في نافذة منبثقة
        });
    });
    
    // أزرار التعديل
    document.querySelectorAll('#journal-entries-table-body .edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const entryId = this.getAttribute('data-id');
            alert(`تعديل القيد رقم ${entryId}`);
            // هنا يمكن إضافة كود لفتح نافذة تعديل القيد
        });
    });
    
    // أزرار الحذف
    document.querySelectorAll('#journal-entries-table-body .delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const entryId = this.getAttribute('data-id');
            if (confirm(`هل أنت متأكد من حذف القيد رقم ${entryId}؟`)) {
                alert(`تم حذف القيد رقم ${entryId}`);
                // هنا يمكن إضافة كود لحذف القيد من قاعدة البيانات وتحديث الجدول
            }
        });
    });
}

/**
 * إعداد مستمعي الأحداث لأزرار الإجراءات في سندات القبض
 */
function setupReceiptVoucherActionButtons() {
    // أزرار عرض التفاصيل
    document.querySelectorAll('#receipt-vouchers-table-body .view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`عرض تفاصيل سند القبض رقم ${voucherId}`);
            // هنا يمكن إضافة كود لعرض تفاصيل سند القبض في نافذة منبثقة
        });
    });
    
    // أزرار التعديل
    document.querySelectorAll('#receipt-vouchers-table-body .edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`تعديل سند القبض رقم ${voucherId}`);
            // هنا يمكن إضافة كود لفتح نافذة تعديل سند القبض
        });
    });
    
    // أزرار الطباعة
    document.querySelectorAll('#receipt-vouchers-table-body .print-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`طباعة سند القبض رقم ${voucherId}`);
            // هنا يمكن إضافة كود لطباعة سند القبض
        });
    });
    
    // أزرار الحذف
    document.querySelectorAll('#receipt-vouchers-table-body .delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            if (confirm(`هل أنت متأكد من حذف سند القبض رقم ${voucherId}؟`)) {
                alert(`تم حذف سند القبض رقم ${voucherId}`);
                // هنا يمكن إضافة كود لحذف سند القبض من قاعدة البيانات وتحديث الجدول
            }
        });
    });
}

/**
 * إعداد مستمعي الأحداث لأزرار الإجراءات في سندات الصرف
 */
function setupPaymentVoucherActionButtons() {
    // أزرار عرض التفاصيل
    document.querySelectorAll('#payment-vouchers-table-body .view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`عرض تفاصيل سند الصرف رقم ${voucherId}`);
            // هنا يمكن إضافة كود لعرض تفاصيل سند الصرف في نافذة منبثقة
        });
    });
    
    // أزرار التعديل
    document.querySelectorAll('#payment-vouchers-table-body .edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`تعديل سند الصرف رقم ${voucherId}`);
            // هنا يمكن إضافة كود لفتح نافذة تعديل سند الصرف
        });
    });
    
    // أزرار الطباعة
    document.querySelectorAll('#payment-vouchers-table-body .print-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            alert(`طباعة سند الصرف رقم ${voucherId}`);
            // هنا يمكن إضافة كود لطباعة سند الصرف
        });
    });
    
    // أزرار الحذف
    document.querySelectorAll('#payment-vouchers-table-body .delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const voucherId = this.getAttribute('data-id');
            if (confirm(`هل أنت متأكد من حذف سند الصرف رقم ${voucherId}؟`)) {
                alert(`تم حذف سند الصرف رقم ${voucherId}`);
                // هنا يمكن إضافة كود لحذف سند الصرف من قاعدة البيانات وتحديث الجدول
            }
        });
    });
}

/**
 * إعداد مستمعي الأحداث لأزرار الإضافة
 */
function setupAddButtonsEventListeners() {
    // زر إضافة قيد يومية
    const addEntryBtn = document.querySelector('.add-entry-btn');
    if (addEntryBtn) {
        addEntryBtn.addEventListener('click', function() {
            showAddJournalEntryModal();
        });
    }
    
    // زر إضافة سند قبض
    const addReceiptBtn = document.querySelector('.add-receipt-btn');
    if (addReceiptBtn) {
        addReceiptBtn.addEventListener('click', function() {
            showAddReceiptVoucherModal();
        });
    }
    
    // زر إضافة سند صرف
    const addPaymentBtn = document.querySelector('.add-payment-btn');
    if (addPaymentBtn) {
        addPaymentBtn.addEventListener('click', function() {
            showAddPaymentVoucherModal();
        });
    }
    
    console.log('تم إعداد مستمعي الأحداث لأزرار الإضافة بنجاح');
}