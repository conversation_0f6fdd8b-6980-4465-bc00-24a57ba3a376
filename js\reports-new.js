// ملف JavaScript الجديد لصفحة التقارير

// بيانات التقارير المختلفة
const reportTypes = {
    accounts: {
        title: 'تقارير الحسابات',
        reports: [
            { id: 'account_statement', name: 'كشف حساب', needsAccount: true, needsCustomer: false },
            { id: 'customer_balances', name: 'أرصدة العملاء', needsAccount: false, needsCustomer: true },
            { id: 'trial_balance', name: 'ميزان المراجعة', needsAccount: false, needsCustomer: false },
            { id: 'profit_loss', name: 'الأرباح والخسائر', needsAccount: false, needsCustomer: false }
        ]
    },
    sales: {
        title: 'تقارير المبيعات',
        reports: [
            { id: 'sales_invoices', name: 'فواتير المبيعات', needsAccount: false, needsCustomer: true },
            { id: 'sales_by_customer', name: 'المبيعات حسب العميل', needsAccount: false, needsCustomer: true },
            { id: 'top_selling_items', name: 'الأصناف الأكثر مبيعاً', needsAccount: false, needsCustomer: false },
            { id: 'sales_summary', name: 'ملخص المبيعات', needsAccount: false, needsCustomer: false }
        ]
    },
    inventory: {
        title: 'تقارير المخزون',
        reports: [
            { id: 'inventory_report', name: 'تقرير المخزون', needsAccount: false, needsCustomer: false },
            { id: 'stock_movement', name: 'حركة المخزون', needsAccount: false, needsCustomer: false },
            { id: 'low_stock', name: 'الأصناف منخفضة المخزون', needsAccount: false, needsCustomer: false }
        ]
    },
    financial: {
        title: 'التقارير المالية',
        reports: [
            { id: 'balance_sheet', name: 'المزانية العمومية', needsAccount: false, needsCustomer: false },
            { id: 'cash_flow', name: 'التدفق النقدي', needsAccount: true, needsCustomer: false },
            { id: 'financial_summary', name: 'الملخص المالي', needsAccount: false, needsCustomer: false }
        ]
    }
};

// بيانات تجريبية للتقارير
const sampleData = {
    accountStatement: [
        { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 0, credit: 5000, balance: 5000 },
        { date: '2023-06-05', description: 'فاتورة مبيعات #001', debit: 1500, credit: 0, balance: 6500 },
        { date: '2023-06-10', description: 'دفعة نقدية', debit: 0, credit: 1000, balance: 5500 },
        { date: '2023-06-15', description: 'فاتورة مبيعات #002', debit: 2000, credit: 0, balance: 7500 }
    ],
    customerBalances: [
        { customer: 'أحمد محمد علي', balance: 1500, status: 'مدين' },
        { customer: 'شركة الأمل للتجارة', balance: -2500, status: 'دائن' },
        { customer: 'فاطمة عبدالله', balance: 750, status: 'مدين' },
        { customer: 'مؤسسة النور', balance: 0, status: 'متوازن' }
    ],
    salesInvoices: [
        { date: '2023-06-01', invoice: 'INV-001', customer: 'أحمد محمد علي', total: 1250, status: 'مدفوعة' },
        { date: '2023-06-03', invoice: 'INV-002', customer: 'شركة النور للتجارة', total: 3500, status: 'مدفوعة' },
        { date: '2023-06-05', invoice: 'INV-003', customer: 'سارة عبدالله', total: 750, status: 'مدفوعة' },
        { date: '2023-06-08', invoice: 'INV-004', customer: 'مؤسسة الأمل', total: 4200, status: 'معلقة' },
        { date: '2023-06-10', invoice: 'INV-005', customer: 'خالد محمود', total: 1800, status: 'مدفوعة' },
        { date: '2023-06-12', invoice: 'INV-006', customer: 'شركة الرياض للتجارة', total: 5600, status: 'معلقة' },
        { date: '2023-06-15', invoice: 'INV-007', customer: 'فاطمة أحمد', total: 950, status: 'مدفوعة' },
        { date: '2023-06-18', invoice: 'INV-008', customer: 'شركة النور للتجارة', total: 2700, status: 'مدفوعة' },
        { date: '2023-06-20', invoice: 'INV-009', customer: 'محمد عبدالرحمن', total: 3200, status: 'معلقة' },
        { date: '2023-06-22', invoice: 'INV-010', customer: 'مؤسسة الصفا', total: 1500, status: 'مدفوعة' },
        { date: '2023-06-25', invoice: 'INV-011', customer: 'أحمد محمد علي', total: 2200, status: 'معلقة' },
        { date: '2023-06-28', invoice: 'INV-012', customer: 'شركة الرياض للتجارة', total: 4800, status: 'مدفوعة' },
        { date: '2023-06-30', invoice: 'INV-013', customer: 'سارة عبدالله', total: 1100, status: 'معلقة' }
    ]
};

let currentCategory = null;
let currentReport = null;

document.addEventListener('DOMContentLoaded', function() {
    initReportsPage();
    bindEvents();
    checkLibrariesLoaded();
});

// التحقق من تحميل المكتبات المطلوبة
function checkLibrariesLoaded() {
    const exportButtons = document.querySelectorAll('.export-btn');

    // التحقق من مكتبة Excel
    if (typeof XLSX === 'undefined') {
        console.warn('مكتبة XLSX غير محملة - وظيفة تصدير Excel معطلة');
        const excelBtn = document.querySelector('.export-btn.excel');
        if (excelBtn) {
            excelBtn.disabled = true;
            excelBtn.title = 'مكتبة Excel غير محملة';
        }
    }

    // التحقق من مكتبات PDF
    if (typeof window.jspdf === 'undefined' || typeof html2canvas === 'undefined') {
        console.warn('مكتبات PDF غير محملة - وظيفة تصدير PDF معطلة');
        const pdfBtn = document.querySelector('.export-btn.pdf');
        if (pdfBtn) {
            pdfBtn.disabled = true;
            pdfBtn.title = 'مكتبات PDF غير محملة';
        }
    }
}

function initReportsPage() {
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateInput = document.getElementById('report-start-date');
    const endDateInput = document.getElementById('report-end-date');
    
    if (startDateInput) {
        startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
    }
    
    if (endDateInput) {
        endDateInput.value = today.toISOString().split('T')[0];
    }
}

function bindEvents() {
    // ربط أحداث بطاقات الفئات
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            selectReportCategory(category);
        });
    });

    // ربط حدث زر إنشاء التقرير
    const generateBtn = document.getElementById('generate-report-btn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateReport);
    }
}

function selectReportCategory(category) {
    currentCategory = category;
    
    // إخفاء قسم اختيار التقرير وإظهار قسم التقرير المحدد
    document.querySelector('.report-selector').style.display = 'none';
    document.getElementById('selected-report').style.display = 'block';
    
    // تحديث عنوان التقرير
    const reportTitle = document.getElementById('report-title');
    reportTitle.textContent = reportTypes[category].title;
    
    // إظهار قائمة التقارير المتاحة
    showReportsList(category);
}

function showReportsList(category) {
    const reportContent = document.getElementById('report-content');
    const reports = reportTypes[category].reports;
    
    let html = `
        <div class="reports-list">
            <h4>اختر التقرير المطلوب:</h4>
            <div class="reports-grid">
    `;
    
    reports.forEach(report => {
        html += `
            <div class="report-item" onclick="selectSpecificReport('${report.id}', '${report.name}', ${report.needsAccount}, ${report.needsCustomer})">
                <h5>${report.name}</h5>
                <p>انقر لعرض التقرير</p>
            </div>
        `;
    });
    
    html += `
            </div>
        </div>
    `;
    
    reportContent.innerHTML = html;
}

function selectSpecificReport(reportId, reportName, needsAccount, needsCustomer) {
    currentReport = reportId;
    
    // تحديث عنوان التقرير
    document.getElementById('report-title').textContent = reportName;
    
    // إظهار/إخفاء الفلاتر حسب نوع التقرير
    const accountFilter = document.getElementById('account-filter');
    const customerFilter = document.getElementById('customer-filter');
    
    accountFilter.style.display = needsAccount ? 'flex' : 'none';
    customerFilter.style.display = needsCustomer ? 'flex' : 'none';
    
    // إظهار رسالة لعرض التقرير
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = `
        <div class="loading-message" style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-info-circle"></i>
            <p>اختر التواريخ والخيارات المطلوبة ثم اضغط "عرض التقرير"</p>
        </div>
    `;
}

function generateReport() {
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    if (!startDate || !endDate) {
        alert('يرجى اختيار تاريخ البداية والنهاية');
        return;
    }
    
    // عرض التقرير حسب النوع
    switch(currentReport) {
        case 'account_statement':
            showAccountStatement();
            break;
        case 'customer_balances':
            showCustomerBalances();
            break;
        case 'sales_invoices':
            showSalesInvoices();
            break;
        default:
            showGenericReport();
    }
}

function showAccountStatement() {
    const reportContent = document.getElementById('report-content');
    const selectedAccount = document.getElementById('account-select')?.value;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;

    // الحصول على بيانات الحساب المحدد
    const accountData = getAccountData(selectedAccount);
    const accountInfo = getAccountInfo(selectedAccount);

    if (!accountData || accountData.length === 0) {
        reportContent.innerHTML = `
            <div class="no-data-message">
                <i class="fas fa-info-circle"></i>
                <h4>لا توجد بيانات</h4>
                <p>لا توجد حركات مالية للحساب المحدد في الفترة المحددة</p>
            </div>
        `;
        return;
    }

    // إنشاء بطاقة معلومات الحساب المحسنة
    let html = `
        <div class="report-header-info">
            <div class="account-details">
                <h4><i class="fas fa-file-invoice-dollar"></i> كشف حساب</h4>
                <div class="account-header-summary">
                    <div class="account-title">
                        <h3>${accountInfo.name}</h3>
                        <span class="account-code">${accountInfo.code}</span>
                    </div>
                    <div class="account-status ${accountInfo.balance >= 0 ? 'positive' : 'negative'}">
                        ${accountInfo.status}
                    </div>
                </div>
                <p class="account-description">${accountInfo.description}</p>
                
                <div class="account-info-grid">
                    <div class="info-item">
                        <strong>نوع الحساب:</strong> ${getAccountTypeArabic(accountInfo.type)}
                    </div>
                    <div class="info-item">
                        <strong>التصنيف:</strong> ${accountInfo.accountCategory}
                    </div>
                    <div class="info-item">
                        <strong>العملة:</strong> ${accountInfo.currency}
                    </div>
                    <div class="info-item">
                        <strong>تاريخ الإنشاء:</strong> ${accountInfo.creationDate}
                    </div>
                    <div class="info-item">
                        <strong>آخر تحديث:</strong> ${accountInfo.lastUpdated}
                    </div>
                    <div class="info-item">
                        <strong>المسؤول:</strong> ${accountInfo.accountManager}
                    </div>
                    <div class="info-item">
                        <strong>الفترة:</strong> من ${startDate || 'البداية'} إلى ${endDate || 'النهاية'}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="report-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="printAccountStatement()"><i class="fas fa-print"></i> طباعة</button>
            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> تصدير Excel</button>
            <button class="btn btn-sm btn-outline-info" onclick="showAccountDetails()"><i class="fas fa-info-circle"></i> تفاصيل الحساب</button>
            <div class="search-box">
                <input type="text" id="transaction-search" placeholder="بحث في الحركات..." oninput="searchTransactions()">
                <i class="fas fa-search"></i>
            </div>
        </div>
        
        <table class="report-table account-statement-table" id="account-statement-table">
            <thead>
                <tr>
                    <th><i class="fas fa-calendar"></i> التاريخ</th>
                    <th><i class="fas fa-file-alt"></i> البيان</th>
                    <th><i class="fas fa-arrow-up text-danger"></i> مدين</th>
                    <th><i class="fas fa-arrow-down text-success"></i> دائن</th>
                    <th><i class="fas fa-balance-scale"></i> الرصيد</th>
                </tr>
            </thead>
            <tbody>
    `;

    let runningBalance = accountInfo.openingBalance || 0;
    let totalDebit = 0;
    let totalCredit = 0;

    // إضافة رصيد افتتاحي إذا كان موجود
    if (accountInfo.openingBalance && accountInfo.openingBalance !== 0) {
        html += `
            <tr class="opening-balance-row">
                <td><span class="date-cell">${startDate || '2023-01-01'}</span></td>
                <td><strong>رصيد مدور</strong></td>
                <td class="empty-cell">-</td>
                <td class="empty-cell">-</td>
                <td><span class="amount balance">${formatCurrency(runningBalance)}</span></td>
            </tr>
        `;
    }

    accountData.forEach(item => {
        const debitAmount = parseFloat(item.debit) || 0;
        const creditAmount = parseFloat(item.credit) || 0;

        runningBalance += debitAmount - creditAmount;
        totalDebit += debitAmount;
        totalCredit += creditAmount;

        html += `
            <tr>
                <td><span class="date-cell">${item.date}</span></td>
                <td>${item.description}</td>
                <td>${debitAmount > 0 ? `<span class="amount debit">${formatCurrency(debitAmount)}</span>` : '<span class="empty-cell">-</span>'}</td>
                <td>${creditAmount > 0 ? `<span class="amount credit">${formatCurrency(creditAmount)}</span>` : '<span class="empty-cell">-</span>'}</td>
                <td><span class="amount balance">${formatCurrency(runningBalance)}</span></td>
            </tr>
        `;
    });

    html += `
            </tbody>
            <tfoot>
                <tr class="totals-row">
                    <td colspan="2"><strong><i class="fas fa-calculator"></i> الإجمالي</strong></td>
                    <td><strong><span class="amount debit">${formatCurrency(totalDebit)}</span></strong></td>
                    <td><strong><span class="amount credit">${formatCurrency(totalCredit)}</span></strong></td>
                    <td><strong><span class="amount balance">${formatCurrency(runningBalance)}</span></strong></td>
                </tr>
            </tfoot>
        </table>

        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card debit">
                    <i class="fas fa-arrow-up"></i>
                    <div class="summary-info">
                        <h5>إجمالي المدين</h5>
                        <span class="amount">${formatCurrency(totalDebit)}</span>
                    </div>
                </div>
                <div class="summary-card credit">
                    <i class="fas fa-arrow-down"></i>
                    <div class="summary-info">
                        <h5>إجمالي الدائن</h5>
                        <span class="amount">${formatCurrency(totalCredit)}</span>
                    </div>
                </div>
                <div class="summary-card balance">
                    <i class="fas fa-balance-scale"></i>
                    <div class="summary-info">
                        <h5>الرصيد النهائي</h5>
                        <span class="amount">${formatCurrency(runningBalance)}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    reportContent.innerHTML = html;

    // إضافة وظيفة البحث في الحركات
    window.searchTransactions = function() {
        const searchTerm = document.getElementById('transaction-search').value.toLowerCase();
        const rows = document.querySelectorAll('#account-statement-table tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    };
    
    // إضافة وظيفة عرض تفاصيل الحساب
    window.showAccountDetails = function() {
        const accountDetails = accountInfo;
        
        // إنشاء نافذة منبثقة لعرض تفاصيل الحساب
        const detailsModal = document.createElement('div');
        detailsModal.className = 'modal-overlay';
        detailsModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تفاصيل الحساب</h3>
                    <button class="close-btn" onclick="this.parentNode.parentNode.parentNode.remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="account-details-grid">
                        <div class="detail-item">
                            <span class="detail-label">اسم الحساب</span>
                            <span class="detail-value">${accountDetails.name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">رقم الحساب</span>
                            <span class="detail-value">${accountDetails.code}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">نوع الحساب</span>
                            <span class="detail-value">${getAccountTypeArabic(accountDetails.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">التصنيف</span>
                            <span class="detail-value">${accountDetails.accountCategory}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الرصيد الحالي</span>
                            <span class="detail-value ${accountDetails.balance >= 0 ? 'positive-balance' : 'negative-balance'}">
                                ${formatCurrency(accountDetails.balance)}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الحالة</span>
                            <span class="detail-value">${accountDetails.status}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">العملة</span>
                            <span class="detail-value">${accountDetails.currency}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">تاريخ الإنشاء</span>
                            <span class="detail-value">${accountDetails.creationDate}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">آخر تحديث</span>
                            <span class="detail-value">${accountDetails.lastUpdated}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">المسؤول</span>
                            <span class="detail-value">${accountDetails.accountManager}</span>
                        </div>
                    </div>
                    <div class="account-description-box">
                        <h4>وصف الحساب</h4>
                        <p>${accountDetails.description}</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(detailsModal);
    };
    
    // إضافة وظيفة طباعة كشف الحساب
    window.printAccountStatement = function() {
        const accountName = accountInfo.name;
        const printContent = document.getElementById('account-statement-table').outerHTML;
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        printWindow.document.write(`
            <html dir="rtl">
            <head>
                <title>كشف حساب - ${accountName}</title>
                <link rel="stylesheet" href="css/bootstrap.min.css">
                <link rel="stylesheet" href="css/styles.css">
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .print-header { text-align: center; margin-bottom: 20px; }
                    .print-header h2 { margin-bottom: 5px; }
                    .print-info { margin-bottom: 15px; }
                    .print-info p { margin: 5px 0; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; }
                    .debit { color: #dc3545; }
                    .credit { color: #28a745; }
                    .totals-row { font-weight: bold; background-color: #f8f9fa; }
                    @media print {
                        button { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <h2>كشف حساب</h2>
                    <h3>${accountName} (${accountInfo.code})</h3>
                </div>
                <div class="print-info">
                    <p><strong>نوع الحساب:</strong> ${getAccountTypeArabic(accountInfo.type)}</p>
                    <p><strong>الفترة:</strong> من ${startDate || 'البداية'} إلى ${endDate || 'النهاية'}</p>
                    <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
                <button onclick="window.print();" style="margin-bottom: 10px;">طباعة</button>
                ${printContent}
                <div style="margin-top: 20px; text-align: center;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام منجز للمحاسبة</p>
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        setTimeout(() => {
            printWindow.focus();
        }, 500);
    };
}
}

function showCustomerBalances() {
    const reportContent = document.getElementById('report-content');
    const data = sampleData.customerBalances;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    // حساب إحصائيات أرصدة العملاء
    const totalCustomers = data.length;
    const debtorCustomers = data.filter(customer => customer.balance > 0).length;
    const creditorCustomers = data.filter(customer => customer.balance < 0).length;
    const balancedCustomers = data.filter(customer => customer.balance === 0).length;
    
    // حساب إجمالي الأرصدة
    const totalDebtorBalance = data.filter(customer => customer.balance > 0)
                                  .reduce((sum, customer) => sum + customer.balance, 0);
    const totalCreditorBalance = data.filter(customer => customer.balance < 0)
                                    .reduce((sum, customer) => sum + Math.abs(customer.balance), 0);
    const netBalance = totalDebtorBalance - totalCreditorBalance;
    
    // إنشاء بطاقات الإحصائيات
    let html = `
        <div class="report-header-info">
            <h4>أرصدة العملاء</h4>
            <p>الفترة: من ${startDate} إلى ${endDate}</p>
        </div>
        
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card total">
                    <div class="card-icon"><i class="fas fa-users"></i></div>
                    <div class="card-content">
                        <h5>إجمالي العملاء</h5>
                        <div class="card-value">${totalCustomers}</div>
                        <div class="card-amount">${netBalance.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
                <div class="summary-card debtor">
                    <div class="card-icon"><i class="fas fa-arrow-up"></i></div>
                    <div class="card-content">
                        <h5>العملاء المدينون</h5>
                        <div class="card-value">${debtorCustomers}</div>
                        <div class="card-amount">${totalDebtorBalance.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
                <div class="summary-card creditor">
                    <div class="card-icon"><i class="fas fa-arrow-down"></i></div>
                    <div class="card-content">
                        <h5>العملاء الدائنون</h5>
                        <div class="card-value">${creditorCustomers}</div>
                        <div class="card-amount">${totalCreditorBalance.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="report-filter-bar">
            <div class="filter-item">
                <label>تصفية حسب الحالة:</label>
                <select id="customer-status-filter" onchange="filterCustomersByStatus()">
                    <option value="all">جميع العملاء</option>
                    <option value="debtor">المدينون فقط</option>
                    <option value="creditor">الدائنون فقط</option>
                    <option value="balanced">المتوازنون فقط</option>
                </select>
            </div>
            <div class="search-box">
                <input type="text" id="customer-search" placeholder="بحث في العملاء..." oninput="searchCustomers()">
                <i class="fas fa-search"></i>
            </div>
        </div>
        
        <table class="report-table" id="customer-balances-table">
            <thead>
                <tr>
                    <th>العميل</th>
                    <th>الرصيد</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.forEach(row => {
        const balanceClass = row.balance > 0 ? 'positive-balance' : (row.balance < 0 ? 'negative-balance' : '');
        const statusType = row.balance > 0 ? 'debtor' : (row.balance < 0 ? 'creditor' : 'balanced');
        
        html += `
            <tr data-status="${statusType}">
                <td>${row.customer}</td>
                <td class="${balanceClass}">${Math.abs(row.balance).toLocaleString('ar-SA')} ر.س</td>
                <td>${row.status}</td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
            <tfoot>
                <tr>
                    <td>صافي الأرصدة</td>
                    <td colspan="2" class="${netBalance >= 0 ? 'positive-balance' : 'negative-balance'}">
                        ${Math.abs(netBalance).toLocaleString('ar-SA')} ر.س
                        ${netBalance >= 0 ? '(مدين)' : '(دائن)'}
                    </td>
                </tr>
            </tfoot>
        </table>
    `;
    
    reportContent.innerHTML = html;
    
    // إضافة وظائف التصفية والبحث
    window.filterCustomersByStatus = function() {
        const filter = document.getElementById('customer-status-filter').value;
        const rows = document.querySelectorAll('#customer-balances-table tbody tr');
        
        rows.forEach(row => {
            if (filter === 'all' || row.getAttribute('data-status') === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    };
    
    window.searchCustomers = function() {
        const searchTerm = document.getElementById('customer-search').value.toLowerCase();
        const rows = document.querySelectorAll('#customer-balances-table tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    };
}

function showSalesInvoices() {
    const reportContent = document.getElementById('report-content');
    const data = sampleData.salesInvoices;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    // حساب إحصائيات الفواتير
    const totalInvoices = data.length;
    const paidInvoices = data.filter(invoice => invoice.status === 'مدفوعة').length;
    const pendingInvoices = data.filter(invoice => invoice.status === 'معلقة').length;
    
    // حساب إجمالي المبيعات
    const totalSales = data.reduce((sum, invoice) => sum + invoice.total, 0);
    const paidSales = data.filter(invoice => invoice.status === 'مدفوعة')
                          .reduce((sum, invoice) => sum + invoice.total, 0);
    const pendingSales = data.filter(invoice => invoice.status === 'معلقة')
                            .reduce((sum, invoice) => sum + invoice.total, 0);
    
    // إنشاء بطاقات الإحصائيات
    let html = `
        <div class="report-header-info">
            <h4>فواتير المبيعات</h4>
            <p>الفترة: من ${startDate} إلى ${endDate}</p>
        </div>
        
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card total">
                    <div class="card-icon"><i class="fas fa-file-invoice"></i></div>
                    <div class="card-content">
                        <h5>إجمالي الفواتير</h5>
                        <div class="card-value">${totalInvoices}</div>
                        <div class="card-amount">${totalSales.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
                <div class="summary-card paid">
                    <div class="card-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="card-content">
                        <h5>الفواتير المدفوعة</h5>
                        <div class="card-value">${paidInvoices}</div>
                        <div class="card-amount">${paidSales.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
                <div class="summary-card pending">
                    <div class="card-icon"><i class="fas fa-clock"></i></div>
                    <div class="card-content">
                        <h5>الفواتير المعلقة</h5>
                        <div class="card-value">${pendingInvoices}</div>
                        <div class="card-amount">${pendingSales.toLocaleString('ar-SA')} ر.س</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="report-filter-bar">
            <div class="filter-item">
                <label>تصفية حسب الحالة:</label>
                <select id="invoice-status-filter" onchange="filterInvoicesByStatus()">
                    <option value="all">جميع الفواتير</option>
                    <option value="paid">المدفوعة فقط</option>
                    <option value="pending">المعلقة فقط</option>
                </select>
            </div>
            <div class="search-box">
                <input type="text" id="invoice-search" placeholder="بحث في الفواتير..." oninput="searchInvoices()">
                <i class="fas fa-search"></i>
            </div>
        </div>
        
        <table class="report-table" id="sales-invoices-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>رقم الفاتورة</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.forEach(row => {
        html += `
            <tr data-status="${row.status === 'مدفوعة' ? 'paid' : 'pending'}">
                <td>${row.date}</td>
                <td>${row.invoice}</td>
                <td>${row.customer}</td>
                <td>${row.total.toLocaleString('ar-SA')} ر.س</td>
                <td><span class="status-badge ${row.status === 'مدفوعة' ? 'paid' : 'pending'}">${row.status}</span></td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3">الإجمالي</td>
                    <td colspan="2">${totalSales.toLocaleString('ar-SA')} ر.س</td>
                </tr>
            </tfoot>
        </table>
    `;
    
    reportContent.innerHTML = html;
    
    // إضافة وظائف التصفية والبحث
    window.filterInvoicesByStatus = function() {
        const filter = document.getElementById('invoice-status-filter').value;
        const rows = document.querySelectorAll('#sales-invoices-table tbody tr');
        
        rows.forEach(row => {
            if (filter === 'all' || row.getAttribute('data-status') === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    };
    
    window.searchInvoices = function() {
        const searchTerm = document.getElementById('invoice-search').value.toLowerCase();
        const rows = document.querySelectorAll('#sales-invoices-table tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    };
}

function showGenericReport() {
    const reportContent = document.getElementById('report-content');
    const reportTitle = document.getElementById('report-title').textContent;
    
    // تحديد التقارير المتاحة حالياً
    const availableReports = [
        { id: 'account-statement', name: 'كشف حساب', icon: 'file-invoice-dollar', category: 'accounts' },
        { id: 'customer-balances', name: 'أرصدة العملاء', icon: 'users', category: 'accounts' },
        { id: 'sales-invoices', name: 'فواتير المبيعات', icon: 'shopping-cart', category: 'sales' }
    ];
    
    // إنشاء روابط للتقارير المتاحة
    let availableReportsHTML = '';
    availableReports.forEach(report => {
        availableReportsHTML += `
            <li style="margin: 12px 0; cursor: pointer;" onclick="selectReportCategory('${report.category}'); setTimeout(() => selectReport('${report.id}'), 100);">
                <i class="fas fa-${report.icon}" style="margin-left: 8px; color: #27ae60;"></i>
                <span style="color: #2980b9; text-decoration: underline;">${report.name}</span>
            </li>
        `;
    });
    
    reportContent.innerHTML = `
        <div class="loading-message" style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-chart-bar" style="font-size: 48px; margin-bottom: 15px; color: #3498db;"></i>
            <h3>تقرير ${reportTitle}</h3>
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 15px; margin: 20px auto; max-width: 600px; border-right: 4px solid #f39c12;">
                <p style="font-size: 16px; font-weight: 500; color: #e67e22;">هذا التقرير قيد التطوير</p>
                <p>سيتم إضافة البيانات والوظائف الكاملة لهذا التقرير في التحديثات القادمة.</p>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; max-width: 600px; margin-left: auto; margin-right: auto; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                <p style="font-weight: bold; font-size: 16px; color: #2c3e50; margin-bottom: 15px;">
                    <i class="fas fa-lightbulb" style="color: #f1c40f; margin-left: 8px;"></i>
                    يمكنك تجربة التقارير التالية المتاحة حالياً:
                </p>
                <ul style="text-align: right; list-style-type: none; padding: 0;">
                    ${availableReportsHTML}
                </ul>
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px dashed #ddd;">
                    <p style="font-size: 14px; color: #7f8c8d;">
                        <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
                        انقر على أي تقرير من القائمة أعلاه للانتقال إليه مباشرة
                    </p>
                </div>
            </div>
        </div>
    `;
}

function backToReportSelection() {
    document.querySelector('.report-selector').style.display = 'block';
    document.getElementById('selected-report').style.display = 'none';
    currentCategory = null;
    currentReport = null;
}

// دالة مساعدة للتحقق من وجود بيانات للتصدير
function hasExportableData() {
    const table = document.querySelector('#report-content table');
    const reportContent = document.getElementById('report-content');

    if (!table && (!reportContent || !reportContent.innerHTML.trim())) {
        return false;
    }

    // التحقق من وجود صفوف في الجدول
    if (table) {
        const tbody = table.querySelector('tbody');
        if (!tbody || tbody.children.length === 0) {
            return false;
        }
    }

    return true;
}

// دالة لتنظيف البيانات للتصدير
function cleanDataForExport(text) {
    return text.replace(/\s+/g, ' ').trim();
}

// دالة لتحويل الجدول إلى بيانات منظمة
function getTableData() {
    const table = document.querySelector('#report-content table');
    if (!table) return null;

    const data = [];
    const headers = [];

    // استخراج العناوين
    const headerRow = table.querySelector('thead tr');
    if (headerRow) {
        Array.from(headerRow.children).forEach(th => {
            headers.push(cleanDataForExport(th.textContent));
        });
        data.push(headers);
    }

    // استخراج البيانات
    const tbody = table.querySelector('tbody');
    if (tbody) {
        Array.from(tbody.children).forEach(tr => {
            const row = [];
            Array.from(tr.children).forEach(td => {
                row.push(cleanDataForExport(td.textContent));
            });
            data.push(row);
        });
    }

    return data;
}

function printReport() {
    if (!currentReport) {
        alert('يرجى اختيار تقرير أولاً');
        return;
    }

    if (!hasExportableData()) {
        alert('لا توجد بيانات للطباعة. يرجى إنشاء التقرير أولاً.');
        return;
    }

    const reportTitle = document.getElementById('report-title').textContent;
    const reportContent = document.getElementById('report-content').innerHTML;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;

    // إنشاء نافذة طباعة جديدة مع خصائص محددة
    const printWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes,toolbar=yes,menubar=yes');

    if (!printWindow) {
        alert('تم حظر النوافذ المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
        return;
    }

    const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>طباعة - ${reportTitle}</title>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            <style>
                body {
                    font-family: 'Cairo', 'Segoe UI', Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                    line-height: 1.6;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    background: white;
                }
                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #2c3e50;
                    padding-bottom: 20px;
                }
                .print-header h1 {
                    color: #2c3e50;
                    margin: 0;
                    font-size: 24px;
                    font-weight: bold;
                }
                .print-header h2 {
                    color: #34495e;
                    margin: 10px 0 0 0;
                    font-size: 20px;
                    font-weight: 600;
                }
                .print-info {
                    margin: 20px 0;
                    font-size: 14px;
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #e9ecef;
                }
                .print-info p {
                    margin: 5px 0;
                    font-weight: 500;
                }
                .print-content {
                    margin: 20px 0;
                }

                /* تنسيق مطابق للبرنامج */
                .report-header {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    border: 1px solid #e9ecef;
                }
                .report-header h2 {
                    color: #2c3e50;
                    margin: 0 0 15px 0;
                    font-size: 22px;
                    font-weight: bold;
                    text-align: center;
                }
                .report-meta {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                    font-size: 14px;
                    color: #666;
                }
                .report-period {
                    background: #e3f2fd;
                    padding: 10px 15px;
                    border-radius: 5px;
                    border-right: 4px solid #2196f3;
                    margin: 15px 0;
                    font-weight: 500;
                }

                /* تنسيق الجداول مطابق للبرنامج */
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    font-size: 13px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border-radius: 8px;
                    overflow: hidden;
                }
                table thead {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                table th {
                    padding: 12px 10px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 14px;
                    border: none;
                    position: relative;
                }
                table th:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 20%;
                    height: 60%;
                    width: 1px;
                    background: rgba(255,255,255,0.3);
                }
                table tbody tr {
                    border-bottom: 1px solid #e9ecef;
                    transition: background-color 0.2s ease;
                }
                table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                table tbody tr:hover {
                    background: #e3f2fd;
                }
                table td {
                    padding: 10px;
                    text-align: center;
                    border: none;
                    color: #495057;
                }
                table tfoot {
                    background: #e9ecef;
                    font-weight: bold;
                }
                table tfoot td {
                    padding: 12px 10px;
                    border-top: 2px solid #dee2e6;
                    color: #2c3e50;
                    font-weight: 600;
                }

                /* تنسيق خاص للمبالغ */
                .amount {
                    font-weight: 600;
                    color: #2c3e50;
                }
                .amount.positive {
                    color: #27ae60;
                }
                .amount.negative {
                    color: #e74c3c;
                }

                /* تنسيق التواريخ */
                .date-cell {
                    font-family: 'Courier New', monospace;
                    font-weight: 500;
                }

                /* تنسيق أزرار التحكم */
                .print-controls {
                    text-align: center;
                    margin: 20px 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    border-radius: 10px;
                    border: 1px solid #ddd;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .print-controls h3 {
                    color: #2c3e50;
                    margin: 0 0 10px 0;
                    font-size: 18px;
                }
                .print-controls p {
                    color: #666;
                    margin: 0 0 15px 0;
                    font-size: 14px;
                }
                .print-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    margin: 0 8px;
                    font-family: inherit;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                }
                .print-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                }
                .close-btn {
                    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
                }

                /* تنسيق خاص للطباعة */
                @media print {
                    body {
                        margin: 0 !important;
                        padding: 15mm !important;
                        font-size: 11px !important;
                        opacity: 1 !important;
                        background: white !important;
                        color: black !important;
                    }
                    .print-controls {
                        display: none !important;
                    }
                    .print-header {
                        border-bottom: 2px solid #000 !important;
                        margin-bottom: 20px !important;
                    }
                    .print-header h1, .print-header h2 {
                        color: #000 !important;
                    }
                    .print-info {
                        background: #f5f5f5 !important;
                        border: 1px solid #ccc !important;
                        -webkit-print-color-adjust: exact;
                    }
                    .report-header {
                        background: #f5f5f5 !important;
                        border: 1px solid #ccc !important;
                        -webkit-print-color-adjust: exact;
                    }
                    .report-period {
                        background: #f0f8ff !important;
                        border-right: 3px solid #000 !important;
                        -webkit-print-color-adjust: exact;
                    }
                    table {
                        box-shadow: none !important;
                        border: 2px solid #000 !important;
                    }
                    table thead {
                        background: #e0e0e0 !important;
                        color: #000 !important;
                        -webkit-print-color-adjust: exact;
                    }
                    table th {
                        background: #e0e0e0 !important;
                        color: #000 !important;
                        border: 1px solid #000 !important;
                        -webkit-print-color-adjust: exact;
                    }
                    table td {
                        border: 1px solid #666 !important;
                        color: #000 !important;
                    }
                    table tbody tr:nth-child(even) {
                        background: #f9f9f9 !important;
                        -webkit-print-color-adjust: exact;
                    }
                    table tfoot {
                        background: #e0e0e0 !important;
                        -webkit-print-color-adjust: exact;
                    }
                    table tfoot td {
                        border: 2px solid #000 !important;
                        font-weight: bold !important;
                        color: #000 !important;
                    }
                    .amount {
                        color: #000 !important;
                        font-weight: bold !important;
                    }
                    /* فواصل الصفحات */
                    .page-break {
                        page-break-before: always;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-controls">
                <h3>🖨️ نافذة الطباعة</h3>
                <p>اضغط على زر "طباعة" لفتح نافذة الطباعة، أو استخدم Ctrl+P</p>
                <button class="print-btn" onclick="safePrint()">
                    🖨️ طباعة
                </button>
                <button class="print-btn" onclick="printAndClose()" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    🖨️ طباعة وإغلاق
                </button>
                <button class="print-btn close-btn" onclick="window.close()">
                    ❌ إغلاق
                </button>
            </div>

            <div class="print-header">
                <h1>نظام إدارة الأعمال</h1>
                <h2>${reportTitle}</h2>
            </div>

            <div class="print-info">
                <p><strong>📅 تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</p>
                <p><strong>📊 الفترة:</strong> من ${startDate || 'غير محدد'} إلى ${endDate || 'غير محدد'}</p>
                <p><strong>👤 المستخدم:</strong> مدير النظام</p>
            </div>

            <div class="print-content">
                ${formatAccountStatement(enhanceReportContent(reportContent), reportTitle)}
            </div>

            <script>
                // التأكد من تحميل المحتوى قبل إظهار النافذة
                window.onload = function() {
                    setTimeout(function() {
                        document.body.style.opacity = '1';
                        // التركيز على النافذة
                        window.focus();
                    }, 200);
                };

                // دالة آمنة للطباعة
                function safePrint() {
                    try {
                        window.print();
                    } catch (error) {
                        alert('حدث خطأ في الطباعة: ' + error.message);
                        console.error('Print error:', error);
                    }
                }

                // دالة للطباعة والإغلاق
                function printAndClose() {
                    try {
                        window.print();
                        // إغلاق النافذة بعد فترة قصيرة للسماح بإتمام الطباعة
                        setTimeout(function() {
                            window.close();
                        }, 1000);
                    } catch (error) {
                        alert('حدث خطأ في الطباعة: ' + error.message);
                        console.error('Print error:', error);
                    }
                }

                // ربط اختصار لوحة المفاتيح للطباعة
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.key === 'p') {
                        e.preventDefault();
                        safePrint();
                    }
                    // إغلاق النافذة بـ Escape
                    if (e.key === 'Escape') {
                        window.close();
                    }
                });

                // منع إغلاق النافذة تلقائياً
                window.addEventListener('beforeunload', function(e) {
                    // يمكن إضافة تأكيد هنا إذا لزم الأمر
                });
            </script>
        </body>
        </html>
    `;

    try {
        printWindow.document.write(printContent);
        printWindow.document.close();

        // التأكد من تحميل النافذة والتركيز عليها
        setTimeout(function() {
            if (!printWindow.closed) {
                printWindow.focus();
            }
        }, 300);

        showSuccessMessage('تم فتح نافذة الطباعة بنجاح');

    } catch (error) {
        console.error('خطأ في فتح نافذة الطباعة:', error);
        showErrorMessage('حدث خطأ في فتح نافذة الطباعة: ' + error.message);
        if (printWindow && !printWindow.closed) {
            printWindow.close();
        }
    }
}

// دالة لتحسين محتوى التقرير للطباعة
function enhanceReportContent(content) {
    if (!content) return '';

    // تحسين الجداول
    let enhancedContent = content.replace(/<table/g, '<table class="enhanced-table"');

    // تحسين خلايا التواريخ
    enhancedContent = enhancedContent.replace(/(\d{4}-\d{2}-\d{2})/g, '<span class="date-cell">$1</span>');

    // تحسين المبالغ المالية
    enhancedContent = enhancedContent.replace(/(\d+[,.]?\d*)\s*(ريال|درهم|دينار|جنيه)/g, '<span class="amount">$1 $2</span>');
    enhancedContent = enhancedContent.replace(/(\d+[,.]?\d*)\s*(?=<\/td>)/g, '<span class="amount">$1</span>');

    // تحسين المبالغ السالبة والموجبة
    enhancedContent = enhancedContent.replace(/<td[^>]*>([^<]*\d+[,.]?\d*[^<]*)<\/td>/g, function(match, amount) {
        if (amount.includes('-') || amount.includes('(')) {
            return match.replace(amount, `<span class="amount negative">${amount}</span>`);
        } else if (/\d/.test(amount)) {
            return match.replace(amount, `<span class="amount positive">${amount}</span>`);
        }
        return match;
    });

    // إضافة أيقونات للعناوين
    enhancedContent = enhancedContent.replace(/<h2([^>]*)>/g, '<h2$1><i class="fas fa-chart-bar"></i> ');
    enhancedContent = enhancedContent.replace(/<h3([^>]*)>/g, '<h3$1><i class="fas fa-list"></i> ');

    // تحسين النصوص الفارغة
    enhancedContent = enhancedContent.replace(/<td[^>]*>\s*-\s*<\/td>/g, '<td style="color: #999; font-style: italic; text-align: center;">لا يوجد</td>');
    enhancedContent = enhancedContent.replace(/<td[^>]*>\s*<\/td>/g, '<td style="color: #999; font-style: italic; text-align: center;">فارغ</td>');

    // تحسين رؤوس الجداول
    enhancedContent = enhancedContent.replace(/<thead[^>]*>/g, '<thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">');

    // تحسين صفوف الجداول
    enhancedContent = enhancedContent.replace(/<tbody[^>]*>/g, '<tbody>');
    let rowIndex = 0;
    enhancedContent = enhancedContent.replace(/<tr(?![^>]*thead)([^>]*)>/g, function(match, attrs) {
        if (match.includes('thead') || match.includes('tfoot')) return match;
        rowIndex++;
        const bgStyle = rowIndex % 2 === 0 ? 'background: #f8f9fa;' : '';
        return `<tr style="${bgStyle}" ${attrs}>`;
    });

    // تحسين تذييل الجداول
    enhancedContent = enhancedContent.replace(/<tfoot[^>]*>/g, '<tfoot style="background: #e9ecef; font-weight: bold;">');

    return enhancedContent;
}

// دالة خاصة لتنسيق كشف الحساب
function formatAccountStatement(content, reportTitle) {
    if (!content || !reportTitle.includes('كشف حساب')) return content;

    // إضافة تنسيق خاص لكشف الحساب
    const accountStatementHeader = `
        <div class="account-statement-header">
            <div class="company-info">
                <h1>نظام إدارة الأعمال</h1>
                <p>تقرير مالي شامل</p>
            </div>
            <div class="report-info">
                <h2>${reportTitle}</h2>
                <div class="report-meta">
                    <span>📅 الفترة: من ${document.getElementById('report-start-date')?.value || 'غير محدد'} إلى ${document.getElementById('report-end-date')?.value || 'غير محدد'}</span>
                </div>
            </div>
        </div>
        <div class="divider"></div>
    `;

    // إضافة ستايل خاص لكشف الحساب
    const accountStatementStyle = `
        <style>
            .account-statement-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding: 20px;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                border-radius: 10px;
                border: 1px solid #ddd;
            }
            .company-info h1 {
                color: #2c3e50;
                margin: 0;
                font-size: 24px;
                font-weight: bold;
            }
            .company-info p {
                color: #666;
                margin: 5px 0 0 0;
                font-size: 14px;
            }
            .report-info h2 {
                color: #2c3e50;
                margin: 0;
                font-size: 20px;
                text-align: left;
            }
            .report-meta {
                color: #666;
                font-size: 14px;
                margin-top: 5px;
                text-align: left;
            }
            .divider {
                height: 3px;
                background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                margin: 20px 0;
                border-radius: 2px;
            }
            @media print {
                .account-statement-header {
                    background: #f5f5f5 !important;
                    border: 1px solid #ccc !important;
                    -webkit-print-color-adjust: exact;
                }
                .divider {
                    background: #333 !important;
                    -webkit-print-color-adjust: exact;
                }
            }

            /* تنسيق إضافي لكشف الحساب */
            .account-info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 15px 0;
            }
            .info-item {
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 5px;
                border-right: 3px solid #007bff;
            }
            .account-statement-table .text-danger {
                color: #dc3545 !important;
            }
            .account-statement-table .text-success {
                color: #28a745 !important;
            }
            .opening-balance-row {
                background: #e3f2fd !important;
                font-weight: 600;
            }
            .totals-row {
                background: #f8f9fa !important;
                border-top: 2px solid #dee2e6 !important;
                font-weight: bold;
            }
            .report-summary {
                margin-top: 30px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            .summary-cards {
                display: flex;
                gap: 20px;
                justify-content: center;
                flex-wrap: wrap;
            }
            .summary-card {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 20px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                min-width: 200px;
            }
            .summary-card.debit {
                border-right: 4px solid #dc3545;
            }
            .summary-card.credit {
                border-right: 4px solid #28a745;
            }
            .summary-card.balance {
                border-right: 4px solid #007bff;
            }
            .summary-card i {
                font-size: 24px;
                color: #666;
            }
            .summary-info h5 {
                margin: 0 0 5px 0;
                color: #333;
                font-size: 14px;
            }
            .summary-info .amount {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
            }
            .no-data-message {
                text-align: center;
                padding: 60px 20px;
                color: #666;
                background: #f8f9fa;
                border-radius: 10px;
                margin: 20px 0;
            }
            .no-data-message i {
                font-size: 48px;
                color: #ddd;
                margin-bottom: 20px;
            }
            .no-data-message h4 {
                color: #999;
                margin: 10px 0;
            }

            @media print {
                .account-info-grid {
                    display: block !important;
                }
                .info-item {
                    display: inline-block;
                    margin: 5px 10px 5px 0;
                    background: #f5f5f5 !important;
                    border-right: 2px solid #000 !important;
                    -webkit-print-color-adjust: exact;
                }
                .summary-cards {
                    display: none !important;
                }
                .report-summary {
                    display: none !important;
                }
            }
        </style>
    `;

    return accountStatementStyle + accountStatementHeader + content;
}

// دوال مساعدة للحصول على بيانات الحسابات
function getAllAccounts() {
    // استيراد بيانات دليل الحسابات من ملف accounting.js
    if (typeof chartOfAccounts !== 'undefined') {
        return flattenAccountsForReports(chartOfAccounts);
    }

    // بيانات افتراضية إذا لم تكن متوفرة
    return [
        { id: 111, code: '1101', name: 'النقدية في الصندوق', type: 'assets', balance: 15000.00, status: 'active' },
        { id: 112, code: '1102', name: 'البنك - الحساب الجاري', type: 'assets', balance: 85000.00, status: 'active' },
        { id: 113, code: '1103', name: 'العملاء', type: 'assets', balance: 45000.00, status: 'active' },
        { id: 121, code: '1201', name: 'المخزون', type: 'assets', balance: 120000.00, status: 'active' },
        { id: 211, code: '2101', name: 'الموردون', type: 'liabilities', balance: 25000.00, status: 'active' },
        { id: 212, code: '2102', name: 'مصروفات مستحقة', type: 'liabilities', balance: 8000.00, status: 'active' },
        { id: 311, code: '3101', name: 'رأس المال', type: 'equity', balance: 200000.00, status: 'active' },
        { id: 411, code: '4101', name: 'مبيعات', type: 'revenue', balance: 150000.00, status: 'active' },
        { id: 511, code: '5101', name: 'تكلفة البضاعة المباعة', type: 'expenses', balance: 80000.00, status: 'active' },
        { id: 512, code: '5102', name: 'مصروفات إدارية', type: 'expenses', balance: 25000.00, status: 'active' }
    ];
}

function flattenAccountsForReports(accounts, result = []) {
    accounts.forEach(account => {
        result.push({
            id: account.id,
            code: account.code,
            name: account.name,
            type: account.type,
            balance: account.balance || 0,
            status: account.status || 'active',
            openingBalance: account.balance || 0
        });
        if (account.children && account.children.length > 0) {
            flattenAccountsForReports(account.children, result);
        }
    });
    return result;
}

function getAccountInfo(accountId) {
    const accounts = getAllAccounts();
    const account = accounts.find(acc => acc.id == accountId || acc.code == accountId);

    if (!account) {
        // إرجاع بيانات افتراضية محسنة إذا لم يتم العثور على الحساب
        return {
            id: 111,
            code: '1101',
            name: 'النقدية في الصندوق',
            type: 'assets',
            balance: 15000.00,
            openingBalance: 15000.00,
            lastUpdated: new Date().toLocaleDateString('ar-SA'),
            status: 'نشط',
            description: 'حساب نقدي للمعاملات اليومية',
            currency: 'ريال سعودي',
            accountManager: 'المدير المالي',
            creationDate: '01/01/2023',
            accountCategory: 'حسابات الميزانية'
        };
    }

    // إضافة معلومات إضافية للحساب
    return {
        ...account,
        lastUpdated: new Date().toLocaleDateString('ar-SA'),
        status: account.status || 'نشط',
        description: getAccountDescription(account.type, account.name),
        currency: 'ريال سعودي',
        accountManager: 'المدير المالي',
        creationDate: '01/01/2023',
        accountCategory: getAccountCategory(account.type)
    };
}

// دالة مساعدة للحصول على وصف الحساب بناءً على نوعه واسمه
function getAccountDescription(type, name) {
    const descriptions = {
        'assets': {
            'النقدية في الصندوق': 'حساب نقدي للمعاملات اليومية',
            'البنك - الحساب الجاري': 'حساب مصرفي للمعاملات البنكية',
            'العملاء': 'حساب لتتبع المبالغ المستحقة من العملاء',
            'المخزون': 'حساب لتتبع قيمة البضائع المتاحة للبيع'
        },
        'liabilities': {
            'الموردون': 'حساب لتتبع المبالغ المستحقة للموردين',
            'مصروفات مستحقة': 'حساب لتتبع المصروفات المستحقة الدفع'
        },
        'equity': {
            'رأس المال': 'حساب لتتبع استثمارات المالكين في الشركة'
        },
        'revenue': {
            'مبيعات': 'حساب لتتبع إيرادات المبيعات'
        },
        'expenses': {
            'تكلفة البضاعة المباعة': 'حساب لتتبع تكلفة البضائع المباعة',
            'مصروفات إدارية': 'حساب لتتبع المصروفات الإدارية والعمومية'
        }
    };
    
    // البحث عن وصف محدد للحساب
    if (descriptions[type] && descriptions[type][name]) {
        return descriptions[type][name];
    }
    
    // إرجاع وصف عام بناءً على نوع الحساب
    const generalDescriptions = {
        'assets': 'حساب لتتبع الأصول والممتلكات',
        'liabilities': 'حساب لتتبع الالتزامات والديون',
        'equity': 'حساب لتتبع حقوق المساهمين',
        'revenue': 'حساب لتتبع مصادر الدخل',
        'expenses': 'حساب لتتبع النفقات والمصروفات'
    };
    
    return generalDescriptions[type] || 'حساب لتتبع المعاملات المالية';
}

// دالة مساعدة للحصول على تصنيف الحساب
function getAccountCategory(type) {
    const categories = {
        'assets': 'حسابات الميزانية',
        'liabilities': 'حسابات الميزانية',
        'equity': 'حسابات الميزانية',
        'revenue': 'حسابات الدخل',
        'expenses': 'حسابات الدخل'
    };
    
    return categories[type] || 'غير مصنف';
}

/* تنسيقات النافذة المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 80%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

/* تنسيقات تفاصيل الحساب */
.account-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-weight: bold;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    color: #333;
}

.positive-balance {
    color: #28a745;
    font-weight: bold;
}

.negative-balance {
    color: #dc3545;
    font-weight: bold;
}

.account-description-box {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
}

.account-description-box h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

/* تنسيقات عناصر التصفية والبحث */
.report-filters {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter-label {
    font-weight: bold;
    margin-left: 5px;
}

.search-box {
    flex-grow: 1;
    max-width: 300px;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 8px 10px 8px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

function getAccountData(accountId) {
    // بيانات نموذجية للحركات المالية
    const sampleTransactions = {
        '111': [ // النقدية في الصندوق
            { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 15000, credit: 0 },
            { date: '2023-06-05', description: 'مبيعات نقدية', debit: 5000, credit: 0 },
            { date: '2023-06-10', description: 'سحب نقدي للبنك', debit: 0, credit: 10000 },
            { date: '2023-06-15', description: 'مبيعات نقدية', debit: 3000, credit: 0 },
            { date: '2023-06-20', description: 'دفع مصروفات', debit: 0, credit: 2000 },
            { date: '2023-06-25', description: 'مبيعات نقدية', debit: 4500, credit: 0 },
            { date: '2023-06-28', description: 'دفع رواتب', debit: 0, credit: 3500 }
        ],
        '112': [ // البنك
            { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 85000, credit: 0 },
            { date: '2023-06-10', description: 'إيداع من الصندوق', debit: 10000, credit: 0 },
            { date: '2023-06-12', description: 'تحصيل من عميل', debit: 15000, credit: 0 },
            { date: '2023-06-18', description: 'دفع للموردين', debit: 0, credit: 20000 },
            { date: '2023-06-25', description: 'رسوم بنكية', debit: 0, credit: 500 },
            { date: '2023-06-27', description: 'إيداع شيك', debit: 12000, credit: 0 },
            { date: '2023-06-30', description: 'سحب للصندوق', debit: 0, credit: 5000 }
        ],
        '113': [ // العملاء
            { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 45000, credit: 0 },
            { date: '2023-06-08', description: 'مبيعات آجلة', debit: 25000, credit: 0 },
            { date: '2023-06-12', description: 'تحصيل نقدي', debit: 0, credit: 15000 },
            { date: '2023-06-20', description: 'مبيعات آجلة', debit: 18000, credit: 0 },
            { date: '2023-06-28', description: 'تحصيل شيك', debit: 0, credit: 12000 }
        ],
        '121': [ // المخزون
            { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 120000, credit: 0 },
            { date: '2023-06-05', description: 'شراء بضاعة', debit: 35000, credit: 0 },
            { date: '2023-06-15', description: 'تكلفة البضاعة المباعة', debit: 0, credit: 28000 },
            { date: '2023-06-22', description: 'شراء بضاعة', debit: 42000, credit: 0 },
            { date: '2023-06-28', description: 'تكلفة البضاعة المباعة', debit: 0, credit: 32000 }
        ],
        '211': [ // الموردون
            { date: '2023-06-01', description: 'رصيد افتتاحي', debit: 0, credit: 25000 },
            { date: '2023-06-05', description: 'شراء بضاعة بالأجل', debit: 0, credit: 35000 },
            { date: '2023-06-18', description: 'دفع للموردين', debit: 20000, credit: 0 },
            { date: '2023-06-22', description: 'شراء بضاعة بالأجل', debit: 0, credit: 42000 }
        ],
        '411': [ // المبيعات
            { date: '2023-06-05', description: 'مبيعات نقدية', debit: 0, credit: 8000 },
            { date: '2023-06-08', description: 'مبيعات آجلة', debit: 0, credit: 25000 },
            { date: '2023-06-15', description: 'مبيعات نقدية', debit: 0, credit: 5000 },
            { date: '2023-06-20', description: 'مبيعات آجلة', debit: 0, credit: 18000 },
            { date: '2023-06-25', description: 'مبيعات نقدية', debit: 0, credit: 7500 }
        ]
    };

    // الحصول على معلومات الحساب
    const accountInfo = getAccountInfo(accountId);
    
    // إذا كان الحساب موجود في البيانات النموذجية، أعد بياناته
    if (sampleTransactions[accountInfo.code]) {
        return sampleTransactions[accountInfo.code];
    }
    
    // إذا لم يكن الحساب موجوداً، أنشئ بيانات افتراضية له
    const currentDate = new Date();
    const startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    
    // إنشاء 5 حركات افتراضية للحساب
    const defaultTransactions = [
        { 
            date: startDate.toISOString().split('T')[0], 
            description: 'رصيد افتتاحي', 
            debit: accountInfo.balance > 0 ? accountInfo.balance : 0, 
            credit: accountInfo.balance < 0 ? Math.abs(accountInfo.balance) : 0 
        }
    ];
    
    // إضافة 4 حركات عشوائية
    for (let i = 1; i <= 4; i++) {
        const transactionDate = new Date(startDate);
        transactionDate.setDate(startDate.getDate() + (i * 7)); // إضافة أسبوع لكل حركة
        
        const isDebit = Math.random() > 0.5;
        const amount = Math.floor(Math.random() * 10000) + 1000; // مبلغ عشوائي بين 1000 و 11000
        
        defaultTransactions.push({
            date: transactionDate.toISOString().split('T')[0],
            description: isDebit ? 'إيداع / إضافة' : 'سحب / خصم',
            debit: isDebit ? amount : 0,
            credit: !isDebit ? amount : 0
        });
    }
    
    return defaultTransactions;
}

function getAccountTypeArabic(type) {
    const types = {
        'assets': 'أصول',
        'liabilities': 'خصوم',
        'equity': 'حقوق ملكية',
        'revenue': 'إيرادات',
        'expenses': 'مصروفات'
    };
    return types[type] || type;
}

function formatCurrency(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return '0.00 ر.س';
    }
    return parseFloat(amount).toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' ر.س';
}

// دالة للطباعة السريعة (بدون نافذة جديدة)
function quickPrint() {
    if (!currentReport) {
        alert('يرجى اختيار تقرير أولاً');
        return;
    }

    if (!hasExportableData()) {
        alert('لا توجد بيانات للطباعة. يرجى إنشاء التقرير أولاً.');
        return;
    }

    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = [
        '.report-selector',
        '.report-actions',
        '.sidebar',
        '.header',
        'nav',
        '.print-controls'
    ];

    const hiddenElements = [];
    elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            if (el && el.style.display !== 'none') {
                hiddenElements.push({element: el, originalDisplay: el.style.display});
                el.style.display = 'none';
            }
        });
    });

    // إضافة ستايل خاص للطباعة
    const printStyle = document.createElement('style');
    printStyle.id = 'quick-print-style';
    printStyle.innerHTML = `
        @media print {
            body * { visibility: hidden; }
            #selected-report, #selected-report * { visibility: visible; }
            #selected-report {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            .report-actions { display: none !important; }
        }
    `;
    document.head.appendChild(printStyle);

    try {
        // طباعة الصفحة
        window.print();
        showSuccessMessage('تم إرسال التقرير للطباعة');
    } catch (error) {
        console.error('خطأ في الطباعة السريعة:', error);
        showErrorMessage('حدث خطأ في الطباعة: ' + error.message);
    } finally {
        // إعادة إظهار العناصر المخفية
        hiddenElements.forEach(item => {
            item.element.style.display = item.originalDisplay;
        });

        // إزالة ستايل الطباعة
        const styleElement = document.getElementById('quick-print-style');
        if (styleElement) {
            styleElement.remove();
        }
    }
}

function exportToExcel() {
    if (!currentReport) {
        alert('يرجى اختيار تقرير أولاً');
        return;
    }

    if (!hasExportableData()) {
        alert('لا توجد بيانات للتصدير. يرجى إنشاء التقرير أولاً.');
        return;
    }

    // إظهار حالة التحميل
    const exportBtn = document.querySelector('.export-btn.excel');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
    exportBtn.disabled = true;

    try {
        const reportTitle = document.getElementById('report-title').textContent;
        const tableData = getTableData();

        if (!tableData || tableData.length === 0) {
            alert('لا توجد بيانات صالحة للتصدير');
            return;
        }

        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

        // إضافة معلومات التقرير
        const reportInfo = [
            [reportTitle],
            [`تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}`],
            [`الفترة: من ${document.getElementById('report-start-date').value} إلى ${document.getElementById('report-end-date').value}`],
            [] // سطر فارغ
        ];

        // دمج معلومات التقرير مع البيانات
        const allData = [...reportInfo, ...tableData];

        // إنشاء worksheet من البيانات
        const ws = XLSX.utils.aoa_to_sheet(allData);

        // تحسين عرض الأعمدة
        const colWidths = [];
        if (tableData.length > 0) {
            for (let i = 0; i < tableData[0].length; i++) {
                let maxWidth = 10;
                for (let j = 0; j < tableData.length; j++) {
                    if (tableData[j][i]) {
                        maxWidth = Math.max(maxWidth, tableData[j][i].toString().length);
                    }
                }
                colWidths.push({ width: Math.min(maxWidth + 2, 50) });
            }
            ws['!cols'] = colWidths;
        }

        // إضافة الورقة إلى الكتاب
        XLSX.utils.book_append_sheet(wb, ws, 'التقرير');

        // تصدير الملف
        const fileName = `${reportTitle}_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        // عرض رسالة نجاح
        showSuccessMessage('تم تصدير التقرير إلى Excel بنجاح');

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        showErrorMessage('حدث خطأ أثناء تصدير التقرير إلى Excel: ' + error.message);
    } finally {
        // إعادة تعيين الزر
        const exportBtn = document.querySelector('.export-btn.excel');
        exportBtn.innerHTML = '<i class="fas fa-file-excel"></i> Excel';
        exportBtn.disabled = false;
    }
}

function exportToPDF() {
    if (!currentReport) {
        alert('يرجى اختيار تقرير أولاً');
        return;
    }

    if (!hasExportableData()) {
        alert('لا توجد بيانات للتصدير. يرجى إنشاء التقرير أولاً.');
        return;
    }

    // إظهار حالة التحميل
    const exportBtn = document.querySelector('.export-btn.pdf');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
    exportBtn.disabled = true;

    try {
        const reportTitle = document.getElementById('report-title').textContent;
        const reportContent = document.getElementById('report-content');

        if (!reportContent || !reportContent.innerHTML.trim()) {
            alert('لا يوجد محتوى للتصدير');
            return;
        }

        // إنشاء عنصر مؤقت للطباعة
        const printElement = document.createElement('div');
        printElement.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 800px;
            background: white;
            padding: 20px;
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        `;

        printElement.innerHTML = `
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 15px;">
                <h1 style="color: #2c3e50; margin: 0 0 10px 0;">${reportTitle}</h1>
                <div style="color: #666; font-size: 14px;">تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}</div>
                <div style="color: #666; font-size: 14px;">الفترة: من ${document.getElementById('report-start-date').value} إلى ${document.getElementById('report-end-date').value}</div>
            </div>
            ${reportContent.innerHTML}
        `;

        document.body.appendChild(printElement);

        // تحويل إلى PDF باستخدام html2canvas و jsPDF
        html2canvas(printElement, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        }).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');

            const imgWidth = 210; // عرض A4 بالمليمتر
            const pageHeight = 295; // ارتفاع A4 بالمليمتر
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;

            let position = 0;

            // إضافة الصفحة الأولى
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // إضافة صفحات إضافية إذا لزم الأمر
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // حفظ الملف
            const fileName = `${reportTitle}_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

            // إزالة العنصر المؤقت
            document.body.removeChild(printElement);

            // عرض رسالة نجاح
            showSuccessMessage('تم تصدير التقرير إلى PDF بنجاح');

        }).catch(error => {
            console.error('خطأ في تصدير PDF:', error);
            document.body.removeChild(printElement);
            showErrorMessage('حدث خطأ أثناء تصدير التقرير إلى PDF: ' + error.message);
        }).finally(() => {
            // إعادة تعيين الزر
            const exportBtn = document.querySelector('.export-btn.pdf');
            exportBtn.innerHTML = '<i class="fas fa-file-pdf"></i> PDF';
            exportBtn.disabled = false;
        });

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showErrorMessage('حدث خطأ أثناء تصدير التقرير إلى PDF: ' + error.message);

        // إعادة تعيين الزر في حالة الخطأ
        const exportBtn = document.querySelector('.export-btn.pdf');
        exportBtn.innerHTML = '<i class="fas fa-file-pdf"></i> PDF';
        exportBtn.disabled = false;
    }
}

// دالة مساعدة لعرض رسائل النجاح والخطأ
function showMessage(message, type = 'success') {
    const bgColor = type === 'success' ? '#27ae60' : '#e74c3c';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 9999;
        font-family: 'Cairo', Arial, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;

    messageDiv.innerHTML = `
        <i class="fas ${icon}" style="margin-left: 8px;"></i>
        ${message}
    `;

    // إضافة CSS للحركة
    if (!document.getElementById('success-message-style')) {
        const style = document.createElement('style');
        style.id = 'success-message-style';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideIn 0.3s ease-out reverse';
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 300);
        }
    }, 3000);
}

// دوال مساعدة
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showErrorMessage(message) {
    showMessage(message, 'error');
}

// دالة تحميل الحسابات إلى قائمة الاختيار
function loadAccountsToSelector() {
    const accountSelect = document.getElementById('account-select');
    if (!accountSelect) return;

    // مسح الخيارات الحالية
    accountSelect.innerHTML = '<option value="">-- اختر الحساب --</option>';

    // الحصول على جميع الحسابات
    const accounts = getAllAccounts();

    // تجميع الحسابات حسب النوع
    const accountsByType = {
        'assets': [],
        'liabilities': [],
        'equity': [],
        'revenue': [],
        'expenses': []
    };

    accounts.forEach(account => {
        if (account.status === 'active') {
            accountsByType[account.type].push(account);
        }
    });

    // إضافة الحسابات مجمعة حسب النوع
    const typeNames = {
        'assets': '🏦 الأصول',
        'liabilities': '📋 الخصوم',
        'equity': '💰 حقوق الملكية',
        'revenue': '📈 الإيرادات',
        'expenses': '📉 المصروفات'
    };

    Object.keys(accountsByType).forEach(type => {
        if (accountsByType[type].length > 0) {
            // إضافة مجموعة
            const optgroup = document.createElement('optgroup');
            optgroup.label = typeNames[type];

            accountsByType[type].forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = `${account.code} - ${account.name}`;
                option.setAttribute('data-code', account.code);
                option.setAttribute('data-type', account.type);
                optgroup.appendChild(option);
            });

            accountSelect.appendChild(optgroup);
        }
    });
}

// تحديث دالة setupEventListeners لتشمل تحديث الحسابات
function updateSetupEventListeners() {
    // إضافة مستمع لتحديث قائمة الحسابات عند تغيير نوع التقرير
    const reportCards = document.querySelectorAll('.report-card');
    reportCards.forEach(card => {
        card.addEventListener('click', function() {
            // تحديث قائمة الحسابات عند اختيار تقرير جديد
            setTimeout(() => {
                loadAccountsToSelector();
            }, 100);
        });
    });
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التواريخ الافتراضية
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const startDateInput = document.getElementById('report-start-date');
    const endDateInput = document.getElementById('report-end-date');

    if (startDateInput) {
        startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
    }
    if (endDateInput) {
        endDateInput.value = today.toISOString().split('T')[0];
    }

    // تحميل قائمة الحسابات
    loadAccountsToSelector();

    // إضافة مستمعي الأحداث
    setupEventListeners();
    updateSetupEventListeners();

    // إعداد البحث في التقارير
    setupReportSearch();
});

// دالة لإعداد البحث في التقارير
function setupReportSearch() {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterReportData(this.value);
            showReportSearchSuggestions(this.value);
        });

        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                filterReportData(this.value);
            }
        });

        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideReportSearchSuggestions();
            }, 200);
        });
    }
}

// دالة لتصفية بيانات التقرير
function filterReportData(searchTerm) {
    if (!searchTerm || searchTerm.length < 1) {
        showAllReportRows();
        return;
    }

    const table = document.querySelector('#report-content table');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const rows = tbody.querySelectorAll('tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let matchFound = false;

        cells.forEach(cell => {
            const cellText = cell.textContent.toLowerCase();
            if (cellText.includes(searchTerm.toLowerCase())) {
                matchFound = true;
            }
        });

        if (matchFound) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // تحديث عداد النتائج
    updateReportResultsCount(visibleCount, rows.length);
}

// دالة لإظهار جميع صفوف التقرير
function showAllReportRows() {
    const table = document.querySelector('#report-content table');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const rows = tbody.querySelectorAll('tr');
    rows.forEach(row => {
        row.style.display = '';
    });

    // إخفاء عداد النتائج
    const resultsCounter = document.getElementById('report-results-counter');
    if (resultsCounter) {
        resultsCounter.style.display = 'none';
    }
}

// دالة لتحديث عداد النتائج
function updateReportResultsCount(visible, total) {
    let resultsCounter = document.getElementById('report-results-counter');
    if (!resultsCounter) {
        resultsCounter = document.createElement('div');
        resultsCounter.id = 'report-results-counter';
        resultsCounter.style.cssText = `
            background: #e3f2fd;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
            color: #1976d2;
            border-right: 4px solid #2196f3;
        `;

        const reportContent = document.getElementById('report-content');
        if (reportContent && reportContent.firstChild) {
            reportContent.insertBefore(resultsCounter, reportContent.firstChild);
        }
    }

    resultsCounter.innerHTML = `
        <i class="fas fa-search"></i>
        عرض ${visible} من أصل ${total} نتيجة
    `;
    resultsCounter.style.display = 'block';
}

// دالة لعرض اقتراحات البحث في التقارير
function showReportSearchSuggestions(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) {
        hideReportSearchSuggestions();
        return;
    }

    // إنشاء قائمة الاقتراحات إذا لم تكن موجودة
    let suggestionsContainer = document.getElementById('report-search-suggestions');
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'report-search-suggestions';
        suggestionsContainer.className = 'report-search-suggestions';

        const searchBox = document.querySelector('.report-search-section .search-box');
        if (searchBox) {
            searchBox.appendChild(suggestionsContainer);
        }
    }

    // الحصول على البيانات المطابقة
    const matchingData = getMatchingReportData(searchTerm.toLowerCase());

    if (matchingData.length === 0) {
        hideReportSearchSuggestions();
        return;
    }

    // إنشاء HTML للاقتراحات
    const suggestionsHTML = matchingData.slice(0, 5).map(item => `
        <div class="report-suggestion-item" onclick="selectReportSuggestion('${item.text}')">
            <div class="report-suggestion-name">${highlightReportMatch(item.text, searchTerm)}</div>
            <div class="report-suggestion-details">${item.context}</div>
        </div>
    `).join('');

    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

// دالة لإخفاء اقتراحات البحث
function hideReportSearchSuggestions() {
    const suggestionsContainer = document.getElementById('report-search-suggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

// دالة للحصول على البيانات المطابقة في التقرير
function getMatchingReportData(searchTerm) {
    const table = document.querySelector('#report-content table');
    if (!table) return [];

    const matchingData = [];
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach((row, rowIndex) => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, cellIndex) => {
            const cellText = cell.textContent.trim();
            if (cellText.toLowerCase().includes(searchTerm)) {
                // تحديد السياق بناءً على موقع الخلية
                let context = '';
                const headers = table.querySelectorAll('thead th');
                if (headers[cellIndex]) {
                    context = `${headers[cellIndex].textContent} - الصف ${rowIndex + 1}`;
                }

                matchingData.push({
                    text: cellText,
                    context: context,
                    row: rowIndex,
                    cell: cellIndex
                });
            }
        });
    });

    // إزالة التكرارات
    const uniqueData = [];
    const seen = new Set();

    matchingData.forEach(item => {
        const key = item.text.toLowerCase();
        if (!seen.has(key)) {
            seen.add(key);
            uniqueData.push(item);
        }
    });

    return uniqueData;
}

// دالة لتمييز النص المطابق في التقارير
function highlightReportMatch(text, searchTerm) {
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
}

// دالة لاختيار اقتراح من التقرير
function selectReportSuggestion(suggestionText) {
    const searchInput = document.getElementById('report-search-input');
    if (searchInput) {
        searchInput.value = suggestionText;
        filterReportData(suggestionText);
        hideReportSearchSuggestions();
    }
}

// دالة لإظهار/إخفاء خانة البحث حسب نوع التقرير
function toggleReportSearch(show = true) {
    const searchSection = document.getElementById('report-search-section');
    if (searchSection) {
        searchSection.style.display = show ? 'block' : 'none';
    }
}

// تحديث دالة generateReport لإظهار خانة البحث
const originalGenerateReport = generateReport;
generateReport = function() {
    originalGenerateReport.call(this);

    // إظهار خانة البحث بعد إنشاء التقرير
    setTimeout(() => {
        const reportContent = document.getElementById('report-content');
        if (reportContent && reportContent.querySelector('table')) {
            toggleReportSearch(true);
        }
    }, 100);
};
