@echo off
R<PERSON> Batch script to start a local server and open launcher.html

echo Starting local server and opening launcher.html...

REM Get the current directory
set CURRENT_DIR=%~dp0

REM Build the path to launcher.html
set LAUNCHER_PATH=%CURRENT_DIR%launcher.html

REM Check if the file exists
if exist "%LAUNCHER_PATH%" (
    echo Opening launcher at: %LAUNCHER_PATH%
    
    REM Open the launcher in the default browser
    start "" "%LAUNCHER_PATH%"
    
    echo Launcher opened successfully!
) else (
    echo Launcher file not found at: %LAUNCHER_PATH%
    exit /b 1
)

exit /b 0