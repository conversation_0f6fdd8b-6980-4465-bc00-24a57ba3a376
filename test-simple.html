<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التكامل البسيط</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .step-card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-left: 15px;
        }
        
        .step-title {
            display: inline-block;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .step-content {
            margin-right: 55px;
            line-height: 1.6;
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn.primary { background: rgba(0, 123, 255, 0.3); border-color: #007bff; }
        .btn.success { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
        .btn.warning { background: rgba(255, 193, 7, 0.3); border-color: #ffc107; }
        
        .status-box {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        
        .navigation {
            text-align: center;
            margin: 30px 0;
        }
        
        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 15px 25px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
            font-size: 16px;
        }
        
        .navigation a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .highlight {
            background: rgba(255, 255, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-check-circle"></i> اختبار التكامل البسيط</h1>
        
        <div class="navigation">
            <a href="customers.html" target="_blank">
                <i class="fas fa-users"></i> العملاء
            </a>
            <a href="suppliers.html" target="_blank">
                <i class="fas fa-truck"></i> الموردين
            </a>
            <a href="accounting.html" target="_blank">
                <i class="fas fa-calculator"></i> دليل الحسابات
            </a>
        </div>

        <!-- الخطوة الأولى -->
        <div class="step-card">
            <div class="step-number">1</div>
            <div class="step-title">إضافة عميل جديد</div>
            <div class="step-content">
                <p>اضغط على <span class="highlight">"العملاء"</span> أعلاه، ثم:</p>
                <ul>
                    <li>اضغط على <strong>"إضافة عميل جديد"</strong></li>
                    <li>املأ الاسم: <code>عميل تجريبي</code></li>
                    <li>املأ الهاتف: <code>+************</code></li>
                    <li>اضغط <strong>"حفظ"</strong></li>
                </ul>
            </div>
        </div>

        <!-- الخطوة الثانية -->
        <div class="step-card">
            <div class="step-number">2</div>
            <div class="step-title">التحقق من دليل الحسابات</div>
            <div class="step-content">
                <p>اضغط على <span class="highlight">"دليل الحسابات"</span> أعلاه، ثم:</p>
                <ul>
                    <li>ابحث عن العميل الجديد في الجدول</li>
                    <li>يجب أن يظهر برقم حساب يبدأ بـ <code>11030</code></li>
                    <li>إذا لم يظهر، اضغط على <strong>"مزامنة شاملة"</strong></li>
                </ul>
            </div>
        </div>

        <!-- الخطوة الثالثة -->
        <div class="step-card">
            <div class="step-number">3</div>
            <div class="step-title">إضافة مورد جديد</div>
            <div class="step-content">
                <p>اضغط على <span class="highlight">"الموردين"</span> أعلاه، ثم:</p>
                <ul>
                    <li>اضغط على <strong>"إضافة مورد جديد"</strong></li>
                    <li>املأ الاسم: <code>مورد تجريبي</code></li>
                    <li>املأ الهاتف: <code>+966502345678</code></li>
                    <li>اضغط <strong>"حفظ"</strong></li>
                </ul>
            </div>
        </div>

        <!-- الخطوة الرابعة -->
        <div class="step-card">
            <div class="step-number">4</div>
            <div class="step-title">التحقق النهائي</div>
            <div class="step-content">
                <p>ارجع إلى <span class="highlight">"دليل الحسابات"</span> وتحقق من:</p>
                <ul>
                    <li>ظهور المورد الجديد برقم حساب يبدأ بـ <code>21030</code></li>
                    <li>وجود كلاً من العميل والمورد في الجدول</li>
                    <li>عمل النظام الاحترافي (الشجرة) بشكل صحيح</li>
                </ul>
            </div>
        </div>

        <!-- أدوات مساعدة -->
        <div class="step-card">
            <div class="step-number"><i class="fas fa-tools"></i></div>
            <div class="step-title">أدوات مساعدة</div>
            <div class="step-content">
                <button class="btn primary" onclick="checkStatus()">
                    <i class="fas fa-search"></i> فحص الحالة الحالية
                </button>
                <button class="btn warning" onclick="clearData()">
                    <i class="fas fa-broom"></i> مسح البيانات التجريبية
                </button>
                <button class="btn success" onclick="openAll()">
                    <i class="fas fa-external-link-alt"></i> فتح جميع الصفحات
                </button>
            </div>
        </div>

        <!-- حالة النظام -->
        <div class="step-card">
            <div class="step-number"><i class="fas fa-info-circle"></i></div>
            <div class="step-title">حالة النظام</div>
            <div class="status-box" id="statusBox">
                اضغط على "فحص الحالة الحالية" لرؤية الإحصائيات...
            </div>
        </div>
    </div>

    <script src="js/data-manager.js"></script>
    <script>
        function checkStatus() {
            const statusBox = document.getElementById('statusBox');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                const customerAccounts = accounts.filter(acc => acc.category === 'customers');
                const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
                
                let status = `📊 إحصائيات النظام:\n\n`;
                status += `العملاء: ${customers.length}\n`;
                status += `الموردين: ${suppliers.length}\n`;
                status += `إجمالي الحسابات: ${accounts.length}\n`;
                status += `حسابات العملاء: ${customerAccounts.length}\n`;
                status += `حسابات الموردين: ${supplierAccounts.length}\n\n`;
                
                if (customers.length > 0) {
                    status += `آخر العملاء:\n`;
                    customers.slice(-3).forEach(customer => {
                        const expectedCode = `11030${String(customer.id).padStart(3, '0')}`;
                        const hasAccount = accounts.find(acc => acc.code === expectedCode);
                        const statusIcon = hasAccount ? '✅' : '❌';
                        status += `  ${statusIcon} ${customer.name} (${expectedCode})\n`;
                    });
                    status += '\n';
                }
                
                if (suppliers.length > 0) {
                    status += `آخر الموردين:\n`;
                    suppliers.slice(-3).forEach(supplier => {
                        const expectedCode = `21030${String(supplier.id).padStart(3, '0')}`;
                        const hasAccount = accounts.find(acc => acc.code === expectedCode);
                        const statusIcon = hasAccount ? '✅' : '❌';
                        status += `  ${statusIcon} ${supplier.name} (${expectedCode})\n`;
                    });
                }
                
                statusBox.textContent = status;
                
            } catch (error) {
                statusBox.textContent = `❌ خطأ في فحص الحالة: ${error.message}`;
            }
        }

        function clearData() {
            if (confirm('هل تريد مسح جميع البيانات التجريبية؟')) {
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizSuppliers');
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('monjizAccounts');
                localStorage.removeItem('professionalChartOfAccounts');
                
                alert('تم مسح جميع البيانات التجريبية');
                checkStatus();
            }
        }

        function openAll() {
            window.open('customers.html', '_blank');
            window.open('suppliers.html', '_blank');
            window.open('accounting.html', '_blank');
            alert('تم فتح جميع الصفحات في تبويبات جديدة');
        }

        // فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
