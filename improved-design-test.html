<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم المحسن - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #007bff;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn.warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        .btn.danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #007bff;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #007bff;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .comparison-table th {
            background: #007bff;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-improved {
            background: #d4edda;
            color: #155724;
        }
        .status-new {
            background: #cce5ff;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 اختبار التصميم المحسن لنظام منجز</h1>

        <!-- ملخص التحسينات -->
        <div class="test-section">
            <h2>📋 ملخص التحسينات المطبقة</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>تصميم الجداول</h3>
                    <p>تحسين تخطيط الجداول مع محاذاة مناسبة وألوان متناسقة</p>
                    <span class="status-badge status-improved">محسن</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📄</div>
                    <h3>نظام التنقل</h3>
                    <p>تحسين أزرار التنقل بين الصفحات مع عداد النتائج</p>
                    <span class="status-badge status-improved">محسن</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>الألوان والشارات</h3>
                    <p>نظام ألوان موحد للفئات والحالات</p>
                    <span class="status-badge status-new">جديد</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>الأداء</h3>
                    <p>تحسين سرعة التحميل والتنقل بين البيانات</p>
                    <span class="status-badge status-improved">محسن</span>
                </div>
            </div>
        </div>

        <!-- مقارنة قبل وبعد -->
        <div class="test-section">
            <h2>📊 مقارنة التحسينات</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>قبل التحسين</th>
                        <th>بعد التحسين</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>تخطيط الجدول</strong></td>
                        <td>عشوائي وغير منتظم</td>
                        <td>منظم مع عرض ثابت للأعمدة</td>
                        <td><span class="status-badge status-improved">✅ محسن</span></td>
                    </tr>
                    <tr>
                        <td><strong>التنقل بين الصفحات</strong></td>
                        <td>لا يعمل</td>
                        <td>يعمل بسلاسة مع عداد النتائج</td>
                        <td><span class="status-badge status-improved">✅ محسن</span></td>
                    </tr>
                    <tr>
                        <td><strong>شارات الفئات</strong></td>
                        <td>لون واحد فقط</td>
                        <td>ألوان مختلفة لكل فئة</td>
                        <td><span class="status-badge status-new">🆕 جديد</span></td>
                    </tr>
                    <tr>
                        <td><strong>أزرار الإجراءات</strong></td>
                        <td>تصميم بسيط</td>
                        <td>ألوان مميزة لكل إجراء</td>
                        <td><span class="status-badge status-improved">✅ محسن</span></td>
                    </tr>
                    <tr>
                        <td><strong>حفظ البيانات</strong></td>
                        <td>تختفي عند إعادة التحميل</td>
                        <td>تبقى محفوظة دائماً</td>
                        <td><span class="status-badge status-improved">✅ محسن</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- اختبار الوظائف -->
        <div class="test-section">
            <h2>🧪 اختبار الوظائف المحسنة</h2>
            
            <h3>📦 اختبار المنتجات:</h3>
            <button class="btn" onclick="testProducts()">إضافة منتجات تجريبية</button>
            <button class="btn success" onclick="openProducts()">فتح صفحة المنتجات المحسنة</button>
            <div id="products-test-result"></div>

            <h3>👥 اختبار العملاء:</h3>
            <button class="btn" onclick="testCustomers()">إضافة عملاء تجريبيين</button>
            <button class="btn success" onclick="openCustomers()">فتح صفحة العملاء المحسنة</button>
            <div id="customers-test-result"></div>

            <h3>📄 اختبار التنقل:</h3>
            <button class="btn warning" onclick="testPagination()">اختبار التنقل بين الصفحات</button>
            <div id="pagination-test-result"></div>

            <h3>🔗 اختبار الربط:</h3>
            <button class="btn" onclick="testLinking()">اختبار الربط بين الصفحات</button>
            <button class="btn success" onclick="openSales()">فتح صفحة المبيعات</button>
            <div id="linking-test-result"></div>
        </div>

        <!-- إدارة البيانات -->
        <div class="test-section">
            <h2>🗂️ إدارة البيانات</h2>
            <button class="btn" onclick="showStats()">عرض الإحصائيات</button>
            <button class="btn warning" onclick="exportData()">تصدير البيانات</button>
            <button class="btn danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="data-management-result"></div>
        </div>
    </div>

    <script>
        // اختبار المنتجات
        function testProducts() {
            const testProducts = [
                { name: 'جهاز عرض محمول - اختبار', category: 'electronics', code: 'PROJ-TEST-001', price: 2500, cost: 2000, quantity: 10, minStock: 2 },
                { name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2 },
                { name: 'هاتف آيفون 15 Pro', category: 'phones', code: 'IPHONE-15P', price: 5200, cost: 4500, quantity: 5, minStock: 1 },
                { name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5 },
                { name: 'مكتب خشبي فاخر', category: 'furniture', code: 'DESK-LUX', price: 1800, cost: 1200, quantity: 3, minStock: 1 }
            ];

            let products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            testProducts.forEach(product => {
                const newProduct = {
                    id: Date.now() + Math.random(),
                    ...product,
                    description: `منتج تجريبي: ${product.name}`
                };
                products.push(newProduct);
            });

            localStorage.setItem('monjizProducts', JSON.stringify(products));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('products-test-result', `✅ تم إضافة ${testProducts.length} منتج تجريبي بنجاح!<br>إجمالي المنتجات: ${products.length}<br>🎨 التصميم الجديد يدعم ألوان مختلفة لكل فئة`, 'success');
        }

        // اختبار العملاء
        function testCustomers() {
            const testCustomers = [
                { name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { name: 'شركة الأنوار للتجارة', type: 'company', phone: '+966502345678', email: '<EMAIL>' },
                { name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' },
                { name: 'مؤسسة النجاح', type: 'company', phone: '+966504567890', email: '<EMAIL>' }
            ];

            let customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            testCustomers.forEach(customer => {
                const newCustomer = {
                    id: Date.now() + Math.random(),
                    ...customer,
                    address: 'الرياض، المملكة العربية السعودية',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                };
                customers.push(newCustomer);
            });

            localStorage.setItem('monjizCustomers', JSON.stringify(customers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('customers-test-result', `✅ تم إضافة ${testCustomers.length} عميل تجريبي بنجاح!<br>إجمالي العملاء: ${customers.length}<br>📄 التنقل المحسن يعمل الآن بسلاسة`, 'success');
        }

        // اختبار التنقل
        function testPagination() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">📄 نتائج اختبار التنقل المحسن:</div>';
            
            if (products.length > 2) {
                const pages = Math.ceil(products.length / 2);
                result += `<p>✅ المنتجات: ${products.length} منتج (${pages} صفحات)</p>`;
                result += `<p>🎯 التنقل يعرض 2 منتج في كل صفحة</p>`;
            } else {
                result += `<p>⚠️ المنتجات: ${products.length} منتج (أضف المزيد لاختبار التنقل)</p>`;
            }
            
            if (customers.length > 2) {
                const pages = Math.ceil(customers.length / 2);
                result += `<p>✅ العملاء: ${customers.length} عميل (${pages} صفحات)</p>`;
                result += `<p>🎯 التنقل يعرض 2 عميل في كل صفحة</p>`;
            } else {
                result += `<p>⚠️ العملاء: ${customers.length} عميل (أضف المزيد لاختبار التنقل)</p>`;
            }
            
            result += '<p>💡 افتح صفحات المنتجات والعملاء لرؤية التنقل المحسن</p>';
            
            document.getElementById('pagination-test-result').innerHTML = result;
        }

        // اختبار الربط
        function testLinking() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">🔗 نتائج اختبار الربط المحسن:</div>';
            
            const hasProjector = products.some(p => p.name.includes('جهاز عرض'));
            const hasTopaz = customers.some(c => c.name.includes('توباز'));
            
            result += `<p>📦 المنتجات: ${products.length} (جهاز عرض: ${hasProjector ? '✅' : '❌'})</p>`;
            result += `<p>👥 العملاء: ${customers.length} (مطعم توباز: ${hasTopaz ? '✅' : '❌'})</p>`;
            
            if (products.length > 0 && customers.length > 0) {
                result += '<div class="success">🎉 الربط جاهز! التصميم المحسن يعرض البيانات بشكل أفضل</div>';
                result += '<p>🎨 ستلاحظ الألوان المحسنة والتنظيم الأفضل في صفحة المبيعات</p>';
            } else {
                result += '<div class="error">❌ أضف منتجات وعملاء أولاً</div>';
            }
            
            document.getElementById('linking-test-result').innerHTML = result;
        }

        // عرض الإحصائيات
        function showStats() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            let result = '<div class="info">📊 إحصائيات النظام المحسن:</div>';
            result += `<p>📦 المنتجات: ${products.length}</p>`;
            result += `<p>👥 العملاء: ${customers.length}</p>`;
            result += `<p>🧾 الفواتير: ${invoices.length}</p>`;
            
            // إحصائيات الفئات
            const categories = {};
            products.forEach(p => {
                categories[p.category] = (categories[p.category] || 0) + 1;
            });
            
            result += '<p><strong>توزيع المنتجات حسب الفئة:</strong></p><ul>';
            Object.entries(categories).forEach(([cat, count]) => {
                result += `<li>${cat}: ${count} منتج</li>`;
            });
            result += '</ul>';
            
            document.getElementById('data-management-result').innerHTML = result;
        }

        // تصدير البيانات
        function exportData() {
            const data = {
                products: JSON.parse(localStorage.getItem('monjizProducts')) || [],
                customers: JSON.parse(localStorage.getItem('monjizCustomers')) || [],
                invoices: JSON.parse(localStorage.getItem('monjizInvoices')) || [],
                exportDate: new Date().toISOString(),
                version: 'improved-design-v1.0'
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'monjiz-improved-data-backup.json';
            a.click();
            
            showResult('data-management-result', '📁 تم تصدير البيانات المحسنة بنجاح', 'success');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.clear();
                showResult('data-management-result', '🗑️ تم مسح جميع البيانات', 'info');
                
                // تحديث جميع النتائج
                setTimeout(() => {
                    document.getElementById('products-test-result').innerHTML = '';
                    document.getElementById('customers-test-result').innerHTML = '';
                    document.getElementById('pagination-test-result').innerHTML = '';
                    document.getElementById('linking-test-result').innerHTML = '';
                }, 1000);
            }
        }

        // فتح الصفحات
        function openProducts() { window.open('products.html', '_blank'); }
        function openCustomers() { window.open('customers.html', '_blank'); }
        function openSales() { window.open('sales.html', '_blank'); }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل الإحصائيات عند فتح الصفحة
        window.addEventListener('load', function() {
            showStats();
            testPagination();
            testLinking();
        });
    </script>
</body>
</html>
