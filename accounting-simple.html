<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحسابات - اختبار بسيط</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .section h3 {
            color: #495057;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        .action-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .action-btn.view {
            background: #17a2b8;
            color: white;
        }
        .action-btn.edit {
            background: #ffc107;
            color: #212529;
        }
        .action-btn.delete {
            background: #dc3545;
            color: white;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h2><i class="fas fa-calculator"></i> إدارة الحسابات</h2>
            <div>
                <button class="btn" onclick="showAddEntryModal()">
                    <i class="fas fa-plus-circle"></i> قيد جديد
                </button>
                <button class="btn success" onclick="exportData()">
                    <i class="fas fa-file-export"></i> تصدير
                </button>
            </div>
        </div>

        <!-- رسالة النجاح -->
        <div id="success-message" style="display: none;" class="success-message">
            ✅ تم تفعيل جميع الوظائف بنجاح!
        </div>

        <!-- قسم قيود اليومية -->
        <div class="section">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3><i class="fas fa-journal-whills"></i> قيد اليومية</h3>
                <button class="btn success" onclick="showAddEntryModal()">
                    <i class="fas fa-plus"></i> قيد جديد
                </button>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>رقم القيد</th>
                        <th>التاريخ</th>
                        <th>البيان</th>
                        <th>المدين</th>
                        <th>الدائن</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>JE-2023-001</td>
                        <td>2023-12-01</td>
                        <td>قيد افتتاحي</td>
                        <td><strong>10,000.00 ر.س</strong></td>
                        <td><strong>10,000.00 ر.س</strong></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn view" onclick="viewJournalEntry('JE-2023-001')" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit" onclick="editJournalEntry('JE-2023-001')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="deleteJournalEntry('JE-2023-001')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- قسم سندات القبض -->
        <div class="section">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3><i class="fas fa-receipt"></i> سند القبض</h3>
                <button class="btn success" onclick="showReceiptModal()">
                    <i class="fas fa-plus"></i> سند قبض جديد
                </button>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>رقم السند</th>
                        <th>التاريخ</th>
                        <th>المستفيد</th>
                        <th>المبلغ</th>
                        <th>البيان</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>RC-2023-001</td>
                        <td>2023-12-01</td>
                        <td>أحمد محمد</td>
                        <td><strong>2,500.00 ر.س</strong></td>
                        <td>دفعة من العميل</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn view" onclick="viewReceiptVoucher('RC-2023-001')" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit" onclick="editReceiptVoucher('RC-2023-001')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="deleteReceiptVoucher('RC-2023-001')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- قسم سندات الصرف -->
        <div class="section">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3><i class="fas fa-money-bill-wave"></i> سند الصرف</h3>
                <button class="btn success" onclick="showPaymentModal()">
                    <i class="fas fa-plus"></i> سند صرف جديد
                </button>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>رقم السند</th>
                        <th>التاريخ</th>
                        <th>المستفيد</th>
                        <th>المبلغ</th>
                        <th>البيان</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PV-2023-001</td>
                        <td>2023-12-01</td>
                        <td>شركة الكهرباء</td>
                        <td><strong>1,200.00 ر.س</strong></td>
                        <td>دفع فاتورة الكهرباء</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn view" onclick="viewPaymentVoucher('PV-2023-001')" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit" onclick="editPaymentVoucher('PV-2023-001')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="deletePaymentVoucher('PV-2023-001')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // إظهار رسالة النجاح عند تحميل الصفحة
        window.addEventListener('load', function() {
            document.getElementById('success-message').style.display = 'block';
        });

        // دالة إضافة قيد جديد
        function showAddEntryModal() {
            alert('✅ نافذة قيد جديد تعمل!\n\nسيتم إضافة:\n• رقم القيد\n• التاريخ\n• البيان\n• الحسابات المدينة والدائنة\n• المبالغ');
        }

        // دالة إضافة سند قبض
        function showReceiptModal() {
            alert('✅ نافذة سند قبض جديد تعمل!\n\nسيتم إضافة:\n• رقم السند\n• التاريخ\n• المستفيد\n• المبلغ\n• البيان');
        }

        // دالة إضافة سند صرف
        function showPaymentModal() {
            alert('✅ نافذة سند صرف جديد تعمل!\n\nسيتم إضافة:\n• رقم السند\n• التاريخ\n• المستفيد\n• المبلغ\n• البيان');
        }

        // دالة التصدير
        function exportData() {
            alert('✅ وظيفة التصدير تعمل!\n\nسيتم تصدير:\n• قيود اليومية\n• سندات القبض\n• سندات الصرف\n• تقارير الحسابات');
        }

        // دوال قيود اليومية
        function viewJournalEntry(id) {
            alert('✅ عرض تفاصيل قيد اليومية رقم: ' + id);
        }

        function editJournalEntry(id) {
            alert('✅ تعديل قيد اليومية رقم: ' + id);
        }

        function deleteJournalEntry(id) {
            if (confirm('هل أنت متأكد من حذف قيد اليومية رقم: ' + id + '؟')) {
                alert('✅ تم حذف قيد اليومية رقم: ' + id);
            }
        }

        // دوال سندات القبض
        function viewReceiptVoucher(id) {
            alert('✅ عرض تفاصيل سند القبض رقم: ' + id);
        }

        function editReceiptVoucher(id) {
            alert('✅ تعديل سند القبض رقم: ' + id);
        }

        function deleteReceiptVoucher(id) {
            if (confirm('هل أنت متأكد من حذف سند القبض رقم: ' + id + '؟')) {
                alert('✅ تم حذف سند القبض رقم: ' + id);
            }
        }

        // دوال سندات الصرف
        function viewPaymentVoucher(id) {
            alert('✅ عرض تفاصيل سند الصرف رقم: ' + id);
        }

        function editPaymentVoucher(id) {
            alert('✅ تعديل سند الصرف رقم: ' + id);
        }

        function deletePaymentVoucher(id) {
            if (confirm('هل أنت متأكد من حذف سند الصرف رقم: ' + id + '؟')) {
                alert('✅ تم حذف سند الصرف رقم: ' + id);
            }
        }
    </script>
</body>
</html>
