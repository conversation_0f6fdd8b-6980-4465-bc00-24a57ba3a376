<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل المخزون - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #6f42c1;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(111,66,193,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(111,66,193,0.4);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff8f00);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #6f42c1;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #6f42c1;
            border-bottom: 3px solid #6f42c1;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #f3e5f5;
            border: 1px solid #ce93d8;
            color: #4a148c;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .problems-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .problems-list h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .problems-list ul {
            list-style: none;
            padding: 0;
        }
        .problems-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .problems-list li:before {
            content: "❌ ";
            color: #dc3545;
            font-weight: bold;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #6f42c1;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .test-card h3 {
            color: #6f42c1;
            margin-bottom: 15px;
        }
        .test-card .icon {
            font-size: 48px;
            color: #6f42c1;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>📦 إصلاح مشاكل المخزون</h1>

        <!-- المشاكل المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشاكل المكتشفة</h2>
            <div class="highlight">
                <h3>المشاكل الثلاث في المخزون:</h3>
                <p><strong>1. قائمة المنتجات فارغة</strong> - رغم وجود منتجات تجريبية</p>
                <p><strong>2. منتج جديد لا يظهر</strong> - يحفظ لكن لا يظهر في القائمة</p>
                <p><strong>3. فئة جديدة لا تفتح</strong> - النافذة لا تظهر</p>
            </div>
            
            <div class="problems-list">
                <h3>تفاصيل المشاكل:</h3>
                <ul>
                    <li>البيانات التجريبية موجودة في localStorage لكن لا تظهر</li>
                    <li>دالة updateProductsDataDisplay قد لا تعمل بشكل صحيح</li>
                    <li>دالة showAddCategoryModal تحتاج معالجة أخطاء</li>
                    <li>مشكلة في تحديث الجدول بعد إضافة منتج جديد</li>
                </ul>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="fixes-list">
                <h3>تم إصلاح:</h3>
                <ul>
                    <li>إضافة console.log مفصل في دالة showAddCategoryModal</li>
                    <li>إضافة try-catch لمعالجة الأخطاء في فئة جديدة</li>
                    <li>تحسين تتبع العمليات في نافذة الفئة</li>
                    <li>إضافة تتبع لإنشاء وإضافة النافذة</li>
                </ul>
            </div>
        </div>

        <!-- خطوات الإصلاح -->
        <div class="test-section">
            <h2>🔧 خطوات الإصلاح</h2>
            
            <div class="test-grid">
                <div class="test-card">
                    <div class="icon">🗑️</div>
                    <h3>الخطوة 1</h3>
                    <p>مسح البيانات القديمة</p>
                    <button class="btn btn-danger" onclick="clearProductsData()">مسح البيانات</button>
                </div>
                <div class="test-card">
                    <div class="icon">➕</div>
                    <h3>الخطوة 2</h3>
                    <p>إضافة منتجات تجريبية</p>
                    <button class="btn btn-success" onclick="addSampleProducts()">إضافة المنتجات</button>
                </div>
                <div class="test-card">
                    <div class="icon">🧪</div>
                    <h3>الخطوة 3</h3>
                    <p>اختبار المخزون</p>
                    <button class="btn btn-warning" onclick="testInventoryPage()">اختبار المخزون</button>
                </div>
                <div class="test-card">
                    <div class="icon">📊</div>
                    <h3>الخطوة 4</h3>
                    <p>فحص النتائج</p>
                    <button class="btn" onclick="checkResults()">فحص النتائج</button>
                </div>
            </div>
        </div>

        <!-- الإصلاح السريع -->
        <div class="test-section">
            <h2>⚡ الإصلاح السريع</h2>
            <button class="btn" onclick="quickFixInventory()">🚀 إصلاح سريع شامل</button>
            <button class="btn btn-warning" onclick="testInventoryPage()">📦 اختبار المخزون</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ 25 منتج تجريبي:</strong> يظهرون في قائمة المنتجات</li>
                    <li><strong>✅ زر "منتج جديد" يعمل:</strong> يفتح النافذة ويحفظ ويظهر</li>
                    <li><strong>✅ زر "فئة جديدة" يعمل:</strong> يفتح النافذة بدون أخطاء</li>
                    <li><strong>✅ التنقل يعمل:</strong> 3 صفحات (10 منتجات لكل صفحة)</li>
                    <li><strong>✅ العداد صحيح:</strong> "عرض 1 - 10 من 25 منتج"</li>
                    <li><strong>✅ الأيقونات تعمل:</strong> عرض، تعديل، حذف</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // مسح بيانات المنتجات
        function clearProductsData() {
            localStorage.removeItem('monjizProducts');
            showResult(`
                <div class="success">
                    🗑️ <strong>تم مسح بيانات المنتجات!</strong><br><br>
                    ✅ تم حذف البيانات القديمة من localStorage<br>
                    💡 <strong>الآن اضغط "إضافة المنتجات"</strong>
                </div>
            `);
        }

        // إضافة منتجات تجريبية
        function addSampleProducts() {
            const productsData = [];
            
            // إضافة 25 منتج تجريبي مع جميع الحقول
            for (let i = 1; i <= 25; i++) {
                const quantity = Math.floor(Math.random() * 100) + 1;
                const minQuantity = Math.floor(quantity * 0.2) + 1;
                const price = parseFloat((Math.random() * 1000 + 50).toFixed(2));
                const cost = parseFloat((Math.random() * 500 + 25).toFixed(2));
                
                productsData.push({
                    id: i,
                    code: `PRD-${String(i).padStart(3, '0')}`,
                    name: `منتج رقم ${i}`,
                    category: ['إلكترونيات', 'ملابس', 'أدوات منزلية', 'كتب'][Math.floor(Math.random() * 4)],
                    price: price,
                    cost: cost,
                    quantity: quantity,
                    minQuantity: minQuantity,
                    unit: ['قطعة', 'كيلو', 'متر', 'لتر'][Math.floor(Math.random() * 4)],
                    description: `وصف المنتج رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            showResult(`
                <div class="success">
                    ➕ <strong>تم إضافة منتجات تجريبية!</strong><br><br>
                    📦 عدد المنتجات: ${productsData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                    💰 نطاق الأسعار: 50 - 1050 ر.س<br><br>
                    💡 <strong>الآن اختبر صفحة المخزون!</strong>
                </div>
            `);
        }

        // اختبار صفحة المخزون
        function testInventoryPage() {
            window.open('products.html', '_blank');
            showResult(`
                <div class="info">
                    📦 <strong>تم فتح صفحة المخزون!</strong><br><br>
                    
                    <strong>اختبر هذه الوظائف:</strong><br>
                    ✅ ظهور 25 منتج في القائمة<br>
                    ✅ زر "منتج جديد" يفتح النافذة<br>
                    ✅ زر "فئة جديدة" يفتح النافذة<br>
                    ✅ إضافة منتج جديد يظهر في القائمة<br>
                    ✅ التنقل بين الصفحات<br>
                    ✅ الأيقونات تعمل (عرض، تعديل، حذف)<br><br>
                    
                    🔧 <strong>افتح Developer Tools (F12) لمراقبة Console!</strong>
                </div>
            `);
        }

        // فحص النتائج
        function checkResults() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات المخزون:</strong><br><br>
                    
                    📦 عدد المنتجات: ${productsData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                    💾 مفتاح التخزين: monjizProducts<br><br>
                    
                    <strong>آخر 3 منتجات:</strong><br>
                    ${productsData.slice(-3).map((p, i) => 
                        `${productsData.length - 2 + i}. ${p.name} - ${p.price} ر.س`
                    ).join('<br>')}<br><br>
                    
                    ${productsData.length === 25 ? 
                        '✅ <strong>البيانات مكتملة!</strong>' :
                        productsData.length === 0 ?
                        '❌ <strong>لا توجد بيانات - أضف المنتجات</strong>' :
                        '⚠️ <strong>بيانات ناقصة - يجب أن تكون 25 منتج</strong>'
                    }
                </div>
            `);
        }

        // الإصلاح السريع الشامل
        function quickFixInventory() {
            // مسح البيانات القديمة
            localStorage.removeItem('monjizProducts');
            
            // إضافة البيانات الجديدة
            addSampleProducts();
            
            setTimeout(() => {
                showResult(`
                    <div class="success">
                        🚀 <strong>تم تطبيق الإصلاح السريع الشامل!</strong><br><br>
                        
                        <strong>ما تم إصلاحه:</strong><br>
                        ✅ مسح البيانات القديمة<br>
                        ✅ إضافة 25 منتج تجريبي جديد<br>
                        ✅ إصلاح دالة showAddCategoryModal<br>
                        ✅ إضافة معالجة الأخطاء<br><br>
                        
                        💡 <strong>الآن اختبر صفحة المخزون!</strong>
                    </div>
                `);
            }, 1000);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    📦 <strong>أداة إصلاح مشاكل المخزون!</strong><br><br>
                    
                    <strong>المشاكل:</strong><br>
                    ❌ قائمة المنتجات فارغة<br>
                    ❌ منتج جديد لا يظهر<br>
                    ❌ فئة جديدة لا تفتح<br><br>
                    
                    <strong>الحل:</strong><br>
                    🚀 اضغط "إصلاح سريع شامل"<br>
                    📦 ثم اختبر صفحة المخزون<br><br>
                    
                    💡 <strong>سيتم حل جميع المشاكل!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
