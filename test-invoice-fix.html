<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح فاتورة جديدة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #28a745;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #28a745;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .fix-box {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fix-box h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .code-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 2px solid #4a5568;
        }
        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .test-steps h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .test-steps ol {
            margin: 0;
            padding-right: 20px;
        }
        .test-steps li {
            margin: 8px 0;
            padding: 5px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>✅ تم إصلاح مشكلة فاتورة جديدة!</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة والحل</h2>
            
            <div class="error-box">
                <h3>❌ المشكلة المكتشفة:</h3>
                <div class="code-box">
                    حدث خطأ في فتح نافذة الفاتورة الجديدة: purchase.id.startsWith is not a function
                </div>
                <p><strong>السبب:</strong> دالة `startsWith` تعمل فقط مع النصوص، لكن `purchase.id` كان رقم</p>
            </div>
            
            <div class="fix-box">
                <h3>✅ الحل المطبق:</h3>
                <p><strong>تحسين دالة generatePurchaseInvoiceNumber:</strong></p>
                <div class="code-box">
// قبل الإصلاح - خطأ
if (purchase.id && purchase.id.startsWith('PUR-')) {
    // خطأ إذا كان purchase.id رقم
}

// بعد الإصلاح - يعمل مع الأرقام والنصوص
let invoiceId = purchase.invoiceNumber || purchase.id;

if (typeof invoiceId === 'number') {
    maxNumber = Math.max(maxNumber, invoiceId);
} else if (typeof invoiceId === 'string' && invoiceId.startsWith('PUR-')) {
    const numberPart = invoiceId.replace('PUR-', '');
    const number = parseInt(numberPart);
    if (!isNaN(number) && number > maxNumber) {
        maxNumber = number;
    }
}
                </div>
            </div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>🧪 خطوات اختبار الإصلاح</h2>
            
            <div class="test-steps">
                <h3>اختبر فاتورة جديدة الآن:</h3>
                <ol>
                    <li><strong>افتح صفحة المشتريات</strong></li>
                    <li><strong>افتح Developer Tools (F12)</strong> وانتقل لتبويب Console</li>
                    <li><strong>اضغط زر "فاتورة جديدة"</strong></li>
                    <li><strong>يجب أن تظهر النافذة بدون أخطاء</strong></li>
                    <li><strong>تحقق من رقم الفاتورة الجديد</strong> (مثل PUR-026)</li>
                    <li><strong>جرب ملء البيانات وحفظ الفاتورة</strong></li>
                </ol>
            </div>
        </div>

        <!-- الاختبار -->
        <div class="test-section">
            <h2>🚀 اختبار الإصلاح</h2>
            <button class="btn" onclick="testNewInvoice()">🛍️ اختبار فاتورة جديدة</button>
            <button class="btn" onclick="checkConsole()">🔧 تعليمات Console</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎊 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ زر "فاتورة جديدة" يعمل:</strong> يفتح النافذة بدون أخطاء</li>
                    <li><strong>✅ رقم فاتورة صحيح:</strong> PUR-026 أو أعلى</li>
                    <li><strong>✅ النافذة تظهر كاملة:</strong> مع جميع الحقول</li>
                    <li><strong>✅ قائمة الموردين تعمل:</strong> تظهر الموردين المحفوظين</li>
                    <li><strong>✅ إضافة أصناف تعمل:</strong> يمكن إضافة منتجات للفاتورة</li>
                    <li><strong>✅ حفظ الفاتورة يعمل:</strong> تُضاف للجدول</li>
                    <li><strong>✅ Console نظيف:</strong> لا توجد رسائل خطأ</li>
                </ul>
            </div>
        </div>

        <!-- رسائل Console المتوقعة -->
        <div class="test-section">
            <h2>📱 رسائل Console المتوقعة</h2>
            
            <div class="highlight">
                <h3>عند الضغط على "فاتورة جديدة" يجب أن تظهر:</h3>
                <div class="code-box">
✅ showAddPurchaseModal: بدء فتح نافذة إضافة فاتورة شراء جديدة...
✅ showAddPurchaseModal: تم منع التمرير
✅ showAddPurchaseModal: إنشاء عنصر النافذة...
✅ showAddPurchaseModal: تم إنشاء عنصر النافذة
✅ showAddPurchaseModal: إضافة النافذة للصفحة...
✅ showAddPurchaseModal: تم إضافة النافذة للصفحة
✅ showAddPurchaseModal: تحميل الموردين...
✅ showAddPurchaseModal: إضافة صنف افتراضي...
✅ showAddPurchaseModal: تم فتح النافذة بنجاح!
                </div>
            </div>
            
            <div class="error-box">
                <h3>❌ يجب ألا تظهر هذه الرسالة بعد الآن:</h3>
                <div class="code-box">
❌ حدث خطأ في فتح نافذة الفاتورة الجديدة: purchase.id.startsWith is not a function
                </div>
            </div>
        </div>
    </div>

    <script>
        // اختبار فاتورة جديدة
        function testNewInvoice() {
            window.open('purchases.html', '_blank');
            showResult(`
                <div class="success">
                    🛍️ <strong>تم فتح صفحة المشتريات!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    1️⃣ اضغط F12 لفتح Developer Tools<br>
                    2️⃣ انتقل لتبويب Console<br>
                    3️⃣ اضغط زر "فاتورة جديدة" (الأخضر)<br>
                    4️⃣ يجب أن تفتح النافذة بدون أخطاء<br>
                    5️⃣ تحقق من رقم الفاتورة الجديد<br><br>
                    
                    ✅ <strong>إذا فتحت النافذة = تم الإصلاح بنجاح!</strong><br>
                    ❌ <strong>إذا ظهر خطأ = أرسل لي رسالة الخطأ</strong>
                </div>
            `);
        }

        // تعليمات Console
        function checkConsole() {
            showResult(`
                <div class="info">
                    🔧 <strong>كيفية فحص Console:</strong><br><br>
                    
                    <strong>خطوات الفحص:</strong><br>
                    1️⃣ اضغط F12 في صفحة المشتريات<br>
                    2️⃣ انتقل لتبويب Console<br>
                    3️⃣ امسح الرسائل القديمة (Ctrl + L)<br>
                    4️⃣ اضغط زر "فاتورة جديدة"<br>
                    5️⃣ راقب الرسائل الجديدة<br><br>
                    
                    <strong>النتائج المتوقعة:</strong><br>
                    ✅ رسائل خضراء = الكود يعمل<br>
                    ✅ النافذة تظهر = الإصلاح نجح<br>
                    ❌ رسائل حمراء = مشكلة جديدة<br><br>
                    
                    💡 <strong>أعلمني بالنتيجة!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="success">
                    ✅ <strong>تم إصلاح مشكلة فاتورة جديدة!</strong><br><br>
                    
                    <strong>المشكلة:</strong><br>
                    ❌ purchase.id.startsWith is not a function<br><br>
                    
                    <strong>الحل:</strong><br>
                    ✅ تحسين دالة generatePurchaseInvoiceNumber<br>
                    ✅ دعم الأرقام والنصوص معاً<br>
                    ✅ معالجة أفضل للأخطاء<br><br>
                    
                    🚀 <strong>اختبر فاتورة جديدة الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
