#!/usr/bin/env python3
# Python script to open accounting.html directly
# Created to fix accounting management functions issue

import os
import sys
import webbrowser
import subprocess
import locale
from pathlib import Path

def set_locale():
    # محاولة تعيين الترميز المناسب للغة العربية
    try:
        # تعيين الترميز إلى UTF-8 لدعم الأحرف العربية
        if sys.platform == 'win32':
            locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
        else:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        print("تم تعيين الترميز للغة العربية بنجاح")
    except locale.Error:
        try:
            # محاولة استخدام UTF-8 كبديل
            locale.setlocale(locale.LC_ALL, '')
            print("تم استخدام الترميز الافتراضي للنظام")
        except locale.Error as e:
            print(f"تحذير: فشل في تعيين الترميز: {e}")

def main():
    # تعيين الترميز المناسب
    set_locale()
    
    # الحصول على المسار الحالي
    current_dir = Path(__file__).parent.absolute()
    
    # بناء المسار إلى accounting.html
    accounting_path = current_dir / 'accounting.html'
    
    # التحقق من وجود الملف
    if accounting_path.exists():
        print(f"فتح صفحة الحسابات من: {accounting_path}")
        
        try:
            # الطريقة 1: محاولة استخدام وحدة webbrowser (الطريقة المفضلة)
            # تحويل إلى تنسيق URL
            file_url = f"file://{accounting_path}"
            if webbrowser.open(file_url):
                print("تم فتح صفحة الحسابات بنجاح باستخدام وحدة webbrowser!")
                return 0
            
            # الطريقة 2: إذا فشلت وحدة webbrowser، جرب استخدام أوامر خاصة بنظام التشغيل
            print("فشلت طريقة webbrowser، جاري تجربة طريقة بديلة...")
            if sys.platform == 'win32':
                # في ويندوز، استخدم أمر start
                # استخدام cmd /c بدلاً من shell=True لتجنب مشكلة الحرف العربي
                subprocess.run(['cmd', '/c', 'start', str(accounting_path)], check=True)
                print("تم فتح صفحة الحسابات بنجاح باستخدام أمر start في ويندوز!")
            elif sys.platform == 'darwin':
                # في ماك، استخدم أمر open
                subprocess.run(['open', str(accounting_path)], check=True)
                print("تم فتح صفحة الحسابات بنجاح باستخدام أمر open في ماك!")
            else:
                # في لينكس، استخدم أمر xdg-open
                subprocess.run(['xdg-open', str(accounting_path)], check=True)
                print("تم فتح صفحة الحسابات بنجاح باستخدام أمر xdg-open في لينكس!")
            
            return 0
        except Exception as e:
            print(f"خطأ في فتح صفحة الحسابات: {e}")
            print("الرجاء محاولة فتح accounting.html مباشرة من مستكشف الملفات")
            return 1
    else:
        print(f"لم يتم العثور على ملف صفحة الحسابات في: {accounting_path}")
        return 1

if __name__ == "__main__":
    sys.exit(main())