<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توجيه إلى صفحة الحسابات - Monjiz</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            direction: rtl;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        p {
            color: #34495e;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .countdown {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            margin: 20px 0;
        }
        .note {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>توجيه إلى صفحة الحسابات</h1>
        <p>سيتم توجيهك تلقائيًا إلى صفحة إدارة الحسابات خلال <span id="countdown">5</span> ثوانٍ.</p>
        <p>إذا لم يتم التوجيه تلقائيًا، يرجى النقر على الزر أدناه:</p>
        <a href="accounting.html" class="btn">الانتقال إلى صفحة الحسابات</a>
        <a href="launcher.html" class="btn btn-secondary">العودة إلى الصفحة الرئيسية</a>
        <div class="note">
            <p>ملاحظة: إذا واجهتك مشكلة في عرض صفحة الحسابات، يرجى استخدام أحد ملفات التشغيل المباشر المتوفرة في المجلد:</p>
            <ul style="text-align: right;">
                <li>open_accounting.vbs</li>
                <li>open_accounting.bat</li>
                <li>open_accounting.ps1</li>
                <li>open_accounting.py</li>
            </ul>
            <p>أو راجع ملف <a href="README-accounting-fix.md" style="color: #3498db;">README-accounting-fix.md</a> للحصول على مزيد من المعلومات.</p>
        </div>
    </div>

    <script>
        // توجيه تلقائي بعد 5 ثوانٍ
        let seconds = 5;
        const countdownElement = document.getElementById('countdown');
        
        const countdown = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;
            
            if (seconds <= 0) {
                clearInterval(countdown);
                window.location.href = 'accounting.html';
            }
        }, 1000);
    </script>
</body>
</html>