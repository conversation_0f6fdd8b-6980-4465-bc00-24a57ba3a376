@echo off
rem هذا الملف يساعد على تشغيل ملف Python بدون مشاكل الحرف العربي

setlocal

rem تحديد المسار الكامل للملف
set "PY_FILE=%~dp0open_launcher.py"

rem التأكد من وجود الملف
if not exist "%PY_FILE%" (
    echo File not found: %PY_FILE%
    pause
    exit /b 1
)

rem محاولة تشغيل الملف باستخدام Python
echo Attempting to run Python file...

rem محاولة استخدام python أولاً
python "%PY_FILE%"

rem التحقق من نجاح التشغيل
if %ERRORLEVEL% neq 0 (
    echo Failed with python. Trying python3...
    python3 "%PY_FILE%"
    
    if %ERRORLEVEL% neq 0 (
        echo Failed with python3. Trying py...
        py "%PY_FILE%"
        
        if %ERRORLEVEL% neq 0 (
            echo Failed to run with Python. Trying direct launcher.html...
            start "" "%~dp0launcher.html"
        )
    )
)

endlocal
exit /b 0