<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إضافة الحسابات - منجز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #667eea;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #333;
        }

        .results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .log-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .log-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .account-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .accounts-table th,
        .accounts-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .accounts-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .accounts-table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .badge-primary { background: #007bff; color: white; }
        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: #333; }
        .badge-danger { background: #dc3545; color: white; }
        .badge-info { background: #17a2b8; color: white; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-card h4 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #666;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار نظام إضافة الحسابات</h1>
            <p>اختبار شامل لوظائف إضافة وعرض الحسابات في نظام منجز المحاسبي</p>
        </div>

        <div class="content">
            <!-- إحصائيات سريعة -->
            <div class="stats">
                <div class="stat-card">
                    <h4 id="total-accounts">0</h4>
                    <p>إجمالي الحسابات</p>
                </div>
                <div class="stat-card">
                    <h4 id="test-results">0</h4>
                    <p>نتائج الاختبارات</p>
                </div>
                <div class="stat-card">
                    <h4 id="success-rate">0%</h4>
                    <p>معدل النجاح</p>
                </div>
            </div>

            <!-- اختبارات أساسية -->
            <div class="test-section">
                <h3><i class="fas fa-flask"></i> الاختبارات الأساسية</h3>
                <button class="btn" onclick="testBasicFunctionality()">
                    <i class="fas fa-play"></i> اختبار الوظائف الأساسية
                </button>
                <button class="btn success" onclick="testAddSingleAccount()">
                    <i class="fas fa-plus"></i> اختبار إضافة حساب واحد
                </button>
                <button class="btn warning" onclick="testAddMultipleAccounts()">
                    <i class="fas fa-layer-group"></i> اختبار إضافة حسابات متعددة
                </button>
                <button class="btn danger" onclick="clearAllAccounts()">
                    <i class="fas fa-trash"></i> مسح جميع الحسابات
                </button>
            </div>

            <!-- اختبار إضافة حساب يدوي -->
            <div class="test-section">
                <h3><i class="fas fa-edit"></i> إضافة حساب يدوي</h3>
                <div class="account-form">
                    <div class="form-group">
                        <label for="test-account-code">رقم الحساب</label>
                        <input type="text" id="test-account-code" placeholder="مثال: 1105">
                    </div>
                    <div class="form-group">
                        <label for="test-account-name">اسم الحساب</label>
                        <input type="text" id="test-account-name" placeholder="مثال: حساب اختبار">
                    </div>
                    <div class="form-group">
                        <label for="test-account-type">نوع الحساب</label>
                        <select id="test-account-type">
                            <option value="assets">أصول</option>
                            <option value="liabilities">خصوم</option>
                            <option value="equity">حقوق الملكية</option>
                            <option value="revenue">إيرادات</option>
                            <option value="expenses">مصروفات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="test-account-balance">الرصيد الافتتاحي</label>
                        <input type="number" id="test-account-balance" placeholder="0" step="0.01">
                    </div>
                </div>
                <button class="btn success" onclick="addManualAccount()">
                    <i class="fas fa-save"></i> إضافة الحساب
                </button>
            </div>

            <!-- عرض النتائج -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> النتائج والسجلات</h3>
                <button class="btn" onclick="refreshAccountsList()">
                    <i class="fas fa-sync"></i> تحديث قائمة الحسابات
                </button>
                <button class="btn warning" onclick="exportTestResults()">
                    <i class="fas fa-download"></i> تصدير النتائج
                </button>
                <div class="results" id="test-results-log">
                    <div class="log-info">جاهز لبدء الاختبارات...</div>
                </div>
            </div>

            <!-- جدول الحسابات -->
            <div class="test-section">
                <h3><i class="fas fa-table"></i> قائمة الحسابات الحالية</h3>
                <table class="accounts-table" id="accounts-display-table">
                    <thead>
                        <tr>
                            <th>رقم الحساب</th>
                            <th>اسم الحساب</th>
                            <th>النوع</th>
                            <th>الرصيد</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody id="accounts-display-body">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                لا توجد حسابات لعرضها
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة للاختبار
        let testResults = [];
        let totalTests = 0;
        let passedTests = 0;

        // دالة تسجيل النتائج
        function logResult(message, type = 'info') {
            const logContainer = document.getElementById('test-results-log');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-SA')}:</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // تحديث الإحصائيات
            if (type === 'success') passedTests++;
            if (type === 'success' || type === 'error') totalTests++;
            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('total-accounts').textContent = getAccountsCount();
            document.getElementById('test-results').textContent = `${passedTests}/${totalTests}`;
            document.getElementById('success-rate').textContent = 
                totalTests > 0 ? `${Math.round((passedTests / totalTests) * 100)}%` : '0%';
        }

        // الحصول على عدد الحسابات
        function getAccountsCount() {
            try {
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                return accounts.length;
            } catch (error) {
                return 0;
            }
        }

        // اختبار الوظائف الأساسية
        function testBasicFunctionality() {
            logResult('بدء اختبار الوظائف الأساسية...', 'info');

            // اختبار التخزين المحلي
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                logResult('✅ التخزين المحلي يعمل بشكل صحيح', 'success');
            } catch (error) {
                logResult('❌ خطأ في التخزين المحلي: ' + error.message, 'error');
                return;
            }

            // اختبار تحميل الحسابات
            try {
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                logResult(`✅ تم تحميل ${accounts.length} حساب من التخزين المحلي`, 'success');
            } catch (error) {
                logResult('❌ خطأ في تحميل الحسابات: ' + error.message, 'error');
            }

            refreshAccountsList();
        }

        // اختبار إضافة حساب واحد
        function testAddSingleAccount() {
            logResult('بدء اختبار إضافة حساب واحد...', 'info');

            const testAccount = {
                id: Date.now(),
                code: '9999',
                name: 'حساب اختبار تلقائي',
                type: 'assets',
                balance: 1000,
                status: 'active',
                level: 1,
                parentCode: null,
                parentName: null,
                createdAt: new Date().toISOString()
            };

            try {
                // تحميل الحسابات الحالية
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // التحقق من عدم وجود الحساب مسبقاً
                const existingAccount = accounts.find(acc => acc.code === testAccount.code);
                if (existingAccount) {
                    // حذف الحساب الموجود أولاً
                    accounts = accounts.filter(acc => acc.code !== testAccount.code);
                    logResult('تم حذف الحساب الموجود مسبقاً', 'warning');
                }

                // إضافة الحساب الجديد
                accounts.push(testAccount);

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                logResult(`✅ تم إضافة الحساب بنجاح: ${testAccount.code} - ${testAccount.name}`, 'success');

                // التحقق من الحفظ
                const savedAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                const savedAccount = savedAccounts.find(acc => acc.code === testAccount.code);

                if (savedAccount) {
                    logResult('✅ تم التحقق من حفظ الحساب في التخزين المحلي', 'success');
                } else {
                    logResult('❌ فشل في حفظ الحساب في التخزين المحلي', 'error');
                }

            } catch (error) {
                logResult('❌ خطأ في إضافة الحساب: ' + error.message, 'error');
            }

            refreshAccountsList();
        }

        // اختبار إضافة حسابات متعددة
        function testAddMultipleAccounts() {
            logResult('بدء اختبار إضافة حسابات متعددة...', 'info');

            const testAccounts = [
                {
                    id: Date.now() + 1,
                    code: '8001',
                    name: 'حساب اختبار 1',
                    type: 'assets',
                    balance: 5000,
                    status: 'active',
                    level: 1,
                    parentCode: null,
                    parentName: null,
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    code: '8002',
                    name: 'حساب اختبار 2',
                    type: 'liabilities',
                    balance: 3000,
                    status: 'active',
                    level: 1,
                    parentCode: null,
                    parentName: null,
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    code: '8003',
                    name: 'حساب اختبار 3',
                    type: 'revenue',
                    balance: 0,
                    status: 'active',
                    level: 1,
                    parentCode: null,
                    parentName: null,
                    createdAt: new Date().toISOString()
                }
            ];

            try {
                // تحميل الحسابات الحالية
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // إضافة الحسابات الجديدة
                testAccounts.forEach(testAccount => {
                    // التحقق من عدم وجود الحساب مسبقاً
                    const existingIndex = accounts.findIndex(acc => acc.code === testAccount.code);
                    if (existingIndex !== -1) {
                        accounts[existingIndex] = testAccount;
                        logResult(`تم تحديث الحساب الموجود: ${testAccount.code}`, 'warning');
                    } else {
                        accounts.push(testAccount);
                        logResult(`تم إضافة الحساب: ${testAccount.code} - ${testAccount.name}`, 'info');
                    }
                });

                // ترتيب الحسابات حسب الكود
                accounts.sort((a, b) => a.code.localeCompare(b.code));

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                logResult(`✅ تم إضافة ${testAccounts.length} حساب بنجاح`, 'success');

            } catch (error) {
                logResult('❌ خطأ في إضافة الحسابات المتعددة: ' + error.message, 'error');
            }

            refreshAccountsList();
        }

        // إضافة حساب يدوي
        function addManualAccount() {
            const code = document.getElementById('test-account-code').value.trim();
            const name = document.getElementById('test-account-name').value.trim();
            const type = document.getElementById('test-account-type').value;
            const balance = parseFloat(document.getElementById('test-account-balance').value) || 0;

            // التحقق من صحة البيانات
            if (!code) {
                logResult('❌ يرجى إدخال رقم الحساب', 'error');
                return;
            }

            if (!name) {
                logResult('❌ يرجى إدخال اسم الحساب', 'error');
                return;
            }

            logResult(`بدء إضافة الحساب اليدوي: ${code} - ${name}`, 'info');

            const newAccount = {
                id: Date.now(),
                code: code,
                name: name,
                type: type,
                balance: balance,
                status: 'active',
                level: 1,
                parentCode: null,
                parentName: null,
                createdAt: new Date().toISOString()
            };

            try {
                // تحميل الحسابات الحالية
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // التحقق من عدم تكرار رقم الحساب
                const existingAccount = accounts.find(acc => acc.code === code);
                if (existingAccount) {
                    logResult('❌ رقم الحساب موجود بالفعل. يرجى استخدام رقم آخر.', 'error');
                    return;
                }

                // إضافة الحساب الجديد
                accounts.push(newAccount);

                // ترتيب الحسابات حسب الكود
                accounts.sort((a, b) => a.code.localeCompare(b.code));

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                logResult(`✅ تم إضافة الحساب اليدوي بنجاح: ${code} - ${name}`, 'success');

                // مسح النموذج
                document.getElementById('test-account-code').value = '';
                document.getElementById('test-account-name').value = '';
                document.getElementById('test-account-balance').value = '';

            } catch (error) {
                logResult('❌ خطأ في إضافة الحساب اليدوي: ' + error.message, 'error');
            }

            refreshAccountsList();
        }

        // مسح جميع الحسابات
        function clearAllAccounts() {
            if (confirm('هل أنت متأكد من مسح جميع الحسابات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                try {
                    localStorage.removeItem('chartOfAccounts');
                    localStorage.removeItem('monjizAccounts');
                    logResult('✅ تم مسح جميع الحسابات', 'warning');
                    refreshAccountsList();
                } catch (error) {
                    logResult('❌ خطأ في مسح الحسابات: ' + error.message, 'error');
                }
            }
        }

        // تحديث قائمة الحسابات
        function refreshAccountsList() {
            try {
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                const tableBody = document.getElementById('accounts-display-body');

                if (accounts.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                لا توجد حسابات لعرضها
                            </td>
                        </tr>
                    `;
                    logResult('لا توجد حسابات لعرضها', 'info');
                } else {
                    tableBody.innerHTML = '';

                    // ترتيب الحسابات حسب الكود
                    accounts.sort((a, b) => a.code.localeCompare(b.code));

                    accounts.forEach(account => {
                        const row = document.createElement('tr');

                        // تحديد نوع الحساب
                        let accountTypeLabel = '';
                        let badgeClass = '';

                        switch (account.type) {
                            case 'assets':
                                accountTypeLabel = 'أصول';
                                badgeClass = 'badge-primary';
                                break;
                            case 'liabilities':
                                accountTypeLabel = 'خصوم';
                                badgeClass = 'badge-warning';
                                break;
                            case 'equity':
                                accountTypeLabel = 'حقوق الملكية';
                                badgeClass = 'badge-info';
                                break;
                            case 'revenue':
                                accountTypeLabel = 'إيرادات';
                                badgeClass = 'badge-success';
                                break;
                            case 'expenses':
                                accountTypeLabel = 'مصروفات';
                                badgeClass = 'badge-danger';
                                break;
                            default:
                                accountTypeLabel = account.type;
                                badgeClass = 'badge-secondary';
                        }

                        row.innerHTML = `
                            <td><strong>${account.code}</strong></td>
                            <td>${account.name}</td>
                            <td><span class="badge ${badgeClass}">${accountTypeLabel}</span></td>
                            <td>${(account.balance || 0).toFixed(2)} ر.س</td>
                            <td><span class="badge ${account.status === 'active' ? 'badge-success' : 'badge-danger'}">${account.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                            <td>${new Date(account.createdAt).toLocaleDateString('ar-SA')}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    logResult(`تم عرض ${accounts.length} حساب في الجدول`, 'success');
                }

                updateStats();

            } catch (error) {
                logResult('❌ خطأ في تحديث قائمة الحسابات: ' + error.message, 'error');
            }
        }

        // تصدير نتائج الاختبار
        function exportTestResults() {
            try {
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                const exportData = {
                    timestamp: new Date().toISOString(),
                    totalAccounts: accounts.length,
                    testResults: {
                        total: totalTests,
                        passed: passedTests,
                        successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
                    },
                    accounts: accounts
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                logResult('✅ تم تصدير نتائج الاختبار بنجاح', 'success');

            } catch (error) {
                logResult('❌ خطأ في تصدير النتائج: ' + error.message, 'error');
            }
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            logResult('تم تحميل صفحة اختبار نظام الحسابات', 'info');
            refreshAccountsList();
            updateStats();
        });
    </script>
</body>
</html>
