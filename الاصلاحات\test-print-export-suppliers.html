<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة وتصدير الموردين - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .feature-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .feature-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .demo-data {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .demo-data table {
            width: 100%;
            border-collapse: collapse;
        }
        .demo-data th,
        .demo-data td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        .demo-data th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ اختبار طباعة وتصدير الموردين</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>أيقونات الطباعة والتصدير في صفحة المشتريات لا تعمل مع بيانات الموردين</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>دوال فارغة أو رسائل تنبيه فقط</li>
                        <li>لا تتعامل مع بيانات الموردين</li>
                        <li>طباعة عامة للصفحة كاملة</li>
                        <li>لا يوجد تصدير حقيقي</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>دوال كاملة وفعالة</li>
                        <li>تتعامل مع بيانات الموردين من localStorage</li>
                        <li>طباعة مخصصة لتقرير الموردين</li>
                        <li>تصدير Excel (CSV) و PDF فعلي</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الميزات المضافة -->
        <div class="test-section">
            <h2>🚀 الميزات المضافة</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">🖨️</div>
                    <h3>طباعة محسنة</h3>
                    <p>طباعة تقرير مخصص للموردين مع تنسيق احترافي</p>
                    <button class="btn" onclick="testPrintFunction()">اختبار الطباعة</button>
                </div>
                <div class="feature-card">
                    <div class="icon">📊</div>
                    <h3>تصدير Excel</h3>
                    <p>تصدير بيانات الموردين إلى ملف CSV يفتح في Excel</p>
                    <button class="btn" onclick="testExcelExport()">اختبار Excel</button>
                </div>
                <div class="feature-card">
                    <div class="icon">📄</div>
                    <h3>تصدير PDF</h3>
                    <p>إنشاء تقرير PDF احترافي للموردين</p>
                    <button class="btn" onclick="testPDFExport()">اختبار PDF</button>
                </div>
            </div>
        </div>

        <!-- بيانات تجريبية -->
        <div class="test-section">
            <h2>📊 بيانات تجريبية</h2>
            <div class="highlight">
                <h3>سنضيف بعض البيانات التجريبية لاختبار الطباعة والتصدير:</h3>
            </div>
            <div class="demo-data">
                <table>
                    <thead>
                        <tr>
                            <th>اسم المورد</th>
                            <th>النوع</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>شركة التقنية المتقدمة</td>
                            <td>شركة</td>
                            <td>+966112345678</td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td>مؤسسة الإمداد الشامل</td>
                            <td>مؤسسة</td>
                            <td>+966501234567</td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td>محمد أحمد التجاري</td>
                            <td>فرد</td>
                            <td>+966551234567</td>
                            <td><EMAIL></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <button class="btn" onclick="addSampleData()">إضافة البيانات التجريبية</button>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار وظائف الطباعة والتصدير:</h3>
                <p>سنختبر جميع وظائف الطباعة والتصدير للتأكد من عملها</p>
            </div>
            
            <button class="btn" onclick="startPrintExportTest()">🚀 بدء اختبار الطباعة والتصدير</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار الوظائف:</h3>
                <ol>
                    <li><strong>إضافة البيانات:</strong> اضغط "إضافة البيانات التجريبية"</li>
                    <li><strong>فتح صفحة المشتريات:</strong> اضغط "فتح صفحة المشتريات"</li>
                    <li><strong>اختبار الطباعة:</strong> اضغط "طباعة وتصدير" ← "طباعة التقرير"</li>
                    <li><strong>اختبار Excel:</strong> اضغط "طباعة وتصدير" ← "تصدير Excel"</li>
                    <li><strong>اختبار PDF:</strong> اضغط "طباعة وتصدير" ← "تصدير PDF"</li>
                    <li><strong>التحقق:</strong> تأكد من تحميل الملفات وعرض التقارير</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="checkSupplierData()">📊 فحص بيانات الموردين</button>
            <button class="btn" onclick="simulatePrintExport()">🖨️ محاكاة الطباعة والتصدير</button>
            <button class="btn" onclick="clearTestData()">🗑️ مسح البيانات التجريبية</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>الطباعة:</strong> تطبع تقرير مخصص للموردين مع تنسيق احترافي</li>
                    <li><strong>تصدير Excel:</strong> ينزل ملف CSV يحتوي على بيانات الموردين</li>
                    <li><strong>تصدير PDF:</strong> يفتح نافذة جديدة بتقرير PDF للطباعة</li>
                    <li><strong>التحقق من البيانات:</strong> يتحقق من وجود بيانات قبل التصدير</li>
                    <li><strong>التنسيق:</strong> تقارير منسقة ومقروءة باللغة العربية</li>
                    <li><strong>التاريخ:</strong> يضيف تاريخ التقرير والطباعة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // إضافة بيانات تجريبية
        function addSampleData() {
            const sampleSuppliers = [
                {
                    id: Date.now() + 1,
                    name: 'شركة التقنية المتقدمة',
                    type: 'شركة',
                    phone: '+966112345678',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    name: 'مؤسسة الإمداد الشامل',
                    type: 'مؤسسة',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    name: 'محمد أحمد التجاري',
                    type: 'فرد',
                    phone: '+966551234567',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                }
            ];

            const existingSuppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            const allSuppliers = [...existingSuppliers, ...sampleSuppliers];
            localStorage.setItem('monjizSuppliers', JSON.stringify(allSuppliers));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة البيانات التجريبية!</strong><br><br>
                    📊 تم إضافة 3 موردين تجريبيين<br>
                    💾 البيانات محفوظة في localStorage<br>
                    🧪 يمكنك الآن اختبار الطباعة والتصدير<br><br>
                    💡 <strong>افتح صفحة المشتريات لاختبار الوظائف!</strong>
                </div>
            `);
        }

        // بدء اختبار الطباعة والتصدير
        function startPrintExportTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار الطباعة والتصدير!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ أضف البيانات التجريبية إذا لم تفعل<br>
                    2️⃣ افتح صفحة المشتريات<br>
                    3️⃣ ابحث عن زر "طباعة وتصدير"<br>
                    4️⃣ اختبر كل وظيفة على حدة<br>
                    5️⃣ تحقق من النتائج<br><br>
                    
                    <strong>🎯 اضغط "فتح صفحة المشتريات" للبدء!</strong>
                </div>
            `);
        }

        // اختبار دالة الطباعة
        function testPrintFunction() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            if (suppliers.length === 0) {
                showResult('❌ لا توجد بيانات موردين. اضغط "إضافة البيانات التجريبية" أولاً', 'info');
                return;
            }
            showResult('🖨️ دالة الطباعة جاهزة! افتح صفحة المشتريات لاختبارها', 'info');
        }

        // اختبار تصدير Excel
        function testExcelExport() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            if (suppliers.length === 0) {
                showResult('❌ لا توجد بيانات موردين. اضغط "إضافة البيانات التجريبية" أولاً', 'info');
                return;
            }
            showResult('📊 دالة تصدير Excel جاهزة! افتح صفحة المشتريات لاختبارها', 'info');
        }

        // اختبار تصدير PDF
        function testPDFExport() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            if (suppliers.length === 0) {
                showResult('❌ لا توجد بيانات موردين. اضغط "إضافة البيانات التجريبية" أولاً', 'info');
                return;
            }
            showResult('📄 دالة تصدير PDF جاهزة! افتح صفحة المشتريات لاختبارها', 'info');
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 ابحث عن زر "طباعة وتصدير" واختبر الوظائف!', 'info');
        }

        // فحص بيانات الموردين
        function checkSupplierData() {
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات الموردين:</strong><br><br>
                    
                    <strong>📈 الإحصائيات:</strong><br>
                    👥 عدد الموردين: ${suppliers.length}<br>
                    🗄️ مفتاح التخزين: monjizSuppliers<br>
                    📝 تنسيق البيانات: JSON<br><br>
                    
                    <strong>📋 البيانات المتاحة:</strong><br>
                    ${suppliers.length > 0 ? 
                        suppliers.map((s, i) => `${i+1}. ${s.name} (${s.type})`).join('<br>') :
                        'لا توجد بيانات - اضغط "إضافة البيانات التجريبية"'
                    }<br><br>
                    
                    ${suppliers.length > 0 ? 
                        '✅ <strong>البيانات جاهزة للطباعة والتصدير!</strong>' :
                        '❌ <strong>أضف بيانات أولاً لاختبار الوظائف</strong>'
                    }
                </div>
            `);
        }

        // محاكاة الطباعة والتصدير
        function simulatePrintExport() {
            showResult(`
                <div class="info">
                    🖨️ <strong>محاكاة الطباعة والتصدير:</strong><br><br>
                    
                    <strong>🔧 الوظائف المتاحة:</strong><br>
                    1️⃣ <strong>printPurchases():</strong> طباعة تقرير الموردين<br>
                    2️⃣ <strong>exportToExcel():</strong> تصدير إلى CSV/Excel<br>
                    3️⃣ <strong>exportToPDF():</strong> تصدير إلى PDF<br><br>
                    
                    <strong>📋 المميزات:</strong><br>
                    • تحقق من وجود البيانات قبل التصدير<br>
                    • تنسيق احترافي للتقارير<br>
                    • دعم اللغة العربية<br>
                    • إضافة التاريخ والوقت<br>
                    • تنزيل تلقائي للملفات<br><br>
                    
                    ✅ <strong>جميع الوظائف جاهزة للاختبار!</strong>
                </div>
            `);
        }

        // مسح البيانات التجريبية
        function clearTestData() {
            localStorage.removeItem('monjizSuppliers');
            showResult('🗑️ تم مسح جميع البيانات التجريبية', 'info');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🖨️ <strong>تم إصلاح وظائف الطباعة والتصدير!</strong><br><br>
                    ✅ دالة طباعة محسنة للموردين<br>
                    ✅ تصدير Excel (CSV) فعلي<br>
                    ✅ تصدير PDF احترافي<br>
                    ✅ تحقق من البيانات قبل التصدير<br>
                    ✅ تنسيق عربي صحيح<br>
                    ✅ إضافة التاريخ والإحصائيات<br><br>
                    🧪 <strong>اضغط "بدء اختبار الطباعة والتصدير" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
