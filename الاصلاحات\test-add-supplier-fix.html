<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح إضافة المورد - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .fixes-list {
            background: white;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .fixes-list h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        .fixes-list ul {
            list-style: none;
            padding: 0;
        }
        .fixes-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .fixes-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .steps-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .steps-list h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .steps-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .steps-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشكلة إضافة المورد</h1>

        <!-- المشكلة المكتشفة -->
        <div class="test-section">
            <h2>🎯 المشكلة المكتشفة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>❌ "مورد جديد" يقول "يرجى إدخال اسم المورد" رغم الإدخال</strong></p>
                <p>المستخدم يدخل البيانات لكن النظام لا يتعرف عليها</p>
            </div>
            
            <div class="fixes-list">
                <h3>الأسباب المكتشفة:</h3>
                <ul>
                    <li>تضارب في event listeners (onsubmit في HTML + addEventListener في JS)</li>
                    <li>استخدام متغير suppliers القديم بدلاً من suppliersAllData الجديد</li>
                    <li>استدعاء loadSuppliers القديمة بدلاً من updateSuppliersDataDisplay الجديدة</li>
                    <li>عدم تحديث suppliersTotalItems بعد الإضافة</li>
                </ul>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="fixes-list">
                <h3>تم إصلاح:</h3>
                <ul>
                    <li>إزالة onsubmit من HTML لتجنب التضارب</li>
                    <li>تحديث دالة saveSupplier لاستخدام النظام الجديد</li>
                    <li>إصلاح دالة editSupplier للعمل مع الـ ID</li>
                    <li>إصلاح دالة viewSupplier للعمل مع الـ ID</li>
                    <li>إصلاح دالة deleteSupplier للعمل مع الـ ID</li>
                    <li>تحديث suppliersTotalItems بعد كل عملية</li>
                    <li>استخدام updateSuppliersDataDisplay بدلاً من loadSuppliers</li>
                </ul>
            </div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>🧪 خطوات اختبار الإصلاح</h2>
            
            <div class="steps-list">
                <h3>اختبر إضافة مورد جديد:</h3>
                <ol>
                    <li><strong>افتح صفحة الموردين</strong></li>
                    <li><strong>اضغط "مورد جديد"</strong></li>
                    <li><strong>أدخل البيانات:</strong>
                        <ul>
                            <li>اسم المورد: "شركة الاختبار"</li>
                            <li>نوع المورد: "شركة"</li>
                            <li>رقم الهاتف: "+966501234567"</li>
                            <li>البريد الإلكتروني: "<EMAIL>"</li>
                        </ul>
                    </li>
                    <li><strong>اضغط "حفظ"</strong></li>
                    <li><strong>يجب أن يظهر:</strong> "تم إضافة المورد بنجاح!"</li>
                    <li><strong>تحقق من الجدول:</strong> المورد الجديد يظهر في القائمة</li>
                </ol>
            </div>
            
            <div class="steps-list">
                <h3>اختبر باقي الوظائف:</h3>
                <ol>
                    <li><strong>اختبر التعديل:</strong> اضغط أيقونة التعديل (صفراء)</li>
                    <li><strong>اختبر العرض:</strong> اضغط أيقونة العرض (زرقاء)</li>
                    <li><strong>اختبر الحذف:</strong> اضغط أيقونة الحذف (حمراء)</li>
                    <li><strong>اختبر التنقل:</strong> تأكد من عمل التنقل بين الصفحات</li>
                </ol>
            </div>
        </div>

        <!-- اختبار سريع -->
        <div class="test-section">
            <h2>⚡ اختبار سريع</h2>
            <button class="btn" onclick="testAddSupplier()">🏢 اختبار إضافة المورد</button>
            <button class="btn" onclick="checkSupplierData()">📊 فحص بيانات الموردين</button>
            <button class="btn" onclick="resetSupplierData()">🔄 إعادة تعيين البيانات</button>
            <div id="test-result"></div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>✅ إضافة المورد:</strong> تعمل بدون أخطاء</li>
                    <li><strong>✅ رسالة النجاح:</strong> "تم إضافة المورد بنجاح!"</li>
                    <li><strong>✅ ظهور في الجدول:</strong> المورد الجديد يظهر فوراً</li>
                    <li><strong>✅ تحديث العداد:</strong> "عرض 1 - 10 من X مورد"</li>
                    <li><strong>✅ التعديل:</strong> يعمل بشكل صحيح</li>
                    <li><strong>✅ العرض:</strong> يظهر تفاصيل المورد</li>
                    <li><strong>✅ الحذف:</strong> يحذف ويحديث الجدول</li>
                    <li><strong>✅ التنقل:</strong> يعمل مع البيانات الجديدة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار إضافة المورد
        function testAddSupplier() {
            window.open('suppliers.html', '_blank');
            showResult(`
                <div class="success">
                    🏢 <strong>تم فتح صفحة الموردين!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    1️⃣ اضغط "مورد جديد"<br>
                    2️⃣ أدخل البيانات (اسم، نوع، هاتف، إيميل)<br>
                    3️⃣ اضغط "حفظ"<br>
                    4️⃣ يجب أن تظهر رسالة "تم إضافة المورد بنجاح!"<br>
                    5️⃣ تحقق من ظهور المورد في الجدول<br><br>
                    
                    💡 <strong>إذا ظهرت رسالة خطأ، أعلمني فوراً!</strong>
                </div>
            `);
        }

        // فحص بيانات الموردين
        function checkSupplierData() {
            const suppliersData = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات الموردين:</strong><br><br>
                    
                    🏢 عدد الموردين: ${suppliersData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(suppliersData.length / 10)}<br>
                    📝 تنسيق البيانات: JSON<br><br>
                    
                    <strong>آخر 3 موردين:</strong><br>
                    ${suppliersData.slice(-3).map((s, i) => 
                        `${suppliersData.length - 2 + i}. ${s.name} (${s.type})`
                    ).join('<br>')}<br><br>
                    
                    ${suppliersData.length > 0 ? 
                        '✅ <strong>البيانات موجودة ومحفوظة!</strong>' :
                        '❌ <strong>لا توجد بيانات - أضف مورد جديد</strong>'
                    }
                </div>
            `);
        }

        // إعادة تعيين بيانات الموردين
        function resetSupplierData() {
            const suppliersData = [];
            for (let i = 1; i <= 25; i++) {
                suppliersData.push({
                    id: i,
                    code: String(i).padStart(3, '0'),
                    name: `مورد رقم ${i}`,
                    type: ['شركة', 'فرد', 'مؤسسة'][Math.floor(Math.random() * 3)],
                    phone: `+96611234${String(i).padStart(4, '0')}`,
                    email: `supplier${i}@example.com`,
                    address: `عنوان المورد رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliersData));
            
            showResult(`
                <div class="success">
                    🔄 <strong>تم إعادة تعيين بيانات الموردين!</strong><br><br>
                    📊 تم إنشاء 25 مورد تجريبي<br>
                    📄 عدد الصفحات: 3<br><br>
                    💡 <strong>افتح صفحة الموردين لرؤية البيانات الجديدة!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم إصلاح مشكلة إضافة المورد!</strong><br><br>
                    
                    <strong>المشاكل المصلحة:</strong><br>
                    ✅ إزالة تضارب event listeners<br>
                    ✅ تحديث النظام للعمل مع البيانات الجديدة<br>
                    ✅ إصلاح جميع دوال الموردين (إضافة، تعديل، عرض، حذف)<br>
                    ✅ تحديث العدادات والتنقل<br><br>
                    
                    🧪 <strong>اختبر إضافة مورد جديد الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
