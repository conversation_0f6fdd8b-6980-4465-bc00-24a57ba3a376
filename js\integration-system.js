/**
 * نظام التكامل التلقائي
 * يربط العملاء والموردين الجدد تلقائياً بدليل الحسابات
 */

class IntegrationSystem {
    constructor() {
        this.isEnabled = true;
        this.debugMode = true;
        this.storageKeys = {
            customers: 'monjizCustomers',
            suppliers: 'monjizSuppliers',
            accounts: 'chartOfAccounts',
            professionalAccounts: 'professionalChartOfAccounts'
        };
        
        this.log('🔗 نظام التكامل التلقائي جاهز');
        this.initializeEventListeners();
    }

    log(message, type = 'info') {
        if (this.debugMode) {
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '🔗';
            console.log(`${prefix} [Integration] ${message}`);
        }
    }

    // تهيئة مستمعي الأحداث
    initializeEventListeners() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === this.storageKeys.customers) {
                this.log('تم اكتشاف تغيير في العملاء');
                this.syncCustomersToAccounts();
            } else if (e.key === this.storageKeys.suppliers) {
                this.log('تم اكتشاف تغيير في الموردين');
                this.syncSuppliersToAccounts();
            }
        });

        // مراقبة تغييرات مخصصة
        document.addEventListener('customerAdded', (e) => {
            this.log(`تم إضافة عميل جديد: ${e.detail.name}`);
            this.addCustomerAccount(e.detail);
        });

        document.addEventListener('supplierAdded', (e) => {
            this.log(`تم إضافة مورد جديد: ${e.detail.name}`);
            this.addSupplierAccount(e.detail);
        });

        this.log('تم تهيئة مستمعي الأحداث');
    }

    // مزامنة جميع العملاء مع دليل الحسابات
    syncCustomersToAccounts() {
        try {
            const customers = JSON.parse(localStorage.getItem(this.storageKeys.customers)) || [];
            let accounts = JSON.parse(localStorage.getItem(this.storageKeys.accounts)) || [];
            
            let addedCount = 0;

            // التأكد من وجود الحسابات الأساسية
            this.ensureBasicCustomerAccounts(accounts);

            customers.forEach(customer => {
                const accountCode = `11030${String(customer.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    const customerAccount = this.createCustomerAccount(customer);
                    accounts.push(customerAccount);
                    addedCount++;
                    this.log(`أضيف حساب العميل: ${customer.name} (${accountCode})`, 'success');
                }
            });

            if (addedCount > 0) {
                localStorage.setItem(this.storageKeys.accounts, JSON.stringify(accounts));
                this.syncToProfessionalSystem(accounts);
                this.notifyAccountsUpdate();
                this.log(`تم إضافة ${addedCount} حساب عميل جديد`, 'success');
            }

            return addedCount;
        } catch (error) {
            this.log(`خطأ في مزامنة العملاء: ${error.message}`, 'error');
            return 0;
        }
    }

    // مزامنة جميع الموردين مع دليل الحسابات
    syncSuppliersToAccounts() {
        try {
            const suppliers = JSON.parse(localStorage.getItem(this.storageKeys.suppliers)) || [];
            let accounts = JSON.parse(localStorage.getItem(this.storageKeys.accounts)) || [];
            
            let addedCount = 0;

            // التأكد من وجود الحسابات الأساسية
            this.ensureBasicSupplierAccounts(accounts);

            suppliers.forEach(supplier => {
                const accountCode = `21030${String(supplier.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    const supplierAccount = this.createSupplierAccount(supplier);
                    accounts.push(supplierAccount);
                    addedCount++;
                    this.log(`أضيف حساب المورد: ${supplier.name} (${accountCode})`, 'success');
                }
            });

            if (addedCount > 0) {
                localStorage.setItem(this.storageKeys.accounts, JSON.stringify(accounts));
                this.syncToProfessionalSystem(accounts);
                this.notifyAccountsUpdate();
                this.log(`تم إضافة ${addedCount} حساب مورد جديد`, 'success');
            }

            return addedCount;
        } catch (error) {
            this.log(`خطأ في مزامنة الموردين: ${error.message}`, 'error');
            return 0;
        }
    }

    // إضافة حساب عميل واحد
    addCustomerAccount(customer) {
        try {
            let accounts = JSON.parse(localStorage.getItem(this.storageKeys.accounts)) || [];
            
            const accountCode = `11030${String(customer.id).padStart(3, '0')}`;
            const exists = accounts.find(acc => acc.code === accountCode);

            if (!exists) {
                this.ensureBasicCustomerAccounts(accounts);
                const customerAccount = this.createCustomerAccount(customer);
                accounts.push(customerAccount);
                
                localStorage.setItem(this.storageKeys.accounts, JSON.stringify(accounts));
                this.syncToProfessionalSystem(accounts);
                this.notifyAccountsUpdate();
                
                this.log(`تم إضافة حساب العميل: ${customer.name}`, 'success');
                return true;
            }
            
            return false;
        } catch (error) {
            this.log(`خطأ في إضافة حساب العميل: ${error.message}`, 'error');
            return false;
        }
    }

    // إضافة حساب مورد واحد
    addSupplierAccount(supplier) {
        try {
            let accounts = JSON.parse(localStorage.getItem(this.storageKeys.accounts)) || [];
            
            const accountCode = `21030${String(supplier.id).padStart(3, '0')}`;
            const exists = accounts.find(acc => acc.code === accountCode);

            if (!exists) {
                this.ensureBasicSupplierAccounts(accounts);
                const supplierAccount = this.createSupplierAccount(supplier);
                accounts.push(supplierAccount);
                
                localStorage.setItem(this.storageKeys.accounts, JSON.stringify(accounts));
                this.syncToProfessionalSystem(accounts);
                this.notifyAccountsUpdate();
                
                this.log(`تم إضافة حساب المورد: ${supplier.name}`, 'success');
                return true;
            }
            
            return false;
        } catch (error) {
            this.log(`خطأ في إضافة حساب المورد: ${error.message}`, 'error');
            return false;
        }
    }

    // إنشاء حساب عميل
    createCustomerAccount(customer) {
        return {
            id: Date.now() + Math.random(),
            code: `11030${String(customer.id).padStart(3, '0')}`,
            name: customer.name,
            type: 'assets',
            subType: 'current_assets',
            category: 'customers',
            parentCode: '11030',
            parentName: 'العملاء',
            balance: 0,
            status: 'active',
            linkedType: 'customer',
            linkedId: customer.id,
            level: 4,
            createdAt: new Date().toISOString(),
            autoCreated: true
        };
    }

    // إنشاء حساب مورد
    createSupplierAccount(supplier) {
        return {
            id: Date.now() + Math.random(),
            code: `21030${String(supplier.id).padStart(3, '0')}`,
            name: supplier.name,
            type: 'liabilities',
            subType: 'current_liabilities',
            category: 'suppliers',
            parentCode: '21030',
            parentName: 'الموردون',
            balance: 0,
            status: 'active',
            linkedType: 'supplier',
            linkedId: supplier.id,
            level: 3,
            createdAt: new Date().toISOString(),
            autoCreated: true
        };
    }

    // التأكد من وجود الحسابات الأساسية للعملاء
    ensureBasicCustomerAccounts(accounts) {
        const basicAccounts = [
            { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, parentCode: null },
            { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, parentCode: '1' },
            { id: 1103, code: '1103', name: 'الذمم المدينة', type: 'assets', level: 3, parentCode: '11' },
            { id: 11030, code: '11030', name: 'العملاء', type: 'assets', level: 3, parentCode: '1103' }
        ];

        basicAccounts.forEach(basicAccount => {
            const exists = accounts.find(acc => acc.code === basicAccount.code);
            if (!exists) {
                accounts.push({
                    ...basicAccount,
                    balance: 0,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    autoCreated: true
                });
            }
        });
    }

    // التأكد من وجود الحسابات الأساسية للموردين
    ensureBasicSupplierAccounts(accounts) {
        const basicAccounts = [
            { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, parentCode: null },
            { id: 21, code: '21', name: 'الخصوم المتداولة', type: 'liabilities', level: 2, parentCode: '2' },
            { id: 2103, code: '2103', name: 'الذمم الدائنة', type: 'liabilities', level: 3, parentCode: '21' },
            { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', level: 3, parentCode: '2103' }
        ];

        basicAccounts.forEach(basicAccount => {
            const exists = accounts.find(acc => acc.code === basicAccount.code);
            if (!exists) {
                accounts.push({
                    ...basicAccount,
                    balance: 0,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    autoCreated: true
                });
            }
        });
    }

    // مزامنة مع النظام الاحترافي
    syncToProfessionalSystem(accounts) {
        try {
            // تحويل الحسابات إلى تنسيق النظام الاحترافي
            const professionalAccounts = this.convertToProfessionalFormat(accounts);
            localStorage.setItem(this.storageKeys.professionalAccounts, JSON.stringify(professionalAccounts));
            this.log('تم تحديث النظام الاحترافي');
        } catch (error) {
            this.log(`خطأ في مزامنة النظام الاحترافي: ${error.message}`, 'error');
        }
    }

    // تحويل الحسابات إلى تنسيق النظام الاحترافي
    convertToProfessionalFormat(accounts) {
        // هذه دالة مبسطة - يمكن تطويرها أكثر
        const professionalAccounts = [];
        
        // تجميع الحسابات حسب المستوى
        const rootAccounts = accounts.filter(acc => !acc.parentCode);
        
        rootAccounts.forEach(rootAccount => {
            const professionalAccount = {
                id: rootAccount.id,
                code: rootAccount.code,
                name: rootAccount.name,
                type: rootAccount.type,
                parentId: null,
                balance: rootAccount.balance || 0,
                level: 0,
                children: this.buildChildrenTree(rootAccount.code, accounts, 1)
            };
            professionalAccounts.push(professionalAccount);
        });

        return professionalAccounts;
    }

    // بناء شجرة الأطفال للنظام الاحترافي
    buildChildrenTree(parentCode, accounts, level) {
        const children = accounts.filter(acc => acc.parentCode === parentCode);
        
        return children.map(child => ({
            id: child.id,
            code: child.code,
            name: child.name,
            type: child.type,
            parentId: child.parentCode,
            balance: child.balance || 0,
            level: level,
            children: this.buildChildrenTree(child.code, accounts, level + 1)
        }));
    }

    // إشعار تحديث الحسابات
    notifyAccountsUpdate() {
        // إرسال حدث مخصص لتحديث الواجهات
        const event = new CustomEvent('accountsUpdated', {
            detail: { timestamp: new Date().toISOString() }
        });
        document.dispatchEvent(event);

        // تحديث فوري لصفحة المحاسبة إذا كانت مفتوحة
        if (typeof loadAccountsFromCentralSystem === 'function') {
            setTimeout(() => {
                loadAccountsFromCentralSystem();
            }, 100);
        }

        // تحديث النظام الاحترافي إذا كان متاحاً
        if (typeof updateProfessionalStats === 'function' && typeof renderProfessionalTree === 'function') {
            setTimeout(() => {
                if (window.professionalChartOfAccounts) {
                    window.professionalChartOfAccounts.accounts = window.professionalChartOfAccounts.loadProfessionalAccounts();
                    updateProfessionalStats();
                    renderProfessionalTree();
                }
            }, 200);
        }
    }

    // مزامنة شاملة
    fullSync() {
        this.log('بدء المزامنة الشاملة...');
        
        const customerCount = this.syncCustomersToAccounts();
        const supplierCount = this.syncSuppliersToAccounts();
        
        const totalAdded = customerCount + supplierCount;
        
        if (totalAdded > 0) {
            this.log(`المزامنة الشاملة مكتملة: ${totalAdded} حساب جديد`, 'success');
        } else {
            this.log('المزامنة الشاملة مكتملة: لا توجد حسابات جديدة');
        }
        
        return { customers: customerCount, suppliers: supplierCount, total: totalAdded };
    }

    // تشخيص حالة التكامل
    diagnose() {
        const customers = JSON.parse(localStorage.getItem(this.storageKeys.customers)) || [];
        const suppliers = JSON.parse(localStorage.getItem(this.storageKeys.suppliers)) || [];
        const accounts = JSON.parse(localStorage.getItem(this.storageKeys.accounts)) || [];
        
        const customerAccounts = accounts.filter(acc => acc.category === 'customers');
        const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
        
        const diagnosis = {
            customers: {
                total: customers.length,
                withAccounts: customerAccounts.length,
                missing: customers.length - customerAccounts.length
            },
            suppliers: {
                total: suppliers.length,
                withAccounts: supplierAccounts.length,
                missing: suppliers.length - supplierAccounts.length
            },
            accounts: {
                total: accounts.length,
                customers: customerAccounts.length,
                suppliers: supplierAccounts.length
            }
        };
        
        this.log('تشخيص التكامل:', 'info');
        console.table(diagnosis);
        
        return diagnosis;
    }
}

// إنشاء مثيل عام للنظام
window.integrationSystem = new IntegrationSystem();

// دوال مساعدة عامة
window.syncAllAccounts = () => window.integrationSystem.fullSync();
window.diagnoseIntegration = () => window.integrationSystem.diagnose();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IntegrationSystem;
}
