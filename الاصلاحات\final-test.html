<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - منجز</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل لنظام منجز</h1>

        <!-- إحصائيات سريعة -->
        <div class="status-grid">
            <div class="status-card">
                <div class="status-number" id="products-count">0</div>
                <div>المنتجات المحفوظة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="customers-count">0</div>
                <div>العملاء المحفوظين</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="invoices-count">0</div>
                <div>الفواتير المحفوظة</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="suppliers-count">0</div>
                <div>الموردين المحفوظين</div>
            </div>
        </div>

        <!-- اختبار المنتجات -->
        <div class="test-section">
            <h2>📦 اختبار المنتجات</h2>
            <button class="btn" onclick="addTestProducts()">إضافة منتجات تجريبية (15 منتج)</button>
            <button class="btn" onclick="testProductPersistence()">اختبار الحفظ والاستمرارية</button>
            <button class="btn" onclick="clearProducts()">مسح المنتجات</button>
            <div id="products-result"></div>
        </div>

        <!-- اختبار العملاء -->
        <div class="test-section">
            <h2>👥 اختبار العملاء</h2>
            <button class="btn" onclick="addTestCustomers()">إضافة عملاء تجريبيين (12 عميل)</button>
            <button class="btn" onclick="testCustomerPersistence()">اختبار الحفظ والاستمرارية</button>
            <button class="btn" onclick="clearCustomers()">مسح العملاء</button>
            <div id="customers-result"></div>
        </div>

        <!-- اختبار التنقل -->
        <div class="test-section">
            <h2>📄 اختبار التنقل بين الصفحات (Pagination)</h2>
            <button class="btn" onclick="testPagination()">اختبار التنقل</button>
            <button class="btn" onclick="openProductsPage()">فتح صفحة المنتجات</button>
            <button class="btn" onclick="openCustomersPage()">فتح صفحة العملاء</button>
            <div id="pagination-result"></div>
        </div>

        <!-- اختبار الربط -->
        <div class="test-section">
            <h2>🔗 اختبار الربط بين الصفحات</h2>
            <button class="btn" onclick="testFullLinking()">اختبار الربط الشامل</button>
            <button class="btn" onclick="openSalesPage()">فتح صفحة المبيعات</button>
            <button class="btn" onclick="openReportsPage()">فتح صفحة التقارير</button>
            <div id="linking-result"></div>
        </div>

        <!-- إدارة البيانات -->
        <div class="test-section">
            <h2>🗑️ إدارة البيانات</h2>
            <button class="btn" onclick="exportData()">تصدير البيانات</button>
            <button class="btn" onclick="clearAllData()">مسح جميع البيانات</button>
            <button class="btn" onclick="refreshStats()">تحديث الإحصائيات</button>
            <div id="data-result"></div>
        </div>
    </div>

    <script>
        // إضافة منتجات تجريبية
        function addTestProducts() {
            const testProducts = [
                { name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000 },
                { name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800 },
                { name: 'هاتف آيفون 15 Pro', category: 'phones', code: 'IPHONE-15P', price: 5200, cost: 4500 },
                { name: 'شاشة سامسونج 32 بوصة', category: 'electronics', code: 'SAM-32', price: 1800, cost: 1400 },
                { name: 'طابعة ليزر HP', category: 'electronics', code: 'HP-LASER', price: 1200, cost: 900 },
                { name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250 },
                { name: 'ماوس لاسلكي', category: 'accessories', code: 'WIRELESS-M', price: 150, cost: 100 },
                { name: 'سماعات بلوتوث', category: 'accessories', code: 'BT-HEAD', price: 280, cost: 200 },
                { name: 'كاميرا ويب 4K', category: 'electronics', code: 'WEBCAM-4K', price: 450, cost: 320 },
                { name: 'مكتب خشبي فاخر', category: 'furniture', code: 'DESK-LUX', price: 1800, cost: 1200 },
                { name: 'كرسي مكتبي جلد', category: 'furniture', code: 'CHAIR-LEATHER', price: 650, cost: 400 },
                { name: 'خزانة ملفات', category: 'furniture', code: 'FILE-CAB', price: 800, cost: 550 },
                { name: 'تابلت آيباد', category: 'electronics', code: 'IPAD-PRO', price: 2800, cost: 2300 },
                { name: 'شاحن لاسلكي', category: 'accessories', code: 'WIRELESS-CH', price: 120, cost: 80 },
                { name: 'حامل شاشة قابل للتعديل', category: 'accessories', code: 'MONITOR-ARM', price: 200, cost: 140 }
            ];

            let products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            testProducts.forEach(product => {
                const newProduct = {
                    id: Date.now() + Math.random(),
                    ...product,
                    quantity: Math.floor(Math.random() * 20) + 5,
                    minStock: Math.floor(Math.random() * 5) + 1,
                    description: `منتج تجريبي: ${product.name}`
                };
                products.push(newProduct);
            });

            localStorage.setItem('monjizProducts', JSON.stringify(products));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('products-result', `✅ تم إضافة ${testProducts.length} منتج تجريبي بنجاح!<br>إجمالي المنتجات: ${products.length}`, 'success');
            refreshStats();
        }

        // إضافة عملاء تجريبيين
        function addTestCustomers() {
            const testCustomers = [
                { name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { name: 'شركة الأنوار للتجارة', type: 'company', phone: '+966502345678', email: '<EMAIL>' },
                { name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' },
                { name: 'مؤسسة النجاح', type: 'company', phone: '+966504567890', email: '<EMAIL>' },
                { name: 'فاطمة السعد', type: 'individual', phone: '+966505678901', email: '<EMAIL>' },
                { name: 'مكتب الاستشارات القانونية', type: 'company', phone: '+966506789012', email: '<EMAIL>' },
                { name: 'محمد الأحمد', type: 'individual', phone: '+966507890123', email: '<EMAIL>' },
                { name: 'صيدلية الشفاء', type: 'company', phone: '+966508901234', email: '<EMAIL>' },
                { name: 'سارة خالد', type: 'individual', phone: '+966509012345', email: '<EMAIL>' },
                { name: 'مركز التدريب المتقدم', type: 'company', phone: '+966500123456', email: '<EMAIL>' },
                { name: 'عبدالله العتيبي', type: 'individual', phone: '+966501234560', email: '<EMAIL>' },
                { name: 'معرض السيارات الحديثة', type: 'company', phone: '+966502345671', email: '<EMAIL>' }
            ];

            let customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            testCustomers.forEach(customer => {
                const newCustomer = {
                    id: Date.now() + Math.random(),
                    ...customer,
                    address: 'الرياض، المملكة العربية السعودية',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                };
                customers.push(newCustomer);
            });

            localStorage.setItem('monjizCustomers', JSON.stringify(customers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('customers-result', `✅ تم إضافة ${testCustomers.length} عميل تجريبي بنجاح!<br>إجمالي العملاء: ${customers.length}`, 'success');
            refreshStats();
        }

        // اختبار الحفظ والاستمرارية للمنتجات
        function testProductPersistence() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            if (products.length === 0) {
                showResult('products-result', '❌ لا توجد منتجات للاختبار. أضف منتجات أولاً.', 'error');
                return;
            }

            showResult('products-result', `✅ اختبار الاستمرارية ناجح!<br>تم العثور على ${products.length} منتج محفوظ<br>آخر منتج: ${products[products.length - 1].name}`, 'success');
        }

        // اختبار الحفظ والاستمرارية للعملاء
        function testCustomerPersistence() {
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            if (customers.length === 0) {
                showResult('customers-result', '❌ لا يوجد عملاء للاختبار. أضف عملاء أولاً.', 'error');
                return;
            }

            showResult('customers-result', `✅ اختبار الاستمرارية ناجح!<br>تم العثور على ${customers.length} عميل محفوظ<br>آخر عميل: ${customers[customers.length - 1].name}`, 'success');
        }

        // اختبار التنقل بين الصفحات
        function testPagination() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">📄 نتائج اختبار التنقل:</div>';
            
            if (products.length > 10) {
                result += `<p>✅ المنتجات: ${products.length} منتج (يحتاج ${Math.ceil(products.length / 10)} صفحات)</p>`;
            } else {
                result += `<p>⚠️ المنتجات: ${products.length} منتج (صفحة واحدة فقط)</p>`;
            }
            
            if (customers.length > 10) {
                result += `<p>✅ العملاء: ${customers.length} عميل (يحتاج ${Math.ceil(customers.length / 10)} صفحات)</p>`;
            } else {
                result += `<p>⚠️ العملاء: ${customers.length} عميل (صفحة واحدة فقط)</p>`;
            }
            
            result += '<p>💡 افتح صفحات المنتجات والعملاء لاختبار التنقل بين الصفحات</p>';
            
            document.getElementById('pagination-result').innerHTML = result;
        }

        // اختبار الربط الشامل
        function testFullLinking() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">🔗 نتائج اختبار الربط الشامل:</div>';
            
            const hasProjector = products.some(p => p.name.includes('جهاز عرض'));
            const hasTopaz = customers.some(c => c.name.includes('توباز'));
            
            result += `<p>📦 المنتجات: ${products.length} (جهاز عرض: ${hasProjector ? '✅' : '❌'})</p>`;
            result += `<p>👥 العملاء: ${customers.length} (مطعم توباز: ${hasTopaz ? '✅' : '❌'})</p>`;
            
            if (products.length > 0 && customers.length > 0) {
                result += '<div class="success">🎉 الربط جاهز! يمكنك فتح صفحة المبيعات وإنشاء فاتورة</div>';
            } else {
                result += '<div class="error">❌ أضف منتجات وعملاء أولاً</div>';
            }
            
            document.getElementById('linking-result').innerHTML = result;
        }

        // تحديث الإحصائيات
        function refreshStats() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            document.getElementById('products-count').textContent = products.length;
            document.getElementById('customers-count').textContent = customers.length;
            document.getElementById('invoices-count').textContent = invoices.length;
            document.getElementById('suppliers-count').textContent = suppliers.length;
        }

        // مسح البيانات
        function clearProducts() {
            localStorage.removeItem('monjizProducts');
            showResult('products-result', '🗑️ تم مسح جميع المنتجات', 'info');
            refreshStats();
        }

        function clearCustomers() {
            localStorage.removeItem('monjizCustomers');
            showResult('customers-result', '🗑️ تم مسح جميع العملاء', 'info');
            refreshStats();
        }

        function clearAllData() {
            localStorage.clear();
            showResult('data-result', '🗑️ تم مسح جميع البيانات', 'info');
            refreshStats();
        }

        // فتح الصفحات
        function openProductsPage() { window.open('products.html', '_blank'); }
        function openCustomersPage() { window.open('customers.html', '_blank'); }
        function openSalesPage() { window.open('sales.html', '_blank'); }
        function openReportsPage() { window.open('reports.html', '_blank'); }

        // تصدير البيانات
        function exportData() {
            const data = {
                products: JSON.parse(localStorage.getItem('monjizProducts')) || [],
                customers: JSON.parse(localStorage.getItem('monjizCustomers')) || [],
                invoices: JSON.parse(localStorage.getItem('monjizInvoices')) || [],
                suppliers: JSON.parse(localStorage.getItem('monjizSuppliers')) || []
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'monjiz-data-backup.json';
            a.click();
            
            showResult('data-result', '📁 تم تصدير البيانات بنجاح', 'success');
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل الإحصائيات عند فتح الصفحة
        window.addEventListener('load', refreshStats);
    </script>
</body>
</html>
