// ملف JavaScript لإدارة المشتريات

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل نظام إدارة المشتريات');
    console.log('حالة النظام المركزي عند التحميل:', window.dataManager ? 'متاح' : 'غير متاح');

    // تهيئة النظام
    initPurchasesSystem();

    // إضافة مستمعي الأحداث
    setupEventListeners();

    // انتظار تحميل النظام المركزي وتحديث القوائم
    setTimeout(() => {
        console.log('حالة النظام المركزي بعد التأخير:', window.dataManager ? 'متاح' : 'غير متاح');
        if (window.dataManager) {
            console.log('عدد المنتجات في النظام المركزي:', window.dataManager.getProducts().length);

            // تحديث قوائم المنتجات والموردين
            updateProductSelects();
            updateSupplierSelects();
        }
    }, 500);
});

// دالة تهيئة نظام المشتريات
function initPurchasesSystem() {
    console.log('تهيئة نظام المشتريات...');
    
    // تحديث الإحصائيات
    updatePurchasesStats();
    
    // تهيئة البحث والتصفية
    initSearchAndFilters();
}

// دالة إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر إضافة فاتورة شراء جديدة
    const addPurchaseBtn = document.querySelector('.add-purchase-btn');
    if (addPurchaseBtn) {
        addPurchaseBtn.addEventListener('click', showAddPurchaseModal);
    }

    // زر إضافة مورد جديد
    const addSupplierBtn = document.querySelector('.add-supplier-btn');
    if (addSupplierBtn) {
        addSupplierBtn.addEventListener('click', showAddSupplierModal);
    }
    
    // زر مسح التصفية
    const clearFiltersBtn = document.getElementById('clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearAllFilters);
    }
    
    // حقل البحث
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // قوائم التصفية
    const statusFilter = document.getElementById('status-filter');
    const paymentFilter = document.getElementById('payment-filter');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    
    if (paymentFilter) {
        paymentFilter.addEventListener('change', applyFilters);
    }
}

// دالة لإدارة القوائم المنسدلة
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        dropdown.classList.toggle('show');
    }

    // إغلاق القوائم الأخرى
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== dropdownId) {
            menu.classList.remove('show');
        }
    });
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.matches('.dropdown-toggle')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});

// دالة لعرض نافذة إضافة فاتورة شراء جديدة
function showAddPurchaseModal() {
    console.log('فتح نافذة إضافة فاتورة شراء جديدة...');
    console.log('حالة النظام المركزي:', window.dataManager ? 'متاح' : 'غير متاح');
    
    // إنشاء النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content-modern">
            <div class="modal-header-modern">
                <h3><i class="fas fa-plus-circle"></i> إضافة فاتورة شراء جديدة</h3>
                <button class="close-btn" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body-modern">
                <form id="add-purchase-form" class="form-modern">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase-supplier">المورد *</label>
                            <select id="purchase-supplier" class="select-modern" required>
                                <option value="">اختر المورد</option>
                                <option value="1">شركة التقنية المتقدمة</option>
                                <option value="2">مؤسسة الإمداد الشامل</option>
                                <option value="3">شركة المواد الأولية</option>
                                <option value="4">مجموعة التوريد المحدودة</option>
                                <option value="5">شركة الخدمات اللوجستية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="purchase-date">التاريخ *</label>
                            <input type="date" id="purchase-date" class="input-modern" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase-payment">طريقة الدفع *</label>
                            <select id="purchase-payment" class="select-modern" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقد</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="transfer">تحويل بنكي</option>
                                <option value="credit">آجل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="purchase-status">الحالة *</label>
                            <select id="purchase-status" class="select-modern" required>
                                <option value="completed">مكتملة</option>
                                <option value="pending">معلقة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="products-section">
                        <h4><i class="fas fa-boxes"></i> الأصناف</h4>
                        <div class="products-container" id="products-container">
                            <div class="product-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>الصنف</label>
                                        <select class="select-modern product-select" required id="initial-product-select" onchange="updateProductPrice(this)">
                                            <option value="">اختر الصنف</option>
                                            <option value="1" data-price="35">أرز بسمتي - كيس 5 كيلو (35 ر.س)</option>
                                            <option value="2" data-price="18">سكر أبيض - كيس 2 كيلو (18 ر.س)</option>
                                            <option value="3" data-price="12">زيت طبخ - عبوة 1 لتر (12 ر.س)</option>
                                            <option value="4" data-price="1800">لابتوب Dell (1800 ر.س)</option>
                                            <option value="5" data-price="18">قهوة عربية (18 ر.س)</option>
                                            <option value="6" data-price="15">شاي (15 ر.س)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>الكمية</label>
                                        <input type="number" class="input-modern quantity-input" min="1" value="1" required onchange="calculateProductTotal(this)">
                                    </div>
                                    <div class="form-group">
                                        <label>السعر</label>
                                        <input type="number" class="input-modern price-input" step="0.01" onchange="calculateProductTotal(this)">
                                    </div>
                                    <div class="form-group">
                                        <label>المجموع</label>
                                        <input type="number" class="input-modern total-input" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label>إجراء</label>
                                        <button type="button" class="btn-icon btn-danger" onclick="removeProductItem(this)" title="حذف الصنف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="products-actions">
                            <button type="button" class="btn-modern btn-secondary" onclick="addProductItem()">
                                <i class="fas fa-plus"></i> إضافة صنف آخر
                            </button>
                        </div>
                    </div>
                    
                    <div class="totals-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label>المجموع الفرعي</label>
                                <input type="number" id="subtotal" class="input-modern" readonly>
                            </div>
                            <div class="form-group">
                                <label>الضريبة (15%)</label>
                                <input type="number" id="tax" class="input-modern" readonly>
                            </div>
                            <div class="form-group">
                                <label><strong>المجموع الكلي</strong></label>
                                <input type="number" id="total" class="input-modern total-final" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="purchase-notes">ملاحظات</label>
                        <textarea id="purchase-notes" class="textarea-modern" rows="3" placeholder="أضف أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-modern btn-primary" onclick="savePurchase()">
                            <i class="fas fa-save"></i> حفظ فاتورة الشراء
                        </button>
                        <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // إضافة النافذة للصفحة
    document.body.appendChild(modal);
    
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('purchase-date').value = today;
    
    // إضافة مستمعي الأحداث
    setupPurchaseModalEvents(modal);
    
    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('purchase-supplier').focus();
    }, 100);

    // تعطيل تحديث القوائم مؤقتاً للاختبار
    console.log('تم تعطيل تحديث القوائم - الاختبار بالبيانات الثابتة');
}

// دالة لتحديث قوائم المنتجات
function updateProductSelects() {
    console.log('تحديث قوائم المنتجات في المشتريات...');

    const productSelects = document.querySelectorAll('.product-select');
    console.log('عدد قوائم المنتجات:', productSelects.length);

    productSelects.forEach((select, index) => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الصنف</option>';

        // إضافة منتجات ثابتة مع الأسعار
        const products = [
            { id: 1, name: 'أرز بسمتي - كيس 5 كيلو', price: 35.00 },
            { id: 2, name: 'سكر أبيض - كيس 2 كيلو', price: 18.00 },
            { id: 3, name: 'زيت طبخ - عبوة 1 لتر', price: 12.00 },
            { id: 4, name: 'لابتوب Dell', price: 1800.00 },
            { id: 5, name: 'قهوة عربية', price: 18.00 },
            { id: 6, name: 'شاي', price: 15.00 }
        ];

        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.setAttribute('data-price', product.price);
            option.textContent = `${product.name} (${product.price} ر.س)`;
            select.appendChild(option);
        });

        console.log(`تم تحديث القائمة ${index + 1} بـ ${products.length} منتج`);
    });
}

// دالة لتحديث قوائم الموردين
function updateSupplierSelects() {
    console.log('تحديث قوائم الموردين في المشتريات...');

    if (!window.dataManager) {
        console.error('النظام المركزي غير متاح');
        return;
    }

    const suppliers = window.dataManager.getSuppliers();
    console.log('الموردين المتاحين:', suppliers);

    const supplierSelects = document.querySelectorAll('.supplier-select, #purchase-supplier');
    console.log('عدد قوائم الموردين:', supplierSelects.length);

    supplierSelects.forEach((select, index) => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر المورد</option>';

        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.name;
            if (supplier.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        console.log(`تم تحديث قائمة الموردين ${index + 1} بـ ${suppliers.length} مورد`);
    });
}

// دالة لتحديث جميع الأسعار في النافذة
function updateAllProductPrices() {
    console.log('تحديث جميع أسعار المنتجات...');

    const productSelects = document.querySelectorAll('.product-select');
    productSelects.forEach((select, index) => {
        if (select.value) {
            console.log(`تحديث سعر المنتج ${index + 1}:`, select.value);
            updateProductPrice(select);
        }
    });
}

// دالة إعداد أحداث النافذة المنبثقة
function setupPurchaseModalEvents(modal) {
    const form = modal.querySelector('#add-purchase-form');
    const productSelect = modal.querySelector('.product-select');
    const quantityInput = modal.querySelector('.quantity-input');
    const priceInput = modal.querySelector('.price-input');
    const totalInput = modal.querySelector('.total-input');
    
    // تحديث السعر عند اختيار المنتج - تم نقله إلى دالة updateProductPrice
    
    // تحديث المجموع عند تغيير الكمية
    quantityInput.addEventListener('input', updateProductTotal);
    
    function updateProductTotal() {
        const quantity = parseInt(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;
        totalInput.value = total;
        updatePurchaseTotals();
    }
    
    function updatePurchaseTotals() {
        const subtotal = parseFloat(totalInput.value) || 0;
        const tax = subtotal * 0.15;
        const total = subtotal + tax;
        
        document.getElementById('subtotal').value = subtotal.toFixed(2);
        document.getElementById('tax').value = tax.toFixed(2);
        document.getElementById('total').value = total.toFixed(2);
    }
    
    // معالجة إرسال النموذج
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // جمع بيانات فاتورة الشراء
        const purchaseData = {
            supplier: document.getElementById('purchase-supplier').value,
            date: document.getElementById('purchase-date').value,
            payment: document.getElementById('purchase-payment').value,
            status: document.getElementById('purchase-status').value,
            product: productSelect.value,
            quantity: quantityInput.value,
            price: priceInput.value,
            subtotal: document.getElementById('subtotal').value,
            tax: document.getElementById('tax').value,
            total: document.getElementById('total').value,
            notes: document.getElementById('purchase-notes').value
        };
        
        // حفظ فاتورة الشراء
        savePurchase(purchaseData);
        
        // إغلاق النافذة
        document.body.removeChild(modal);
        
        // عرض رسالة نجاح
        showAlert('تم إنشاء فاتورة الشراء بنجاح!', 'success');
    });
}

// دالة إغلاق النافذة المنبثقة - تعديل لضمان الإغلاق من أول ضغطة
function closeModal(element) {
    console.log('تم النقر على زر الإغلاق');
    // تحديد النافذة المنبثقة بشكل مباشر
    const modal = element.closest('.modal-overlay');
    if (modal) {
        // إزالة النافذة من الصفحة مباشرة
        document.body.removeChild(modal);
        console.log('تم إغلاق النافذة');
    } else {
        console.log('لم يتم العثور على النافذة');
    }
    return false; // منع انتشار الحدث
}

// دالة حفظ فاتورة الشراء
function savePurchase() {
    console.log('حفظ فاتورة الشراء...');

    // جمع بيانات النموذج
    const supplier = document.getElementById('purchase-supplier').value;
    const date = document.getElementById('purchase-date').value;
    const payment = document.getElementById('purchase-payment').value;
    const status = document.getElementById('purchase-status').value;
    const notes = document.getElementById('purchase-notes').value;

    // جمع بيانات الأصناف
    const items = [];
    const productItems = document.querySelectorAll('.product-item');

    productItems.forEach(item => {
        const select = item.querySelector('.product-select');
        const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(item.querySelector('.price-input').value) || 0;
        const total = parseFloat(item.querySelector('.total-input').value) || 0;

        if (select.value && quantity > 0 && price > 0) {
            items.push({
                productId: parseInt(select.value),
                productName: select.options[select.selectedIndex].text,
                quantity: quantity,
                price: price,
                total: total
            });
        }
    });

    // التحقق من صحة البيانات
    if (!supplier || !date || items.length === 0) {
        alert('يرجى ملء جميع الحقول المطلوبة وإضافة صنف واحد على الأقل');
        return;
    }

    // حساب الإجماليات
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const grandTotal = subtotal + tax;

    // إنشاء فاتورة الشراء
    const purchase = {
        invoiceNumber: 'PUR-' + Date.now(),
        supplier: supplier,
        date: date,
        payment: payment,
        status: status,
        notes: notes,
        items: items,
        subtotal: subtotal,
        tax: tax,
        total: grandTotal
    };

    // حفظ الفاتورة باستخدام النظام المركزي
    const savedPurchase = window.dataManager.addPurchase(purchase);

    if (savedPurchase) {
        alert(`تم حفظ فاتورة الشراء بنجاح!\nرقم الفاتورة: ${savedPurchase.invoiceNumber}\nالإجمالي: ${grandTotal.toFixed(2)} ر.س`);

        // إغلاق النافذة
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            closeModal(modal.querySelector('.close-btn'));
        }

        // تحديث الصفحة لإظهار البيانات الجديدة
        setTimeout(() => {
            location.reload();
        }, 1000);
    } else {
        alert('حدث خطأ أثناء حفظ الفاتورة');
    }
}

// دالة عرض التنبيهات
function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        z-index: 10000;
        animation: slideIn 0.3s ease-in-out;
    `;
    
    if (type === 'success') {
        alert.style.backgroundColor = '#28a745';
    } else if (type === 'error') {
        alert.style.backgroundColor = '#dc3545';
    } else {
        alert.style.backgroundColor = '#17a2b8';
    }
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        document.body.removeChild(alert);
    }, 3000);
}

// دوال أخرى مطلوبة
function updatePurchasesStats() {
    console.log('تحديث إحصائيات المشتريات...');
}

function initSearchAndFilters() {
    console.log('تهيئة البحث والتصفية...');
}

function clearAllFilters() {
    console.log('مسح جميع التصفيات...');
}

function handleSearch() {
    console.log('البحث...');
}

function applyFilters() {
    console.log('تطبيق التصفيات...');
}

function editPurchase(id) {
    console.log('تعديل فاتورة الشراء:', id);
}

function viewPurchase(id) {
    console.log('عرض فاتورة الشراء:', id);
}

function deletePurchase(id) {
    if (confirm('هل أنت متأكد من حذف فاتورة الشراء هذه؟')) {
        console.log('حذف فاتورة الشراء:', id);
        showAlert('تم حذف فاتورة الشراء بنجاح!', 'success');
    }
}

function printPurchases() {
    console.log('طباعة تقرير المشتريات...');
}

function exportToExcel() {
    console.log('تصدير إلى Excel...');
}

function exportToPDF() {
    console.log('تصدير إلى PDF...');
}

// دالة لعرض نافذة إضافة مورد جديد
function showAddSupplierModal() {
    // إنشاء النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content-modern">
            <div class="modal-header-modern">
                <h3><i class="fas fa-user-plus"></i> إضافة مورد جديد</h3>
                <button class="close-btn" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body-modern">
                <form id="add-supplier-form" class="form-modern">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplier-type">نوع المورد *</label>
                            <select id="supplier-type" class="select-modern" required>
                                <option value="">اختر نوع المورد</option>
                                <option value="individual">فرد</option>
                                <option value="company">شركة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="supplier-name">اسم المورد *</label>
                            <input type="text" id="supplier-name" class="input-modern" required placeholder="أدخل اسم المورد">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplier-phone">رقم الهاتف *</label>
                            <input type="tel" id="supplier-phone" class="input-modern" required placeholder="05xxxxxxxx">
                        </div>
                        <div class="form-group">
                            <label for="supplier-email">البريد الإلكتروني</label>
                            <input type="email" id="supplier-email" class="input-modern" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplier-company">اسم الشركة</label>
                            <input type="text" id="supplier-company" class="input-modern" placeholder="اسم الشركة (اختياري)">
                        </div>
                        <div class="form-group">
                            <label for="supplier-tax-number">الرقم الضريبي</label>
                            <input type="text" id="supplier-tax-number" class="input-modern" placeholder="الرقم الضريبي (اختياري)">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="supplier-address">العنوان</label>
                            <textarea id="supplier-address" class="textarea-modern" rows="3" placeholder="أدخل عنوان المورد"></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplier-category">فئة المورد</label>
                            <select id="supplier-category" class="select-modern">
                                <option value="">اختر الفئة</option>
                                <option value="electronics">إلكترونيات</option>
                                <option value="food">مواد غذائية</option>
                                <option value="clothing">ملابس</option>
                                <option value="furniture">أثاث</option>
                                <option value="stationery">قرطاسية</option>
                                <option value="medical">مستلزمات طبية</option>
                                <option value="automotive">قطع غيار</option>
                                <option value="construction">مواد بناء</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="supplier-payment-terms">شروط الدفع</label>
                            <select id="supplier-payment-terms" class="select-modern">
                                <option value="">اختر شروط الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="credit_7">آجل 7 أيام</option>
                                <option value="credit_15">آجل 15 يوم</option>
                                <option value="credit_30">آجل 30 يوم</option>
                                <option value="credit_60">آجل 60 يوم</option>
                                <option value="credit_90">آجل 90 يوم</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="supplier-notes">ملاحظات</label>
                            <textarea id="supplier-notes" class="textarea-modern" rows="2" placeholder="ملاحظات إضافية عن المورد"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer-modern">
                <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="submit" form="add-supplier-form" class="btn-modern btn-success">
                    <i class="fas fa-save"></i> حفظ المورد
                </button>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.appendChild(modal);

    // إضافة مستمع الحدث لإرسال النموذج
    const form = document.getElementById('add-supplier-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleSupplierFormSubmit();
    });

    // التركيز على أول حقل
    setTimeout(() => {
        const firstInput = modal.querySelector('select, input');
        if (firstInput) firstInput.focus();
    }, 100);
}

// دالة لمعالجة إرسال نموذج المورد
function handleSupplierFormSubmit() {
    // جمع بيانات النموذج
    const supplierData = {
        type: document.getElementById('supplier-type').value,
        name: document.getElementById('supplier-name').value,
        phone: document.getElementById('supplier-phone').value,
        email: document.getElementById('supplier-email').value,
        company: document.getElementById('supplier-company').value,
        taxNumber: document.getElementById('supplier-tax-number').value,
        address: document.getElementById('supplier-address').value,
        category: document.getElementById('supplier-category').value,
        paymentTerms: document.getElementById('supplier-payment-terms').value,
        notes: document.getElementById('supplier-notes').value,
        createdAt: new Date().toISOString()
    };

    // التحقق من صحة البيانات
    if (!supplierData.name.trim()) {
        alert('يرجى إدخال اسم المورد');
        return;
    }

    if (!supplierData.phone.trim()) {
        alert('يرجى إدخال رقم الهاتف');
        return;
    }

    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى الخادم
    console.log('بيانات المورد الجديد:', supplierData);

    // عرض رسالة نجاح
    showSupplierSuccessMessage(supplierData);

    // إغلاق النافذة
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        document.body.removeChild(modal);
    }
}

// دالة لعرض رسالة نجاح إضافة المورد
function showSupplierSuccessMessage(supplierData) {
    const successModal = document.createElement('div');
    successModal.className = 'modal-overlay';
    successModal.innerHTML = `
        <div class="modal-content-modern success-modal">
            <div class="modal-header-modern success">
                <h3><i class="fas fa-check-circle"></i> تم إضافة المورد بنجاح</h3>
            </div>
            <div class="modal-body-modern">
                <div class="success-details">
                    <p><strong>اسم المورد:</strong> ${supplierData.name}</p>
                    <p><strong>رقم الهاتف:</strong> ${supplierData.phone}</p>
                    ${supplierData.email ? `<p><strong>البريد الإلكتروني:</strong> ${supplierData.email}</p>` : ''}
                    ${supplierData.company ? `<p><strong>الشركة:</strong> ${supplierData.company}</p>` : ''}
                </div>
            </div>
            <div class="modal-footer-modern">
                <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                    <i class="fas fa-times"></i> إغلاق
                </button>
                <button type="button" class="btn-modern btn-success" onclick="showAddSupplierModal(); closeModal(this)">
                    <i class="fas fa-plus"></i> إضافة مورد آخر
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(successModal);

    // إغلاق تلقائي بعد 5 ثوانٍ
    setTimeout(() => {
        if (document.body.contains(successModal)) {
            document.body.removeChild(successModal);
        }
    }, 5000);
}

// دوال التعامل مع الأصناف
function updateProductPrice(selectElement) {
    console.log('=== تم استدعاء updateProductPrice ===');
    console.log('selectElement:', selectElement);

    const productItem = selectElement.closest('.product-item');
    console.log('productItem:', productItem);

    const priceInput = productItem.querySelector('.price-input');
    console.log('priceInput:', priceInput);

    const selectedOption = selectElement.options[selectElement.selectedIndex];
    console.log('selectedOption:', selectedOption);
    console.log('data-price:', selectedOption ? selectedOption.dataset.price : 'لا يوجد');

    if (selectedOption && selectedOption.dataset.price) {
        priceInput.value = selectedOption.dataset.price;
        console.log('تم تعيين السعر:', selectedOption.dataset.price);
    } else {
        priceInput.value = '';
        console.log('تم مسح السعر');
    }

    calculateProductTotal(priceInput);
    console.log('=== انتهى updateProductPrice ===');
}

function calculateProductTotal(element) {
    const productItem = element.closest('.product-item');
    const quantityInput = productItem.querySelector('.quantity-input');
    const priceInput = productItem.querySelector('.price-input');
    const totalInput = productItem.querySelector('.total-input');

    const quantity = parseFloat(quantityInput.value) || 0;
    const price = parseFloat(priceInput.value) || 0;

    console.log('حساب مجموع المنتج:', { quantity, price });

    const total = quantity * price;
    totalInput.value = total.toFixed(2);

    console.log('المجموع المحسوب:', total);

    updateGrandTotal();
}

function updateGrandTotal() {
    const totalInputs = document.querySelectorAll('.total-input');
    let subtotal = 0;

    totalInputs.forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const tax = subtotal * 0.15; // ضريبة 15%
    const grandTotal = subtotal + tax;

    // تحديث الحقول
    const subtotalInput = document.getElementById('subtotal');
    const taxInput = document.getElementById('tax');
    const totalInput = document.getElementById('total');

    if (subtotalInput) subtotalInput.value = subtotal.toFixed(2);
    if (taxInput) taxInput.value = tax.toFixed(2);
    if (totalInput) totalInput.value = grandTotal.toFixed(2);
}

function addProductItem() {
    const container = document.getElementById('products-container');
    const newItem = document.createElement('div');
    newItem.className = 'product-item';

    // إضافة منتجات ثابتة مع الأسعار
    let productOptions = '<option value="">اختر الصنف</option>';

    const products = [
        { id: 1, name: 'أرز بسمتي - كيس 5 كيلو', price: 35.00 },
        { id: 2, name: 'سكر أبيض - كيس 2 كيلو', price: 18.00 },
        { id: 3, name: 'زيت طبخ - عبوة 1 لتر', price: 12.00 },
        { id: 4, name: 'لابتوب Dell', price: 1800.00 },
        { id: 5, name: 'قهوة عربية', price: 18.00 },
        { id: 6, name: 'شاي', price: 15.00 }
    ];

    products.forEach(product => {
        productOptions += `<option value="${product.id}" data-price="${product.price}">${product.name} (${product.price} ر.س)</option>`;
    });

    newItem.innerHTML = `
        <div class="form-row">
            <div class="form-group">
                <label>الصنف</label>
                <select class="select-modern product-select" required>
                    ${productOptions}
                </select>
            </div>
            <div class="form-group">
                <label>الكمية</label>
                <input type="number" class="input-modern quantity-input" min="1" value="1" required onchange="calculateProductTotal(this)">
            </div>
            <div class="form-group">
                <label>السعر</label>
                <input type="number" class="input-modern price-input" step="0.01" onchange="calculateProductTotal(this)">
            </div>
            <div class="form-group">
                <label>المجموع</label>
                <input type="number" class="input-modern total-input" readonly>
            </div>
            <div class="form-group">
                <label>إجراء</label>
                <button type="button" class="btn-icon btn-danger" onclick="removeProductItem(this)" title="حذف الصنف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    container.appendChild(newItem);

    // إضافة event listener للقائمة الجديدة
    const newProductSelect = newItem.querySelector('.product-select');
    if (newProductSelect) {
        newProductSelect.addEventListener('change', function() {
            console.log('تم تغيير المنتج في صنف جديد:', this.value);
            updateProductPrice(this);
        });
        console.log('تم إضافة event listener للصنف الجديد');
    }
}

function removeProductItem(button) {
    const productItem = button.closest('.product-item');
    const container = document.getElementById('products-container');

    // التأكد من وجود صنف واحد على الأقل
    if (container.children.length > 1) {
        productItem.remove();
        updateGrandTotal();
    } else {
        alert('يجب أن يكون هناك صنف واحد على الأقل');
    }
}
