# PowerShell script to open launcher.html
# Updated to handle Arabic character issue and provide better error handling

# Get the current directory
$currentPath = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition
$launcherPath = Join-Path -Path $currentPath -ChildPath "launcher.html"

# Check if the file exists
if (Test-Path $launcherPath) {
    Write-Host "Opening launcher at: $launcherPath"
    
    try {
        # Method 1: Try using Start-Process (preferred method)
        Start-Process $launcherPath
        Write-Host "Launcher opened successfully using Start-Process!"
    } catch {
        Write-Host "Start-Process method failed, trying alternative method..."
        
        try {
            # Method 2: Try using Invoke-Item
            Invoke-Item $launcherPath
            Write-Host "Launcher opened successfully using Invoke-Item!"
        } catch {
            Write-Host "Invoke-Item method failed, trying alternative method..."
            
            try {
                # Method 3: Try using cmd.exe start command
                cmd.exe /c start "" "$launcherPath"
                Write-Host "Launcher opened successfully using cmd.exe start command!"
            } catch {
                Write-Host "Error opening launcher: $_"
                Write-Host "Please try using one of the alternative launcher files:"
                Write-Host "- open_launcher.vbs (recommended for Windows)"
                Write-Host "- Or open launcher.html directly from your file explorer"
                exit 1
            }
        }
    }
} else {
    Write-Host "Launcher file not found at: $launcherPath"
    exit 1
}

exit 0