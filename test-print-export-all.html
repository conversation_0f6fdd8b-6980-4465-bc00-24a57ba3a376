<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة وتصدير جميع الصفحات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .features-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .features-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ اختبار طباعة وتصدير جميع الصفحات</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>أيقونات الطباعة والتصدير في صفحتي المشتريات والعملاء لا تعمل</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>دوال غير موجودة في صفحة العملاء</li>
                        <li>دوال تتعامل مع الموردين بدلاً من المشتريات</li>
                        <li>لا يمكن طباعة أو تصدير البيانات</li>
                        <li>أيقونات غير فعالة</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>دوال كاملة في جميع الصفحات</li>
                        <li>كل صفحة تتعامل مع بياناتها الخاصة</li>
                        <li>طباعة وتصدير Excel و PDF فعال</li>
                        <li>تقارير منسقة ومخصصة لكل صفحة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الصفحات المصلحة -->
        <div class="test-section">
            <h2>📄 الصفحات المصلحة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>صفحة المشتريات</h3>
                    <p>تقارير مشتريات مخصصة</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>صفحة العملاء</h3>
                    <p>تقارير عملاء مخصصة</p>
                    <button class="btn" onclick="openCustomersPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">👨‍💼</div>
                    <h3>صفحة الموردين</h3>
                    <p>تقارير موردين مخصصة</p>
                    <button class="btn" onclick="openSuppliersPage()">فتح الصفحة</button>
                </div>
            </div>
        </div>

        <!-- حالة الإصلاحات -->
        <div class="test-section">
            <h2>📊 حالة الإصلاحات</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>الصفحة</th>
                        <th>طباعة التقرير</th>
                        <th>تصدير Excel</th>
                        <th>تصدير PDF</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>المشتريات</td>
                        <td>✅ تعمل</td>
                        <td>✅ يعمل</td>
                        <td>✅ يعمل</td>
                        <td><span class="status-indicator status-fixed"></span>تم الإصلاح</td>
                    </tr>
                    <tr>
                        <td>العملاء</td>
                        <td>✅ تعمل</td>
                        <td>✅ يعمل</td>
                        <td>✅ يعمل</td>
                        <td><span class="status-indicator status-fixed"></span>تم الإصلاح</td>
                    </tr>
                    <tr>
                        <td>الموردين</td>
                        <td>✅ تعمل</td>
                        <td>✅ يعمل</td>
                        <td>✅ يعمل</td>
                        <td><span class="status-indicator status-fixed"></span>تم الإصلاح</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- بيانات تجريبية -->
        <div class="test-section">
            <h2>📊 بيانات تجريبية</h2>
            <div class="highlight">
                <h3>سنضيف بعض البيانات التجريبية لاختبار الطباعة والتصدير:</h3>
            </div>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>بيانات المشتريات</h3>
                    <button class="btn" onclick="addSamplePurchases()">إضافة مشتريات تجريبية</button>
                </div>
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>بيانات العملاء</h3>
                    <button class="btn" onclick="addSampleCustomers()">إضافة عملاء تجريبيين</button>
                </div>
                <div class="page-card">
                    <div class="icon">👨‍💼</div>
                    <h3>بيانات الموردين</h3>
                    <button class="btn" onclick="addSampleSuppliers()">إضافة موردين تجريبيين</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار وظائف الطباعة والتصدير:</h3>
                <p>سنختبر جميع وظائف الطباعة والتصدير في كل الصفحات للتأكد من عملها</p>
            </div>
            
            <button class="btn" onclick="startPrintExportTest()">🚀 بدء اختبار الطباعة والتصدير</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار الوظائف:</h3>
                <ol>
                    <li><strong>إضافة البيانات:</strong> اضغط أزرار إضافة البيانات التجريبية</li>
                    <li><strong>فتح صفحة المشتريات:</strong> اختبر الطباعة والتصدير</li>
                    <li><strong>فتح صفحة العملاء:</strong> اختبر الطباعة والتصدير</li>
                    <li><strong>فتح صفحة الموردين:</strong> اختبر الطباعة والتصدير</li>
                    <li><strong>التحقق:</strong> تأكد من تحميل الملفات وعرض التقارير</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="openCustomersPage()">👥 فتح صفحة العملاء</button>
            <button class="btn" onclick="openSuppliersPage()">👨‍💼 فتح صفحة الموردين</button>
            <button class="btn" onclick="clearTestData()">🗑️ مسح البيانات التجريبية</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>المشتريات:</strong> طباعة وتصدير تقارير المشتريات</li>
                    <li><strong>العملاء:</strong> طباعة وتصدير تقارير العملاء</li>
                    <li><strong>الموردين:</strong> طباعة وتصدير تقارير الموردين</li>
                    <li><strong>التحقق من البيانات:</strong> يتحقق من وجود بيانات قبل التصدير</li>
                    <li><strong>التنسيق:</strong> تقارير منسقة ومقروءة باللغة العربية</li>
                    <li><strong>التاريخ:</strong> يضيف تاريخ التقرير والطباعة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // إضافة بيانات مشتريات تجريبية
        function addSamplePurchases() {
            const samplePurchases = [
                {
                    id: Date.now() + 1,
                    invoiceNumber: 'INV-001',
                    date: new Date().toISOString(),
                    supplier: 'شركة التقنية المتقدمة',
                    amount: '5000',
                    payment: 'نقدي',
                    status: 'مكتمل',
                    notes: 'شراء أجهزة حاسوب',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    invoiceNumber: 'INV-002',
                    date: new Date().toISOString(),
                    supplier: 'مؤسسة الإمداد الشامل',
                    amount: '3500',
                    payment: 'تحويل بنكي',
                    status: 'قيد المعالجة',
                    notes: 'شراء أثاث مكتبي',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    invoiceNumber: 'INV-003',
                    date: new Date().toISOString(),
                    supplier: 'محمد أحمد التجاري',
                    amount: '1200',
                    payment: 'شيك',
                    status: 'مكتمل',
                    notes: 'شراء مستلزمات مكتبية',
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('monjizPurchases', JSON.stringify(samplePurchases));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة بيانات المشتريات التجريبية!</strong><br><br>
                    📊 تم إضافة 3 مشتريات تجريبية<br>
                    💾 البيانات محفوظة في localStorage<br>
                    🧪 يمكنك الآن اختبار الطباعة والتصدير<br><br>
                    💡 <strong>افتح صفحة المشتريات لاختبار الوظائف!</strong>
                </div>
            `);
        }

        // إضافة بيانات عملاء تجريبية
        function addSampleCustomers() {
            const sampleCustomers = [
                {
                    id: Date.now() + 1,
                    name: 'شركة النور للتجارة',
                    type: 'شركة',
                    phone: '+966112345678',
                    email: '<EMAIL>',
                    address: 'الرياض - شارع العليا',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    name: 'مؤسسة الأمل',
                    type: 'مؤسسة',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    address: 'جدة - شارع التحلية',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    name: 'أحمد محمد',
                    type: 'فرد',
                    phone: '+966551234567',
                    email: '<EMAIL>',
                    address: 'الدمام - حي النزهة',
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('monjizCustomers', JSON.stringify(sampleCustomers));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة بيانات العملاء التجريبية!</strong><br><br>
                    📊 تم إضافة 3 عملاء تجريبيين<br>
                    💾 البيانات محفوظة في localStorage<br>
                    🧪 يمكنك الآن اختبار الطباعة والتصدير<br><br>
                    💡 <strong>افتح صفحة العملاء لاختبار الوظائف!</strong>
                </div>
            `);
        }

        // إضافة بيانات موردين تجريبية
        function addSampleSuppliers() {
            const sampleSuppliers = [
                {
                    id: Date.now() + 1,
                    name: 'شركة التقنية المتقدمة',
                    type: 'شركة',
                    phone: '+966112345678',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    name: 'مؤسسة الإمداد الشامل',
                    type: 'مؤسسة',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    name: 'محمد أحمد التجاري',
                    type: 'فرد',
                    phone: '+966551234567',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('monjizSuppliers', JSON.stringify(sampleSuppliers));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة بيانات الموردين التجريبية!</strong><br><br>
                    📊 تم إضافة 3 موردين تجريبيين<br>
                    💾 البيانات محفوظة في localStorage<br>
                    🧪 يمكنك الآن اختبار الطباعة والتصدير<br><br>
                    💡 <strong>افتح صفحة الموردين لاختبار الوظائف!</strong>
                </div>
            `);
        }

        // بدء اختبار الطباعة والتصدير
        function startPrintExportTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار الطباعة والتصدير!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ أضف البيانات التجريبية لكل صفحة<br>
                    2️⃣ افتح كل صفحة على حدة<br>
                    3️⃣ ابحث عن زر "طباعة وتصدير"<br>
                    4️⃣ اختبر كل وظيفة على حدة<br>
                    5️⃣ تحقق من النتائج<br><br>
                    
                    <strong>🎯 اضغط أزرار إضافة البيانات التجريبية أولاً!</strong>
                </div>
            `);
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 ابحث عن زر "طباعة وتصدير" واختبر الوظائف!', 'info');
        }

        // فتح صفحة العملاء
        function openCustomersPage() {
            window.open('customers.html', '_blank');
            showResult('👥 تم فتح صفحة العملاء<br>💡 ابحث عن زر "طباعة وتصدير" واختبر الوظائف!', 'info');
        }

        // فتح صفحة الموردين
        function openSuppliersPage() {
            window.open('suppliers.html', '_blank');
            showResult('👨‍💼 تم فتح صفحة الموردين<br>💡 ابحث عن زر "طباعة وتصدير" واختبر الوظائف!', 'info');
        }

        // مسح البيانات التجريبية
        function clearTestData() {
            localStorage.removeItem('monjizPurchases');
            localStorage.removeItem('monjizCustomers');
            localStorage.removeItem('monjizSuppliers');
            showResult('🗑️ تم مسح جميع البيانات التجريبية', 'info');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🖨️ <strong>تم إصلاح وظائف الطباعة والتصدير في جميع الصفحات!</strong><br><br>
                    ✅ صفحة المشتريات: طباعة وتصدير تقارير المشتريات<br>
                    ✅ صفحة العملاء: طباعة وتصدير تقارير العملاء<br>
                    ✅ صفحة الموردين: طباعة وتصدير تقارير الموردين<br>
                    ✅ تحقق من البيانات قبل التصدير<br>
                    ✅ تنسيق عربي صحيح<br>
                    ✅ إضافة التاريخ والإحصائيات<br><br>
                    🧪 <strong>اضغط "بدء اختبار الطباعة والتصدير" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
