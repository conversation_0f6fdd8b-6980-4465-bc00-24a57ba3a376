// system-language.js - وظائف لصفحة تغيير لغة النظام

document.addEventListener('DOMContentLoaded', function() {
    // إضافة وظيفة لعرض وإخفاء الصور التوضيحية
    const toggleImageBtns = document.querySelectorAll('.toggle-image-btn');
    
    toggleImageBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const imageContainer = document.getElementById(targetId);
            
            if (imageContainer) {
                // تبديل حالة العرض
                if (imageContainer.style.display === 'none' || imageContainer.style.display === '') {
                    imageContainer.style.display = 'block';
                    this.textContent = 'إخفاء الصورة';
                } else {
                    imageContainer.style.display = 'none';
                    this.textContent = 'عرض الصورة';
                }
            }
        });
    });

    // إضافة وظيفة لعرض تأثير عند النقر على اختصارات لوحة المفاتيح
    const shortcutElements = document.querySelectorAll('.keyboard-shortcut');
    
    shortcutElements.forEach(element => {
        element.addEventListener('click', function() {
            // إضافة تأثير عند النقر على اختصار لوحة المفاتيح
            this.classList.add('active');
            
            // إزالة التأثير بعد 500 مللي ثانية
            setTimeout(() => {
                this.classList.remove('active');
            }, 500);
        });
    });

    // إضافة وظيفة لعرض معلومات إضافية عند النقر على زر المعلومات
    const infoButtons = document.querySelectorAll('.info-btn');
    
    if (infoButtons.length > 0) {
        infoButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const infoContainer = document.getElementById(targetId);
                
                if (infoContainer) {
                    // تبديل حالة العرض
                    if (infoContainer.style.display === 'none' || infoContainer.style.display === '') {
                        infoContainer.style.display = 'block';
                        this.textContent = 'إخفاء المعلومات';
                    } else {
                        infoContainer.style.display = 'none';
                        this.textContent = 'عرض المعلومات';
                    }
                }
            });
        });
    }

    // إضافة وظيفة للتحقق من لغة النظام الحالية
    const checkSystemLanguageBtn = document.getElementById('check-system-language');
    
    if (checkSystemLanguageBtn) {
        checkSystemLanguageBtn.addEventListener('click', function() {
            const languageDisplay = document.getElementById('current-system-language');
            
            if (languageDisplay) {
                // محاولة الكشف عن لغة المتصفح
                const browserLanguage = navigator.language || navigator.userLanguage;
                
                languageDisplay.textContent = 'لغة المتصفح الحالية: ' + browserLanguage;
                languageDisplay.style.display = 'block';
            }
        });
    }
});