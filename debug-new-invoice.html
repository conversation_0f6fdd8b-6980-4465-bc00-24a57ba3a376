<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة فاتورة جديدة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #dc3545;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220,53,69,0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff8f00);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .error {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(231,76,60,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #dc3545;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #dc3545;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .debug-steps {
            background: white;
            border: 2px solid #dc3545;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .debug-steps h3 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        .debug-steps ol {
            margin: 0;
            padding-right: 20px;
        }
        .debug-steps li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .console-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            border: 2px solid #4a5568;
        }
        .console-box h4 {
            color: #68d391;
            margin-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            border: 2px solid #dc3545;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .test-card h3 {
            color: #dc3545;
            margin-bottom: 15px;
        }
        .test-card .icon {
            font-size: 48px;
            color: #dc3545;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة فاتورة جديدة</h1>

        <!-- المشكلة -->
        <div class="test-section">
            <h2>🎯 المشكلة</h2>
            <div class="highlight">
                <h3>زر "فاتورة جديدة" لا يفتح النافذة</h3>
                <p>الزر موجود ومفعل لكن النافذة لا تظهر عند الضغط عليه</p>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            
            <div class="debug-steps">
                <h3>تم إضافة تتبع مفصل:</h3>
                <ul>
                    <li>✅ إضافة console.log في بداية دالة showAddPurchaseModal</li>
                    <li>✅ إضافة try-catch لمعالجة الأخطاء</li>
                    <li>✅ إضافة console.log عند إنشاء النافذة</li>
                    <li>✅ إضافة console.log عند إضافة النافذة للصفحة</li>
                    <li>✅ إضافة console.log في تحميل الموردين</li>
                    <li>✅ إضافة console.log في إضافة الصنف الافتراضي</li>
                </ul>
            </div>
        </div>

        <!-- خطوات التشخيص -->
        <div class="test-section">
            <h2>🔧 خطوات التشخيص</h2>
            
            <div class="test-grid">
                <div class="test-card">
                    <div class="icon">🛍️</div>
                    <h3>الخطوة 1</h3>
                    <p>فتح صفحة المشتريات</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح المشتريات</button>
                </div>
                <div class="test-card">
                    <div class="icon">🔧</div>
                    <h3>الخطوة 2</h3>
                    <p>فتح Developer Tools</p>
                    <button class="btn btn-warning" onclick="showDevToolsInstructions()">تعليمات F12</button>
                </div>
                <div class="test-card">
                    <div class="icon">🎯</div>
                    <h3>الخطوة 3</h3>
                    <p>اختبار الزر</p>
                    <button class="btn btn-success" onclick="testButtonInstructions()">تعليمات الاختبار</button>
                </div>
                <div class="test-card">
                    <div class="icon">📊</div>
                    <h3>الخطوة 4</h3>
                    <p>تحليل النتائج</p>
                    <button class="btn" onclick="analyzeResults()">تحليل النتائج</button>
                </div>
            </div>
        </div>

        <!-- رسائل Console المتوقعة -->
        <div class="test-section">
            <h2>📱 رسائل Console المتوقعة</h2>
            
            <div class="console-box">
                <h4>عند الضغط على "فاتورة جديدة" يجب أن تظهر:</h4>
                <div style="color: #68d391;">✅ showAddPurchaseModal: بدء فتح نافذة إضافة فاتورة شراء جديدة...</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: تم منع التمرير</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: إنشاء عنصر النافذة...</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: تم إنشاء عنصر النافذة</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: إضافة النافذة للصفحة...</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: تم إضافة النافذة للصفحة</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: تحميل الموردين...</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: إضافة صنف افتراضي...</div>
                <div style="color: #68d391;">✅ showAddPurchaseModal: تم فتح النافذة بنجاح!</div>
            </div>
            
            <div style="background: #fed7d7; color: #c53030; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #c53030;">إذا ظهرت رسالة خطأ:</h4>
                <div>❌ showAddPurchaseModal: خطأ في فتح النافذة: [تفاصيل الخطأ]</div>
                <p><strong>أرسل لي تفاصيل الخطأ لإصلاحه!</strong></p>
            </div>
        </div>

        <!-- الاختبار -->
        <div class="test-section">
            <h2>🧪 الاختبار</h2>
            <div id="test-result"></div>
        </div>

        <!-- الحلول المحتملة -->
        <div class="test-section">
            <h2>💡 الحلول المحتملة</h2>
            <div class="info">
                <h3>إذا لم تعمل النافذة:</h3>
                <ul>
                    <li><strong>مشكلة CSS:</strong> النافذة موجودة لكن مخفية</li>
                    <li><strong>مشكلة JavaScript:</strong> خطأ في الكود يمنع التنفيذ</li>
                    <li><strong>مشكلة DOM:</strong> العناصر غير موجودة</li>
                    <li><strong>مشكلة تضارب:</strong> كود آخر يتداخل</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult(`
                <div class="success">
                    🛍️ <strong>تم فتح صفحة المشتريات!</strong><br><br>
                    
                    <strong>الآن:</strong><br>
                    1️⃣ اضغط F12 لفتح Developer Tools<br>
                    2️⃣ انتقل لتبويب Console<br>
                    3️⃣ اضغط زر "فاتورة جديدة"<br>
                    4️⃣ راقب الرسائل في Console<br><br>
                    
                    💡 <strong>أرسل لي ما تراه في Console!</strong>
                </div>
            `);
        }

        // تعليمات Developer Tools
        function showDevToolsInstructions() {
            showResult(`
                <div class="info">
                    🔧 <strong>كيفية فتح Developer Tools:</strong><br><br>
                    
                    <strong>الطرق:</strong><br>
                    ⌨️ اضغط F12<br>
                    ⌨️ أو Ctrl + Shift + I<br>
                    🖱️ أو كليك يمين → Inspect<br><br>
                    
                    <strong>بعد الفتح:</strong><br>
                    1️⃣ انتقل لتبويب Console<br>
                    2️⃣ امسح الرسائل القديمة (Ctrl + L)<br>
                    3️⃣ اضغط زر "فاتورة جديدة"<br>
                    4️⃣ راقب الرسائل الجديدة<br><br>
                    
                    💡 <strong>أرسل لي لقطة شاشة من Console!</strong>
                </div>
            `);
        }

        // تعليمات اختبار الزر
        function testButtonInstructions() {
            showResult(`
                <div class="success">
                    🎯 <strong>تعليمات اختبار الزر:</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ تأكد من فتح Console (F12)<br>
                    2️⃣ ابحث عن زر "فاتورة جديدة" (أخضر)<br>
                    3️⃣ اضغط الزر مرة واحدة<br>
                    4️⃣ راقب Console للرسائل<br>
                    5️⃣ تحقق من ظهور النافذة<br><br>
                    
                    <strong>النتائج المحتملة:</strong><br>
                    ✅ النافذة تظهر = المشكلة محلولة<br>
                    ❌ لا تظهر + رسائل Console = أرسل الرسائل<br>
                    ❌ لا تظهر + لا رسائل = مشكلة في الزر<br><br>
                    
                    💡 <strong>أعلمني بالنتيجة!</strong>
                </div>
            `);
        }

        // تحليل النتائج
        function analyzeResults() {
            showResult(`
                <div class="info">
                    📊 <strong>تحليل النتائج:</strong><br><br>
                    
                    <strong>إذا ظهرت جميع رسائل Console:</strong><br>
                    ✅ الكود يعمل بشكل صحيح<br>
                    ✅ النافذة تُضاف للصفحة<br>
                    🔍 المشكلة قد تكون في CSS<br><br>
                    
                    <strong>إذا توقفت الرسائل في مكان معين:</strong><br>
                    ❌ خطأ JavaScript في ذلك المكان<br>
                    🔧 سأصلح الخطأ المحدد<br><br>
                    
                    <strong>إذا لم تظهر أي رسائل:</strong><br>
                    ❌ الزر غير مفعل<br>
                    ❌ مشكلة في onclick<br><br>
                    
                    💡 <strong>أرسل لي تفاصيل ما تراه!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="error">
                    🔍 <strong>أداة تشخيص مشكلة فاتورة جديدة!</strong><br><br>
                    
                    <strong>المشكلة:</strong><br>
                    ❌ زر "فاتورة جديدة" لا يفتح النافذة<br><br>
                    
                    <strong>الحل:</strong><br>
                    🔧 تم إضافة تتبع مفصل للكود<br>
                    📊 سنحلل رسائل Console معاً<br><br>
                    
                    🚀 <strong>ابدأ بفتح صفحة المشتريات!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
