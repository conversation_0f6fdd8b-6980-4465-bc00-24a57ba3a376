// ملف JavaScript لصفحة الحسابات

document.addEventListener('DOMContentLoaded', function() {
    // بيانات المعاملات المالية (نموذج)
    const transactions = [
        { id: 1, date: '2023-06-01', type: 'income', category: 'المبيعات', description: 'مبيعات شهر يونيو', amount: 5000, reference: 'INV-2023-001' },
        { id: 2, date: '2023-06-05', type: 'expense', category: 'الإيجار', description: 'إيجار المكتب', amount: 1200, reference: 'RENT-2023-06' },
        { id: 3, date: '2023-06-10', type: 'income', category: 'الخدمات', description: 'خدمات استشارية', amount: 2500, reference: 'SRV-2023-010' },
        { id: 4, date: '2023-06-15', type: 'expense', category: 'الرواتب', description: 'رواتب الموظفين', amount: 3500, reference: 'SAL-2023-06' },
        { id: 5, date: '2023-06-20', type: 'expense', category: 'المرافق', description: 'فواتير الكهرباء والماء', amount: 350, reference: 'UTIL-2023-06' },
        { id: 6, date: '2023-06-25', type: 'income', category: 'المبيعات', description: 'مبيعات نهاية الشهر', amount: 4200, reference: 'INV-2023-002' },
        { id: 7, date: '2023-07-01', type: 'expense', category: 'التسويق', description: 'حملة إعلانية', amount: 800, reference: 'MKT-2023-07' },
        { id: 8, date: '2023-07-05', type: 'income', category: 'المبيعات', description: 'مبيعات بداية الشهر', amount: 3800, reference: 'INV-2023-003' },
        { id: 9, date: '2023-07-10', type: 'expense', category: 'الصيانة', description: 'صيانة المعدات', amount: 600, reference: 'MNT-2023-07' },
        { id: 10, date: '2023-07-15', type: 'expense', category: 'الرواتب', description: 'رواتب الموظفين', amount: 3500, reference: 'SAL-2023-07' }
    ];

    // فئات الإيرادات (نموذج)
    const incomeCategories = [
        { id: 1, name: 'المبيعات', icon: 'shopping-cart' },
        { id: 2, name: 'الخدمات', icon: 'cogs' },
        { id: 3, name: 'الاستثمارات', icon: 'chart-line' },
        { id: 4, name: 'الإيجارات', icon: 'building' },
        { id: 5, name: 'أخرى', icon: 'ellipsis-h' }
    ];

    // فئات المصروفات (نموذج)
    const expenseCategories = [
        { id: 1, name: 'الرواتب', icon: 'users' },
        { id: 2, name: 'الإيجار', icon: 'home' },
        { id: 3, name: 'المرافق', icon: 'bolt' },
        { id: 4, name: 'التسويق', icon: 'bullhorn' },
        { id: 5, name: 'الصيانة', icon: 'tools' },
        { id: 6, name: 'المشتريات', icon: 'shopping-basket' },
        { id: 7, name: 'أخرى', icon: 'ellipsis-h' }
    ];

    // تحميل البيانات الإحصائية
    loadFinancialStats();

    // إنشاء الرسم البياني
    createFinancialChart();

    // عرض المعاملات المالية مع التنقل
    displayTransactionsWithPagination(transactions);

    // عرض فئات الإيرادات والمصروفات
    displayCategories(incomeCategories, expenseCategories);

    // إضافة مستمعي الأحداث
    setupEventListeners();

    // تهيئة دليل الحسابات
    initChartOfAccounts();

    // تهيئة التبويبات
    initTabs();

    // التحقق من تهيئة التبويبات
    setTimeout(() => {
        const tabs = document.querySelectorAll('.nav-tab');
        const panes = document.querySelectorAll('.tab-pane');
        if (tabs.length === 0 || panes.length === 0) {
            console.error('خطأ: لم يتم العثور على التبويبات أو اللوحات');
        }
    }, 1000);

    // إعداد مستمعي الأحداث لدليل الحسابات
    setupAccountsEventListeners();

    // تحميل البيانات الأولية
    loadInitialData();

    // إضافة زر الاختبار (للتطوير فقط)
    // addTestButton();
});

// دالة لتحميل البيانات الإحصائية
function loadFinancialStats() {
    // في التطبيق الحقيقي، هذه البيانات ستأتي من الخادم
    const stats = {
        income: 15500,
        expenses: 9950,
        profit: 5550,
        balance: 25550
    };

    // تحديث قيم الإحصائيات
    document.querySelector('.income .stat-value').textContent = formatCurrency(stats.income);
    document.querySelector('.expenses .stat-value').textContent = formatCurrency(stats.expenses);
    document.querySelector('.profit .stat-value').textContent = formatCurrency(stats.profit);
    document.querySelector('.balance .stat-value').textContent = formatCurrency(stats.balance);
}

// دالة لإنشاء الرسم البياني للإيرادات والمصروفات
function createFinancialChart() {
    const ctx = document.getElementById('financialChart').getContext('2d');
    
    // بيانات الرسم البياني (نموذج)
    const chartData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
        datasets: [
            {
                label: 'الإيرادات',
                data: [12000, 15000, 10000, 18000, 14000, 15500, 17000],
                backgroundColor: 'rgba(39, 174, 96, 0.2)',
                borderColor: 'rgba(39, 174, 96, 1)',
                borderWidth: 2,
                tension: 0.4
            },
            {
                label: 'المصروفات',
                data: [8000, 9000, 7500, 10000, 8500, 9950, 10500],
                backgroundColor: 'rgba(231, 76, 60, 0.2)',
                borderColor: 'rgba(231, 76, 60, 1)',
                borderWidth: 2,
                tension: 0.4
            }
        ]
    };

    // إنشاء الرسم البياني
    new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    labels: {
                        boxWidth: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += formatCurrency(context.raw);
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value, true);
                        }
                    }
                }
            }
        }
    });
}

// دالة لعرض المعاملات المالية
function displayTransactions(transactions) {
    const tableBody = document.querySelector('.transactions-table tbody');
    tableBody.innerHTML = '';

    if (transactions.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="7" class="text-center">لا توجد معاملات مالية</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    transactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${transaction.date}</td>
            <td>
                <span class="transaction-type ${transaction.type === 'income' ? 'type-income' : 'type-expense'}">
                    ${transaction.type === 'income' ? 'إيراد' : 'مصروف'}
                </span>
            </td>
            <td>${transaction.category}</td>
            <td>${transaction.description}</td>
            <td>${transaction.reference}</td>
            <td class="transaction-amount ${transaction.type === 'income' ? 'amount-income' : 'amount-expense'}">
                ${formatCurrency(transaction.amount)}
            </td>
            <td>
                <div class="transaction-actions">
                    <button class="action-btn view-btn" data-id="${transaction.id}" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" data-id="${transaction.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${transaction.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addTransactionActionListeners();
}

// دالة لعرض فئات الإيرادات والمصروفات
function displayCategories(incomeCategories, expenseCategories) {
    const incomeList = document.querySelector('.income-categories');
    const expenseList = document.querySelector('.expense-categories');

    // عرض فئات الإيرادات
    incomeList.innerHTML = '';
    incomeCategories.forEach(category => {
        const item = document.createElement('li');
        item.className = 'category-item income-category';
        item.innerHTML = `
            <div class="category-name">
                <div class="category-icon">
                    <i class="fas fa-${category.icon}"></i>
                </div>
                <span>${category.name}</span>
            </div>
            <div class="category-actions">
                <button class="edit-category-btn" data-id="${category.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-category-btn" data-id="${category.id}" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        incomeList.appendChild(item);
    });

    // عرض فئات المصروفات
    expenseList.innerHTML = '';
    expenseCategories.forEach(category => {
        const item = document.createElement('li');
        item.className = 'category-item expense-category';
        item.innerHTML = `
            <div class="category-name">
                <div class="category-icon">
                    <i class="fas fa-${category.icon}"></i>
                </div>
                <span>${category.name}</span>
            </div>
            <div class="category-actions">
                <button class="edit-category-btn" data-id="${category.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-category-btn" data-id="${category.id}" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        expenseList.appendChild(item);
    });

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addCategoryActionListeners();
}

// دالة لإضافة مستمعي الأحداث لأزرار إجراءات المعاملات
function addTransactionActionListeners() {
    // أزرار عرض التفاصيل
    document.querySelectorAll('.view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            viewTransactionDetails(transactionId);
        });
    });

    // أزرار التعديل
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            editTransaction(transactionId);
        });
    });

    // أزرار الحذف
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            deleteTransaction(transactionId);
        });
    });
}

// دالة لإضافة مستمعي الأحداث لأزرار إجراءات الفئات
function addCategoryActionListeners() {
    // أزرار تعديل الفئات
    document.querySelectorAll('.edit-category-btn').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            editCategory(categoryId);
        });
    });

    // أزرار حذف الفئات
    document.querySelectorAll('.delete-category-btn').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            deleteCategory(categoryId);
        });
    });
}

// دالة لإعداد مستمعي الأحداث
function setupEventListeners() {
    // زر إضافة معاملة جديدة
    const addTransactionBtn = document.querySelector('.add-transaction-btn');
    if (addTransactionBtn) {
        addTransactionBtn.addEventListener('click', function() {
            showAddTransactionModal();
        });
    }

    // زر تصدير البيانات
    const exportBtn = document.querySelector('.export-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportFinancialData();
        });
    }

    // حقل البحث
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            searchTransactions(this.value);
        });
    }

    // قائمة تصفية النوع
    const typeFilter = document.querySelector('#typeFilter');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            filterTransactionsByType(this.value);
        });
    }

    // قائمة تصفية الفئة
    const categoryFilter = document.querySelector('#categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterTransactionsByCategory(this.value);
        });
    }

    // أزرار تصفية التاريخ
    const applyDateBtn = document.querySelector('.apply-date-btn');
    if (applyDateBtn) {
        applyDateBtn.addEventListener('click', function() {
            const startDate = document.querySelector('#startDate').value;
            const endDate = document.querySelector('#endDate').value;
            filterTransactionsByDate(startDate, endDate);
        });
    }

    // قائمة فترة الرسم البياني
    const chartPeriod = document.querySelector('#chartPeriod');
    if (chartPeriod) {
        chartPeriod.addEventListener('change', function() {
            updateChartPeriod(this.value);
        });
    }

    // أزرار إضافة فئة جديدة
    const addIncomeCategoryBtn = document.querySelector('.add-income-category-btn');
    if (addIncomeCategoryBtn) {
        addIncomeCategoryBtn.addEventListener('click', function() {
            showAddCategoryModal('income');
        });
    }

    const addExpenseCategoryBtn = document.querySelector('.add-expense-category-btn');
    if (addExpenseCategoryBtn) {
        addExpenseCategoryBtn.addEventListener('click', function() {
            showAddCategoryModal('expense');
        });
    }

    // أزرار ترقيم الصفحات
    document.querySelectorAll('.pagination-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.hasAttribute('disabled') && !this.classList.contains('active')) {
                const page = this.getAttribute('data-page');
                goToPage(page);
            }
        });
    });
}

// دالة لعرض تفاصيل المعاملة
function viewTransactionDetails(transactionId) {
    // في التطبيق الحقيقي، سيتم جلب بيانات المعاملة من الخادم
    console.log(`عرض تفاصيل المعاملة رقم ${transactionId}`);
    alert(`عرض تفاصيل المعاملة رقم ${transactionId}`);
}

// دالة لتعديل المعاملة
function editTransaction(transactionId) {
    // في التطبيق الحقيقي، سيتم جلب بيانات المعاملة من الخادم وعرضها في نموذج التعديل
    console.log(`تعديل المعاملة رقم ${transactionId}`);
    alert(`تعديل المعاملة رقم ${transactionId}`);
}

// دالة لحذف المعاملة
function deleteTransaction(transactionId) {
    // في التطبيق الحقيقي، سيتم إرسال طلب حذف إلى الخادم
    if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
        console.log(`حذف المعاملة رقم ${transactionId}`);
        alert(`تم حذف المعاملة رقم ${transactionId}`);
    }
}

// دالة لتعديل الفئة
function editCategory(categoryId) {
    // في التطبيق الحقيقي، سيتم جلب بيانات الفئة من الخادم وعرضها في نموذج التعديل
    console.log(`تعديل الفئة رقم ${categoryId}`);
    alert(`تعديل الفئة رقم ${categoryId}`);
}

// دالة لحذف الفئة
function deleteCategory(categoryId) {
    // في التطبيق الحقيقي، سيتم إرسال طلب حذف إلى الخادم
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
        console.log(`حذف الفئة رقم ${categoryId}`);
        alert(`تم حذف الفئة رقم ${categoryId}`);
    }
}

// دالة لعرض نافذة إضافة معاملة جديدة
function showAddTransactionModal() {
    // في التطبيق الحقيقي، سيتم عرض نافذة منبثقة لإضافة معاملة جديدة
    console.log('عرض نافذة إضافة معاملة جديدة');
    alert('عرض نافذة إضافة معاملة جديدة');
}

// دالة لعرض نافذة إضافة فئة جديدة
function showAddCategoryModal(type) {
    // في التطبيق الحقيقي، سيتم عرض نافذة منبثقة لإضافة فئة جديدة
    console.log(`عرض نافذة إضافة فئة ${type === 'income' ? 'إيرادات' : 'مصروفات'} جديدة`);
    alert(`عرض نافذة إضافة فئة ${type === 'income' ? 'إيرادات' : 'مصروفات'} جديدة`);
}

// دالة لتصدير البيانات المالية
function exportFinancialData() {
    // في التطبيق الحقيقي، سيتم تصدير البيانات إلى ملف
    console.log('تصدير البيانات المالية');
    alert('تم تصدير البيانات المالية');
}

// دالة للبحث في المعاملات
function searchTransactions(query) {
    // في التطبيق الحقيقي، سيتم البحث في البيانات وعرض النتائج
    console.log(`البحث عن: ${query}`);
}

// دالة لتصفية المعاملات حسب النوع
function filterTransactionsByType(type) {
    // في التطبيق الحقيقي، سيتم تصفية البيانات وعرض النتائج
    console.log(`تصفية حسب النوع: ${type}`);
}

// دالة لتصفية المعاملات حسب الفئة
function filterTransactionsByCategory(category) {
    // في التطبيق الحقيقي، سيتم تصفية البيانات وعرض النتائج
    console.log(`تصفية حسب الفئة: ${category}`);
}

// دالة لتصفية المعاملات حسب التاريخ
function filterTransactionsByDate(startDate, endDate) {
    // في التطبيق الحقيقي، سيتم تصفية البيانات وعرض النتائج
    console.log(`تصفية حسب التاريخ: من ${startDate} إلى ${endDate}`);
}

// دالة لتحديث فترة الرسم البياني
function updateChartPeriod(period) {
    // في التطبيق الحقيقي، سيتم تحديث بيانات الرسم البياني وإعادة رسمه
    console.log(`تحديث فترة الرسم البياني: ${period}`);
}

// دالة للانتقال إلى صفحة معينة
function goToPage(page) {
    // في التطبيق الحقيقي، سيتم تحميل البيانات للصفحة المحددة
    console.log(`الانتقال إلى الصفحة: ${page}`);
}

// دالة لتنسيق المبالغ المالية
function formatCurrency(amount, abbreviated = false) {
    if (abbreviated && amount >= 1000) {
        return (amount / 1000).toFixed(1) + ' ألف ر.س';
    }
    return amount.toLocaleString('ar-SA') + ' ر.س';
}

// بيانات دليل الحسابات
const chartOfAccounts = [
    // الأصول
    {
        id: 1,
        code: '1000',
        name: 'الأصول',
        type: 'assets',
        parentId: null,
        balance: 0,
        status: 'active',
        description: 'مجموعة الأصول الرئيسية',
        children: [
            {
                id: 11,
                code: '1100',
                name: 'الأصول المتداولة',
                type: 'assets',
                parentId: 1,
                balance: 0,
                status: 'active',
                description: 'الأصول قصيرة الأجل',
                children: [
                    {
                        id: 111,
                        code: '1101',
                        name: 'النقدية في الصندوق',
                        type: 'assets',
                        parentId: 11,
                        balance: 15000.00,
                        status: 'active',
                        description: 'النقدية المتوفرة في الصندوق'
                    },
                    {
                        id: 112,
                        code: '1102',
                        name: 'البنك - الحساب الجاري',
                        type: 'assets',
                        parentId: 11,
                        balance: 85000.00,
                        status: 'active',
                        description: 'الحساب الجاري في البنك'
                    },
                    {
                        id: 113,
                        code: '1103',
                        name: 'العملاء',
                        type: 'assets',
                        parentId: 11,
                        balance: 45000.00,
                        status: 'active',
                        description: 'مستحقات العملاء'
                    },
                    {
                        id: 114,
                        code: '1104',
                        name: 'المخزون',
                        type: 'assets',
                        parentId: 11,
                        balance: 120000.00,
                        status: 'active',
                        description: 'قيمة المخزون'
                    }
                ]
            },
            {
                id: 12,
                code: '1200',
                name: 'الأصول الثابتة',
                type: 'assets',
                parentId: 1,
                balance: 0,
                status: 'active',
                description: 'الأصول طويلة الأجل',
                children: [
                    {
                        id: 121,
                        code: '1201',
                        name: 'الأراضي والمباني',
                        type: 'assets',
                        parentId: 12,
                        balance: 500000.00,
                        status: 'active',
                        description: 'قيمة الأراضي والمباني'
                    },
                    {
                        id: 122,
                        code: '1202',
                        name: 'المعدات والآلات',
                        type: 'assets',
                        parentId: 12,
                        balance: 150000.00,
                        status: 'active',
                        description: 'قيمة المعدات والآلات'
                    },
                    {
                        id: 123,
                        code: '1203',
                        name: 'الأثاث والتجهيزات',
                        type: 'assets',
                        parentId: 12,
                        balance: 25000.00,
                        status: 'active',
                        description: 'قيمة الأثاث والتجهيزات'
                    }
                ]
            }
        ]
    },
    // الخصوم
    {
        id: 2,
        code: '2000',
        name: 'الخصوم',
        type: 'liabilities',
        parentId: null,
        balance: 0,
        status: 'active',
        description: 'مجموعة الخصوم الرئيسية',
        children: [
            {
                id: 21,
                code: '2100',
                name: 'الخصوم المتداولة',
                type: 'liabilities',
                parentId: 2,
                balance: 0,
                status: 'active',
                description: 'الخصوم قصيرة الأجل',
                children: [
                    {
                        id: 211,
                        code: '2101',
                        name: 'الموردون',
                        type: 'liabilities',
                        parentId: 21,
                        balance: 35000.00,
                        status: 'active',
                        description: 'مستحقات الموردين'
                    },
                    {
                        id: 212,
                        code: '2102',
                        name: 'الرواتب المستحقة',
                        type: 'liabilities',
                        parentId: 21,
                        balance: 12000.00,
                        status: 'active',
                        description: 'رواتب مستحقة للموظفين'
                    },
                    {
                        id: 213,
                        code: '2103',
                        name: 'الضرائب المستحقة',
                        type: 'liabilities',
                        parentId: 21,
                        balance: 8000.00,
                        status: 'active',
                        description: 'ضرائب مستحقة للحكومة'
                    }
                ]
            },
            {
                id: 22,
                code: '2200',
                name: 'الخصوم طويلة الأجل',
                type: 'liabilities',
                parentId: 2,
                balance: 0,
                status: 'active',
                description: 'الخصوم طويلة الأجل',
                children: [
                    {
                        id: 221,
                        code: '2201',
                        name: 'القروض البنكية',
                        type: 'liabilities',
                        parentId: 22,
                        balance: 200000.00,
                        status: 'active',
                        description: 'قروض من البنوك'
                    }
                ]
            }
        ]
    },
    // حقوق الملكية
    {
        id: 3,
        code: '3000',
        name: 'حقوق الملكية',
        type: 'equity',
        parentId: null,
        balance: 0,
        status: 'active',
        description: 'مجموعة حقوق الملكية',
        children: [
            {
                id: 31,
                code: '3100',
                name: 'رأس المال',
                type: 'equity',
                parentId: 3,
                balance: 500000.00,
                status: 'active',
                description: 'رأس المال المدفوع'
            },
            {
                id: 32,
                code: '3200',
                name: 'الأرباح المحتجزة',
                type: 'equity',
                parentId: 3,
                balance: 150000.00,
                status: 'active',
                description: 'الأرباح المحتجزة من السنوات السابقة'
            }
        ]
    },
    // الإيرادات
    {
        id: 4,
        code: '4000',
        name: 'الإيرادات',
        type: 'revenue',
        parentId: null,
        balance: 0,
        status: 'active',
        description: 'مجموعة الإيرادات',
        children: [
            {
                id: 41,
                code: '4100',
                name: 'إيرادات المبيعات',
                type: 'revenue',
                parentId: 4,
                balance: 250000.00,
                status: 'active',
                description: 'إيرادات من بيع البضائع'
            },
            {
                id: 42,
                code: '4200',
                name: 'إيرادات الخدمات',
                type: 'revenue',
                parentId: 4,
                balance: 75000.00,
                status: 'active',
                description: 'إيرادات من تقديم الخدمات'
            }
        ]
    },
    // المصروفات
    {
        id: 5,
        code: '5000',
        name: 'المصروفات',
        type: 'expenses',
        parentId: null,
        balance: 0,
        status: 'active',
        description: 'مجموعة المصروفات',
        children: [
            {
                id: 51,
                code: '5100',
                name: 'تكلفة البضاعة المباعة',
                type: 'expenses',
                parentId: 5,
                balance: 150000.00,
                status: 'active',
                description: 'تكلفة البضائع المباعة'
            },
            {
                id: 52,
                code: '5200',
                name: 'مصروفات التشغيل',
                type: 'expenses',
                parentId: 5,
                balance: 0,
                status: 'active',
                description: 'مصروفات التشغيل العامة',
                children: [
                    {
                        id: 521,
                        code: '5201',
                        name: 'الرواتب والأجور',
                        type: 'expenses',
                        parentId: 52,
                        balance: 45000.00,
                        status: 'active',
                        description: 'رواتب وأجور الموظفين'
                    },
                    {
                        id: 522,
                        code: '5202',
                        name: 'الإيجار',
                        type: 'expenses',
                        parentId: 52,
                        balance: 18000.00,
                        status: 'active',
                        description: 'إيجار المكاتب والمحلات'
                    },
                    {
                        id: 523,
                        code: '5203',
                        name: 'المرافق العامة',
                        type: 'expenses',
                        parentId: 52,
                        balance: 8000.00,
                        status: 'active',
                        description: 'فواتير الكهرباء والماء والهاتف'
                    }
                ]
            }
        ]
    }
];

// دالة تهيئة دليل الحسابات
function initChartOfAccounts() {
    displayAccountsTree();
    displayAccountsTable();
    setupAccountsEventListeners();
}

// دالة عرض شجرة الحسابات
function displayAccountsTree() {
    const treeContainer = document.getElementById('accounts-tree');
    if (!treeContainer) return;

    treeContainer.innerHTML = '';

    chartOfAccounts.forEach(account => {
        const accountElement = createAccountTreeNode(account);
        treeContainer.appendChild(accountElement);
    });
}

// دالة إنشاء عقدة في شجرة الحسابات
function createAccountTreeNode(account, level = 0) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = 'account-node';
    nodeDiv.style.marginRight = `${level * 20}px`;

    const itemDiv = document.createElement('div');
    itemDiv.className = 'account-item';
    itemDiv.dataset.accountId = account.id;

    // زر التوسيع/الطي
    if (account.children && account.children.length > 0) {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'account-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        toggleBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleAccountChildren(account.id);
        });
        itemDiv.appendChild(toggleBtn);
    } else {
        const spacer = document.createElement('div');
        spacer.style.width = '20px';
        itemDiv.appendChild(spacer);
    }

    // معلومات الحساب
    const infoDiv = document.createElement('div');
    infoDiv.className = 'account-info';

    const codeSpan = document.createElement('span');
    codeSpan.className = 'account-code';
    codeSpan.textContent = account.code;

    const nameSpan = document.createElement('span');
    nameSpan.className = 'account-name';
    nameSpan.textContent = account.name;

    const balanceSpan = document.createElement('span');
    balanceSpan.className = `account-balance ${account.balance < 0 ? 'negative' : ''}`;
    balanceSpan.textContent = `${account.balance.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;

    const statusSpan = document.createElement('span');
    statusSpan.className = `account-status ${account.status}`;
    statusSpan.textContent = account.status === 'active' ? 'نشط' : 'غير نشط';

    infoDiv.appendChild(codeSpan);
    infoDiv.appendChild(nameSpan);
    infoDiv.appendChild(balanceSpan);
    infoDiv.appendChild(statusSpan);

    itemDiv.appendChild(infoDiv);
    nodeDiv.appendChild(itemDiv);

    // إضافة الحسابات الفرعية
    if (account.children && account.children.length > 0) {
        const childrenDiv = document.createElement('div');
        childrenDiv.className = 'account-children';
        childrenDiv.id = `children-${account.id}`;

        account.children.forEach(child => {
            const childNode = createAccountTreeNode(child, level + 1);
            childrenDiv.appendChild(childNode);
        });

        nodeDiv.appendChild(childrenDiv);
    }

    return nodeDiv;
}

// دالة توسيع/طي الحسابات الفرعية
function toggleAccountChildren(accountId) {
    const childrenDiv = document.getElementById(`children-${accountId}`);
    const toggleBtn = document.querySelector(`[data-account-id="${accountId}"] .account-toggle`);

    if (childrenDiv) {
        if (childrenDiv.classList.contains('hidden')) {
            childrenDiv.classList.remove('hidden');
            toggleBtn.classList.add('expanded');
        } else {
            childrenDiv.classList.add('hidden');
            toggleBtn.classList.remove('expanded');
        }
    }
}

// دالة عرض جدول الحسابات
function displayAccountsTable() {
    const tableBody = document.getElementById('accounts-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    const flatAccounts = flattenAccounts(chartOfAccounts);

    flatAccounts.forEach(account => {
        const row = createAccountTableRow(account);
        tableBody.appendChild(row);
    });
}

// دالة تحويل شجرة الحسابات إلى قائمة مسطحة
function flattenAccounts(accounts, result = []) {
    accounts.forEach(account => {
        result.push(account);
        if (account.children && account.children.length > 0) {
            flattenAccounts(account.children, result);
        }
    });
    return result;
}

// دالة إنشاء صف في جدول الحسابات
function createAccountTableRow(account) {
    const row = document.createElement('tr');

    // رقم الحساب
    const codeCell = document.createElement('td');
    codeCell.className = 'account-code';
    codeCell.textContent = account.code;

    // اسم الحساب
    const nameCell = document.createElement('td');
    nameCell.className = 'account-name';
    nameCell.textContent = account.name;

    // نوع الحساب
    const typeCell = document.createElement('td');
    const typeSpan = document.createElement('span');
    typeSpan.className = `account-type ${account.type}`;
    typeSpan.textContent = getAccountTypeLabel(account.type);
    typeCell.appendChild(typeSpan);

    // الحساب الأب
    const parentCell = document.createElement('td');
    const parentAccount = findAccountById(account.parentId);
    parentCell.textContent = parentAccount ? parentAccount.name : '-';

    // الرصيد
    const balanceCell = document.createElement('td');
    balanceCell.className = `account-balance ${account.balance < 0 ? 'negative' : ''}`;
    balanceCell.textContent = `${account.balance.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;

    // الحالة
    const statusCell = document.createElement('td');
    const statusSpan = document.createElement('span');
    statusSpan.className = `account-status ${account.status}`;
    statusSpan.textContent = account.status === 'active' ? 'نشط' : 'غير نشط';
    statusCell.appendChild(statusSpan);

    // الإجراءات
    const actionsCell = document.createElement('td');
    actionsCell.innerHTML = `
        <button class="action-btn view-btn" onclick="viewAccount(${account.id})" title="عرض">
            <i class="fas fa-eye"></i>
        </button>
        <button class="action-btn edit-btn" onclick="editAccount(${account.id})" title="تعديل">
            <i class="fas fa-edit"></i>
        </button>
        <button class="action-btn delete-btn" onclick="deleteAccount(${account.id})" title="حذف">
            <i class="fas fa-trash"></i>
        </button>
    `;

    row.appendChild(codeCell);
    row.appendChild(nameCell);
    row.appendChild(typeCell);
    row.appendChild(parentCell);
    row.appendChild(balanceCell);
    row.appendChild(statusCell);
    row.appendChild(actionsCell);

    return row;
}

// دالة الحصول على تسمية نوع الحساب
function getAccountTypeLabel(type) {
    const labels = {
        'assets': 'الأصول',
        'liabilities': 'الخصوم',
        'equity': 'حقوق الملكية',
        'revenue': 'الإيرادات',
        'expenses': 'المصروفات'
    };
    return labels[type] || type;
}

// دالة البحث عن حساب بالمعرف
function findAccountById(id, accounts = chartOfAccounts) {
    for (const account of accounts) {
        if (account.id === id) {
            return account;
        }
        if (account.children) {
            const found = findAccountById(id, account.children);
            if (found) return found;
        }
    }
    return null;
}

// دالة إعداد مستمعي الأحداث لدليل الحسابات
function setupAccountsEventListeners() {
    // البحث في الحسابات
    const searchInput = document.getElementById('accounts-search');
    if (searchInput) {
        searchInput.addEventListener('input', filterAccounts);
    }

    // تصفية نوع الحساب
    const typeFilter = document.getElementById('account-type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', filterAccounts);
    }

    // تصفية حالة الحساب
    const statusFilter = document.getElementById('account-status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', filterAccounts);
    }

    // زر إضافة حساب جديد
    const addAccountBtn = document.querySelector('.add-account-btn');
    if (addAccountBtn) {
        addAccountBtn.addEventListener('click', showAddAccountModal);
    }

    // أزرار إغلاق النوافذ المنبثقة
    const closeButtons = document.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });

    // أزرار الإلغاء
    const cancelButtons = document.querySelectorAll('#cancel-add-account, #cancel-edit-account');
    cancelButtons.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });

    // نماذج الحسابات
    const addForm = document.getElementById('add-account-form');
    if (addForm) {
        addForm.addEventListener('submit', handleAddAccount);
    }

    const editForm = document.getElementById('edit-account-form');
    if (editForm) {
        editForm.addEventListener('submit', handleEditAccount);
    }
}

// دالة تصفية الحسابات
function filterAccounts() {
    const searchTerm = document.getElementById('accounts-search').value.toLowerCase();
    const typeFilter = document.getElementById('account-type-filter').value;
    const statusFilter = document.getElementById('account-status-filter').value;

    const flatAccounts = flattenAccounts(chartOfAccounts);

    const filteredAccounts = flatAccounts.filter(account => {
        const matchesSearch = account.name.toLowerCase().includes(searchTerm) ||
                            account.code.includes(searchTerm);
        const matchesType = !typeFilter || account.type === typeFilter;
        const matchesStatus = !statusFilter || account.status === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
    });

    // إعادة عرض الجدول مع النتائج المفلترة
    const tableBody = document.getElementById('accounts-table-body');
    if (tableBody) {
        tableBody.innerHTML = '';
        filteredAccounts.forEach(account => {
            const row = createAccountTableRow(account);
            tableBody.appendChild(row);
        });
    }
}

// دالة عرض نافذة إضافة حساب
function showAddAccountModal() {
    const modal = document.getElementById('add-account-modal');
    if (modal) {
        populateParentAccountOptions('parent-account');
        modal.classList.add('show');
    }
}

// دالة ملء خيارات الحساب الأب
function populateParentAccountOptions(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // مسح الخيارات الحالية (عدا الخيار الأول)
    while (select.children.length > 1) {
        select.removeChild(select.lastChild);
    }

    const flatAccounts = flattenAccounts(chartOfAccounts);

    flatAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.code} - ${account.name}`;
        select.appendChild(option);
    });
}

// دالة إغلاق النافذة المنبثقة
function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
    });
}

// دالة معالجة إضافة حساب جديد
function handleAddAccount(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const newAccount = {
        id: Date.now(), // معرف مؤقت
        code: formData.get('account-code'),
        name: formData.get('account-name'),
        type: formData.get('account-type'),
        parentId: formData.get('parent-account') ? parseInt(formData.get('parent-account')) : null,
        balance: parseFloat(formData.get('account-balance')) || 0,
        status: formData.get('account-status'),
        description: formData.get('account-description')
    };

    // التحقق من عدم تكرار رقم الحساب
    const existingAccount = flattenAccounts(chartOfAccounts).find(acc => acc.code === newAccount.code);
    if (existingAccount) {
        alert('رقم الحساب موجود بالفعل. يرجى اختيار رقم آخر.');
        return;
    }

    // إضافة الحساب الجديد
    if (newAccount.parentId) {
        const parentAccount = findAccountById(newAccount.parentId);
        if (parentAccount) {
            if (!parentAccount.children) {
                parentAccount.children = [];
            }
            parentAccount.children.push(newAccount);
        }
    } else {
        chartOfAccounts.push(newAccount);
    }

    // إعادة عرض البيانات
    displayAccountsTree();
    displayAccountsTable();

    // إغلاق النافذة المنبثقة
    closeModal();

    // مسح النموذج
    e.target.reset();

    alert('تم إضافة الحساب بنجاح!');
}

// دالة عرض تفاصيل الحساب
function viewAccount(accountId) {
    const account = findAccountById(accountId);
    if (account) {
        alert(`تفاصيل الحساب:\nرقم الحساب: ${account.code}\nاسم الحساب: ${account.name}\nالنوع: ${getAccountTypeLabel(account.type)}\nالرصيد: ${account.balance.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س\nالحالة: ${account.status === 'active' ? 'نشط' : 'غير نشط'}\nالوصف: ${account.description || 'لا يوجد وصف'}`);
    }
}

// دالة تعديل الحساب
function editAccount(accountId) {
    const account = findAccountById(accountId);
    if (!account) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('edit-account-id').value = account.id;
    document.getElementById('edit-account-code').value = account.code;
    document.getElementById('edit-account-name').value = account.name;
    document.getElementById('edit-account-type').value = account.type;
    document.getElementById('edit-account-balance').value = account.balance;
    document.getElementById('edit-account-status').value = account.status;
    document.getElementById('edit-account-description').value = account.description || '';

    // ملء خيارات الحساب الأب
    populateParentAccountOptions('edit-parent-account');
    if (account.parentId) {
        document.getElementById('edit-parent-account').value = account.parentId;
    }

    // عرض النافذة المنبثقة
    const modal = document.getElementById('edit-account-modal');
    if (modal) {
        modal.classList.add('show');
    }
}

// دالة معالجة تعديل الحساب
function handleEditAccount(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const accountId = parseInt(formData.get('account-id'));
    const account = findAccountById(accountId);

    if (!account) return;

    // تحديث بيانات الحساب
    account.code = formData.get('account-code');
    account.name = formData.get('account-name');
    account.type = formData.get('account-type');
    account.status = formData.get('account-status');
    account.description = formData.get('account-description');

    // إعادة عرض البيانات
    displayAccountsTree();
    displayAccountsTable();

    // إغلاق النافذة المنبثقة
    closeModal();

    alert('تم تحديث الحساب بنجاح!');
}

// دالة حذف الحساب
function deleteAccount(accountId) {
    const account = findAccountById(accountId);
    if (!account) return;

    if (confirm(`هل أنت متأكد من حذف الحساب "${account.name}"؟\nسيتم حذف جميع الحسابات الفرعية أيضاً.`)) {
        // البحث عن الحساب وحذفه
        removeAccountFromTree(accountId, chartOfAccounts);

        // إعادة عرض البيانات
        displayAccountsTree();
        displayAccountsTable();

        alert('تم حذف الحساب بنجاح!');
    }
}

// دالة حذف الحساب من الشجرة
function removeAccountFromTree(accountId, accounts) {
    for (let i = 0; i < accounts.length; i++) {
        if (accounts[i].id === accountId) {
            accounts.splice(i, 1);
            return true;
        }
        if (accounts[i].children) {
            if (removeAccountFromTree(accountId, accounts[i].children)) {
                return true;
            }
        }
    }
    return false;
}

// وظائف التبويبات
function initTabs() {
    // الحصول على جميع أزرار التبويبات
    const navTabs = document.querySelectorAll('.nav-tab');

    // إضافة مستمع أحداث لكل تبويب
    navTabs.forEach((tab) => {
        const tabName = tab.getAttribute('data-tab');

        tab.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // تفعيل التبويب
            activateTab(tabName);
        });
    });

    // تفعيل التبويب الأول افتراضياً
    if (navTabs.length > 0) {
        const firstTabName = navTabs[0].getAttribute('data-tab');
        activateTab(firstTabName);
    }
}

// وظيفة تفعيل التبويب
function activateTab(tabName) {
    // إزالة الفئة النشطة من جميع التبويبات
    const allTabs = document.querySelectorAll('.nav-tab');
    const allPanes = document.querySelectorAll('.tab-pane');

    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    allPanes.forEach(pane => {
        pane.classList.remove('active');
        pane.style.display = 'none';
    });

    // تفعيل التبويب المحدد
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // تفعيل اللوحة المقابلة
    const targetPane = document.getElementById(`${tabName}-tab`);
    if (targetPane) {
        targetPane.classList.add('active');
        targetPane.style.display = 'block';

        // تحميل محتوى التبويب
        loadTabContent(tabName);
    }
}

// تحميل محتوى التبويب
function loadTabContent(tabName) {
    switch(tabName) {
        case 'overview':
            // تحديث الإحصائيات
            loadFinancialStats();
            break;
        case 'chart-of-accounts':
            // تحديث دليل الحسابات
            displayAccountsTree();
            displayAccountsTable();
            break;
        case 'journal-entries':
            // تحميل قيود اليومية
            loadJournalEntries();
            loadJournalEntriesTab();
            break;
        case 'receipt-voucher':
            // تحميل سندات القبض
            loadReceiptVouchers();
            loadReceiptVouchersTab();
            break;
        case 'payment-voucher':
            // تحميل سندات الصرف
            loadPaymentVouchers();
            loadPaymentVouchersTab();
            break;
    }
}

// بيانات قيود اليومية (نموذج)
const journalEntries = [
    {
        id: 1,
        entryNumber: 'JE-2023-001',
        date: '2023-07-01',
        description: 'قيد افتتاحي للنقدية',
        debit: 15000.00,
        credit: 15000.00,
        status: 'posted'
    },
    {
        id: 2,
        entryNumber: 'JE-2023-002',
        date: '2023-07-02',
        description: 'شراء بضاعة نقداً',
        debit: 5000.00,
        credit: 5000.00,
        status: 'posted'
    },
    {
        id: 3,
        entryNumber: 'JE-2023-003',
        date: '2023-07-03',
        description: 'بيع بضاعة نقداً',
        debit: 8000.00,
        credit: 8000.00,
        status: 'draft'
    }
];

// بيانات سندات القبض (نموذج)
const receiptVouchers = [
    {
        id: 1,
        voucherNumber: 'RV-2023-001',
        date: '2023-07-01',
        receivedFrom: 'شركة الأمل للتجارة',
        amount: 12000.00,
        description: 'تحصيل فاتورة رقم INV-001',
        status: 'approved'
    },
    {
        id: 2,
        voucherNumber: 'RV-2023-002',
        date: '2023-07-02',
        receivedFrom: 'محمد أحمد علي',
        amount: 3500.00,
        description: 'تحصيل مستحقات عميل',
        status: 'pending'
    }
];

// بيانات سندات الصرف (نموذج)
const paymentVouchers = [
    {
        id: 1,
        voucherNumber: 'PV-2023-001',
        date: '2023-07-01',
        paidTo: 'شركة الكهرباء',
        amount: 2500.00,
        description: 'دفع فاتورة الكهرباء',
        status: 'approved'
    },
    {
        id: 2,
        voucherNumber: 'PV-2023-002',
        date: '2023-07-02',
        paidTo: 'مكتب المحاسبة القانونية',
        amount: 1800.00,
        description: 'أتعاب مهنية',
        status: 'pending'
    }
];

// تحميل قيود اليومية
function loadJournalEntries() {
    const tableBody = document.getElementById('journal-entries-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    journalEntries.forEach(entry => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${entry.entryNumber}</td>
            <td>${entry.date}</td>
            <td>${entry.description}</td>
            <td>${entry.debit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>${entry.credit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>
                <button class="action-btn view-btn" onclick="viewJournalEntry(${entry.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editJournalEntry(${entry.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteJournalEntry(${entry.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// تحميل سندات القبض
function loadReceiptVouchers() {
    const tableBody = document.getElementById('receipt-vouchers-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    receiptVouchers.forEach(voucher => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${voucher.voucherNumber}</td>
            <td>${voucher.date}</td>
            <td>${voucher.receivedFrom}</td>
            <td>${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>${voucher.description}</td>
            <td>
                <button class="action-btn view-btn" onclick="viewReceiptVoucher(${voucher.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editReceiptVoucher(${voucher.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteReceiptVoucher(${voucher.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// تحميل سندات الصرف
function loadPaymentVouchers() {
    const tableBody = document.getElementById('payment-vouchers-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    paymentVouchers.forEach(voucher => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${voucher.voucherNumber}</td>
            <td>${voucher.date}</td>
            <td>${voucher.paidTo}</td>
            <td>${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>${voucher.description}</td>
            <td>
                <button class="action-btn view-btn" onclick="viewPaymentVoucher(${voucher.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editPaymentVoucher(${voucher.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deletePaymentVoucher(${voucher.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// وظائف قيود اليومية
function viewJournalEntry(id) {
    const entry = journalEntries.find(e => e.id === id);
    if (entry) {
        alert(`تفاصيل القيد:\nرقم القيد: ${entry.entryNumber}\nالتاريخ: ${entry.date}\nالبيان: ${entry.description}\nالمدين: ${entry.debit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س\nالدائن: ${entry.credit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`);
    }
}

function editJournalEntry(id) {
    alert('سيتم إضافة نافذة تعديل قيد اليومية قريباً');
}

function deleteJournalEntry(id) {
    if (confirm('هل أنت متأكد من حذف هذا القيد؟')) {
        const index = journalEntries.findIndex(e => e.id === id);
        if (index > -1) {
            journalEntries.splice(index, 1);
            loadJournalEntries();
            alert('تم حذف القيد بنجاح!');
        }
    }
}

// وظائف سندات القبض
function viewReceiptVoucher(id) {
    const voucher = receiptVouchers.find(v => v.id === id);
    if (voucher) {
        alert(`تفاصيل سند القبض:\nرقم السند: ${voucher.voucherNumber}\nالتاريخ: ${voucher.date}\nالمستلم من: ${voucher.receivedFrom}\nالمبلغ: ${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س\nالبيان: ${voucher.description}`);
    }
}

function editReceiptVoucher(id) {
    alert('سيتم إضافة نافذة تعديل سند القبض قريباً');
}

function deleteReceiptVoucher(id) {
    if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
        const index = receiptVouchers.findIndex(v => v.id === id);
        if (index > -1) {
            receiptVouchers.splice(index, 1);
            loadReceiptVouchers();
            alert('تم حذف السند بنجاح!');
        }
    }
}

// وظائف سندات الصرف
function viewPaymentVoucher(id) {
    const voucher = paymentVouchers.find(v => v.id === id);
    if (voucher) {
        alert(`تفاصيل سند الصرف:\nرقم السند: ${voucher.voucherNumber}\nالتاريخ: ${voucher.date}\nالمدفوع إلى: ${voucher.paidTo}\nالمبلغ: ${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س\nالبيان: ${voucher.description}`);
    }
}

function editPaymentVoucher(id) {
    alert('سيتم إضافة نافذة تعديل سند الصرف قريباً');
}

function deletePaymentVoucher(id) {
    if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
        const index = paymentVouchers.findIndex(v => v.id === id);
        if (index > -1) {
            paymentVouchers.splice(index, 1);
            loadPaymentVouchers();
            alert('تم حذف السند بنجاح!');
        }
    }
}

// وظائف الأزرار الرئيسية
function showAddJournalEntryModal() {
    const modal = document.getElementById('add-journal-entry-modal');
    if (modal) {
        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('entry-date');
        if (dateInput) {
            dateInput.value = today;
        }

        // تحديث قوائم الحسابات
        populateAccountOptions('debit-account');
        populateAccountOptions('credit-account');

        // عرض النافذة
        modal.style.display = 'flex';
        modal.classList.add('show');
    }
}

function exportAccountingData() {
    console.log('تصدير البيانات المحاسبية...');

    // جمع جميع البيانات
    const accountingData = {
        chartOfAccounts: chartOfAccounts,
        journalEntries: journalEntries,
        receiptVouchers: receiptVouchers,
        paymentVouchers: paymentVouchers,
        exportDate: new Date().toISOString(),
        exportedBy: 'نظام إدارة الحسابات'
    };

    // تحويل البيانات إلى JSON
    const dataStr = JSON.stringify(accountingData, null, 2);

    // إنشاء ملف للتحميل
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    // إنشاء رابط التحميل
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = `accounting-data-${new Date().toISOString().split('T')[0]}.json`;

    // تنفيذ التحميل
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // تنظيف الذاكرة
    URL.revokeObjectURL(url);

    alert('تم تصدير البيانات المحاسبية بنجاح!');
}

// وظيفة اختبار التبويبات
function testTabs() {
    console.log('=== اختبار التبويبات ===');

    const tabs = document.querySelectorAll('.nav-tab');
    const panes = document.querySelectorAll('.tab-pane');

    console.log(`عدد أزرار التبويبات: ${tabs.length}`);
    console.log(`عدد لوحات التبويبات: ${panes.length}`);

    // اختبار كل تبويب
    tabs.forEach((tab, index) => {
        const dataTab = tab.getAttribute('data-tab');
        const isActive = tab.classList.contains('active');
        console.log(`تبويب ${index + 1}: ${dataTab} - نشط: ${isActive}`);

        // البحث عن اللوحة المقابلة
        const targetPane = document.getElementById(`${dataTab}-tab`);
        if (targetPane) {
            const paneActive = targetPane.classList.contains('active');
            console.log(`  -> لوحة ${targetPane.id} موجودة - نشطة: ${paneActive}`);
        } else {
            console.error(`  -> لوحة ${dataTab}-tab غير موجودة!`);
        }
    });

    // اختبار تبديل التبويب الثاني
    if (tabs.length > 1) {
        console.log('اختبار تبديل التبويب الثاني...');
        const secondTab = tabs[1];
        secondTab.click();

        setTimeout(() => {
            const isActive = secondTab.classList.contains('active');
            console.log(`التبويب الثاني نشط بعد النقر: ${isActive}`);
        }, 100);
    }
}

// إضافة أزرار اختبار للصفحة
function addTestButton() {
    const testContainer = document.createElement('div');
    testContainer.style.position = 'fixed';
    testContainer.style.top = '10px';
    testContainer.style.right = '10px';
    testContainer.style.zIndex = '9999';
    testContainer.style.display = 'flex';
    testContainer.style.flexDirection = 'column';
    testContainer.style.gap = '5px';

    // زر اختبار عام
    const testBtn = document.createElement('button');
    testBtn.textContent = 'اختبار التبويبات';
    testBtn.style.padding = '8px 12px';
    testBtn.style.backgroundColor = '#e74c3c';
    testBtn.style.color = 'white';
    testBtn.style.border = 'none';
    testBtn.style.borderRadius = '5px';
    testBtn.style.cursor = 'pointer';
    testBtn.style.fontSize = '12px';
    testBtn.onclick = testTabs;
    testContainer.appendChild(testBtn);

    // أزرار اختبار لكل تبويب
    const tabNames = ['overview', 'chart-of-accounts', 'journal-entries', 'receipt-voucher', 'payment-voucher'];
    const tabLabels = ['نظرة عامة', 'دليل الحسابات', 'قيد اليومية', 'سند القبض', 'سند الصرف'];

    tabNames.forEach((tabName, index) => {
        const btn = document.createElement('button');
        btn.textContent = tabLabels[index];
        btn.style.padding = '6px 10px';
        btn.style.backgroundColor = '#3498db';
        btn.style.color = 'white';
        btn.style.border = 'none';
        btn.style.borderRadius = '3px';
        btn.style.cursor = 'pointer';
        btn.style.fontSize = '11px';
        btn.onclick = () => {
            console.log(`اختبار تفعيل التبويب: ${tabName}`);
            activateTab(tabName);
        };
        testContainer.appendChild(btn);
    });

    document.body.appendChild(testContainer);
}

// ==================== وظائف الطباعة والتصدير ====================

// طباعة قيود اليومية
function printJournalEntries() {
    const printWindow = window.open('', '_blank');
    const printContent = generateJournalEntriesPrintContent();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>قيود اليومية</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 15px; }
                .company-name { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
                .report-title { font-size: 18px; color: #666; }
                .report-date { font-size: 14px; color: #888; margin-top: 10px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .total-row { background-color: #f9f9f9; font-weight: bold; }
                .entry-details { margin-bottom: 20px; }
                .entry-header { background-color: #e3f2fd; font-weight: bold; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            ${printContent}
            <script>window.print(); window.close();</script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// إنشاء محتوى طباعة قيود اليومية
function generateJournalEntriesPrintContent() {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    let content = `
        <div class="header">
            <div class="company-name">نظام إدارة الحسابات</div>
            <div class="report-title">تقرير قيود اليومية</div>
            <div class="report-date">تاريخ الطباعة: ${currentDate}</div>
        </div>
    `;

    if (journalEntries.length === 0) {
        content += '<p style="text-align: center; color: #666;">لا توجد قيود يومية للعرض</p>';
        return content;
    }

    journalEntries.forEach((entry, index) => {
        content += `
            <div class="entry-details">
                <table>
                    <tr class="entry-header">
                        <td colspan="4">
                            قيد رقم: ${entry.id} | التاريخ: ${entry.date} | الحالة: ${getStatusText(entry.status)}
                        </td>
                    </tr>
                    <tr class="entry-header">
                        <td colspan="4">الوصف: ${entry.description}</td>
                    </tr>
                    <tr>
                        <th>الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                    </tr>
        `;

        let totalDebit = 0;
        let totalCredit = 0;

        entry.entries.forEach(item => {
            const accountName = getAccountName(item.accountId);
            totalDebit += item.debit || 0;
            totalCredit += item.credit || 0;

            content += `
                <tr>
                    <td>${accountName}</td>
                    <td>${item.description}</td>
                    <td>${item.debit ? formatCurrency(item.debit) : '-'}</td>
                    <td>${item.credit ? formatCurrency(item.credit) : '-'}</td>
                </tr>
            `;
        });

        content += `
                    <tr class="total-row">
                        <td colspan="2">الإجمالي</td>
                        <td>${formatCurrency(totalDebit)}</td>
                        <td>${formatCurrency(totalCredit)}</td>
                    </tr>
                </table>
            </div>
        `;
    });

    return content;
}

// تصدير قيود اليومية إلى Excel
function exportJournalEntriesToExcel() {
    if (journalEntries.length === 0) {
        alert('لا توجد قيود يومية للتصدير');
        return;
    }

    const workbook = XLSX.utils.book_new();
    const worksheetData = [];

    // إضافة العنوان
    worksheetData.push(['تقرير قيود اليومية']);
    worksheetData.push(['تاريخ التصدير: ' + new Date().toLocaleDateString('ar-SA')]);
    worksheetData.push([]); // سطر فارغ

    journalEntries.forEach((entry, index) => {
        // معلومات القيد
        worksheetData.push([`قيد رقم: ${entry.id}`, `التاريخ: ${entry.date}`, `الحالة: ${getStatusText(entry.status)}`]);
        worksheetData.push([`الوصف: ${entry.description}`]);

        // رؤوس الأعمدة
        worksheetData.push(['الحساب', 'البيان', 'مدين', 'دائن']);

        let totalDebit = 0;
        let totalCredit = 0;

        // بيانات القيد
        entry.entries.forEach(item => {
            const accountName = getAccountName(item.accountId);
            totalDebit += item.debit || 0;
            totalCredit += item.credit || 0;

            worksheetData.push([
                accountName,
                item.description,
                item.debit || 0,
                item.credit || 0
            ]);
        });

        // الإجمالي
        worksheetData.push(['الإجمالي', '', totalDebit, totalCredit]);
        worksheetData.push([]); // سطر فارغ
    });

    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'قيود اليومية');

    const fileName = `journal-entries-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    alert('تم تصدير قيود اليومية إلى Excel بنجاح!');
}

// تصدير قيود اليومية إلى PDF
function exportJournalEntriesToPDF() {
    if (journalEntries.length === 0) {
        alert('لا توجد قيود يومية للتصدير');
        return;
    }

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF('p', 'mm', 'a4');

    // إعداد الخط العربي
    doc.setFont('helvetica');
    doc.setFontSize(16);

    // العنوان
    doc.text('تقرير قيود اليومية', 105, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text('تاريخ التصدير: ' + new Date().toLocaleDateString('ar-SA'), 105, 30, { align: 'center' });

    let yPosition = 50;

    journalEntries.forEach((entry, index) => {
        // التحقق من المساحة المتبقية
        if (yPosition > 250) {
            doc.addPage();
            yPosition = 20;
        }

        // معلومات القيد
        doc.setFontSize(12);
        doc.text(`Entry ID: ${entry.id} | Date: ${entry.date} | Status: ${getStatusText(entry.status)}`, 20, yPosition);
        yPosition += 10;
        doc.text(`Description: ${entry.description}`, 20, yPosition);
        yPosition += 15;

        // إنشاء جدول للقيد
        const tableData = [];
        let totalDebit = 0;
        let totalCredit = 0;

        entry.entries.forEach(item => {
            const accountName = getAccountName(item.accountId);
            totalDebit += item.debit || 0;
            totalCredit += item.credit || 0;

            tableData.push([
                accountName,
                item.description,
                item.debit ? item.debit.toFixed(2) : '-',
                item.credit ? item.credit.toFixed(2) : '-'
            ]);
        });

        // إضافة صف الإجمالي
        tableData.push([
            'Total',
            '',
            totalDebit.toFixed(2),
            totalCredit.toFixed(2)
        ]);

        doc.autoTable({
            head: [['Account', 'Description', 'Debit', 'Credit']],
            body: tableData,
            startY: yPosition,
            styles: { fontSize: 10, cellPadding: 3 },
            headStyles: { fillColor: [52, 152, 219] },
            footStyles: { fillColor: [241, 241, 241], fontStyle: 'bold' }
        });

        yPosition = doc.lastAutoTable.finalY + 15;
    });

    const fileName = `journal-entries-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

    alert('تم تصدير قيود اليومية إلى PDF بنجاح!');
}

// ==================== وظائف التقارير المالية ====================

// طباعة التقرير المالي
function printFinancialReport() {
    const printWindow = window.open('', '_blank');
    const printContent = generateFinancialReportPrintContent();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>التقرير المالي</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 15px; }
                .company-name { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
                .report-title { font-size: 18px; color: #666; }
                .report-date { font-size: 14px; color: #888; margin-top: 10px; }
                .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 30px 0; }
                .stat-card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; text-align: center; }
                .stat-title { font-size: 16px; color: #666; margin-bottom: 10px; }
                .stat-value { font-size: 24px; font-weight: bold; color: #333; }
                .income { border-left: 4px solid #27ae60; }
                .expenses { border-left: 4px solid #e74c3c; }
                .profit { border-left: 4px solid #3498db; }
                .balance { border-left: 4px solid #f39c12; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                th { background-color: #f5f5f5; font-weight: bold; }
                @media print {
                    body { margin: 0; }
                    .stats-grid { grid-template-columns: repeat(2, 1fr); }
                }
            </style>
        </head>
        <body>
            ${printContent}
            <script>window.print(); window.close();</script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// إنشاء محتوى طباعة التقرير المالي
function generateFinancialReportPrintContent() {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const stats = getFinancialStats();

    let content = `
        <div class="header">
            <div class="company-name">نظام إدارة الحسابات</div>
            <div class="report-title">التقرير المالي الشامل</div>
            <div class="report-date">تاريخ الطباعة: ${currentDate}</div>
        </div>

        <div class="stats-grid">
            <div class="stat-card income">
                <div class="stat-title">إجمالي الإيرادات</div>
                <div class="stat-value">${formatCurrency(stats.income)}</div>
            </div>
            <div class="stat-card expenses">
                <div class="stat-title">إجمالي المصروفات</div>
                <div class="stat-value">${formatCurrency(stats.expenses)}</div>
            </div>
            <div class="stat-card profit">
                <div class="stat-title">صافي الربح</div>
                <div class="stat-value">${formatCurrency(stats.profit)}</div>
            </div>
            <div class="stat-card balance">
                <div class="stat-title">الرصيد الحالي</div>
                <div class="stat-value">${formatCurrency(stats.balance)}</div>
            </div>
        </div>
    `;

    // إضافة جدول الحسابات الرئيسية
    content += `
        <h3>ملخص الحسابات الرئيسية</h3>
        <table>
            <tr>
                <th>نوع الحساب</th>
                <th>عدد الحسابات</th>
                <th>إجمالي الرصيد</th>
            </tr>
    `;

    const accountSummary = getAccountsSummary();
    accountSummary.forEach(item => {
        content += `
            <tr>
                <td>${item.type}</td>
                <td>${item.count}</td>
                <td>${formatCurrency(item.balance)}</td>
            </tr>
        `;
    });

    content += '</table>';

    return content;
}

// تصدير التقرير المالي إلى Excel
function exportFinancialReportToExcel() {
    const workbook = XLSX.utils.book_new();
    const stats = getFinancialStats();

    // ورقة الإحصائيات المالية
    const statsData = [
        ['التقرير المالي الشامل'],
        ['تاريخ التصدير: ' + new Date().toLocaleDateString('ar-SA')],
        [],
        ['البيان', 'القيمة'],
        ['إجمالي الإيرادات', stats.income],
        ['إجمالي المصروفات', stats.expenses],
        ['صافي الربح', stats.profit],
        ['الرصيد الحالي', stats.balance],
        [],
        ['ملخص الحسابات الرئيسية'],
        ['نوع الحساب', 'عدد الحسابات', 'إجمالي الرصيد']
    ];

    const accountSummary = getAccountsSummary();
    accountSummary.forEach(item => {
        statsData.push([item.type, item.count, item.balance]);
    });

    const statsWorksheet = XLSX.utils.aoa_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'التقرير المالي');

    // ورقة قيود اليومية
    if (journalEntries.length > 0) {
        const journalData = [
            ['قيود اليومية'],
            [],
            ['رقم القيد', 'التاريخ', 'الوصف', 'الحالة', 'إجمالي مدين', 'إجمالي دائن']
        ];

        journalEntries.forEach(entry => {
            const totalDebit = entry.entries.reduce((sum, item) => sum + (item.debit || 0), 0);
            const totalCredit = entry.entries.reduce((sum, item) => sum + (item.credit || 0), 0);

            journalData.push([
                entry.id,
                entry.date,
                entry.description,
                getStatusText(entry.status),
                totalDebit,
                totalCredit
            ]);
        });

        const journalWorksheet = XLSX.utils.aoa_to_sheet(journalData);
        XLSX.utils.book_append_sheet(workbook, journalWorksheet, 'قيود اليومية');
    }

    const fileName = `financial-report-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    alert('تم تصدير التقرير المالي إلى Excel بنجاح!');
}

// تصدير التقرير المالي إلى PDF
function exportFinancialReportToPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF('p', 'mm', 'a4');
    const stats = getFinancialStats();

    // العنوان
    doc.setFontSize(18);
    doc.text('Financial Report', 105, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text('Export Date: ' + new Date().toLocaleDateString('en-US'), 105, 30, { align: 'center' });

    // الإحصائيات المالية
    const statsTableData = [
        ['Total Income', stats.income.toFixed(2)],
        ['Total Expenses', stats.expenses.toFixed(2)],
        ['Net Profit', stats.profit.toFixed(2)],
        ['Current Balance', stats.balance.toFixed(2)]
    ];

    doc.autoTable({
        head: [['Item', 'Amount']],
        body: statsTableData,
        startY: 50,
        styles: { fontSize: 12, cellPadding: 5 },
        headStyles: { fillColor: [52, 152, 219] }
    });

    // ملخص الحسابات
    let yPosition = doc.lastAutoTable.finalY + 20;
    doc.setFontSize(14);
    doc.text('Accounts Summary', 20, yPosition);

    const accountSummary = getAccountsSummary();
    const accountTableData = accountSummary.map(item => [
        item.type,
        item.count.toString(),
        item.balance.toFixed(2)
    ]);

    doc.autoTable({
        head: [['Account Type', 'Count', 'Total Balance']],
        body: accountTableData,
        startY: yPosition + 10,
        styles: { fontSize: 11, cellPadding: 4 },
        headStyles: { fillColor: [46, 204, 113] }
    });

    const fileName = `financial-report-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

    alert('تم تصدير التقرير المالي إلى PDF بنجاح!');
}

// ==================== الوظائف المساعدة ====================

// الحصول على الإحصائيات المالية
function getFinancialStats() {
    let income = 0;
    let expenses = 0;

    // حساب الإيرادات والمصروفات من قيود اليومية
    journalEntries.forEach(entry => {
        if (entry.status === 'posted') {
            entry.entries.forEach(item => {
                const account = findAccountById(item.accountId);
                if (account) {
                    if (account.type === 'revenue') {
                        income += item.credit || 0;
                    } else if (account.type === 'expense') {
                        expenses += item.debit || 0;
                    }
                }
            });
        }
    });

    // إضافة سندات القبض والصرف
    receiptVouchers.forEach(voucher => {
        if (voucher.status === 'posted') {
            income += voucher.amount;
        }
    });

    paymentVouchers.forEach(voucher => {
        if (voucher.status === 'posted') {
            expenses += voucher.amount;
        }
    });

    const profit = income - expenses;
    const balance = profit; // يمكن تحسينه لحساب الرصيد الفعلي

    return { income, expenses, profit, balance };
}

// الحصول على ملخص الحسابات
function getAccountsSummary() {
    const summary = {};

    // تجميع الحسابات حسب النوع
    function processAccount(account) {
        const type = getAccountTypeText(account.type);
        if (!summary[type]) {
            summary[type] = { count: 0, balance: 0 };
        }
        summary[type].count++;
        summary[type].balance += account.balance || 0;

        // معالجة الحسابات الفرعية
        if (account.children) {
            account.children.forEach(child => processAccount(child));
        }
    }

    // معالجة جميع الحسابات
    Object.values(chartOfAccounts).forEach(account => {
        processAccount(account);
    });

    // تحويل إلى مصفوفة
    return Object.keys(summary).map(type => ({
        type: type,
        count: summary[type].count,
        balance: summary[type].balance
    }));
}

// الحصول على نص نوع الحساب
function getAccountTypeText(type) {
    const types = {
        'asset': 'الأصول',
        'liability': 'الخصوم',
        'equity': 'حقوق الملكية',
        'revenue': 'الإيرادات',
        'expense': 'المصروفات'
    };
    return types[type] || type;
}

// الحصول على نص حالة القيد
function getStatusText(status) {
    const statuses = {
        'draft': 'مسودة',
        'posted': 'مرحل',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

// البحث عن حساب بالمعرف
function findAccountById(accountId) {
    function searchInAccount(account) {
        if (account.id === accountId) {
            return account;
        }
        if (account.children) {
            for (let child of account.children) {
                const found = searchInAccount(child);
                if (found) return found;
            }
        }
        return null;
    }

    for (let account of Object.values(chartOfAccounts)) {
        const found = searchInAccount(account);
        if (found) return found;
    }
    return null;
}



function importAccounts() {
    alert('سيتم إضافة وظيفة استيراد الحسابات قريباً');
}

function exportAccounts() {
    alert('سيتم إضافة وظيفة تصدير دليل الحسابات قريباً');
}

function showAddReceiptVoucherModal() {
    const modal = document.getElementById('add-receipt-voucher-modal');
    if (modal) {
        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('receipt-date');
        if (dateInput) {
            dateInput.value = today;
        }

        // عرض النافذة
        modal.style.display = 'flex';
        modal.classList.add('show');
    }
}

function showAddPaymentVoucherModal() {
    const modal = document.getElementById('add-payment-voucher-modal');
    if (modal) {
        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('payment-date');
        if (dateInput) {
            dateInput.value = today;
        }

        // عرض النافذة
        modal.style.display = 'flex';
        modal.classList.add('show');
    }
}

// تحديث وظيفة تحميل الإحصائيات المالية
function loadFinancialStats() {
    // حساب الإحصائيات من البيانات الموجودة
    const totalRevenue = receiptVouchers.reduce((sum, voucher) => sum + voucher.amount, 0);
    const totalExpenses = paymentVouchers.reduce((sum, voucher) => sum + voucher.amount, 0);
    const netProfit = totalRevenue - totalExpenses;
    const currentBalance = netProfit + 150000; // رصيد افتتاحي افتراضي

    // تحديث القيم في الواجهة
    const revenueElement = document.querySelector('.stat-card.income .stat-value');
    const expensesElement = document.querySelector('.stat-card.expenses .stat-value');
    const profitElement = document.querySelector('.stat-card.profit .stat-value');
    const balanceElement = document.querySelector('.stat-card.balance .stat-value');

    if (revenueElement) revenueElement.textContent = `${totalRevenue.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;
    if (expensesElement) expensesElement.textContent = `${totalExpenses.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;
    if (profitElement) profitElement.textContent = `${netProfit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;
    if (balanceElement) balanceElement.textContent = `${currentBalance.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س`;
}

// تحميل البيانات الأولية
function loadInitialData() {
    // تحميل الإحصائيات المالية
    loadFinancialStats();

    // تحميل قيود اليومية
    loadJournalEntries();

    // تحميل قيود اليومية في التبويب
    loadJournalEntriesTab();

    // تحميل سندات القبض
    loadReceiptVouchers();

    // تحميل سندات الصرف
    loadPaymentVouchers();

    // إعداد مستمعي الأحداث للنوافذ المنبثقة الجديدة
    setupNewModalEventListeners();
}

// إعداد مستمعي الأحداث للنوافذ المنبثقة الجديدة
function setupNewModalEventListeners() {
    // نافذة قيد اليومية
    const journalEntryForm = document.getElementById('add-journal-entry-form');
    if (journalEntryForm) {
        journalEntryForm.addEventListener('submit', handleAddJournalEntry);
    }

    const cancelJournalBtn = document.getElementById('cancel-add-journal-entry');
    if (cancelJournalBtn) {
        cancelJournalBtn.addEventListener('click', function() {
            closeModal('add-journal-entry-modal');
            // إعادة تعيين النموذج
            const form = document.getElementById('add-journal-entry-form');
            if (form) {
                form.reset();
            }
        });
    }

    // نافذة سند القبض
    const receiptVoucherForm = document.getElementById('add-receipt-voucher-form');
    if (receiptVoucherForm) {
        receiptVoucherForm.addEventListener('submit', handleAddReceiptVoucher);
    }

    const cancelReceiptBtn = document.getElementById('cancel-add-receipt-voucher');
    if (cancelReceiptBtn) {
        cancelReceiptBtn.addEventListener('click', function() {
            closeModal('add-receipt-voucher-modal');
            // إعادة تعيين النموذج
            const form = document.getElementById('add-receipt-voucher-form');
            if (form) {
                form.reset();
            }
        });
    }

    // نافذة سند الصرف
    const paymentVoucherForm = document.getElementById('add-payment-voucher-form');
    if (paymentVoucherForm) {
        paymentVoucherForm.addEventListener('submit', handleAddPaymentVoucher);
    }

    const cancelPaymentBtn = document.getElementById('cancel-add-payment-voucher');
    if (cancelPaymentBtn) {
        cancelPaymentBtn.addEventListener('click', function() {
            closeModal('add-payment-voucher-modal');
            // إعادة تعيين النموذج
            const form = document.getElementById('add-payment-voucher-form');
            if (form) {
                form.reset();
            }
        });
    }

    // أزرار إغلاق النوافذ
    document.querySelectorAll('.close-modal').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';

                // إعادة تعيين النموذج إذا كان موجوداً
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                }
            }
        });
    });

    // إغلاق النافذة بالنقر خارجها
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.remove('show');
                this.style.display = 'none';

                // إعادة تعيين النموذج إذا كان موجوداً
                const form = this.querySelector('form');
                if (form) {
                    form.reset();
                }
            }
        });
    });
}

// ملء قوائم الحسابات
function populateAccountOptions(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // مسح الخيارات الموجودة
    select.innerHTML = '<option value="">اختر الحساب</option>';

    // إضافة الحسابات من دليل الحسابات
    const flatAccounts = flattenAccounts(chartOfAccounts);
    flatAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.code} - ${account.name}`;
        select.appendChild(option);
    });
}

// معالج إضافة قيد جديد
function handleAddJournalEntry(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const form = e.target;
    const editId = form.dataset.editId;

    if (editId) {
        // تعديل قيد موجود
        const entryIndex = journalEntries.findIndex(entry => entry.id == editId);
        if (entryIndex > -1) {
            journalEntries[entryIndex] = {
                ...journalEntries[entryIndex],
                date: formData.get('date'),
                description: formData.get('description'),
                debitAccount: formData.get('debitAccount'),
                creditAccount: formData.get('creditAccount'),
                debit: parseFloat(formData.get('amount')),
                credit: parseFloat(formData.get('amount'))
            };

            alert('تم تحديث القيد بنجاح!');
        }
    } else {
        // إضافة قيد جديد
        const newEntry = {
            id: journalEntries.length + 1,
            entryNumber: `JE-2023-${String(journalEntries.length + 1).padStart(3, '0')}`,
            date: formData.get('date'),
            description: formData.get('description'),
            debitAccount: formData.get('debitAccount'),
            creditAccount: formData.get('creditAccount'),
            debit: parseFloat(formData.get('amount')),
            credit: parseFloat(formData.get('amount')),
            status: 'draft'
        };

        journalEntries.push(newEntry);
        alert('تم إضافة القيد بنجاح!');
    }

    // تحديث العرض
    loadJournalEntries();
    loadJournalEntriesTab();
    loadFinancialStats();
    closeModal('add-journal-entry-modal');

    // إعادة تعيين النموذج
    form.reset();
    delete form.dataset.editId;
}

// معالج إضافة سند قبض
function handleAddReceiptVoucher(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const form = e.target;
    const editId = form.dataset.editId;

    if (editId) {
        // تعديل سند موجود
        const voucherIndex = receiptVouchers.findIndex(voucher => voucher.id == editId);
        if (voucherIndex > -1) {
            receiptVouchers[voucherIndex] = {
                ...receiptVouchers[voucherIndex],
                date: formData.get('date'),
                receivedFrom: formData.get('receivedFrom'),
                amount: parseFloat(formData.get('amount')),
                description: formData.get('description')
            };

            alert('تم تحديث سند القبض بنجاح!');
        }
    } else {
        // إضافة سند جديد
        const newVoucher = {
            id: receiptVouchers.length + 1,
            voucherNumber: `RV-2023-${String(receiptVouchers.length + 1).padStart(3, '0')}`,
            date: formData.get('date'),
            receivedFrom: formData.get('receivedFrom'),
            amount: parseFloat(formData.get('amount')),
            description: formData.get('description'),
            status: 'pending'
        };

        receiptVouchers.push(newVoucher);
        alert('تم إضافة سند القبض بنجاح!');
    }

    // تحديث العرض
    loadReceiptVouchers();
    loadReceiptVouchersTab();
    loadFinancialStats();
    closeModal('add-receipt-voucher-modal');

    // إعادة تعيين النموذج
    form.reset();
    delete form.dataset.editId;
}

// معالج إضافة سند صرف
function handleAddPaymentVoucher(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const form = e.target;
    const editId = form.dataset.editId;

    if (editId) {
        // تعديل سند موجود
        const voucherIndex = paymentVouchers.findIndex(voucher => voucher.id == editId);
        if (voucherIndex > -1) {
            paymentVouchers[voucherIndex] = {
                ...paymentVouchers[voucherIndex],
                date: formData.get('date'),
                paidTo: formData.get('paidTo'),
                amount: parseFloat(formData.get('amount')),
                description: formData.get('description')
            };

            alert('تم تحديث سند الصرف بنجاح!');
        }
    } else {
        // إضافة سند جديد
        const newVoucher = {
            id: paymentVouchers.length + 1,
            voucherNumber: `PV-2023-${String(paymentVouchers.length + 1).padStart(3, '0')}`,
            date: formData.get('date'),
            paidTo: formData.get('paidTo'),
            amount: parseFloat(formData.get('amount')),
            description: formData.get('description'),
            status: 'pending'
        };

        paymentVouchers.push(newVoucher);
        alert('تم إضافة سند الصرف بنجاح!');
    }

    // تحديث العرض
    loadPaymentVouchers();
    loadPaymentVouchersTab();
    loadFinancialStats();
    closeModal('add-payment-voucher-modal');

    // إعادة تعيين النموذج
    form.reset();
    delete form.dataset.editId;
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';

        // إعادة تعيين النموذج إذا كان موجوداً
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
            // إزالة معرف التعديل إذا كان موجوداً
            delete form.dataset.editId;

            // إعادة تعيين نص الأزرار
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                if (submitBtn.innerHTML.includes('تحديث القيد')) {
                    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ القيد';
                } else if (submitBtn.innerHTML.includes('تحديث السند') && form.id.includes('receipt')) {
                    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ السند';
                } else if (submitBtn.innerHTML.includes('تحديث السند') && form.id.includes('payment')) {
                    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ السند';
                }
            }
        }
    }
}

// تحميل قيود اليومية في التبويب
function loadJournalEntriesTab() {
    const journalEntriesTable = document.querySelector('#journal-entries-tab .data-table tbody');
    if (!journalEntriesTable) return;

    // مسح المحتوى الموجود
    journalEntriesTable.innerHTML = '';

    // إضافة القيود إلى الجدول
    journalEntries.forEach(entry => {
        const row = document.createElement('tr');

        // الحصول على أسماء الحسابات
        const debitAccountName = getAccountName(entry.debitAccount);
        const creditAccountName = getAccountName(entry.creditAccount);

        row.innerHTML = `
            <td>${entry.entryNumber}</td>
            <td>${formatDate(entry.date)}</td>
            <td>${entry.description}</td>
            <td>${debitAccountName}</td>
            <td>${creditAccountName}</td>
            <td class="amount">${entry.debit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>
                <span class="status-badge ${entry.status}">${getStatusText(entry.status)}</span>
            </td>
            <td class="actions">
                <button class="action-btn view-btn" onclick="viewJournalEntry('${entry.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editJournalEntry('${entry.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteJournalEntry('${entry.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        journalEntriesTable.appendChild(row);
    });

    // إضافة رسالة إذا لم توجد قيود
    if (journalEntries.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="8" class="empty-state">
                <i class="fas fa-journal-whills"></i>
                <p>لا توجد قيود يومية</p>
                <button class="btn primary-btn" onclick="showAddJournalEntryModal()">
                    <i class="fas fa-plus"></i> إضافة قيد جديد
                </button>
            </td>
        `;
        journalEntriesTable.appendChild(emptyRow);
    }
}

// الحصول على اسم الحساب من المعرف
function getAccountName(accountId) {
    const flatAccounts = flattenAccounts(chartOfAccounts);
    const account = flatAccounts.find(acc => acc.id === accountId);
    return account ? `${account.code} - ${account.name}` : 'حساب غير محدد';
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// الحصول على نص الحالة
function getStatusText(status) {
    const statusTexts = {
        'draft': 'مسودة',
        'posted': 'مرحل',
        'cancelled': 'ملغي'
    };
    return statusTexts[status] || status;
}

// عرض تفاصيل القيد
function viewJournalEntry(entryId) {
    const entry = journalEntries.find(e => e.id == entryId);
    if (!entry) return;

    const debitAccountName = getAccountName(entry.debitAccount);
    const creditAccountName = getAccountName(entry.creditAccount);

    const details = `
        <div class="entry-details">
            <h4>تفاصيل القيد: ${entry.entryNumber}</h4>
            <div class="detail-row">
                <strong>التاريخ:</strong> ${formatDate(entry.date)}
            </div>
            <div class="detail-row">
                <strong>البيان:</strong> ${entry.description}
            </div>
            <div class="detail-row">
                <strong>الحساب المدين:</strong> ${debitAccountName}
            </div>
            <div class="detail-row">
                <strong>الحساب الدائن:</strong> ${creditAccountName}
            </div>
            <div class="detail-row">
                <strong>المبلغ:</strong> ${entry.debit.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س
            </div>
            <div class="detail-row">
                <strong>الحالة:</strong> <span class="status-badge ${entry.status}">${getStatusText(entry.status)}</span>
            </div>
        </div>
    `;

    // عرض التفاصيل في نافذة منبثقة
    showDetailsModal('تفاصيل القيد', details);
}

// تعديل القيد
function editJournalEntry(entryId) {
    const entry = journalEntries.find(e => e.id == entryId);
    if (!entry) return;

    // ملء النموذج بالبيانات الموجودة
    document.getElementById('entry-date').value = entry.date;
    document.getElementById('entry-description').value = entry.description;
    document.getElementById('entry-amount').value = entry.debit;

    // تحديث قوائم الحسابات
    populateAccountOptions('debit-account');
    populateAccountOptions('credit-account');

    // تعيين الحسابات المحددة
    setTimeout(() => {
        document.getElementById('debit-account').value = entry.debitAccount;
        document.getElementById('credit-account').value = entry.creditAccount;
    }, 100);

    // تغيير النموذج لوضع التعديل
    const form = document.getElementById('add-journal-entry-form');
    form.dataset.editId = entryId;

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث القيد';

    // عرض النافذة
    const modal = document.getElementById('add-journal-entry-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');
}

// حذف القيد
function deleteJournalEntry(entryId) {
    const entry = journalEntries.find(e => e.id == entryId);
    if (!entry) return;

    if (confirm(`هل أنت متأكد من حذف القيد: ${entry.entryNumber}؟`)) {
        const index = journalEntries.findIndex(e => e.id == entryId);
        if (index > -1) {
            journalEntries.splice(index, 1);
            loadJournalEntriesTab();
            loadFinancialStats(); // تحديث الإحصائيات
            alert('تم حذف القيد بنجاح!');
        }
    }
}

// عرض نافذة التفاصيل
function showDetailsModal(title, content) {
    // إنشاء نافذة منبثقة للتفاصيل
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.style.display = 'flex';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-info-circle"></i> ${title}</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                <button class="btn secondary-btn close-details-modal">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إضافة مستمعي الأحداث
    modal.querySelector('.close-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.querySelector('.close-details-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// تحميل سندات القبض في التبويب
function loadReceiptVouchersTab() {
    const receiptVouchersTable = document.querySelector('#receipt-voucher-tab .data-table tbody');
    if (!receiptVouchersTable) return;

    // مسح المحتوى الموجود
    receiptVouchersTable.innerHTML = '';

    // إضافة السندات إلى الجدول
    receiptVouchers.forEach(voucher => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${voucher.voucherNumber}</td>
            <td>${formatDate(voucher.date)}</td>
            <td>${voucher.receivedFrom}</td>
            <td class="amount">${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>${voucher.description}</td>
            <td>
                <span class="status-badge ${voucher.status}">${getStatusText(voucher.status)}</span>
            </td>
            <td class="actions">
                <button class="action-btn view-btn" onclick="viewReceiptVoucher('${voucher.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editReceiptVoucher('${voucher.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteReceiptVoucher('${voucher.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        receiptVouchersTable.appendChild(row);
    });

    // إضافة رسالة إذا لم توجد سندات
    if (receiptVouchers.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="7" class="empty-state">
                <i class="fas fa-receipt"></i>
                <p>لا توجد سندات قبض</p>
                <button class="btn primary-btn" onclick="showAddReceiptVoucherModal()">
                    <i class="fas fa-plus"></i> إضافة سند قبض جديد
                </button>
            </td>
        `;
        receiptVouchersTable.appendChild(emptyRow);
    }
}

// تحميل سندات الصرف في التبويب
function loadPaymentVouchersTab() {
    const paymentVouchersTable = document.querySelector('#payment-voucher-tab .data-table tbody');
    if (!paymentVouchersTable) return;

    // مسح المحتوى الموجود
    paymentVouchersTable.innerHTML = '';

    // إضافة السندات إلى الجدول
    paymentVouchers.forEach(voucher => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${voucher.voucherNumber}</td>
            <td>${formatDate(voucher.date)}</td>
            <td>${voucher.paidTo}</td>
            <td class="amount">${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
            <td>${voucher.description}</td>
            <td>
                <span class="status-badge ${voucher.status}">${getStatusText(voucher.status)}</span>
            </td>
            <td class="actions">
                <button class="action-btn view-btn" onclick="viewPaymentVoucher('${voucher.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" onclick="editPaymentVoucher('${voucher.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deletePaymentVoucher('${voucher.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        paymentVouchersTable.appendChild(row);
    });

    // إضافة رسالة إذا لم توجد سندات
    if (paymentVouchers.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="7" class="empty-state">
                <i class="fas fa-money-bill-wave"></i>
                <p>لا توجد سندات صرف</p>
                <button class="btn primary-btn" onclick="showAddPaymentVoucherModal()">
                    <i class="fas fa-plus"></i> إضافة سند صرف جديد
                </button>
            </td>
        `;
        paymentVouchersTable.appendChild(emptyRow);
    }
}

// وظائف سندات القبض
function viewReceiptVoucher(voucherId) {
    const voucher = receiptVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    const details = `
        <div class="entry-details">
            <h4>تفاصيل سند القبض: ${voucher.voucherNumber}</h4>
            <div class="detail-row">
                <strong>التاريخ:</strong> ${formatDate(voucher.date)}
            </div>
            <div class="detail-row">
                <strong>المستلم من:</strong> ${voucher.receivedFrom}
            </div>
            <div class="detail-row">
                <strong>المبلغ:</strong> ${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س
            </div>
            <div class="detail-row">
                <strong>البيان:</strong> ${voucher.description}
            </div>
            <div class="detail-row">
                <strong>الحالة:</strong> <span class="status-badge ${voucher.status}">${getStatusText(voucher.status)}</span>
            </div>
        </div>
    `;

    showDetailsModal('تفاصيل سند القبض', details);
}

function editReceiptVoucher(voucherId) {
    const voucher = receiptVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    // ملء النموذج بالبيانات الموجودة
    document.getElementById('receipt-date').value = voucher.date;
    document.getElementById('received-from').value = voucher.receivedFrom;
    document.getElementById('receipt-amount').value = voucher.amount;
    document.getElementById('receipt-description').value = voucher.description;

    // تغيير النموذج لوضع التعديل
    const form = document.getElementById('add-receipt-voucher-form');
    form.dataset.editId = voucherId;

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث السند';

    // عرض النافذة
    const modal = document.getElementById('add-receipt-voucher-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');
}

function deleteReceiptVoucher(voucherId) {
    const voucher = receiptVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    if (confirm(`هل أنت متأكد من حذف سند القبض: ${voucher.voucherNumber}؟`)) {
        const index = receiptVouchers.findIndex(v => v.id == voucherId);
        if (index > -1) {
            receiptVouchers.splice(index, 1);
            loadReceiptVouchersTab();
            loadFinancialStats();
            alert('تم حذف سند القبض بنجاح!');
        }
    }
}

// وظائف سندات الصرف
function viewPaymentVoucher(voucherId) {
    const voucher = paymentVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    const details = `
        <div class="entry-details">
            <h4>تفاصيل سند الصرف: ${voucher.voucherNumber}</h4>
            <div class="detail-row">
                <strong>التاريخ:</strong> ${formatDate(voucher.date)}
            </div>
            <div class="detail-row">
                <strong>المدفوع إلى:</strong> ${voucher.paidTo}
            </div>
            <div class="detail-row">
                <strong>المبلغ:</strong> ${voucher.amount.toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س
            </div>
            <div class="detail-row">
                <strong>البيان:</strong> ${voucher.description}
            </div>
            <div class="detail-row">
                <strong>الحالة:</strong> <span class="status-badge ${voucher.status}">${getStatusText(voucher.status)}</span>
            </div>
        </div>
    `;

    showDetailsModal('تفاصيل سند الصرف', details);
}

function editPaymentVoucher(voucherId) {
    const voucher = paymentVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    // ملء النموذج بالبيانات الموجودة
    document.getElementById('payment-date').value = voucher.date;
    document.getElementById('paid-to').value = voucher.paidTo;
    document.getElementById('payment-amount').value = voucher.amount;
    document.getElementById('payment-description').value = voucher.description;

    // تغيير النموذج لوضع التعديل
    const form = document.getElementById('add-payment-voucher-form');
    form.dataset.editId = voucherId;

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث السند';

    // عرض النافذة
    const modal = document.getElementById('add-payment-voucher-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');
}

function deletePaymentVoucher(voucherId) {
    const voucher = paymentVouchers.find(v => v.id == voucherId);
    if (!voucher) return;

    if (confirm(`هل أنت متأكد من حذف سند الصرف: ${voucher.voucherNumber}؟`)) {
        const index = paymentVouchers.findIndex(v => v.id == voucherId);
        if (index > -1) {
            paymentVouchers.splice(index, 1);
            loadPaymentVouchersTab();
            loadFinancialStats();
            alert('تم حذف سند الصرف بنجاح!');
        }
    }
}

// نظام التنقل للمحاسبة باستخدام النظام الموحد
let accountingPagination;

function initializeAccountingPagination(transactions) {
    accountingPagination = new UnifiedPagination({
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: transactions.length,
        containerId: 'pagination',
        data: transactions,
        displayFunction: displayAccountingPage,
        onPageChange: function(page) {
            console.log('تم الانتقال إلى صفحة المحاسبة:', page);
        }
    });
}

function displayAccountingPage(pageTransactions, currentPage) {
    console.log('عرض المحاسبة - الصفحة:', currentPage, 'المعاملات:', pageTransactions.length);

    const tableBody = document.getElementById('transactions-table-body');
    if (!tableBody) {
        console.error('جدول المعاملات غير موجود');
        return;
    }

    if (pageTransactions.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-calculator" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    لا توجد معاملات مالية لعرضها
                </td>
            </tr>
        `;
        return;
    }

    let tableHTML = '';
    pageTransactions.forEach(transaction => {
        const typeClass = transaction.type === 'income' ? 'type-income' : 'type-expense';
        const typeText = transaction.type === 'income' ? 'إيراد' : 'مصروف';
        const amountClass = transaction.type === 'income' ? 'amount-income' : 'amount-expense';

        tableHTML += `
            <tr>
                <td>${transaction.date}</td>
                <td>${transaction.reference || transaction.id}</td>
                <td>${transaction.description}</td>
                <td>${transaction.category}</td>
                <td>
                    <span class="transaction-type ${typeClass}">
                        ${typeText}
                    </span>
                </td>
                <td class="transaction-amount ${amountClass}">
                    ${formatCurrency(transaction.amount)}
                </td>
                <td>${transaction.account || 'النقدية'}</td>
                <td>
                    <div class="transaction-actions">
                        <button class="action-btn view-btn" data-id="${transaction.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-btn" data-id="${transaction.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${transaction.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = tableHTML;

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addTransactionActionListeners();
}

// تحديث دالة displayTransactions لتستخدم النظام الجديد
function displayTransactionsWithPagination(transactions) {
    // إضافة المزيد من البيانات للاختبار
    const extendedTransactions = [
        ...transactions,
        { id: 11, date: '2023-07-20', type: 'income', category: 'المبيعات', description: 'مبيعات منتصف الشهر', amount: 2800, reference: 'INV-2023-004' },
        { id: 12, date: '2023-07-25', type: 'expense', category: 'المشتريات', description: 'شراء مواد خام', amount: 1500, reference: 'PUR-2023-07' },
        { id: 13, date: '2023-08-01', type: 'expense', category: 'الإيجار', description: 'إيجار المكتب', amount: 1200, reference: 'RENT-2023-08' },
        { id: 14, date: '2023-08-05', type: 'income', category: 'الخدمات', description: 'خدمات تقنية', amount: 3200, reference: 'SRV-2023-020' },
        { id: 15, date: '2023-08-10', type: 'expense', category: 'الرواتب', description: 'رواتب الموظفين', amount: 3500, reference: 'SAL-2023-08' },
        { id: 16, date: '2023-08-15', type: 'income', category: 'المبيعات', description: 'مبيعات منتصف أغسطس', amount: 4100, reference: 'INV-2023-005' },
        { id: 17, date: '2023-08-20', type: 'expense', category: 'التسويق', description: 'إعلانات رقمية', amount: 900, reference: 'MKT-2023-08' },
        { id: 18, date: '2023-08-25', type: 'income', category: 'الاستثمارات', description: 'عوائد استثمارية', amount: 1800, reference: 'INV-2023-08' }
    ];

    initializeAccountingPagination(extendedTransactions);
}

// دالة لتحديث روابط التنقل
function updateNavigationLinks() {
    const navLinks = document.querySelectorAll('.main-nav a');

    navLinks.forEach(link => {
        const href = link.textContent.trim();

        switch(href) {
            case 'الرئيسية':
                link.href = 'index.html';
                break;
            case 'المبيعات':
                link.href = 'sales.html';
                break;
            case 'المشتريات':
                link.href = 'purchases.html';
                break;
            case 'العملاء':
                link.href = 'customers.html';
                break;
            case 'المنتجات':
            case 'المخزون':
                link.href = 'products.html';
                break;
            case 'التقارير':
                link.href = 'reports.html';
                break;
            case 'الحسابات':
                link.href = 'accounting.html';
                break;
            default:
                // لا نغير الرابط إذا لم نجد تطابق
                break;
        }
    });
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    updateNavigationLinks();
});