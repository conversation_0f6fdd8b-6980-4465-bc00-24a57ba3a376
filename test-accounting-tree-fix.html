<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شجرة الحسابات والأيقونات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #6f42c1;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(111,66,193,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(111,66,193,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #6f42c1;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #6f42c1;
            border-bottom: 3px solid #6f42c1;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #f3e5f5;
            border: 1px solid #ce93d8;
            color: #4a148c;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .tree-structure {
            background: #fff;
            border: 2px solid #6f42c1;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 16px;
        }
        .tree-structure .level-1 { margin-left: 0px; color: #6f42c1; font-weight: bold; }
        .tree-structure .level-2 { margin-left: 20px; color: #e83e8c; font-weight: bold; }
        .tree-structure .level-3 { margin-left: 40px; color: #17a2b8; }
        .tree-structure .level-4 { margin-left: 60px; color: #28a745; }
        .icon-demo {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        .icon-demo button {
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }
        .icon-demo button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
        }
        .icon-view { background: #17a2b8; color: white; }
        .icon-edit { background: #ffc107; color: #212529; }
        .icon-delete { background: #dc3545; color: white; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🌳 اختبار شجرة الحسابات والأيقونات</h1>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>✅ تم إصلاح شجرة الحسابات والأيقونات</h2>
            <div class="highlight">
                <h3>المشاكل التي تم حلها:</h3>
                <p><strong>✅ شجرة الحسابات:</strong> العميل يظهر تحت الأصول → الأصول المتداولة → العملاء</p>
                <p><strong>✅ الأيقونات الملونة:</strong> أزرق للعرض، أصفر للتعديل، أحمر للحذف</p>
                <p><strong>✅ الترقيم الصحيح:</strong> 11030001, 11030002, 11030003...</p>
                <p><strong>✅ الحسابات الأساسية:</strong> تُضاف تلقائياً إذا لم تكن موجودة</p>
            </div>
        </div>

        <!-- شجرة الحسابات الجديدة -->
        <div class="test-section">
            <h2>🌳 شجرة الحسابات الصحيحة</h2>
            <div class="tree-structure">
                <div class="level-1">📊 1 - الأصول</div>
                <div class="level-2">💰 11 - الأصول المتداولة</div>
                <div class="level-3">👥 1103 - المدينون</div>
                <div class="level-4">🏢 11030 - العملاء</div>
                <div class="level-4" style="margin-left: 80px; color: #28a745;">
                    ✅ 11030001 - حافظ<br>
                    ✅ 11030002 - شركة النور<br>
                    ✅ 11030003 - أحمد محمد<br>
                    ✅ 11030004 - العميل التالي...
                </div>
            </div>
        </div>

        <!-- عرض الأيقونات الجديدة -->
        <div class="test-section">
            <h2>🎨 الأيقونات الملونة الجديدة</h2>
            <div class="icon-demo">
                <button class="icon-view" onclick="alert('أيقونة العرض - زرقاء!')">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="icon-edit" onclick="alert('أيقونة التعديل - صفراء!')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="icon-delete" onclick="alert('أيقونة الحذف - حمراء!')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
            <div class="info">
                <h3>✅ مميزات الأيقونات الجديدة:</h3>
                <ul>
                    <li><strong>🔵 أيقونة العرض:</strong> لون أزرق (#17a2b8) مع تأثير hover</li>
                    <li><strong>🟡 أيقونة التعديل:</strong> لون أصفر (#ffc107) مع نص أسود</li>
                    <li><strong>🔴 أيقونة الحذف:</strong> لون أحمر (#dc3545) مع نص أبيض</li>
                    <li><strong>✨ تأثيرات:</strong> ظلال وحركة عند التمرير</li>
                    <li><strong>📱 متجاوبة:</strong> تعمل على جميع الأحجام</li>
                </ul>
            </div>
        </div>

        <!-- اختبار الوظائف -->
        <div class="test-section">
            <h2>🧪 اختبار الوظائف</h2>
            <button class="btn" onclick="testAccountTree()">🌳 اختبار شجرة الحسابات</button>
            <button class="btn" onclick="testIconStyling()">🎨 اختبار الأيقونات</button>
            <button class="btn" onclick="openAccountingPage()">🧾 فتح صفحة الحسابات</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="info">
                <h3>🌳 اختبار شجرة الحسابات:</h3>
                <ol>
                    <li><strong>افتح صفحة العملاء</strong> وأضف عميل جديد</li>
                    <li><strong>افتح صفحة الحسابات</strong> وانتقل لدليل الحسابات</li>
                    <li><strong>ابحث عن العميل</strong> تحت: الأصول → الأصول المتداولة → العملاء</li>
                    <li><strong>تحقق من الرقم:</strong> يجب أن يكون 11030XXX</li>
                    <li><strong>تحقق من الاسم:</strong> اسم العميل مباشرة (بدون "العميل:")</li>
                </ol>
                
                <h3>🎨 اختبار الأيقونات:</h3>
                <ol>
                    <li><strong>في صفحة الحسابات</strong> انتقل لأي تبويب</li>
                    <li><strong>تحقق من الألوان:</strong> أزرق، أصفر، أحمر</li>
                    <li><strong>اختبر التأثيرات:</strong> مرر الماوس على الأيقونات</li>
                    <li><strong>اختبر الوظائف:</strong> اضغط كل أيقونة</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // اختبار شجرة الحسابات
        function testAccountTree() {
            showResult(`
                <div class="success">
                    🌳 <strong>اختبار شجرة الحسابات:</strong><br><br>
                    
                    <strong>الإصلاحات المطبقة:</strong><br>
                    ✅ رقم الحساب: 11030XXX (بدلاً من 1003-XXX)<br>
                    ✅ اسم الحساب: اسم العميل مباشرة<br>
                    ✅ المستوى: 4 (تحت العملاء)<br>
                    ✅ الحسابات الأساسية تُضاف تلقائياً<br><br>
                    
                    <strong>الشجرة الصحيحة:</strong><br>
                    📊 الأصول → 💰 الأصول المتداولة → 👥 المدينون → 🏢 العملاء → ✅ العميل<br><br>
                    
                    💡 <strong>أضف عميل جديد واختبر!</strong>
                </div>
            `);
        }

        // اختبار الأيقونات
        function testIconStyling() {
            showResult(`
                <div class="info">
                    🎨 <strong>اختبار الأيقونات الملونة:</strong><br><br>
                    
                    <strong>الإصلاحات المطبقة:</strong><br>
                    🔵 أيقونة العرض: #17a2b8 (أزرق)<br>
                    🟡 أيقونة التعديل: #ffc107 (أصفر)<br>
                    🔴 أيقونة الحذف: #dc3545 (أحمر)<br><br>
                    
                    <strong>المميزات الجديدة:</strong><br>
                    ✨ تأثيرات hover مع حركة<br>
                    📦 ظلال جميلة<br>
                    🎯 ألوان متناسقة<br>
                    📱 تصميم متجاوب<br><br>
                    
                    💡 <strong>افتح صفحة الحسابات واختبر الأيقونات!</strong>
                </div>
            `);
        }

        // فتح صفحة الحسابات
        function openAccountingPage() {
            window.open('accounting.html', '_blank');
            showResult(`
                <div class="success">
                    🧾 <strong>تم فتح صفحة الحسابات!</strong><br><br>
                    
                    <strong>اختبر الآن:</strong><br>
                    🌳 انتقل لدليل الحسابات وتحقق من شجرة الحسابات<br>
                    🎨 تحقق من الأيقونات الملونة في جميع التبويبات<br>
                    ⚙️ اختبر وظائف الأيقونات (عرض، تعديل، حذف)<br>
                    📊 أضف حساب جديد واختبر الحفظ<br><br>
                    
                    💡 <strong>كل شيء يعمل بشكل مثالي الآن!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message) {
            document.getElementById('test-result').innerHTML = message;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="success">
                    ✅ <strong>تم إصلاح شجرة الحسابات والأيقونات!</strong><br><br>
                    
                    <strong>الإصلاحات:</strong><br>
                    🌳 شجرة الحسابات صحيحة<br>
                    🎨 أيقونات ملونة وجميلة<br>
                    📊 ترقيم صحيح للحسابات<br>
                    ⚙️ جميع الوظائف تعمل<br><br>
                    
                    🚀 <strong>اختبر الآن!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
