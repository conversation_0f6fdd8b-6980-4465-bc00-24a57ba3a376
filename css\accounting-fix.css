/**
 * ملف أنماط إصلاح مشكلة وظائف إدارة الحسابات
 * هذا الملف يحتوي على الأنماط اللازمة لإصلاح مشكلة عرض وظائف إدارة الحسابات
 */

/* أنماط عامة للتبويبات */
.accounting-nav .nav-tab {
    cursor: pointer;
    transition: all 0.3s ease;
}

.accounting-nav .nav-tab:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.accounting-nav .nav-tab.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* أنماط محتوى التبويبات */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* أنماط شجرة الحسابات */
.accounts-tree {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 20px;
}

.account-item {
    margin-bottom: 5px;
}

.account-header {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

.account-header:hover {
    background-color: #e9ecef;
}

.expand-btn, .expand-spacer {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 5px;
}

.account-info {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.account-code {
    font-weight: bold;
    margin-left: 10px;
    min-width: 60px;
}

.account-name {
    flex-grow: 1;
}

.account-type {
    color: #6c757d;
    font-size: 0.9em;
    margin-right: 10px;
}

.account-actions {
    display: flex;
    gap: 5px;
}

.account-children {
    display: none;
    padding-right: 20px;
    margin-top: 5px;
}

.account-children.expanded {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* أنماط الجداول */
.journal-entries-table-container,
.receipt-vouchers-table-container,
.payment-vouchers-table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.journal-entries-table,
.receipt-vouchers-table,
.payment-vouchers-table {
    width: 100%;
    border-collapse: collapse;
}

.journal-entries-table th,
.receipt-vouchers-table th,
.payment-vouchers-table th {
    background-color: #f8f9fa;
    padding: 12px;
    text-align: right;
    border-bottom: 2px solid #dee2e6;
}

.journal-entries-table td,
.receipt-vouchers-table td,
.payment-vouchers-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
}

.journal-entries-table tr:hover,
.receipt-vouchers-table tr:hover,
.payment-vouchers-table tr:hover {
    background-color: #f8f9fa;
}

/* أنماط أدوات البحث والتصفية */
.journal-entries-search-filter,
.receipt-vouchers-search-filter,
.payment-vouchers-search-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.search-box {
    position: relative;
    flex-grow: 1;
}

.search-box i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.date-range {
    display: flex;
    gap: 10px;
    align-items: center;
}

.date-input {
    display: flex;
    flex-direction: column;
}

.date-input label {
    font-size: 0.9em;
    margin-bottom: 5px;
}

.date-input input {
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.apply-date-btn {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    align-self: flex-end;
}

.apply-date-btn:hover {
    background-color: #0069d9;
}

/* أنماط حالة القيود والسندات */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
}

.status-posted {
    background-color: #28a745;
    color: white;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

/* أنماط أزرار الإجراءات */
.entry-actions,
.voucher-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn {
    background-color: #17a2b8;
    color: white;
}

.edit-btn {
    background-color: #ffc107;
    color: #212529;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.print-btn {
    background-color: #6c757d;
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
}

/* أنماط رسالة تأكيد الإصلاح */
.fix-confirmation-message {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #2ecc71;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 9999;
    animation: slideIn 0.5s ease-out;
}

.message-content {
    display: flex;
    align-items: center;
}

.message-content i {
    font-size: 24px;
    margin-left: 10px;
}

.message-content p {
    margin: 0;
    font-size: 16px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* أنماط المعاملات */
.transaction-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
}

.type-income {
    background-color: #28a745;
    color: white;
}

.type-expense {
    background-color: #dc3545;
    color: white;
}

.transaction-amount {
    font-weight: bold;
}

.amount-income {
    color: #28a745;
}

.amount-expense {
    color: #dc3545;
}