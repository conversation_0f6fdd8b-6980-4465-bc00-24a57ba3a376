<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط المبيعات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .btn.warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            box-shadow: 0 2px 5px rgba(255,193,7,0.3);
        }
        .btn.danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            box-shadow: 0 2px 5px rgba(220,53,69,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border-color: #28a745;
        }
        .status-number {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #28a745;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار ربط المبيعات مع المنتجات والعملاء</h1>

        <!-- حالة البيانات الحالية -->
        <div class="test-section">
            <h2>📊 حالة البيانات الحالية</h2>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number" id="products-count">0</div>
                    <div>المنتجات المحفوظة</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="customers-count">0</div>
                    <div>العملاء المحفوظين</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="invoices-count">0</div>
                    <div>الفواتير المحفوظة</div>
                </div>
            </div>
            <button class="btn primary" onclick="refreshStatus()">تحديث الحالة</button>
            <div id="status-result"></div>
        </div>

        <!-- إعداد البيانات التجريبية -->
        <div class="test-section">
            <h2>🛠️ إعداد البيانات التجريبية</h2>
            <button class="btn" onclick="setupTestData()">إعداد بيانات تجريبية كاملة</button>
            <button class="btn primary" onclick="addTestProducts()">إضافة منتجات فقط</button>
            <button class="btn primary" onclick="addTestCustomers()">إضافة عملاء فقط</button>
            <div id="setup-result"></div>
        </div>

        <!-- اختبار الربط -->
        <div class="test-section">
            <h2>🧪 اختبار الربط</h2>
            <button class="btn" onclick="testSalesLinking()">اختبار ربط المبيعات</button>
            <button class="btn primary" onclick="openSalesPage()">فتح صفحة المبيعات</button>
            <div id="linking-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار اليدوي</h2>
            <div class="step-list">
                <h3>للتأكد من عمل الربط:</h3>
                <ol>
                    <li><strong>اضغط "إعداد بيانات تجريبية كاملة"</strong> - لإضافة منتجات وعملاء</li>
                    <li><strong>اضغط "فتح صفحة المبيعات"</strong> - لفتح صفحة المبيعات في تبويب جديد</li>
                    <li><strong>في صفحة المبيعات، اضغط "فاتورة جديدة"</strong></li>
                    <li><strong>تحقق من قائمة العملاء</strong> - يجب أن تجد العملاء المضافين</li>
                    <li><strong>تحقق من قائمة المنتجات</strong> - يجب أن تجد المنتجات المضافة</li>
                    <li><strong>اختر عميل ومنتج وأنشئ فاتورة</strong> - للتأكد من عمل النظام</li>
                </ol>
            </div>
        </div>

        <!-- إدارة البيانات -->
        <div class="test-section">
            <h2>🗂️ إدارة البيانات</h2>
            <button class="btn warning" onclick="exportAllData()">تصدير جميع البيانات</button>
            <button class="btn danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="management-result"></div>
        </div>
    </div>

    <script>
        // تحديث حالة البيانات
        function refreshStatus() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            document.getElementById('products-count').textContent = products.length;
            document.getElementById('customers-count').textContent = customers.length;
            document.getElementById('invoices-count').textContent = invoices.length;
            
            let statusMsg = '<div class="info">📊 تم تحديث الحالة:</div>';
            statusMsg += `<p>📦 المنتجات: ${products.length}</p>`;
            statusMsg += `<p>👥 العملاء: ${customers.length}</p>`;
            statusMsg += `<p>🧾 الفواتير: ${invoices.length}</p>`;
            
            if (products.length > 0 && customers.length > 0) {
                statusMsg += '<div class="success">✅ البيانات جاهزة للربط!</div>';
            } else {
                statusMsg += '<div class="error">❌ تحتاج لإضافة منتجات وعملاء</div>';
            }
            
            document.getElementById('status-result').innerHTML = statusMsg;
        }

        // إعداد بيانات تجريبية كاملة
        function setupTestData() {
            addTestProducts();
            setTimeout(() => {
                addTestCustomers();
                setTimeout(() => {
                    refreshStatus();
                    showResult('setup-result', '✅ تم إعداد البيانات التجريبية بنجاح!<br>📦 5 منتجات + 👥 4 عملاء', 'success');
                }, 100);
            }, 100);
        }

        // إضافة منتجات تجريبية
        function addTestProducts() {
            const testProducts = [
                { name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000, quantity: 10, minStock: 2 },
                { name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2 },
                { name: 'هاتف آيفون 15 Pro', category: 'phones', code: 'IPHONE-15P', price: 5200, cost: 4500, quantity: 5, minStock: 1 },
                { name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5 },
                { name: 'شاشة سامسونج 32 بوصة', category: 'electronics', code: 'SAM-32', price: 1800, cost: 1400, quantity: 6, minStock: 2 }
            ];

            let products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            testProducts.forEach(product => {
                const newProduct = {
                    id: Date.now() + Math.random(),
                    ...product,
                    description: `منتج تجريبي: ${product.name}`
                };
                products.push(newProduct);
            });

            localStorage.setItem('monjizProducts', JSON.stringify(products));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            console.log('تم إضافة', testProducts.length, 'منتج تجريبي');
        }

        // إضافة عملاء تجريبيين
        function addTestCustomers() {
            const testCustomers = [
                { name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { name: 'شركة الأنوار للتجارة', type: 'company', phone: '+966502345678', email: '<EMAIL>' },
                { name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' },
                { name: 'مؤسسة النجاح التجارية', type: 'company', phone: '+966504567890', email: '<EMAIL>' }
            ];

            let customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            testCustomers.forEach(customer => {
                const newCustomer = {
                    id: Date.now() + Math.random(),
                    ...customer,
                    address: 'الرياض، المملكة العربية السعودية',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                };
                customers.push(newCustomer);
            });

            localStorage.setItem('monjizCustomers', JSON.stringify(customers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            console.log('تم إضافة', testCustomers.length, 'عميل تجريبي');
        }

        // اختبار ربط المبيعات
        function testSalesLinking() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            let result = '<div class="info">🔗 نتائج اختبار الربط:</div>';
            
            // فحص وجود البيانات المطلوبة
            const hasProjector = products.some(p => p.name.includes('جهاز عرض'));
            const hasTopaz = customers.some(c => c.name.includes('توباز'));
            const hasLaptop = products.some(p => p.name.includes('لابتوب'));
            const hasCompany = customers.some(c => c.type === 'company');
            
            result += `<p>📦 إجمالي المنتجات: ${products.length}</p>`;
            result += `<p>👥 إجمالي العملاء: ${customers.length}</p>`;
            result += `<p>🎯 جهاز عرض محمول: ${hasProjector ? '✅ موجود' : '❌ غير موجود'}</p>`;
            result += `<p>🏢 مطعم توباز: ${hasTopaz ? '✅ موجود' : '❌ غير موجود'}</p>`;
            result += `<p>💻 لابتوب: ${hasLaptop ? '✅ موجود' : '❌ غير موجود'}</p>`;
            result += `<p>🏢 شركات: ${hasCompany ? '✅ موجود' : '❌ غير موجود'}</p>`;
            
            if (products.length >= 3 && customers.length >= 3) {
                result += '<div class="success">🎉 ممتاز! البيانات كافية لاختبار الربط</div>';
                result += '<p>💡 افتح صفحة المبيعات الآن وأنشئ فاتورة جديدة</p>';
                result += '<p>🔍 ستجد جميع المنتجات والعملاء في القوائم المنسدلة</p>';
            } else {
                result += '<div class="error">❌ البيانات غير كافية للاختبار</div>';
                result += '<p>💡 اضغط "إعداد بيانات تجريبية كاملة" أولاً</p>';
            }
            
            document.getElementById('linking-result').innerHTML = result;
        }

        // فتح صفحة المبيعات
        function openSalesPage() {
            window.open('sales.html', '_blank');
            showResult('linking-result', '🚀 تم فتح صفحة المبيعات في تبويب جديد<br>💡 اضغط "فاتورة جديدة" لاختبار الربط', 'info');
        }

        // تصدير جميع البيانات
        function exportAllData() {
            const data = {
                products: JSON.parse(localStorage.getItem('monjizProducts')) || [],
                customers: JSON.parse(localStorage.getItem('monjizCustomers')) || [],
                invoices: JSON.parse(localStorage.getItem('monjizInvoices')) || [],
                suppliers: JSON.parse(localStorage.getItem('monjizSuppliers')) || [],
                exportDate: new Date().toISOString(),
                version: 'sales-linking-test-v1.0'
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'monjiz-sales-linking-backup.json';
            a.click();
            
            showResult('management-result', '📁 تم تصدير جميع البيانات بنجاح', 'success');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\nسيتم حذف المنتجات والعملاء والفواتير')) {
                localStorage.clear();
                refreshStatus();
                showResult('management-result', '🗑️ تم مسح جميع البيانات', 'info');
                
                // مسح جميع النتائج
                setTimeout(() => {
                    document.getElementById('setup-result').innerHTML = '';
                    document.getElementById('linking-result').innerHTML = '';
                }, 1000);
            }
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل الحالة عند فتح الصفحة
        window.addEventListener('load', function() {
            refreshStatus();
            testSalesLinking();
        });
    </script>
</body>
</html>
