<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحسابات - نظام إدارة الأعمال</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accounting.css">
    <link rel="stylesheet" href="css/accounting-fix.css">
    <link rel="stylesheet" href="css/chart-of-accounts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* تنسيق الأيقونات حسب النمط المتبع في البرنامج */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
        }

        .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 10px !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        .action-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        /* تحسين الأيقونات */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
        }

        .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 10px !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        .action-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        /* CSS إضافي قوي لضمان تطبيق التنسيق */
        table .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 10px !important;
        }

        table .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        table .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        table .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        table .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        table .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
        }

        /* تنسيق خاص لجدول الحسابات */
        #accounts-table-body .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 10px !important;
        }

        #accounts-table-body .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        /* تنسيق نافذة إضافة حساب جديد */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 30px;
        }

        #add-account-modal.active {
            display: flex !important;
        }

        /* تنسيق النظام الموحد */
        .unified-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 20px;
        }

        .unified-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-content h4 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-content p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .unified-tree-container {
            padding: 30px;
        }

        .tree-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .tree-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .unified-tree {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .unified-account-item {
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .unified-account-item:hover {
            background: #f8f9fa;
            border-color: #e9ecef;
            transform: translateX(5px);
        }

        .unified-account-content {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            gap: 12px;
        }

        .unified-account-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
        }

        .unified-account-icon.assets { background: linear-gradient(135deg, #28a745, #20c997); }
        .unified-account-icon.liabilities { background: linear-gradient(135deg, #dc3545, #fd7e14); }
        .unified-account-icon.equity { background: linear-gradient(135deg, #6f42c1, #e83e8c); }
        .unified-account-icon.revenue { background: linear-gradient(135deg, #17a2b8, #20c997); }
        .unified-account-icon.expenses { background: linear-gradient(135deg, #ffc107, #fd7e14); }

        .unified-account-details {
            flex: 1;
            min-width: 0;
        }

        .unified-account-name {
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 4px 0;
            font-size: 15px;
        }

        .unified-account-code {
            font-size: 12px;
            color: #7f8c8d;
            font-family: 'Courier New', monospace;
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
        }

        .unified-account-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .unified-account-item:hover .unified-account-actions {
            opacity: 1;
        }

        .unified-action-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .unified-action-btn.edit { background: #28a745; }
        .unified-action-btn.delete { background: #dc3545; }
        .unified-action-btn.view { background: #17a2b8; }

        .unified-action-btn:hover {
            transform: scale(1.1);
        }

        /* تنسيق التبويبات */
        .nav-tab {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* تنسيق نافذة إضافة الحساب */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row:last-child {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #28a745;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .primary-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .secondary-btn {
            background: #6c757d;
            color: white;
        }

        .secondary-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="products.html"><i class="fas fa-boxes"></i> المنتجات</a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html" class="active"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- صفحة الحسابات -->
    <section class="accounting-page">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-calculator"></i> إدارة الحسابات</h2>
                <div class="header-actions">
                    <button class="btn add-transaction-btn" onclick="showAddJournalEntryModal()"><i class="fas fa-plus-circle"></i> قيد جديد</button>
                    <button class="btn export-btn" onclick="exportAccountingData()"><i class="fas fa-file-export"></i> تصدير</button>
                </div>
            </div>

            <!-- قائمة التنقل الفرعية للحسابات -->
            <div class="accounting-nav">
                <div class="nav-tabs">
                    <button class="nav-tab" data-tab="overview">
                        <i class="fas fa-chart-pie"></i>
                        نظرة عامة
                    </button>
                    <button class="nav-tab active" data-tab="chart-of-accounts">
                        <i class="fas fa-book"></i>
                        دليل الحسابات
                    </button>
                    <button class="nav-tab" data-tab="journal-entries">
                        <i class="fas fa-journal-whills"></i>
                        قيد اليومية
                    </button>
                    <button class="nav-tab" data-tab="receipt-voucher">
                        <i class="fas fa-receipt"></i>
                        سند القبض
                    </button>
                    <button class="nav-tab" data-tab="payment-voucher">
                        <i class="fas fa-money-bill-wave"></i>
                        سند الصرف
                    </button>
                </div>
            </div>

            <!-- محتوى التبويبات -->
            <div class="tab-content">
                <!-- تبويب النظرة العامة -->
                <div class="tab-pane" id="overview-tab">
                    <!-- أزرار التصدير للتقارير -->
                    <div class="section-header">
                        <h3><i class="fas fa-chart-pie"></i> التقارير المالية</h3>
                        <div class="header-actions">
                            <div class="export-buttons">
                                <button class="btn secondary-btn" onclick="printFinancialReport()">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                                <button class="btn secondary-btn" onclick="exportFinancialReportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn secondary-btn" onclick="exportFinancialReportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- لوحة الإحصائيات المالية -->
                    <div class="financial-stats">
                <div class="stat-card income">
                    <div class="stat-icon"><i class="fas fa-arrow-down"></i></div>
                    <div class="stat-info">
                        <h3>إجمالي الإيرادات</h3>
                        <p class="stat-value">125,750.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card expenses">
                    <div class="stat-icon"><i class="fas fa-arrow-up"></i></div>
                    <div class="stat-info">
                        <h3>إجمالي المصروفات</h3>
                        <p class="stat-value">87,250.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card profit">
                    <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="stat-info">
                        <h3>صافي الربح</h3>
                        <p class="stat-value">38,500.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card balance">
                    <div class="stat-icon"><i class="fas fa-wallet"></i></div>
                    <div class="stat-info">
                        <h3>الرصيد الحالي</h3>
                        <p class="stat-value">215,350.00 ر.س</p>
                        <p class="stat-period">حتى تاريخه</p>
                    </div>
                </div>
            </div>

            <!-- الرسم البياني للإيرادات والمصروفات -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>الإيرادات والمصروفات</h3>
                    <div class="chart-period">
                        <select id="chart-period-select">
                            <option value="month">هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذه السنة</option>
                        </select>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="financial-chart"></canvas>
                </div>
            </div>

            <!-- أدوات البحث والتصفية -->
            <div class="search-filter-container">
                <div class="search-box">
                    <input type="text" placeholder="بحث عن معاملة..." id="transaction-search">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
                <div class="filter-options">
                    <select id="transaction-type-filter">
                        <option value="all">جميع المعاملات</option>
                        <option value="income">إيرادات</option>
                        <option value="expense">مصروفات</option>
                    </select>
                    <select id="date-filter">
                        <option value="all">كل الفترات</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="custom">تاريخ مخصص</option>
                    </select>
                    <button class="filter-btn" id="advanced-filter-btn">
                        <i class="fas fa-filter"></i> تصفية متقدمة
                    </button>
                </div>
            </div>

            <!-- تاريخ مخصص (مخفي بشكل افتراضي) -->
            <div class="custom-date-filter" id="custom-date-filter" style="display: none;">
                <div class="date-range">
                    <div class="date-input">
                        <label for="start-date">من تاريخ:</label>
                        <input type="date" id="start-date">
                    </div>
                    <div class="date-input">
                        <label for="end-date">إلى تاريخ:</label>
                        <input type="date" id="end-date">
                    </div>
                    <button class="apply-date-btn">تطبيق</button>
                </div>
            </div>

            <!-- جدول المعاملات المالية -->
            <div class="table-container">
                <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم المعاملة</th>
                            <th>الوصف</th>
                            <th>الفئة</th>
                            <th>النوع</th>
                            <th>المبلغ</th>
                            <th>الحساب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transactions-table-body">
                        <!-- سيتم إضافة صفوف المعاملات هنا عبر JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- نظام التنقل الموحد -->
            <div id="pagination"></div>
                </div>

                <!-- تبويب دليل الحسابات -->
                <div class="tab-pane active" id="chart-of-accounts-tab">
                    <div class="chart-of-accounts-modern">
                        <!-- رأس القسم -->
                        <div class="section-header-modern">
                            <div class="header-title">
                                <h3><i class="fas fa-book"></i> دليل الحسابات</h3>
                                <p>إدارة شاملة لجميع الحسابات المالية</p>
                            </div>
                            <div class="header-actions" style="display: none;">
                                <!-- الأزرار مخفية - سيتم استخدام النظام الموحد فقط -->
                            </div>
                        </div>

                        <!-- أدوات البحث والتصفية -->
                        <div class="accounts-toolbar">
                            <div class="search-section">
                                <div class="search-box-modern">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="accounts-search" placeholder="البحث في الحسابات...">
                                </div>
                            </div>
                            <div class="filter-section">
                                <select id="account-type-filter" class="filter-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="assets">الأصول</option>
                                    <option value="liabilities">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                    <option value="revenue">الإيرادات</option>
                                    <option value="expenses">المصروفات</option>
                                </select>
                                <select id="account-status-filter" class="filter-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <!-- عرض الحسابات بتصميم حديث -->
                        <div class="accounts-display">


                            <!-- جدول تفاصيل الحسابات -->
                            <div class="accounts-table-modern">
                                <div class="table-header">
                                    <h4>تفاصيل الحسابات</h4>
                                    <div class="table-actions">
                                        <button class="btn-icon" onclick="toggleTableView()" title="تبديل العرض">
                                            <i class="fas fa-th-list"></i>
                                        </button>
                                        <button class="btn-icon" onclick="printAccountsTable()" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="table-container">
                                    <table class="modern-table" id="accounts-table">
                                        <thead>
                                            <tr>
                                                <th>الرقم</th>
                                                <th>اسم الحساب</th>
                                                <th>النوع</th>
                                                <th>الرصيد</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="accounts-table-body">
                                            <!-- شجرة الحسابات -->
                                            <tr>
                                                <td><strong>1</strong></td>
                                                <td><strong>الأصول</strong></td>
                                                <td><span class="badge badge-primary">أصول</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal" style="display: flex !important; gap: 6px !important; justify-content: center !important;">
                                                        <button class="action-btn view" onclick="viewAccount('1')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('1')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 30px;"><strong>11</strong></td>
                                                <td style="padding-right: 30px;"><strong>الأصول المتداولة</strong></td>
                                                <td><span class="badge badge-primary">أصول متداولة</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('11')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('11')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 60px;"><strong>11030</strong></td>
                                                <td style="padding-right: 60px;"><strong>العملاء</strong></td>
                                                <td><span class="badge badge-info">عملاء</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('11030')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('11030')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 90px;">********</td>
                                                <td style="padding-right: 90px;">حافظ</td>
                                                <td><span class="badge badge-success">عميل</span></td>
                                                <td>0.00 ر.س</td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('********')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('********')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn delete" onclick="deleteAccount('********')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 90px;">********</td>
                                                <td style="padding-right: 90px;">حمدا</td>
                                                <td><span class="badge badge-success">عميل</span></td>
                                                <td>0.00 ر.س</td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('********')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('********')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn delete" onclick="deleteAccount('********')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- الخصوم والموردون -->
                                            <tr>
                                                <td><strong>2</strong></td>
                                                <td><strong>الخصوم</strong></td>
                                                <td><span class="badge badge-warning">خصوم</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('2')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('2')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 60px;"><strong>21030</strong></td>
                                                <td style="padding-right: 60px;"><strong>الموردون</strong></td>
                                                <td><span class="badge badge-warning">موردين</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('21030')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('21030')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- النظام الاحترافي لشجرة الحسابات -->
                        <div id="professional-accounts-system" style="display: none;">
                            <div class="professional-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                                <h2><i class="fas fa-sitemap"></i> النظام الاحترافي لشجرة الحسابات</h2>
                                <p>نظام متقدم ومنظم بشكل احترافي لإدارة دليل الحسابات</p>
                                <button class="btn" onclick="switchToClassicView()" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 5px; margin-top: 10px;">
                                    <i class="fas fa-arrow-left"></i> العودة للنظام الكلاسيكي
                                </button>
                            </div>

                            <!-- شريط الأدوات الاحترافي -->
                            <div class="professional-toolbar" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                                <div class="search-box-pro" style="position: relative; flex: 1; max-width: 300px;">
                                    <input type="text" id="professionalSearchInput" placeholder="البحث في الحسابات..." style="width: 100%; padding: 12px 40px 12px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;">
                                    <i class="fas fa-search" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                                </div>

                                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                    <button class="btn" onclick="professionalAddAccount()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; display: inline-flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-plus"></i> إضافة حساب
                                    </button>
                                    <button class="btn" onclick="professionalExpandAll()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; display: inline-flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-expand-arrows-alt"></i> توسيع الكل
                                    </button>
                                    <button class="btn" onclick="professionalCollapseAll()" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #333; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; display: inline-flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-compress-arrows-alt"></i> طي الكل
                                    </button>
                                    <button class="btn" onclick="professionalAddFrenchBank()" style="background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; display: inline-flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-university"></i> إضافة البنك الفرنسي
                                    </button>
                                    <button class="btn" onclick="professionalExportAccounts()" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; display: inline-flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-download"></i> تصدير
                                    </button>
                                </div>
                            </div>

                            <!-- إحصائيات النظام الاحترافي -->
                            <div class="professional-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                                <div class="stat-card-pro" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-left: 4px solid #667eea;">
                                    <h3 id="professionalTotalAccounts" style="color: #667eea; font-size: 2rem; margin-bottom: 10px;">0</h3>
                                    <p style="color: #6c757d; font-size: 1rem;">إجمالي الحسابات</p>
                                </div>
                                <div class="stat-card-pro" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                                    <h3 id="professionalMainAccounts" style="color: #28a745; font-size: 2rem; margin-bottom: 10px;">0</h3>
                                    <p style="color: #6c757d; font-size: 1rem;">الحسابات الرئيسية</p>
                                </div>
                                <div class="stat-card-pro" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-left: 4px solid #17a2b8;">
                                    <h3 id="professionalSubAccounts" style="color: #17a2b8; font-size: 2rem; margin-bottom: 10px;">0</h3>
                                    <p style="color: #6c757d; font-size: 1rem;">الحسابات الفرعية</p>
                                </div>
                                <div class="stat-card-pro" style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                                    <h3 id="professionalTotalBalance" style="color: #ffc107; font-size: 2rem; margin-bottom: 10px;">0</h3>
                                    <p style="color: #6c757d; font-size: 1rem;">إجمالي الأرصدة</p>
                                </div>
                            </div>

                            <!-- شجرة الحسابات الاحترافية -->
                            <div class="professional-tree-container" style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                                <div class="tree-header-pro" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px 20px; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">
                                    <i class="fas fa-tree"></i> شجرة الحسابات الاحترافية
                                </div>
                                <div class="tree-container-pro" id="professionalAccountsTree" style="max-height: 600px; overflow-y: auto;">
                                    <div class="empty-state-pro" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                                        <i class="fas fa-folder-open" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;"></i>
                                        <h3 style="margin-bottom: 10px; color: #495057;">لا توجد حسابات</h3>
                                        <p>ابدأ بإضافة حساب جديد أو استيراد البيانات</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- الأقسام المالية -->
            <div class="financial-sections">
                <div class="section-header">
                    <h3>الأقسام المالية</h3>
                    <button class="btn-sm add-category-btn"><i class="fas fa-plus"></i> إضافة فئة</button>
                </div>
                <div class="categories-container">
                    <div class="category-column">
                        <h4>فئات الإيرادات</h4>
                        <ul class="category-list" id="income-categories">
                            <!-- سيتم إضافة فئات الإيرادات هنا عبر JavaScript -->
                        </ul>
                    </div>
                    <div class="category-column">
                        <h4>فئات المصروفات</h4>
                        <ul class="category-list" id="expense-categories">
                            <!-- سيتم إضافة فئات المصروفات هنا عبر JavaScript -->
                        </ul>
                    </div>
                </div>
                </div>

                <!-- تبويب قيد اليومية -->
                <div class="tab-pane" id="journal-entries-tab">
                    <div class="journal-entries-section">
                        <div class="section-header">
                            <h3><i class="fas fa-journal-whills"></i> قيد اليومية</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-journal-entry-btn" onclick="showAddJournalEntryModal()">
                                    <i class="fas fa-plus"></i> قيد جديد
                                </button>
                                <div class="export-buttons">
                                    <button class="btn secondary-btn" onclick="printJournalEntries()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                    <button class="btn secondary-btn" onclick="exportJournalEntriesToExcel()">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </button>
                                    <button class="btn secondary-btn" onclick="exportJournalEntriesToPDF()">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جدول قيود اليومية -->
                        <div class="journal-entries-table-container">
                            <table class="journal-entries-table">
                                <thead>
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>البيان</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="journal-entries-table-body">
                                    <tr>
                                        <td>JE-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>قيد افتتاحي</td>
                                        <td><strong>10,000.00 ر.س</strong></td>
                                        <td><strong>10,000.00 ر.س</strong></td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewJournalEntry('JE-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editJournalEntry('JE-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteJournalEntry('JE-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>JE-2023-002</td>
                                        <td>2023-12-02</td>
                                        <td>مشتريات</td>
                                        <td><strong>5,000.00 ر.س</strong></td>
                                        <td><strong>5,000.00 ر.س</strong></td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewJournalEntry('JE-2023-002')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editJournalEntry('JE-2023-002')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteJournalEntry('JE-2023-002')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب سند القبض -->
                <div class="tab-pane" id="receipt-voucher-tab">
                    <div class="receipt-voucher-section">
                        <div class="section-header">
                            <h3><i class="fas fa-receipt"></i> سند القبض</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-receipt-btn" onclick="showAddReceiptVoucherModal()">
                                    <i class="fas fa-plus"></i> سند قبض جديد
                                </button>
                            </div>
                        </div>

                        <!-- جدول سندات القبض -->
                        <div class="receipt-vouchers-table-container">
                            <table class="receipt-vouchers-table">
                                <thead>
                                    <tr>
                                        <th>رقم السند</th>
                                        <th>التاريخ</th>
                                        <th>المستلم من</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="receipt-vouchers-table-body">
                                    <tr>
                                        <td>RC-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>أحمد محمد</td>
                                        <td><strong>2,500.00 ر.س</strong></td>
                                        <td>دفعة من العميل</td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewReceiptVoucher('RC-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editReceiptVoucher('RC-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteReceiptVoucher('RC-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب سند الصرف -->
                <div class="tab-pane" id="payment-voucher-tab">
                    <div class="payment-voucher-section">
                        <div class="section-header">
                            <h3><i class="fas fa-money-bill-wave"></i> سند الصرف</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-payment-btn" onclick="showAddPaymentVoucherModal()">
                                    <i class="fas fa-plus"></i> سند صرف جديد
                                </button>
                            </div>
                        </div>

                        <!-- جدول سندات الصرف -->
                        <div class="payment-vouchers-table-container">
                            <table class="payment-vouchers-table">
                                <thead>
                                    <tr>
                                        <th>رقم السند</th>
                                        <th>التاريخ</th>
                                        <th>المدفوع إلى</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="payment-vouchers-table-body">
                                    <tr>
                                        <td>PV-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>شركة الكهرباء</td>
                                        <td><strong>1,200.00 ر.س</strong></td>
                                        <td>دفع فاتورة الكهرباء</td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewPaymentVoucher('PV-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editPaymentVoucher('PV-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deletePaymentVoucher('PV-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- النظام الموحد -->
    <section id="unified-chart-section" class="content-section active" style="display: block;">
        <div class="section-header">
            <div class="header-content">
                <div class="header-title">
                    <h3><i class="fas fa-layer-group"></i> النظام الموحد - دليل الحسابات</h3>
                    <p>عرض هرمي احترافي بتصميم حديث</p>
                </div>
                <div class="header-actions">
                    <button class="btn-modern primary" onclick="showAddAccountModal()">
                        <i class="fas fa-plus"></i> حساب جديد
                    </button>
                    <button class="btn-modern secondary" onclick="exportUnifiedAccounts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>

        <div class="unified-container">
            <!-- إحصائيات سريعة -->
            <div class="unified-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="unified-total-accounts">0</h4>
                        <p>إجمالي الحسابات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="unified-assets-count">0</h4>
                        <p>الأصول</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="unified-liabilities-count">0</h4>
                        <p>الخصوم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="unified-equity-count">0</h4>
                        <p>حقوق الملكية</p>
                    </div>
                </div>
            </div>

            <!-- شجرة الحسابات الموحدة -->
            <div class="unified-tree-container">
                <div class="tree-header">
                    <h4><i class="fas fa-sitemap"></i> الهيكل الهرمي للحسابات</h4>
                </div>
                <div id="unified-accounts-tree" class="unified-tree">
                    <!-- سيتم ملء الشجرة هنا -->
                </div>
            </div>
        </div>
    </section>

    <!-- نافذة إضافة حساب جديد -->
    <div id="add-account-modal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button class="close-btn" onclick="closeAddAccountModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-code">رقم الحساب *</label>
                            <input type="text" id="account-code" name="account-code" required
                                   placeholder="مثال: 1101" maxlength="10">
                        </div>
                        <div class="form-group">
                            <label for="account-name">اسم الحساب *</label>
                            <input type="text" id="account-name" name="account-name" required
                                   placeholder="مثال: البنك الأهلي">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-type">نوع الحساب *</label>
                            <select id="account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">أصول</option>
                                <option value="liabilities">خصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">إيرادات</option>
                                <option value="expenses">مصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent-account">الحساب الأب (اختياري)</label>
                            <select id="parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-balance">الرصيد الافتتاحي</label>
                            <input type="number" id="account-balance" name="account-balance"
                                   value="0" step="0.01" min="0">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn secondary-btn" onclick="closeAddAccountModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn primary-btn" onclick="saveAccountSimple()">
                            <i class="fas fa-save"></i> حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حساب جديد - قديمة -->
    <div class="modal" id="add-account-modal-old">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button class="close-modal" id="close-add-account-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-code">رقم الحساب *</label>
                            <input type="text" id="account-code" name="account-code" required
                                   placeholder="مثال: 1001" maxlength="10">
                            <small class="form-help">يجب أن يكون رقم الحساب فريداً</small>
                        </div>
                        <div class="form-group">
                            <label for="account-name">اسم الحساب *</label>
                            <input type="text" id="account-name" name="account-name" required
                                   placeholder="مثال: النقدية في الصندوق">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-type">نوع الحساب *</label>
                            <select id="account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">الأصول</option>
                                <option value="liabilities">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expenses">المصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent-account">الحساب الأب</label>
                            <select id="parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                                <!-- سيتم ملء الخيارات عبر JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-balance">الرصيد الافتتاحي</label>
                            <input type="number" id="account-balance" name="account-balance"
                                   step="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="account-status">حالة الحساب</label>
                            <select id="account-status" name="account-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="account-description">وصف الحساب</label>
                        <textarea id="account-description" name="account-description"
                                  rows="3" placeholder="وصف اختياري للحساب"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ الحساب
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-account">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة النظام الاحترافي -->
    <div class="modal" id="professionalAccountModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
        <div class="modal-content" style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
                <h3 id="professionalModalTitle" style="color: #2c3e50; margin: 0;">إضافة حساب جديد</h3>
                <button class="close-btn" onclick="closeProfessionalModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #6c757d; padding: 5px; border-radius: 50%; transition: all 0.3s ease;">&times;</button>
            </div>

            <form id="professionalAccountForm">
                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalAccountCode" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">رقم الحساب *</label>
                    <input type="text" id="professionalAccountCode" required placeholder="مثال: 1101" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalAccountName" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">اسم الحساب *</label>
                    <input type="text" id="professionalAccountName" required placeholder="مثال: البنك الأهلي" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalAccountType" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">نوع الحساب *</label>
                    <select id="professionalAccountType" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                        <option value="">اختر نوع الحساب</option>
                        <option value="assets">الأصول</option>
                        <option value="liabilities">الخصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">الإيرادات</option>
                        <option value="expenses">المصروفات</option>
                    </select>
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalParentAccount" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">الحساب الأب</label>
                    <select id="professionalParentAccount" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                        <option value="">حساب رئيسي</option>
                    </select>
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalAccountBalance" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">الرصيد الافتتاحي</label>
                    <input type="number" id="professionalAccountBalance" step="0.01" value="0" placeholder="0.00" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                </div>

                <div class="form-group" style="margin-bottom: 20px;">
                    <label for="professionalAccountDescription" style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50;">الوصف</label>
                    <input type="text" id="professionalAccountDescription" placeholder="وصف اختياري للحساب" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease;">
                </div>

                <div class="form-actions" style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                    <button type="button" class="btn" onclick="closeProfessionalModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem;">إلغاء</button>
                    <button type="submit" class="btn" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 0.9rem;">حفظ الحساب</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة قيد جديد -->
    <div class="modal" id="add-journal-entry-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-journal-whills"></i> إضافة قيد جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-journal-entry-form">
                    <div class="form-group">
                        <label for="entry-date">التاريخ:</label>
                        <input type="date" id="entry-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="entry-description">البيان:</label>
                        <textarea id="entry-description" name="description" rows="3" placeholder="وصف القيد..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="debit-account">الحساب المدين:</label>
                        <select id="debit-account" name="debitAccount" required>
                            <option value="">اختر الحساب المدين</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="credit-account">الحساب الدائن:</label>
                        <select id="credit-account" name="creditAccount" required>
                            <option value="">اختر الحساب الدائن</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="entry-amount">المبلغ:</label>
                        <input type="number" id="entry-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ القيد
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-journal-entry">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة سند قبض -->
    <div class="modal" id="add-receipt-voucher-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-receipt"></i> إضافة سند قبض جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-receipt-voucher-form">
                    <div class="form-group">
                        <label for="receipt-date">التاريخ:</label>
                        <input type="date" id="receipt-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="received-from">المستلم من:</label>
                        <input type="text" id="received-from" name="receivedFrom" placeholder="اسم العميل أو الجهة..." required>
                    </div>
                    <div class="form-group">
                        <label for="receipt-amount">المبلغ:</label>
                        <input type="number" id="receipt-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="receipt-description">البيان:</label>
                        <textarea id="receipt-description" name="description" rows="3" placeholder="وصف سند القبض..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ السند
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-receipt-voucher">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة سند صرف -->
    <div class="modal" id="add-payment-voucher-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill-wave"></i> إضافة سند صرف جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-payment-voucher-form">
                    <div class="form-group">
                        <label for="payment-date">التاريخ:</label>
                        <input type="date" id="payment-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="paid-to">المدفوع إلى:</label>
                        <input type="text" id="paid-to" name="paidTo" placeholder="اسم المورد أو الجهة..." required>
                    </div>
                    <div class="form-group">
                        <label for="payment-amount">المبلغ:</label>
                        <input type="number" id="payment-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="payment-description">البيان:</label>
                        <textarea id="payment-description" name="description" rows="3" placeholder="وصف سند الصرف..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ السند
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-payment-voucher">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حساب -->
    <div class="modal" id="edit-account-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> تعديل الحساب</h3>
                <button class="close-modal" id="close-edit-account-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-account-form">
                    <!-- نفس حقول النموذج السابق مع قيم محملة -->
                    <input type="hidden" id="edit-account-id" name="account-id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-code">رقم الحساب *</label>
                            <input type="text" id="edit-account-code" name="account-code" required
                                   maxlength="10">
                        </div>
                        <div class="form-group">
                            <label for="edit-account-name">اسم الحساب *</label>
                            <input type="text" id="edit-account-name" name="account-name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-type">نوع الحساب *</label>
                            <select id="edit-account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">الأصول</option>
                                <option value="liabilities">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expenses">المصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-parent-account">الحساب الأب</label>
                            <select id="edit-parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-balance">الرصيد الحالي</label>
                            <input type="number" id="edit-account-balance" name="account-balance"
                                   step="0.01" readonly>
                            <small class="form-help">الرصيد الحالي للقراءة فقط</small>
                        </div>
                        <div class="form-group">
                            <label for="edit-account-status">حالة الحساب</label>
                            <select id="edit-account-status" name="account-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-account-description">وصف الحساب</label>
                        <textarea id="edit-account-description" name="account-description"
                                  rows="3"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-edit-account">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حساب جديد - بسيطة وفعالة -->
    <div id="new-account-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
        <div style="background: white; border-radius: 12px; width: 500px; max-width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <!-- رأس النافذة -->
            <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; font-size: 18px;"><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button onclick="closeNewAccountModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div style="padding: 25px;">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">رقم الحساب *</label>
                    <input type="text" id="new-account-code" placeholder="مثال: 1101" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;" maxlength="10">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">اسم الحساب *</label>
                    <input type="text" id="new-account-name" placeholder="مثال: البنك الأهلي" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">نوع الحساب *</label>
                    <select id="new-account-type" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; background: white;">
                        <option value="">اختر نوع الحساب</option>
                        <option value="assets">أصول</option>
                        <option value="liabilities">خصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">إيرادات</option>
                        <option value="expenses">مصروفات</option>
                    </select>
                </div>

                <div style="margin-bottom: 25px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">الرصيد الافتتاحي</label>
                    <input type="number" id="new-account-balance" placeholder="0.00" value="0" step="0.01" min="0" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                </div>

                <!-- أزرار الحفظ والإلغاء -->
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="closeNewAccountModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button onclick="saveNewAccountSimple()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold; box-shadow: 0 3px 10px rgba(40,167,69,0.3);">
                        <i class="fas fa-save"></i> حفظ الحساب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- القدم -->
    <footer class="main-footer">
        <div class="container">
            <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة الأعمال</p>
        </div>
    </footer>

    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

    <script src="js/data-manager.js"></script>
    <script src="js/main.js"></script>
    <script src="js/accounting.js"></script>
    <script src="js/accounting-fix.js"></script>
    <script src="js/modal-fix.js"></script>
    <script src="js/chart-of-accounts-data.js"></script>
    <script src="js/chart-of-accounts-modern.js"></script>
    <script src="js/account-system-test.js"></script>
    <script src="js/integration-system.js"></script>

    <!-- سكريبت إضافي لإصلاح الأيقونات -->
    <script>
        // تطبيق التنسيق على جميع الأيقونات عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('تطبيق تنسيق الأيقونات عند تحميل الصفحة...');

            // تنسيق جميع الأيقونات
            function fixAllIcons() {
                // تحديد جميع الأزرار
                const allButtons = document.querySelectorAll('.action-btn');

                // تطبيق التنسيق على كل زر
                allButtons.forEach(btn => {
                    // تنسيق الزر
                    btn.style.width = '26px';
                    btn.style.height = '26px';
                    btn.style.borderRadius = '4px';
                    btn.style.border = 'none';
                    btn.style.display = 'flex';
                    btn.style.alignItems = 'center';
                    btn.style.justifyContent = 'center';
                    btn.style.margin = '0';
                    btn.style.padding = '0';

                    // تنسيق حسب النوع
                    if (btn.classList.contains('view')) {
                        btn.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('edit')) {
                        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('delete')) {
                        btn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                        btn.style.color = 'white';
                    }

                    // تنسيق الأيقونة
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.color = 'white';
                        icon.style.fontSize = '10px';
                    }
                });

                // تنسيق حاويات الأزرار
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    container.style.display = 'flex';
                    container.style.flexDirection = 'row';
                    container.style.gap = '6px';
                    container.style.justifyContent = 'center';
                });
            }

            // تطبيق التنسيق عند التحميل
            fixAllIcons();

            // تطبيق التنسيق بعد تأخير
            setTimeout(fixAllIcons, 500);
            setTimeout(fixAllIcons, 1000);
            setTimeout(fixAllIcons, 2000);

            // تطبيق التنسيق عند النقر على أي تبويب
            const allTabs = document.querySelectorAll('.tab-link, .nav-link, .account-type-filter');
            allTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    setTimeout(fixAllIcons, 100);
                    setTimeout(fixAllIcons, 500);
                });
            });

            // تطبيق التنسيق كل ثانية
            setInterval(fixAllIcons, 1000);
        });
    </script>

    <script>
        // دالة تحميل الحسابات من النظام المركزي
        function loadAccountsFromCentralSystem() {
            console.log('تحميل الحسابات من النظام المركزي...');

            // تحميل الحسابات من localStorage
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) ||
                          JSON.parse(localStorage.getItem('monjizAccounts')) || [];

            // إضافة الحسابات الافتراضية إذا لم تكن موجودة
            if (accounts.length === 0) {
                accounts = createDefaultAccounts();
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
            }

            // تزامن الحسابات بين المفتاحين
            syncAccountsStorage(accounts);

            // التحقق من حسابات العملاء والموردين وإضافة المفقود منها (بدون الكتابة فوق الحسابات الموجودة)
            try {
                ensureCustomerSupplierAccounts();
                // إعادة تحميل الحسابات بعد التحقق من العملاء والموردين
                accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            } catch (error) {
                console.error('خطأ في التحقق من حسابات العملاء والموردين:', error);
            }

            console.log('الحسابات المحملة:', accounts);

            // تحديث جدول الحسابات
            updateAccountsTable(accounts);

            // تطبيق تنسيق الأيقونات بعد تحميل الحسابات
            setTimeout(function() {
                // تنسيق الأيقونات في دليل الحسابات
                const accountsButtons = document.querySelectorAll('#accounts-table-body .action-btn');
                accountsButtons.forEach(btn => {
                    if (btn.classList.contains('view')) {
                        btn.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('edit')) {
                        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('delete')) {
                        btn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                        btn.style.color = 'white';
                    }

                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.color = 'white';
                    }
                });
            }, 500);

            if (!window.dataManager) {
                console.error('النظام المركزي غير متاح');
                return;
            }

            // تحميل الحسابات من localStorage أولاً
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إذا لم تكن هناك حسابات في localStorage، حاول تحميلها من النظام المركزي
            if (accounts.length === 0 && window.dataManager) {
                accounts = window.dataManager.getAccounts() || [];
            }

            console.log('الحسابات المحملة:', accounts);

            // تحديث جدول الحسابات
            updateAccountsTable(accounts);
        }

        // دالة تحديث جدول الحسابات
        function updateAccountsTable(accounts) {
            console.log('🔄 بدء تحديث جدول الحسابات، عدد الحسابات:', accounts.length);

            const tableBody = document.getElementById('accounts-table-body');
            if (!tableBody) {
                console.error('❌ جدول الحسابات غير موجود');
                return;
            }

            // مسح الجدول الحالي
            tableBody.innerHTML = '';
            console.log('✅ تم مسح الجدول الحالي');

            if (accounts.length === 0) {
                console.log('⚠️ لا توجد حسابات لعرضها');
                tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد حسابات</td></tr>';
                return;
            }

            // ترتيب الحسابات حسب الكود
            accounts.sort((a, b) => a.code.localeCompare(b.code));
            console.log('✅ تم ترتيب الحسابات');

            // بناء شجرة الحسابات
            const accountTree = buildAccountTree(accounts);
            console.log('✅ تم بناء شجرة الحسابات:', accountTree.length, 'حساب جذر');

            // عرض الحسابات بشكل شجرة
            renderAccountTree(accountTree, tableBody);

            console.log(`✅ تم عرض ${accounts.length} حساب في الجدول`);
        }

        // دالة تحديث مبسطة للجدول (بدون شجرة)
        function updateAccountsTableSimple(accounts) {
            console.log('🔄 تحديث مبسط للجدول، عدد الحسابات:', accounts ? accounts.length : 'غير محدد');

            // تحميل الحسابات إذا لم تُمرر
            if (!accounts) {
                accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                console.log('📊 تم تحميل الحسابات من التخزين:', accounts.length);
            }

            const tableBody = document.getElementById('accounts-table-body');
            if (!tableBody) {
                console.error('❌ جدول الحسابات غير موجود');
                return;
            }

            // مسح الجدول الحالي
            tableBody.innerHTML = '';
            console.log('✅ تم مسح الجدول الحالي');

            if (accounts.length === 0) {
                console.log('⚠️ لا توجد حسابات لعرضها');
                tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد حسابات</td></tr>';
                return;
            }

            // ترتيب الحسابات حسب الكود
            accounts.sort((a, b) => a.code.localeCompare(b.code));
            console.log('✅ تم ترتيب الحسابات');

            // عرض الحسابات مباشرة بدون شجرة
            accounts.forEach((account, index) => {
                console.log(`📊 إضافة الحساب ${index + 1}: ${account.code} - ${account.name}`);

                const row = document.createElement('tr');

                // تحديد نوع الحساب
                let accountTypeLabel = '';
                let badgeClass = '';

                switch (account.type) {
                    case 'assets':
                        accountTypeLabel = 'أصول';
                        badgeClass = 'badge-primary';
                        break;
                    case 'liabilities':
                        accountTypeLabel = 'خصوم';
                        badgeClass = 'badge-warning';
                        break;
                    case 'equity':
                        accountTypeLabel = 'حقوق الملكية';
                        badgeClass = 'badge-info';
                        break;
                    case 'revenue':
                        accountTypeLabel = 'إيرادات';
                        badgeClass = 'badge-success';
                        break;
                    case 'expenses':
                        accountTypeLabel = 'مصروفات';
                        badgeClass = 'badge-danger';
                        break;
                    default:
                        accountTypeLabel = account.type;
                        badgeClass = 'badge-secondary';
                }

                row.innerHTML = `
                    <td><strong>${account.code}</strong></td>
                    <td>${account.name}</td>
                    <td><span class="badge ${badgeClass}">${accountTypeLabel}</span></td>
                    <td>${(account.balance || 0).toFixed(2)} ر.س</td>
                    <td><span class="status-badge ${account.status || 'active'}">${account.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons-horizontal" style="display: flex !important; gap: 6px !important; justify-content: center !important;">
                            <button class="action-btn view" onclick="viewAccount('${account.code}')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn edit" onclick="editAccount('${account.code}')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteAccount('${account.code}')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            console.log(`✅ تم عرض ${accounts.length} حساب في الجدول بالطريقة المبسطة`);
        }

        // دالة بناء شجرة الحسابات
        function buildAccountTree(accounts) {
            console.log('🌳 بناء شجرة الحسابات من', accounts.length, 'حساب');

            if (accounts.length === 0) {
                console.log('⚠️ لا توجد حسابات لبناء الشجرة');
                return [];
            }

            // إنشاء قاموس للحسابات
            const accountMap = {};
            accounts.forEach(account => {
                accountMap[account.code] = { ...account, children: [] };
            });

            console.log('📋 خريطة الحسابات:', Object.keys(accountMap));

            // بناء الشجرة
            const rootAccounts = [];
            accounts.forEach(account => {
                const accountNode = accountMap[account.code];

                if (account.parentCode && accountMap[account.parentCode]) {
                    // حساب فرعي
                    accountMap[account.parentCode].children.push(accountNode);
                    console.log(`├─ ${account.code} (${account.name}) → تحت ${account.parentCode}`);
                } else {
                    // حساب جذر (ليس له والد أو والده غير موجود)
                    rootAccounts.push(accountNode);
                    console.log(`🌟 ${account.code} (${account.name}) → حساب جذر`);
                }
            });

            console.log('✅ تم بناء الشجرة:', rootAccounts.length, 'حساب جذر');

            // طباعة تفاصيل الشجرة
            rootAccounts.forEach((root, index) => {
                console.log(`🌳 الجذر ${index + 1}: ${root.code} - ${root.name} (${root.children.length} فرع)`);
            });

            return rootAccounts;
        }

        // دالة عرض شجرة الحسابات
        function renderAccountTree(accountTree, tableBody, level = 0) {
            console.log(`🌳 عرض شجرة الحسابات، المستوى ${level}، عدد الحسابات: ${accountTree.length}`);

            accountTree.forEach((account, index) => {
                console.log(`📊 عرض الحساب ${index + 1}: ${account.code} - ${account.name}`);
                const row = document.createElement('tr');

                // تحديد المسافة البادئة حسب المستوى
                const paddingRight = level * 30;

                // تحديد نوع الحساب
                let accountTypeLabel = '';
                let badgeClass = '';

                if (account.category === 'customers') {
                    accountTypeLabel = 'عميل';
                    badgeClass = 'badge-success';
                } else if (account.category === 'suppliers') {
                    accountTypeLabel = 'مورد';
                    badgeClass = 'badge-warning';
                } else {
                    switch (account.type) {
                        case 'assets':
                            accountTypeLabel = 'أصول';
                            badgeClass = 'badge-primary';
                            break;
                        case 'liabilities':
                            accountTypeLabel = 'خصوم';
                            badgeClass = 'badge-warning';
                            break;
                        case 'equity':
                            accountTypeLabel = 'حقوق الملكية';
                            badgeClass = 'badge-info';
                            break;
                        case 'revenue':
                            accountTypeLabel = 'إيرادات';
                            badgeClass = 'badge-success';
                            break;
                        case 'expenses':
                            accountTypeLabel = 'مصروفات';
                            badgeClass = 'badge-danger';
                            break;
                        default:
                            accountTypeLabel = account.type;
                            badgeClass = 'badge-secondary';
                    }
                }

                // إضافة أيقونة الشجرة
                const treeIcon = level > 0 ? '<i class="fas fa-level-up-alt fa-rotate-90" style="margin-left: 8px; color: #aaa;"></i>' : '';

                // تحديد ما إذا كان الحساب رئيسيًا (له أبناء)
                const isParent = account.children && account.children.length > 0;
                const nameStyle = isParent ? 'font-weight: bold;' : '';

                row.innerHTML = `
                    <td style="padding-right: ${paddingRight}px;"><strong>${account.code}</strong></td>
                    <td style="padding-right: ${paddingRight}px; ${nameStyle}">${treeIcon} ${account.name}</td>
                    <td><span class="badge ${badgeClass}">${accountTypeLabel}</span></td>
                    <td>${(account.balance || 0).toFixed(2)} ر.س</td>
                    <td><span class="status-badge ${account.status || 'active'}">${account.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons-horizontal" style="display: flex !important; gap: 6px !important; justify-content: center !important;">
                            <button class="action-btn view" onclick="viewAccount('${account.code}')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn edit" onclick="editAccount('${account.code}')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteAccount('${account.code}')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);

                // عرض الحسابات الفرعية بشكل متسلسل
                if (account.children && account.children.length > 0) {
                    renderAccountTree(account.children, tableBody, level + 1);
                }
            });
        }

        // دالة مساعدة لتحديد نوع الحساب
        function getAccountTypeBadge(type) {
            const badges = {
                'assets': 'primary',
                'liabilities': 'warning',
                'equity': 'info',
                'revenue': 'success',
                'expenses': 'danger'
            };
            return badges[type] || 'secondary';
        }

        // دالة مساعدة لتحديد اسم نوع الحساب
        function getAccountTypeLabel(type) {
            const labels = {
                'assets': 'أصول',
                'liabilities': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expenses': 'مصروفات',
                'customers': 'عميل',
                'suppliers': 'مورد'
            };
            return labels[type] || type;
        }

        // دالة تزامن الحسابات بين مفاتيح التخزين المختلفة
        function syncAccountsStorage(accounts) {
            try {
                // حفظ في كلا المفتاحين للتوافق
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
                console.log('✅ تم تزامن الحسابات بين مفاتيح التخزين');
            } catch (error) {
                console.error('❌ خطأ في تزامن الحسابات:', error);
            }
        }

        // دالة للتحقق من وجود حساب العميل/المورد في دليل الحسابات
        function ensureCustomerSupplierAccounts() {
            console.log('🔄 التحقق من حسابات العملاء والموردين...');

            try {
                // تحميل العملاء
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                console.log('📊 عدد العملاء:', customers.length);

                // تحميل الموردين
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                console.log('📊 عدد الموردين:', suppliers.length);

                // تحميل الحسابات
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // التحقق من حسابات العملاء
                customers.forEach(customer => {
                    const customerAccountCode = `11030${String(customer.id).padStart(3, '0')}`;
                    const exists = accounts.find(acc => acc.code === customerAccountCode);

                    if (!exists) {
                        console.log(`➕ إضافة حساب العميل المفقود: ${customer.name} (${customerAccountCode})`);
                        // إضافة حساب العميل المفقود
                        const customerAccount = {
                            id: Date.now() + Math.random(),
                            code: customerAccountCode,
                            name: customer.name,
                            type: 'assets',
                            category: 'customers',
                            parentCode: '11030',
                            parentName: 'العملاء',
                            balance: 0,
                            status: 'active',
                            linkedType: 'customer',
                            linkedId: customer.id,
                            level: 4,
                            createdAt: new Date().toISOString()
                        };
                        accounts.push(customerAccount);
                    }
                });

                // التحقق من حسابات الموردين
                suppliers.forEach(supplier => {
                    const supplierAccountCode = `21030${String(supplier.id).padStart(3, '0')}`;
                    const exists = accounts.find(acc => acc.code === supplierAccountCode);

                    if (!exists) {
                        console.log(`➕ إضافة حساب المورد المفقود: ${supplier.name} (${supplierAccountCode})`);
                        // إضافة حساب المورد المفقود
                        const supplierAccount = {
                            id: Date.now() + Math.random(),
                            code: supplierAccountCode,
                            name: supplier.name,
                            type: 'liabilities',
                            category: 'suppliers',
                            parentCode: '21030',
                            parentName: 'الموردون',
                            balance: 0,
                            status: 'active',
                            linkedType: 'supplier',
                            linkedId: supplier.id,
                            level: 3,
                            createdAt: new Date().toISOString()
                        };
                        accounts.push(supplierAccount);
                    }
                });

                // حفظ الحسابات المحدثة
                syncAccountsStorage(accounts);

                console.log('✅ تم التحقق من حسابات العملاء والموردين');
                return accounts;

            } catch (error) {
                console.error('❌ خطأ في التحقق من حسابات العملاء والموردين:', error);
                return JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            }
        }

        // دالة إنشاء الحسابات الافتراضية
        function createDefaultAccounts() {
            return [
                // الأصول الرئيسية
                { id: 1, code: '1', name: 'الأصول', type: 'assets', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', balance: 0, status: 'active', level: 2, parentCode: '1', createdAt: new Date().toISOString() },
                { id: 1101, code: '1101', name: 'النقدية والبنوك', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '11', createdAt: new Date().toISOString() },
                { id: 110101, code: '110101', name: 'الصندوق', type: 'assets', balance: 0, status: 'active', level: 4, parentCode: '1101', createdAt: new Date().toISOString() },
                { id: 110102, code: '110102', name: 'البنوك', type: 'assets', balance: 0, status: 'active', level: 4, parentCode: '1101', createdAt: new Date().toISOString() },

                // البنوك التفصيلية
                { id: 11010201, code: '11010201', name: 'بنك الراجحي', type: 'assets', balance: 0, status: 'active', level: 5, parentCode: '110102', createdAt: new Date().toISOString() },
                { id: 11010202, code: '11010202', name: 'البنك الأهلي', type: 'assets', balance: 0, status: 'active', level: 5, parentCode: '110102', createdAt: new Date().toISOString() },
                { id: 11010203, code: '11010203', name: 'بنك الرياض', type: 'assets', balance: 0, status: 'active', level: 5, parentCode: '110102', createdAt: new Date().toISOString() },
                { id: 11010204, code: '11010204', name: 'بنك الإنماء', type: 'assets', balance: 0, status: 'active', level: 5, parentCode: '110102', createdAt: new Date().toISOString() },
                { id: 1102, code: '1102', name: 'العملاء', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '11', createdAt: new Date().toISOString() },

                // الأصول الثابتة
                { id: 12, code: '12', name: 'الأصول الثابتة', type: 'assets', balance: 0, status: 'active', level: 2, parentCode: '1', createdAt: new Date().toISOString() },
                { id: 1201, code: '1201', name: 'المباني', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '12', createdAt: new Date().toISOString() },
                { id: 1202, code: '1202', name: 'المعدات', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '12', createdAt: new Date().toISOString() },

                // الخصوم الرئيسية
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 21, code: '21', name: 'الخصوم المتداولة', type: 'liabilities', balance: 0, status: 'active', level: 2, parentCode: '2', createdAt: new Date().toISOString() },
                { id: 2101, code: '2101', name: 'الموردون', type: 'liabilities', balance: 0, status: 'active', level: 3, parentCode: '21', createdAt: new Date().toISOString() },
                { id: 2102, code: '2102', name: 'المصروفات المستحقة', type: 'liabilities', balance: 0, status: 'active', level: 3, parentCode: '21', createdAt: new Date().toISOString() },

                // حقوق الملكية
                { id: 3, code: '3', name: 'حقوق الملكية', type: 'equity', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 31, code: '31', name: 'رأس المال', type: 'equity', balance: 0, status: 'active', level: 2, parentCode: '3', createdAt: new Date().toISOString() },
                { id: 32, code: '32', name: 'الأرباح المحتجزة', type: 'equity', balance: 0, status: 'active', level: 2, parentCode: '3', createdAt: new Date().toISOString() },

                // الإيرادات
                { id: 4, code: '4', name: 'الإيرادات', type: 'revenue', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 41, code: '41', name: 'إيرادات المبيعات', type: 'revenue', balance: 0, status: 'active', level: 2, parentCode: '4', createdAt: new Date().toISOString() },
                { id: 42, code: '42', name: 'إيرادات أخرى', type: 'revenue', balance: 0, status: 'active', level: 2, parentCode: '4', createdAt: new Date().toISOString() },

                // المصروفات
                { id: 5, code: '5', name: 'المصروفات', type: 'expenses', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 51, code: '51', name: 'تكلفة البضاعة المباعة', type: 'expenses', balance: 0, status: 'active', level: 2, parentCode: '5', createdAt: new Date().toISOString() },
                { id: 52, code: '52', name: 'المصروفات الإدارية', type: 'expenses', balance: 0, status: 'active', level: 2, parentCode: '5', createdAt: new Date().toISOString() },
                { id: 5201, code: '5201', name: 'الرواتب', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() },
                { id: 5202, code: '5202', name: 'الإيجار', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() },
                { id: 5203, code: '5203', name: 'الكهرباء والماء', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() }
            ];
        }

        // دالة التحقق من صحة رقم الحساب
        function validateAccountCode(code, parentCode = null) {
            // التحقق من أن الرقم ليس فارغًا
            if (!code || code.trim() === '') {
                return { valid: false, message: 'رقم الحساب مطلوب' };
            }

            // التحقق من أن الرقم يحتوي على أرقام فقط
            if (!/^\d+$/.test(code)) {
                return { valid: false, message: 'رقم الحساب يجب أن يحتوي على أرقام فقط' };
            }

            // التحقق من طول الرقم
            if (code.length > 10) {
                return { valid: false, message: 'رقم الحساب لا يجب أن يزيد عن 10 أرقام' };
            }

            // التحقق من التسلسل الهرمي إذا كان هناك حساب أب
            if (parentCode) {
                if (!code.startsWith(parentCode)) {
                    return { valid: false, message: `رقم الحساب يجب أن يبدأ برقم الحساب الأب: ${parentCode}` };
                }

                if (code.length <= parentCode.length) {
                    return { valid: false, message: 'رقم الحساب الفرعي يجب أن يكون أطول من رقم الحساب الأب' };
                }
            }

            return { valid: true, message: 'رقم الحساب صحيح' };
        }

        function getAccountTypeText(type) {
            const types = {
                'assets': 'الأصول',
                'liabilities': 'الخصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'الإيرادات',
                'expenses': 'المصروفات'
            };
            return types[type] || 'غير محدد';
        }

        // دالة مراقبة التحديثات من الصفحات الأخرى
        function setupDataUpdateListener() {
            // مراقبة تحديثات localStorage
            window.addEventListener('storage', function(e) {
                if (e.key === 'monjizDataUpdate') {
                    console.log('🔄 تم اكتشاف تحديث من صفحة أخرى، إعادة تحميل الحسابات...');
                    setTimeout(() => {
                        loadAccountsFromCentralSystem();
                    }, 500);
                }
            });

            // مراقبة BroadcastChannel إذا كان متاحاً
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.addEventListener('message', function(event) {
                    if (event.data.type === 'customers-updated' || event.data.type === 'suppliers-updated') {
                        console.log('🔄 تم اكتشاف تحديث عبر BroadcastChannel:', event.data.type);
                        setTimeout(() => {
                            loadAccountsFromCentralSystem();
                        }, 500);
                    }
                });
            }
        }

        // تحميل الحسابات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تحميل الصفحة...');

            // تحميل النظام الموحد
            setTimeout(() => {
                if (typeof loadUnifiedAccounts === 'function') {
                    loadUnifiedAccounts();
                }
            }, 100);

            // تشغيل مراقبة التحديثات بين النظامين
            watchForClassicUpdates();

            // إعداد مراقبة التحديثات
            setupDataUpdateListener();

            // إضافة مستمعي الأحداث للتبويبات
            setupTabNavigation();

            // تطبيق التنسيق كل ثانية للتأكد من التطبيق
            setInterval(forceHorizontalButtons, 1000);
        });

        // دالة إعداد التنقل بين التبويبات
        function setupTabNavigation() {
            console.log('إعداد التنقل بين التبويبات...');

            // إضافة مستمعي الأحداث للتبويبات
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    console.log('تم النقر على التبويب:', tabId);

                    // إزالة الفئة النشطة من جميع التبويبات
                    document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');

                    // إظهار المحتوى المناسب
                    const tabContent = document.getElementById(tabId + '-tab');
                    if (tabContent) {
                        tabContent.classList.add('active');
                        console.log('تم تفعيل المحتوى:', tabId + '-tab');
                    } else {
                        console.error('لم يتم العثور على محتوى التبويب:', tabId + '-tab');
                    }

                    // إذا كان التبويب هو دليل الحسابات، قم بتحميل الحسابات
                    if (tabId === 'chart-of-accounts') {
                        setTimeout(() => {
                            loadAccountsFromCentralSystem();
                        }, 100);
                    }
                });
            });

            // إضافة مستمع حدث لزر إضافة الحساب
            const addAccountBtn = document.getElementById('add-account-btn');
            if (addAccountBtn) {
                console.log('تم العثور على زر إضافة الحساب');
                addAccountBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('تم النقر على زر إضافة الحساب من خلال addEventListener');
                    showAddAccountModal();
                });
            } else {
                console.error('لم يتم العثور على زر إضافة الحساب');
            }
        }



        // إجبار الأيقونات على النمط الصحيح
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق التنسيق على جميع الأيقونات
            function forceHorizontalButtons() {
                console.log('تطبيق تنسيق الأيقونات...');

                // تنسيق الحاويات
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    container.style.setProperty('display', 'flex', 'important');
                    container.style.setProperty('flex-direction', 'row', 'important');
                    container.style.setProperty('flex-wrap', 'nowrap', 'important');
                    container.style.setProperty('gap', '6px', 'important');
                    container.style.setProperty('justify-content', 'center', 'important');
                    container.style.setProperty('align-items', 'center', 'important');
                    container.style.setProperty('padding', '3px', 'important');
                });

                // تنسيق الأزرار
                const buttons = document.querySelectorAll('.action-btn');
                buttons.forEach(btn => {
                    btn.style.setProperty('width', '26px', 'important');
                    btn.style.setProperty('height', '26px', 'important');
                    btn.style.setProperty('border-radius', '4px', 'important');
                    btn.style.setProperty('border', 'none', 'important');
                    btn.style.setProperty('display', 'flex', 'important');
                    btn.style.setProperty('align-items', 'center', 'important');
                    btn.style.setProperty('justify-content', 'center', 'important');
                    btn.style.setProperty('cursor', 'pointer', 'important');
                    btn.style.setProperty('transition', 'all 0.2s ease', 'important');
                    btn.style.setProperty('font-size', '10px', 'important');
                    btn.style.setProperty('margin', '0', 'important');
                    btn.style.setProperty('padding', '0', 'important');

                    if (btn.classList.contains('view')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8, #138496)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    } else if (btn.classList.contains('edit')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #28a745, #20c997)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    } else if (btn.classList.contains('delete')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545, #c82333)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    }

                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.setProperty('color', 'white', 'important');
                        icon.style.setProperty('font-size', '10px', 'important');
                    }

                    // إضافة تأثيرات التحويم
                    btn.addEventListener('mouseenter', function() {
                        this.style.setProperty('transform', 'translateY(-2px)', 'important');
                        this.style.setProperty('box-shadow', '0 4px 12px rgba(0,0,0,0.15)', 'important');
                    });

                    btn.addEventListener('mouseleave', function() {
                        this.style.setProperty('transform', 'translateY(0)', 'important');
                        this.style.setProperty('box-shadow', 'none', 'important');
                    });
                });
            }

            // تطبيق التنسيق عند تحميل الصفحة
            forceHorizontalButtons();

            // إعادة تطبيق التنسيق عند تحديث المحتوى
            const observer = new MutationObserver(forceHorizontalButtons);
            observer.observe(document.body, { childList: true, subtree: true });

            // تطبيق التنسيق عند النقر على تبويب دليل الحسابات
            const accountsTabLinks = document.querySelectorAll('.nav-tab');
            accountsTabLinks.forEach(link => {
                link.addEventListener('click', function() {
                    console.log('تم النقر على تبويب:', this.getAttribute('data-tab'));

                    // إزالة الفئة النشطة من جميع التبويبات
                    document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');
                    const targetTab = this.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetTab + '-tab');
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }

                    // إعادة تحميل الحسابات عند فتح تبويب دليل الحسابات
                    if (targetTab === 'chart-of-accounts') {
                        setTimeout(() => {
                            loadAccountsFromCentralSystem();
                        }, 100);
                    }

                    // تأخير قليل للسماح بتحديث المحتوى
                    setTimeout(forceHorizontalButtons, 100);
                    setTimeout(forceHorizontalButtons, 500);
                });
            });

            // تطبيق التنسيق عند تغيير عرض الحسابات
            const accountTypeFilters = document.querySelectorAll('.account-type-filter');
            accountTypeFilters.forEach(filter => {
                filter.addEventListener('click', function() {
                    // تأخير قليل للسماح بتحديث المحتوى
                    setTimeout(forceHorizontalButtons, 100);
                    setTimeout(forceHorizontalButtons, 500);
                });
            });

            // تطبيق التنسيق كل ثانية للتأكد من التطبيق
            setInterval(forceHorizontalButtons, 1000);
        });
















        // دالة للحصول على تسمية نوع الحساب
        function getAccountTypeLabel(type) {
            const types = {
                'assets': 'أصول',
                'liabilities': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expenses': 'مصروفات'
            };
            return types[type] || type;
        }

        // دالة لمسح جميع البيانات المحفوظة (للاختبار)
        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('monjizAccounts');
                console.log('تم مسح جميع البيانات');
                alert('تم مسح جميع البيانات. سيتم إعادة تحميل الصفحة.');
                location.reload();
            }
        }

        // دالة اختبار النظام
        function testAccountSystem() {
            console.log('🧪 بدء اختبار النظام الشامل...');

            // تشغيل الاختبارات الشاملة إذا كانت متوفرة
            if (typeof runAccountSystemTests === 'function') {
                console.log('🔬 تشغيل الاختبارات الشاملة...');
                const testResults = runAccountSystemTests();

                // عرض النتائج في نافذة منبثقة
                const resultMessage = `نتائج اختبار النظام:

إجمالي الاختبارات: ${testResults.total}
الاختبارات الناجحة: ${testResults.passed}
الاختبارات الفاشلة: ${testResults.failed}
معدل النجاح: ${testResults.successRate}%

${testResults.failed > 0 ? 'تحقق من وحدة التحكم لمزيد من التفاصيل.' : 'جميع الاختبارات نجحت! 🎉'}`;

                alert(resultMessage);
                return;
            }

            // الاختبار الأساسي إذا لم تكن الاختبارات الشاملة متوفرة
            console.log('📊 تشغيل الاختبار الأساسي...');

            // اختبار تحميل الحسابات
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            console.log('📊 عدد الحسابات المحفوظة:', accounts.length);

            if (accounts.length === 0) {
                console.log('⚠️ لا توجد حسابات، سيتم إنشاء الحسابات الافتراضية');
                const defaultAccounts = createDefaultAccounts();
                localStorage.setItem('chartOfAccounts', JSON.stringify(defaultAccounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(defaultAccounts));
                console.log('✅ تم إنشاء', defaultAccounts.length, 'حساب افتراضي');
                accounts = defaultAccounts;
            }

            // اختبار الربط مع العملاء والموردين
            console.log('🔗 اختبار الربط مع العملاء والموردين...');
            accounts = ensureCustomerSupplierAccounts();

            // إعادة تحميل الحسابات
            loadAccountsFromCentralSystem();

            // عرض معلومات الربط
            showLinkingInfo();

            alert(`تم اختبار النظام!\n\nعدد الحسابات: ${accounts.length}\nتحقق من وحدة التحكم للمزيد من التفاصيل.`);
        }

        // دالة عرض معلومات الربط
        function showLinkingInfo() {
            console.log('🔗 معلومات الربط بين دليل الحسابات والعملاء/الموردين:');
            console.log('');
            console.log('📊 العملاء (Customers):');
            console.log('   المسار: الأصول (1) → الأصول المتداولة (11) → المدينون (1103) → العملاء (11030) → العميل (********, ********, ...)');
            console.log('   النوع: assets (أصول)');
            console.log('   المستوى: 4');
            console.log('');
            console.log('📊 الموردين (Suppliers):');
            console.log('   المسار: الخصوم (2) → الموردون (21030) → المورد (********, ********, ...)');
            console.log('   النوع: liabilities (خصوم)');
            console.log('   المستوى: 3');
            console.log('');

            // عرض الإحصائيات الحالية
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];

            const customerAccounts = accounts.filter(acc => acc.category === 'customers');
            const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');

            console.log('📈 الإحصائيات الحالية:');
            console.log(`   إجمالي الحسابات: ${accounts.length}`);
            console.log(`   العملاء: ${customers.length} عميل، ${customerAccounts.length} حساب`);
            console.log(`   الموردين: ${suppliers.length} مورد، ${supplierAccounts.length} حساب`);

            // عرض تفاصيل العملاء
            console.log('');
            console.log('👥 تفاصيل العملاء:');
            customers.forEach(customer => {
                const accountCode = `11030${String(customer.id).padStart(3, '0')}`;
                const hasAccount = accounts.find(acc => acc.code === accountCode);
                console.log(`   ${customer.name} (ID: ${customer.id}) → ${accountCode} ${hasAccount ? '✅' : '❌'}`);
            });

            // عرض تفاصيل الموردين
            console.log('');
            console.log('🏪 تفاصيل الموردين:');
            suppliers.forEach(supplier => {
                const accountCode = `21030${String(supplier.id).padStart(3, '0')}`;
                const hasAccount = accounts.find(acc => acc.code === accountCode);
                console.log(`   ${supplier.name} (ID: ${supplier.id}) → ${accountCode} ${hasAccount ? '✅' : '❌'}`);
            });

            if (customers.length !== customerAccounts.length) {
                console.warn('⚠️ عدم تطابق في حسابات العملاء!');
            }

            if (suppliers.length !== supplierAccounts.length) {
                console.warn('⚠️ عدم تطابق في حسابات الموردين!');
            }
        }

        // دالة إصلاح الربط المفقود
        function fixMissingLinks() {
            console.log('🔧 بدء إصلاح الروابط المفقودة...');

            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            let addedCount = 0;

            // إصلاح حسابات العملاء المفقودة
            customers.forEach(customer => {
                const accountCode = `11030${String(customer.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    console.log(`➕ إضافة حساب العميل المفقود: ${customer.name} (${accountCode})`);

                    // إضافة الحسابات الأساسية أولاً
                    const basicAccounts = [
                        { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, balance: 0, status: 'active', parentCode: null },
                        { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, balance: 0, status: 'active', parentCode: '1' },
                        { id: 1103, code: '1103', name: 'المدينون', type: 'assets', level: 3, balance: 0, status: 'active', parentCode: '11' },
                        { id: 11030, code: '11030', name: 'العملاء', type: 'assets', category: 'customers', level: 3, balance: 0, status: 'active', parentCode: '1103' }
                    ];

                    basicAccounts.forEach(basicAccount => {
                        const basicExists = accounts.find(acc => acc.code === basicAccount.code);
                        if (!basicExists) {
                            accounts.push({...basicAccount, createdAt: new Date().toISOString()});
                        }
                    });

                    // إضافة حساب العميل
                    const customerAccount = {
                        id: Date.now() + Math.random(),
                        code: accountCode,
                        name: customer.name,
                        type: 'assets',
                        category: 'customers',
                        parentCode: '11030',
                        parentName: 'العملاء',
                        balance: 0,
                        status: 'active',
                        linkedType: 'customer',
                        linkedId: customer.id,
                        level: 4,
                        createdAt: new Date().toISOString()
                    };
                    accounts.push(customerAccount);
                    addedCount++;
                }
            });

            // إصلاح حسابات الموردين المفقودة
            suppliers.forEach(supplier => {
                const accountCode = `21030${String(supplier.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    console.log(`➕ إضافة حساب المورد المفقود: ${supplier.name} (${accountCode})`);

                    // إضافة الحسابات الأساسية أولاً
                    const basicAccounts = [
                        { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, balance: 0, status: 'active', parentCode: null },
                        { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', category: 'suppliers', level: 2, balance: 0, status: 'active', parentCode: '2' }
                    ];

                    basicAccounts.forEach(basicAccount => {
                        const basicExists = accounts.find(acc => acc.code === basicAccount.code);
                        if (!basicExists) {
                            accounts.push({...basicAccount, createdAt: new Date().toISOString()});
                        }
                    });

                    // إضافة حساب المورد
                    const supplierAccount = {
                        id: Date.now() + Math.random(),
                        code: accountCode,
                        name: supplier.name,
                        type: 'liabilities',
                        category: 'suppliers',
                        parentCode: '21030',
                        parentName: 'الموردون',
                        balance: 0,
                        status: 'active',
                        linkedType: 'supplier',
                        linkedId: supplier.id,
                        level: 3,
                        createdAt: new Date().toISOString()
                    };
                    accounts.push(supplierAccount);
                    addedCount++;
                }
            });

            // حفظ الحسابات المحدثة
            if (addedCount > 0) {
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
                console.log(`✅ تم إضافة ${addedCount} حساب مفقود`);

                // إعادة تحميل الجدول
                loadAccountsFromCentralSystem();

                alert(`تم إصلاح الروابط المفقودة!\nتم إضافة ${addedCount} حساب مفقود.`);
            } else {
                console.log('✅ جميع الروابط موجودة');
                alert('جميع الروابط موجودة بالفعل!');
            }
        }

        // دالة فحص البيانات المحفوظة
        function debugStorageData() {
            console.log('🔍 فحص البيانات المحفوظة في localStorage:');
            console.log('');

            // فحص جميع مفاتيح localStorage
            const keys = Object.keys(localStorage);
            console.log('📋 جميع المفاتيح المحفوظة:', keys);

            // فحص بيانات العملاء
            const customersKey1 = localStorage.getItem('monjizCustomers');
            const customersKey2 = localStorage.getItem('customers');
            const customersKey3 = localStorage.getItem('customersData');

            console.log('');
            console.log('👥 بيانات العملاء:');
            console.log('   monjizCustomers:', customersKey1 ? JSON.parse(customersKey1).length + ' عميل' : 'غير موجود');
            console.log('   customers:', customersKey2 ? JSON.parse(customersKey2).length + ' عميل' : 'غير موجود');
            console.log('   customersData:', customersKey3 ? JSON.parse(customersKey3).length + ' عميل' : 'غير موجود');

            if (customersKey1) {
                const customers = JSON.parse(customersKey1);
                console.log('   تفاصيل العملاء في monjizCustomers:');
                customers.forEach((customer, index) => {
                    console.log(`     ${index + 1}. ${customer.name} (ID: ${customer.id})`);
                });
            }

            // فحص بيانات الموردين
            const suppliersKey1 = localStorage.getItem('monjizSuppliers');
            const suppliersKey2 = localStorage.getItem('suppliers');
            const suppliersKey3 = localStorage.getItem('suppliersData');

            console.log('');
            console.log('🏪 بيانات الموردين:');
            console.log('   monjizSuppliers:', suppliersKey1 ? JSON.parse(suppliersKey1).length + ' مورد' : 'غير موجود');
            console.log('   suppliers:', suppliersKey2 ? JSON.parse(suppliersKey2).length + ' مورد' : 'غير موجود');
            console.log('   suppliersData:', suppliersKey3 ? JSON.parse(suppliersKey3).length + ' مورد' : 'غير موجود');

            if (suppliersKey1) {
                const suppliers = JSON.parse(suppliersKey1);
                console.log('   تفاصيل الموردين في monjizSuppliers:');
                suppliers.forEach((supplier, index) => {
                    console.log(`     ${index + 1}. ${supplier.name} (ID: ${supplier.id})`);
                });
            }

            // فحص بيانات الحسابات
            const accountsKey1 = localStorage.getItem('chartOfAccounts');
            const accountsKey2 = localStorage.getItem('monjizAccounts');

            console.log('');
            console.log('📊 بيانات الحسابات:');
            console.log('   chartOfAccounts:', accountsKey1 ? JSON.parse(accountsKey1).length + ' حساب' : 'غير موجود');
            console.log('   monjizAccounts:', accountsKey2 ? JSON.parse(accountsKey2).length + ' حساب' : 'غير موجود');

            if (accountsKey1) {
                const accounts = JSON.parse(accountsKey1);
                const customerAccounts = accounts.filter(acc => acc.category === 'customers');
                const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
                console.log(`   حسابات العملاء: ${customerAccounts.length}`);
                console.log(`   حسابات الموردين: ${supplierAccounts.length}`);

                if (customerAccounts.length > 0) {
                    console.log('   تفاصيل حسابات العملاء:');
                    customerAccounts.forEach(acc => {
                        console.log(`     ${acc.code} - ${acc.name}`);
                    });
                }

                if (supplierAccounts.length > 0) {
                    console.log('   تفاصيل حسابات الموردين:');
                    supplierAccounts.forEach(acc => {
                        console.log(`     ${acc.code} - ${acc.name}`);
                    });
                }
            }
        }

        // دالة إعادة بناء الروابط من الصفر
        function rebuildAllLinks() {
            console.log('🔄 إعادة بناء جميع الروابط من الصفر...');

            // البحث عن البيانات في جميع المفاتيح المحتملة
            let customers = [];
            let suppliers = [];

            // محاولة تحميل العملاء من مفاتيح مختلفة
            const customerKeys = ['monjizCustomers', 'customers', 'customersData'];
            for (const key of customerKeys) {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                            customers = parsed;
                            console.log(`✅ تم العثور على ${customers.length} عميل في ${key}`);
                            break;
                        }
                    } catch (e) {
                        console.warn(`⚠️ خطأ في قراءة ${key}:`, e);
                    }
                }
            }

            // محاولة تحميل الموردين من مفاتيح مختلفة
            const supplierKeys = ['monjizSuppliers', 'suppliers', 'suppliersData'];
            for (const key of supplierKeys) {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                            suppliers = parsed;
                            console.log(`✅ تم العثور على ${suppliers.length} مورد في ${key}`);
                            break;
                        }
                    } catch (e) {
                        console.warn(`⚠️ خطأ في قراءة ${key}:`, e);
                    }
                }
            }

            console.log(`📊 إجمالي البيانات: ${customers.length} عميل، ${suppliers.length} مورد`);

            if (customers.length === 0 && suppliers.length === 0) {
                alert('لم يتم العثور على أي عملاء أو موردين لربطهم!');
                return;
            }

            // بناء الحسابات من الصفر
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إضافة الحسابات الأساسية
            const basicAccounts = [
                { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, balance: 0, status: 'active', parentCode: null },
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, balance: 0, status: 'active', parentCode: '1' },
                { id: 1103, code: '1103', name: 'المدينون', type: 'assets', level: 3, balance: 0, status: 'active', parentCode: '11' },
                { id: 11030, code: '11030', name: 'العملاء', type: 'assets', category: 'customers', level: 4, balance: 0, status: 'active', parentCode: '1103' },
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, balance: 0, status: 'active', parentCode: null },
                { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', category: 'suppliers', level: 2, balance: 0, status: 'active', parentCode: '2' }
            ];

            basicAccounts.forEach(basicAccount => {
                const exists = accounts.find(acc => acc.code === basicAccount.code);
                if (!exists) {
                    accounts.push({...basicAccount, createdAt: new Date().toISOString()});
                    console.log(`➕ أضيف الحساب الأساسي: ${basicAccount.name} (${basicAccount.code})`);
                }
            });

            let addedCount = 0;

            // إضافة حسابات العملاء
            customers.forEach(customer => {
                const accountCode = `11030${String(customer.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    const customerAccount = {
                        id: Date.now() + Math.random(),
                        code: accountCode,
                        name: customer.name,
                        type: 'assets',
                        category: 'customers',
                        parentCode: '11030',
                        parentName: 'العملاء',
                        balance: 0,
                        status: 'active',
                        linkedType: 'customer',
                        linkedId: customer.id,
                        level: 5,
                        createdAt: new Date().toISOString()
                    };
                    accounts.push(customerAccount);
                    addedCount++;
                    console.log(`➕ أضيف حساب العميل: ${customer.name} (${accountCode})`);
                }
            });

            // إضافة حسابات الموردين
            suppliers.forEach(supplier => {
                const accountCode = `21030${String(supplier.id).padStart(3, '0')}`;
                const exists = accounts.find(acc => acc.code === accountCode);

                if (!exists) {
                    const supplierAccount = {
                        id: Date.now() + Math.random(),
                        code: accountCode,
                        name: supplier.name,
                        type: 'liabilities',
                        category: 'suppliers',
                        parentCode: '21030',
                        parentName: 'الموردون',
                        balance: 0,
                        status: 'active',
                        linkedType: 'supplier',
                        linkedId: supplier.id,
                        level: 3,
                        createdAt: new Date().toISOString()
                    };
                    accounts.push(supplierAccount);
                    addedCount++;
                    console.log(`➕ أضيف حساب المورد: ${supplier.name} (${accountCode})`);
                }
            });

            // حفظ الحسابات المحدثة
            localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

            console.log(`✅ تم إعادة بناء الروابط. أضيف ${addedCount} حساب جديد`);

            // إعادة تحميل الجدول
            loadAccountsFromCentralSystem();

            alert(`تم إعادة بناء جميع الروابط!\nتم إضافة ${addedCount} حساب جديد.\nإجمالي العملاء: ${customers.length}\nإجمالي الموردين: ${suppliers.length}`);
        }

        // دالة فحص مباشر للبيانات
        function checkDataNow() {
            console.log('🔍 فحص مباشر للبيانات الآن...');
            console.log('');

            // فحص جميع مفاتيح localStorage
            console.log('📋 جميع مفاتيح localStorage:');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                try {
                    const parsed = JSON.parse(value);
                    if (Array.isArray(parsed)) {
                        console.log(`   ${key}: ${parsed.length} عنصر`);
                        if (parsed.length > 0 && (key.includes('customer') || key.includes('supplier'))) {
                            console.log(`     أول عنصر:`, parsed[0]);
                            console.log(`     آخر عنصر:`, parsed[parsed.length - 1]);
                        }
                    } else {
                        console.log(`   ${key}: كائن`);
                    }
                } catch (e) {
                    console.log(`   ${key}: نص (${value.length} حرف)`);
                }
            }

            console.log('');
            console.log('🔍 البحث عن "خيبر" و "المنجم33":');

            // البحث في جميع البيانات
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);

                if (value.includes('خيبر') || value.includes('المنجم33')) {
                    console.log(`✅ وجد في ${key}:`, value);
                }
            }
        }

        // دالة الإصلاح المباشر
        function directFix() {
            // إضافة البيانات مباشرة بدون تعقيدات

            // 1. إضافة العملاء
            const customers = [
                {
                    id: 1,
                    name: 'خيبر',
                    type: 'individual',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'خيبر',
                    createdAt: '2024-01-01'
                }
            ];
            localStorage.setItem('monjizCustomers', JSON.stringify(customers));

            // 2. إضافة الموردين
            const suppliers = [
                {
                    id: 1,
                    name: 'المنجم33',
                    type: 'company',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض',
                    createdAt: '2024-01-01'
                }
            ];
            localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));

            // 3. إضافة الحسابات في دليل الحسابات
            const accounts = [
                // الحسابات الأساسية
                { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, balance: 0, status: 'active', parentCode: null, createdAt: '2024-01-01' },
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, balance: 0, status: 'active', parentCode: '1', createdAt: '2024-01-01' },
                { id: 1103, code: '1103', name: 'المدينون', type: 'assets', level: 3, balance: 0, status: 'active', parentCode: '11', createdAt: '2024-01-01' },
                { id: 11030, code: '11030', name: 'العملاء', type: 'assets', category: 'customers', level: 4, balance: 0, status: 'active', parentCode: '1103', createdAt: '2024-01-01' },
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, balance: 0, status: 'active', parentCode: null, createdAt: '2024-01-01' },
                { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', category: 'suppliers', level: 2, balance: 0, status: 'active', parentCode: '2', createdAt: '2024-01-01' },

                // حساب خيبر
                {
                    id: 110301,
                    code: '********',
                    name: 'خيبر',
                    type: 'assets',
                    category: 'customers',
                    parentCode: '11030',
                    parentName: 'العملاء',
                    balance: 0,
                    status: 'active',
                    linkedType: 'customer',
                    linkedId: 1,
                    level: 5,
                    createdAt: '2024-01-01'
                },

                // حساب المنجم33
                {
                    id: 210301,
                    code: '********',
                    name: 'المنجم33',
                    type: 'liabilities',
                    category: 'suppliers',
                    parentCode: '21030',
                    parentName: 'الموردون',
                    balance: 0,
                    status: 'active',
                    linkedType: 'supplier',
                    linkedId: 1,
                    level: 3,
                    createdAt: '2024-01-01'
                }
            ];

            localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

            // 4. إعادة تحميل الجدول
            loadAccountsFromCentralSystem();

            // 5. رسالة تأكيد
            alert('تم الإصلاح بنجاح!\n\nتم إضافة:\n- خيبر كعميل\n- المنجم33 كمورد\n- حساباتهما في دليل الحسابات\n\nتحقق من الجدول الآن!');
        }

        // دالة بسيطة لإضافة خيبر والمنجم33
        function addSimpleTestData() {
            try {
                // إضافة خيبر كعميل
                let customers = JSON.parse(localStorage.getItem('monjizCustomers') || '[]');

                const khaybarExists = customers.find(c => c.name === 'خيبر');
                if (!khaybarExists) {
                    customers.push({
                        id: customers.length + 1,
                        name: 'خيبر',
                        type: 'individual',
                        phone: '**********',
                        email: '<EMAIL>',
                        address: 'خيبر',
                        createdAt: new Date().toLocaleDateString('ar-SA')
                    });
                    localStorage.setItem('monjizCustomers', JSON.stringify(customers));
                }

                // إضافة المنجم33 كمورد
                let suppliers = JSON.parse(localStorage.getItem('monjizSuppliers') || '[]');

                const manjamExists = suppliers.find(s => s.name === 'المنجم33');
                if (!manjamExists) {
                    suppliers.push({
                        id: suppliers.length + 1,
                        name: 'المنجم33',
                        type: 'company',
                        phone: '**********',
                        email: '<EMAIL>',
                        address: 'الرياض',
                        createdAt: new Date().toLocaleDateString('ar-SA')
                    });
                    localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));
                }

                // إضافة حساباتهم في دليل الحسابات
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts') || '[]');

                // إضافة الحسابات الأساسية أولاً
                const basicAccounts = [
                    { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, balance: 0, status: 'active', parentCode: null },
                    { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, balance: 0, status: 'active', parentCode: '1' },
                    { id: 1103, code: '1103', name: 'المدينون', type: 'assets', level: 3, balance: 0, status: 'active', parentCode: '11' },
                    { id: 11030, code: '11030', name: 'العملاء', type: 'assets', category: 'customers', level: 4, balance: 0, status: 'active', parentCode: '1103' },
                    { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, balance: 0, status: 'active', parentCode: null },
                    { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', category: 'suppliers', level: 2, balance: 0, status: 'active', parentCode: '2' }
                ];

                basicAccounts.forEach(basic => {
                    if (!accounts.find(acc => acc.code === basic.code)) {
                        accounts.push({...basic, createdAt: new Date().toISOString()});
                    }
                });

                // إضافة حساب خيبر
                const khaybar = customers.find(c => c.name === 'خيبر');
                if (khaybar) {
                    const khaybarCode = `11030${String(khaybar.id).padStart(3, '0')}`;
                    if (!accounts.find(acc => acc.code === khaybarCode)) {
                        accounts.push({
                            id: Date.now(),
                            code: khaybarCode,
                            name: 'خيبر',
                            type: 'assets',
                            category: 'customers',
                            parentCode: '11030',
                            parentName: 'العملاء',
                            balance: 0,
                            status: 'active',
                            linkedType: 'customer',
                            linkedId: khaybar.id,
                            level: 5,
                            createdAt: new Date().toISOString()
                        });
                    }
                }

                // إضافة حساب المنجم33
                const manjam = suppliers.find(s => s.name === 'المنجم33');
                if (manjam) {
                    const manjamCode = `21030${String(manjam.id).padStart(3, '0')}`;
                    if (!accounts.find(acc => acc.code === manjamCode)) {
                        accounts.push({
                            id: Date.now() + 1,
                            code: manjamCode,
                            name: 'المنجم33',
                            type: 'liabilities',
                            category: 'suppliers',
                            parentCode: '21030',
                            parentName: 'الموردون',
                            balance: 0,
                            status: 'active',
                            linkedType: 'supplier',
                            linkedId: manjam.id,
                            level: 3,
                            createdAt: new Date().toISOString()
                        });
                    }
                }

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                // إعادة تحميل الجدول
                loadAccountsFromCentralSystem();

                alert(`تم إضافة البيانات بنجاح!\n\nالعملاء: ${customers.length}\nالموردين: ${suppliers.length}\nالحسابات: ${accounts.length}`);

            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            }
        }

        // دالة إضافة حسابات تجريبية
        function addTestAccounts() {
            console.log('🧪 إضافة حسابات تجريبية...');

            // أولاً: إضافة العملاء والموردين إلى قواعد البيانات الخاصة بهم
            let customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            let suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];

            // إضافة خيبر كعميل إذا لم يكن موجوداً
            const khaybarExists = customers.find(c => c.name === 'خيبر');
            if (!khaybarExists) {
                const khaybarCustomer = {
                    id: customers.length + 1,
                    name: 'خيبر',
                    type: 'individual',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'خيبر، المملكة العربية السعودية',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                };
                customers.push(khaybarCustomer);
                localStorage.setItem('monjizCustomers', JSON.stringify(customers));
                console.log('➕ أضيف خيبر كعميل');
            }

            // إضافة المنجم33 كمورد إذا لم يكن موجوداً
            const manjamExists = suppliers.find(s => s.name === 'المنجم33');
            if (!manjamExists) {
                const manjamSupplier = {
                    id: suppliers.length + 1,
                    name: 'المنجم33',
                    type: 'company',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض، المملكة العربية السعودية',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                };
                suppliers.push(manjamSupplier);
                localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));
                console.log('➕ أضيف المنجم33 كمورد');
            }

            // ثانياً: إضافة الحسابات في دليل الحسابات
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إضافة الحسابات الأساسية
            const basicAccounts = [
                { id: 1, code: '1', name: 'الأصول', type: 'assets', level: 1, balance: 0, status: 'active', parentCode: null },
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, balance: 0, status: 'active', parentCode: '1' },
                { id: 1103, code: '1103', name: 'المدينون', type: 'assets', level: 3, balance: 0, status: 'active', parentCode: '11' },
                { id: 11030, code: '11030', name: 'العملاء', type: 'assets', category: 'customers', level: 4, balance: 0, status: 'active', parentCode: '1103' },
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', level: 1, balance: 0, status: 'active', parentCode: null },
                { id: 21030, code: '21030', name: 'الموردون', type: 'liabilities', category: 'suppliers', level: 2, balance: 0, status: 'active', parentCode: '2' }
            ];

            basicAccounts.forEach(basicAccount => {
                const exists = accounts.find(acc => acc.code === basicAccount.code);
                if (!exists) {
                    accounts.push({...basicAccount, createdAt: new Date().toISOString()});
                }
            });

            // إضافة حسابات العملاء والموردين
            const khaybarCustomer = customers.find(c => c.name === 'خيبر');
            const manjamSupplier = suppliers.find(s => s.name === 'المنجم33');

            const testAccounts = [];

            if (khaybarCustomer) {
                const khaybarAccountCode = `11030${String(khaybarCustomer.id).padStart(3, '0')}`;
                const khaybarAccountExists = accounts.find(acc => acc.code === khaybarAccountCode);
                if (!khaybarAccountExists) {
                    testAccounts.push({
                        id: Date.now(),
                        code: khaybarAccountCode,
                        name: 'خيبر',
                        type: 'assets',
                        category: 'customers',
                        parentCode: '11030',
                        parentName: 'العملاء',
                        balance: 0,
                        status: 'active',
                        linkedType: 'customer',
                        linkedId: khaybarCustomer.id,
                        level: 5,
                        createdAt: new Date().toISOString()
                    });
                }
            }

            if (manjamSupplier) {
                const manjamAccountCode = `21030${String(manjamSupplier.id).padStart(3, '0')}`;
                const manjamAccountExists = accounts.find(acc => acc.code === manjamAccountCode);
                if (!manjamAccountExists) {
                    testAccounts.push({
                        id: Date.now() + 1,
                        code: manjamAccountCode,
                        name: 'المنجم33',
                        type: 'liabilities',
                        category: 'suppliers',
                        parentCode: '21030',
                        parentName: 'الموردون',
                        balance: 0,
                        status: 'active',
                        linkedType: 'supplier',
                        linkedId: manjamSupplier.id,
                        level: 3,
                        createdAt: new Date().toISOString()
                    });
                }
            }

            testAccounts.forEach(testAccount => {
                accounts.push(testAccount);
                console.log(`➕ أضيف حساب: ${testAccount.name} (${testAccount.code})`);
            });

            // حفظ الحسابات
            localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

            console.log(`✅ تم إضافة الحسابات التجريبية`);

            // إعادة تحميل الجدول
            loadAccountsFromCentralSystem();

            alert(`تم إضافة البيانات التجريبية!\n\nالعملاء: ${customers.length}\nالموردين: ${suppliers.length}\nالحسابات المضافة: ${testAccounts.length}`);
        }

        // دالة فحص بسيطة للبيانات
        function simpleDataCheck() {
            try {
                let message = 'تقرير فحص البيانات:\n\n';

                // فحص العملاء
                const customers = localStorage.getItem('monjizCustomers');
                if (customers) {
                    const customersList = JSON.parse(customers);
                    message += `العملاء: ${customersList.length} عميل\n`;
                    customersList.forEach((customer, index) => {
                        message += `  ${index + 1}. ${customer.name}\n`;
                    });
                } else {
                    message += 'العملاء: لا توجد بيانات\n';
                }

                message += '\n';

                // فحص الموردين
                const suppliers = localStorage.getItem('monjizSuppliers');
                if (suppliers) {
                    const suppliersList = JSON.parse(suppliers);
                    message += `الموردين: ${suppliersList.length} مورد\n`;
                    suppliersList.forEach((supplier, index) => {
                        message += `  ${index + 1}. ${supplier.name}\n`;
                    });
                } else {
                    message += 'الموردين: لا توجد بيانات\n';
                }

                message += '\n';

                // فحص الحسابات
                const accounts = localStorage.getItem('chartOfAccounts');
                if (accounts) {
                    const accountsList = JSON.parse(accounts);
                    message += `الحسابات: ${accountsList.length} حساب\n`;

                    const customerAccounts = accountsList.filter(acc => acc.category === 'customers');
                    const supplierAccounts = accountsList.filter(acc => acc.category === 'suppliers');

                    message += `حسابات العملاء: ${customerAccounts.length}\n`;
                    customerAccounts.forEach(acc => {
                        message += `  ${acc.code} - ${acc.name}\n`;
                    });

                    message += `حسابات الموردين: ${supplierAccounts.length}\n`;
                    supplierAccounts.forEach(acc => {
                        message += `  ${acc.code} - ${acc.name}\n`;
                    });
                } else {
                    message += 'الحسابات: لا توجد بيانات\n';
                }

                // البحث عن خيبر والمنجم33
                message += '\n--- البحث عن خيبر والمنجم33 ---\n';

                let foundKhaybar = false;
                let foundManjam = false;

                if (customers) {
                    const customersList = JSON.parse(customers);
                    const khaybar = customersList.find(c => c.name.includes('خيبر'));
                    if (khaybar) {
                        foundKhaybar = true;
                        message += `✅ خيبر موجود كعميل (ID: ${khaybar.id})\n`;
                    }
                }

                if (suppliers) {
                    const suppliersList = JSON.parse(suppliers);
                    const manjam = suppliersList.find(s => s.name.includes('المنجم33'));
                    if (manjam) {
                        foundManjam = true;
                        message += `✅ المنجم33 موجود كمورد (ID: ${manjam.id})\n`;
                    }
                }

                if (!foundKhaybar) {
                    message += '❌ خيبر غير موجود في العملاء\n';
                }

                if (!foundManjam) {
                    message += '❌ المنجم33 غير موجود في الموردين\n';
                }

                alert(message);

            } catch (error) {
                alert('حدث خطأ في فحص البيانات: ' + error.message);
            }
        }

        // دالة عرض تقرير البيانات في نافذة
        function showDataReport() {
            let report = '🔍 تقرير فحص البيانات:\n\n';

            // فحص جميع مفاتيح localStorage
            report += '📋 جميع البيانات المحفوظة:\n';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                try {
                    const parsed = JSON.parse(value);
                    if (Array.isArray(parsed)) {
                        report += `   ${key}: ${parsed.length} عنصر\n`;
                        if (parsed.length > 0 && (key.includes('customer') || key.includes('supplier'))) {
                            report += `     أول عنصر: ${parsed[0].name || 'بدون اسم'}\n`;
                            if (parsed.length > 1) {
                                report += `     آخر عنصر: ${parsed[parsed.length - 1].name || 'بدون اسم'}\n`;
                            }
                        }
                    } else {
                        report += `   ${key}: كائن\n`;
                    }
                } catch (e) {
                    report += `   ${key}: نص (${value.length} حرف)\n`;
                }
            }

            report += '\n🔍 البحث عن "خيبر" و "المنجم33":\n';
            let foundKhaybar = false;
            let foundManjam = false;

            // البحث في جميع البيانات
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);

                if (value.includes('خيبر')) {
                    foundKhaybar = true;
                    report += `✅ وجد "خيبر" في ${key}\n`;
                }

                if (value.includes('المنجم33')) {
                    foundManjam = true;
                    report += `✅ وجد "المنجم33" في ${key}\n`;
                }
            }

            if (!foundKhaybar) {
                report += '❌ لم يتم العثور على "خيبر"\n';
            }

            if (!foundManjam) {
                report += '❌ لم يتم العثور على "المنجم33"\n';
            }

            // فحص الحسابات
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            const customerAccounts = accounts.filter(acc => acc.category === 'customers');
            const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');

            report += `\n📊 إحصائيات الحسابات:\n`;
            report += `   إجمالي الحسابات: ${accounts.length}\n`;
            report += `   حسابات العملاء: ${customerAccounts.length}\n`;
            report += `   حسابات الموردين: ${supplierAccounts.length}\n`;

            if (customerAccounts.length > 0) {
                report += '\n👥 حسابات العملاء الموجودة:\n';
                customerAccounts.forEach(acc => {
                    report += `   ${acc.code} - ${acc.name}\n`;
                });
            }

            if (supplierAccounts.length > 0) {
                report += '\n🏪 حسابات الموردين الموجودة:\n';
                supplierAccounts.forEach(acc => {
                    report += `   ${acc.code} - ${acc.name}\n`;
                });
            }

            // عرض التقرير في نافذة منبثقة
            alert(report);

            // طباعة التقرير في وحدة التحكم أيضاً
            console.log(report);
        }

        // دالة مراقبة التغييرات في localStorage
        function monitorStorageChanges() {
            console.log('👁️ بدء مراقبة التغييرات في localStorage...');

            // حفظ الحالة الحالية
            const currentState = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                currentState[key] = localStorage.getItem(key);
            }

            // مراقبة التغييرات كل ثانية
            const monitor = setInterval(() => {
                let hasChanges = false;

                // فحص المفاتيح الجديدة أو المحدثة
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const currentValue = localStorage.getItem(key);

                    if (currentState[key] !== currentValue) {
                        console.log(`🔄 تغيير في ${key}`);
                        if (key.includes('customer') || key.includes('supplier') || key.includes('Account')) {
                            console.log(`   القيمة الجديدة:`, currentValue);
                        }
                        currentState[key] = currentValue;
                        hasChanges = true;
                    }
                }

                // فحص المفاتيح المحذوفة
                Object.keys(currentState).forEach(key => {
                    if (!localStorage.getItem(key)) {
                        console.log(`🗑️ حذف ${key}`);
                        delete currentState[key];
                        hasChanges = true;
                    }
                });

                if (hasChanges) {
                    console.log('🔄 تم اكتشاف تغييرات، إعادة تحميل الحسابات...');
                    setTimeout(() => {
                        loadAccountsFromCentralSystem();
                    }, 1000);
                }
            }, 2000);

            // إيقاف المراقبة بعد 5 دقائق
            setTimeout(() => {
                clearInterval(monitor);
                console.log('⏹️ تم إيقاف مراقبة localStorage');
            }, 300000);

            console.log('✅ تم بدء المراقبة (ستتوقف تلقائياً بعد 5 دقائق)');
        }

        // إضافة زر مسح البيانات للاختبار (يمكن حذفه لاحقاً)
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة زر مسح البيانات في وحدة التحكم
            console.log('💡 أوامر وحدة التحكم المتاحة:');
            console.log('   clearAllData() - لمسح جميع البيانات المحفوظة');
            console.log('   testAccountSystem() - لاختبار النظام');
            console.log('   diagnoseAccountSystem() - تشخيص سريع للنظام');
            console.log('   testAddFrenchBank() - اختبار إضافة البنك الفرنسي');
            console.log('   quickAddAccountTest() - إضافة حساب اختبار سريع');
            console.log('   clearTestAccounts() - مسح حسابات الاختبار');
            console.log('   updateAccountsTableSimple() - تحديث الجدول بطريقة مبسطة');
            console.log('   showLinkingInfo() - لعرض معلومات الربط');
            console.log('   fixMissingLinks() - لإصلاح الروابط المفقودة');
            console.log('   debugStorageData() - لفحص البيانات المحفوظة');
            console.log('   rebuildAllLinks() - لإعادة بناء جميع الروابط');
            console.log('   checkDataNow() - فحص مباشر للبيانات');
            console.log('   addTestAccounts() - إضافة حسابات تجريبية');
            console.log('   monitorStorageChanges() - مراقبة التغييرات');
        });

        // دالة فتح نافذة إضافة حساب جديد
        function showAddAccountModal() {
            console.log('فتح نافذة إضافة حساب جديد');
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.classList.add('active');
                loadParentAccounts();

                // مسح النموذج
                document.getElementById('add-account-form').reset();

                // التركيز على أول حقل
                document.getElementById('account-code').focus();
            }
        }

        // دالة إغلاق نافذة إضافة حساب جديد
        function closeAddAccountModal() {
            console.log('إغلاق نافذة إضافة حساب جديد');
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }

        // دالة تحميل الحسابات الأب
        function loadParentAccounts() {
            const parentSelect = document.getElementById('parent-account');
            const accountTypeSelect = document.getElementById('account-type');

            if (!parentSelect) return;

            // تحميل الحسابات الحالية
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // تصفية الحسابات حسب النوع المحدد
            const selectedType = accountTypeSelect.value;
            if (selectedType) {
                accounts = accounts.filter(account => account.type === selectedType);
            }

            // مسح الخيارات الحالية
            parentSelect.innerHTML = '<option value="">حساب رئيسي</option>';

            // إضافة الحسابات كخيارات
            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.code;

                // إضافة مسافات بادئة حسب المستوى
                const indent = '  '.repeat((account.level || 1) - 1);
                option.textContent = `${indent}${account.code} - ${account.name}`;

                parentSelect.appendChild(option);
            });
        }

        // دالة حفظ الحساب الجديد المبسطة
        function saveNewAccountSimple() {
            console.log('🔄 حفظ حساب جديد (مبسط)...');

            // جمع البيانات من النموذج المبسط
            const code = document.getElementById('simple-account-code')?.value.trim();
            const name = document.getElementById('simple-account-name')?.value.trim();
            const type = document.getElementById('simple-account-type')?.value;
            const parentCode = document.getElementById('simple-parent-account')?.value;
            const balance = parseFloat(document.getElementById('simple-account-balance')?.value) || 0;

            console.log('📝 بيانات الحساب المبسط:', { code, name, type, parentCode, balance });

            // التحقق من صحة البيانات
            if (!code) {
                alert('يرجى إدخال رقم الحساب');
                return;
            }

            if (!name) {
                alert('يرجى إدخال اسم الحساب');
                return;
            }

            if (!type) {
                alert('يرجى اختيار نوع الحساب');
                return;
            }

            // إنشاء كائن الحساب
            const account = {
                id: Date.now(),
                code: code,
                name: name,
                type: type,
                balance: balance,
                status: 'active',
                level: parentCode ? 2 : 1,
                parentCode: parentCode || null,
                createdAt: new Date().toISOString()
            };

            try {
                console.log('💾 حفظ الحساب الجديد:', account);

                // محاولة الحفظ في النظام المركزي أولاً
                let savedAccount = null;
                if (window.dataManager) {
                    savedAccount = window.dataManager.addAccount(account);
                    console.log('✅ تم حفظ الحساب في النظام المركزي:', savedAccount);
                }

                // إذا فشل النظام المركزي، احفظ مباشرة في localStorage
                if (!savedAccount) {
                    console.log('⚠️ النظام المركزي غير متاح، الحفظ المباشر...');

                    // تحميل الحسابات الحالية
                    let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                    // التحقق من عدم تكرار رقم الحساب
                    if (accounts.some(a => a.code === code)) {
                        alert('رقم الحساب موجود بالفعل. يرجى استخدام رقم آخر.');
                        return;
                    }

                    // إضافة الحساب الجديد
                    accounts.push(account);

                    // حفظ الحسابات
                    localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                    localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                    savedAccount = account;
                }

                // إرسال إشعارات التحديث
                if (window.BroadcastChannel) {
                    const channel = new BroadcastChannel('monjiz-updates');
                    channel.postMessage({
                        type: 'accounts-updated',
                        data: savedAccount
                    });
                }

                // إشعار localStorage
                localStorage.setItem('monjizDataUpdate', JSON.stringify({
                    type: 'accounts-updated',
                    timestamp: Date.now(),
                    data: savedAccount
                }));

                // إغلاق النافذة
                closeNewAccountModal();

                // تحديث النظامين فوراً
                setTimeout(() => {
                    console.log('🔄 تحديث النظام الكلاسيكي...');
                    loadAccountsFromCentralSystem();
                }, 100);

                if (professionalChartOfAccounts) {
                    setTimeout(() => {
                        console.log('🔄 تحديث النظام الاحترافي...');
                        professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                        updateProfessionalStats();
                        renderProfessionalTree();
                    }, 200);
                }

                alert(`تم إضافة الحساب بنجاح!\n\nرقم الحساب: ${code}\nاسم الحساب: ${name}\n\nتحقق من النظامين لرؤية التحديث.`);

            } catch (error) {
                console.error('❌ خطأ في حفظ الحساب:', error);
                alert('حدث خطأ أثناء حفظ الحساب. يرجى المحاولة مرة أخرى.');
            }
        }

        // دالة حفظ الحساب الجديد (محسنة)
        function saveNewAccount() {
            console.log('🔄 بدء حفظ حساب جديد...');

            // جمع البيانات من النموذج
            const code = document.getElementById('account-code').value.trim();
            const name = document.getElementById('account-name').value.trim();
            const type = document.getElementById('account-type').value;
            const parentCode = document.getElementById('parent-account').value;
            const balance = parseFloat(document.getElementById('account-balance').value) || 0;

            console.log('📝 بيانات الحساب الجديد:', { code, name, type, parentCode, balance });

            // التحقق من صحة البيانات
            if (!code) {
                alert('يرجى إدخال رقم الحساب');
                return;
            }

            if (!name) {
                alert('يرجى إدخال اسم الحساب');
                return;
            }

            if (!type) {
                alert('يرجى اختيار نوع الحساب');
                return;
            }

            // تحديد مستوى الحساب
            let level = 1;
            let parentAccountData = null;

            if (parentCode) {
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                parentAccountData = accounts.find(acc => acc.code === parentCode);
                if (parentAccountData) {
                    level = (parentAccountData.level || 1) + 1;
                }
            }

            // إنشاء كائن الحساب
            const account = {
                id: Date.now(),
                code: code,
                name: name,
                type: type,
                balance: balance,
                status: 'active',
                level: level,
                parentCode: parentCode || null,
                parentName: parentAccountData ? parentAccountData.name : null,
                createdAt: new Date().toISOString()
            };

            try {
                console.log('💾 حفظ الحساب الجديد:', account);

                // محاولة الحفظ في النظام المركزي أولاً
                let savedAccount = null;
                if (window.dataManager) {
                    savedAccount = window.dataManager.addAccount(account);
                    console.log('✅ تم حفظ الحساب في النظام المركزي:', savedAccount);
                }

                // إذا فشل النظام المركزي، احفظ مباشرة في localStorage
                if (!savedAccount) {
                    console.log('⚠️ النظام المركزي غير متاح، الحفظ المباشر...');

                    // تحميل الحسابات الحالية
                    let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                    // التحقق من عدم تكرار رقم الحساب
                    if (accounts.some(a => a.code === code)) {
                        alert('رقم الحساب موجود بالفعل. يرجى استخدام رقم آخر.');
                        return;
                    }

                    // إضافة الحساب الجديد
                    accounts.push(account);

                    // حفظ الحسابات في جميع المفاتيح
                    localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                    localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
                    localStorage.setItem('professionalChartOfAccounts', JSON.stringify(accounts));

                    savedAccount = account;
                }

                // إغلاق النافذة
                closeAddAccountModal();

                // إرسال إشعارات التحديث
                if (window.BroadcastChannel) {
                    const channel = new BroadcastChannel('monjiz-updates');
                    channel.postMessage({
                        type: 'accounts-updated',
                        data: savedAccount
                    });
                }

                // إشعار localStorage
                localStorage.setItem('monjizDataUpdate', JSON.stringify({
                    type: 'accounts-updated',
                    timestamp: Date.now(),
                    data: savedAccount
                }));

                // تحديث النظامين فوراً
                setTimeout(() => {
                    console.log('🔄 تحديث النظام الكلاسيكي...');
                    loadAccountsFromCentralSystem();
                }, 100);

                // تحديث النظام الاحترافي
                if (professionalChartOfAccounts) {
                    setTimeout(() => {
                        console.log('🔄 تحديث النظام الاحترافي...');
                        professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                        updateProfessionalStats();
                        renderProfessionalTree();
                        console.log('✅ تم تحديث النظام الاحترافي');
                    }, 100);
                }

                alert(`تم إضافة الحساب بنجاح!\n\nرقم الحساب: ${code}\nاسم الحساب: ${name}\n\nتحقق من النظامين لرؤية التحديث.`);

            } catch (error) {
                console.error('❌ خطأ في حفظ الحساب:', error);
                alert('حدث خطأ أثناء حفظ الحساب. يرجى المحاولة مرة أخرى.');
            }
        }

        // دالة تشخيص سريعة للنظام
        function diagnoseAccountSystem() {
            console.log('🔍 تشخيص سريع لنظام الحسابات...');

            // فحص التخزين المحلي
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            console.log('📊 عدد الحسابات في التخزين:', accounts.length);

            // فحص الجدول
            const tableBody = document.getElementById('accounts-table-body');
            if (tableBody) {
                const rows = tableBody.querySelectorAll('tr');
                console.log('📋 عدد الصفوف في الجدول:', rows.length);
            } else {
                console.error('❌ جدول الحسابات غير موجود');
            }

            // فحص النموذج
            const form = document.getElementById('add-account-form');
            if (form) {
                console.log('✅ نموذج إضافة الحساب موجود');
            } else {
                console.error('❌ نموذج إضافة الحساب غير موجود');
            }

            // عرض آخر 5 حسابات
            if (accounts.length > 0) {
                console.log('📝 آخر 5 حسابات:');
                accounts.slice(-5).forEach(acc => {
                    console.log(`  - ${acc.code}: ${acc.name} (${acc.type})`);
                });
            }

            return {
                accountsCount: accounts.length,
                tableRowsCount: tableBody ? tableBody.querySelectorAll('tr').length : 0,
                formExists: !!form,
                tableExists: !!tableBody
            };
        }

        // دالة اختبار مباشرة لإضافة حساب البنك الفرنسي
        function testAddFrenchBank() {
            console.log('🏦 اختبار إضافة البنك الفرنسي...');

            const testAccount = {
                id: Date.now(),
                code: '1105',
                name: 'البنك الفرنسي',
                type: 'assets',
                balance: 50000,
                status: 'active',
                level: 1,
                parentCode: null,
                parentName: null,
                createdAt: new Date().toISOString()
            };

            try {
                // تحميل الحسابات الحالية
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                console.log('📊 عدد الحسابات قبل الإضافة:', accounts.length);

                // إزالة الحساب إذا كان موجوداً
                accounts = accounts.filter(acc => acc.code !== testAccount.code);
                console.log('🗑️ تم إزالة الحساب المكرر إن وجد');

                // إضافة الحساب الجديد
                accounts.push(testAccount);
                console.log('➕ تم إضافة الحساب، العدد الجديد:', accounts.length);

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
                console.log('💾 تم حفظ الحسابات');

                // التحقق من الحفظ
                const savedAccounts = JSON.parse(localStorage.getItem('chartOfAccounts'));
                const savedAccount = savedAccounts.find(acc => acc.code === testAccount.code);

                if (savedAccount) {
                    console.log('✅ تم التحقق من حفظ البنك الفرنسي:', savedAccount.name);

                    // تحديث الجدول مباشرة بالطريقة المبسطة
                    console.log('🔄 تحديث الجدول بالطريقة المبسطة...');
                    updateAccountsTableSimple(savedAccounts);

                    // التحقق من ظهور الحساب في الجدول
                    setTimeout(() => {
                        const tableBody = document.getElementById('accounts-table-body');
                        if (tableBody) {
                            const rows = tableBody.querySelectorAll('tr');
                            let found = false;

                            rows.forEach(row => {
                                const codeCell = row.querySelector('td:first-child');
                                if (codeCell && codeCell.textContent.includes('1105')) {
                                    found = true;
                                    console.log('✅ تم العثور على البنك الفرنسي في الجدول!');
                                }
                            });

                            if (!found) {
                                console.error('❌ البنك الفرنسي غير موجود في الجدول');
                                console.log('🔍 محتوى الجدول الحالي:');
                                rows.forEach((row, index) => {
                                    const codeCell = row.querySelector('td:first-child');
                                    const nameCell = row.querySelector('td:nth-child(2)');
                                    if (codeCell && nameCell) {
                                        console.log(`  ${index + 1}. ${codeCell.textContent.trim()} - ${nameCell.textContent.trim()}`);
                                    }
                                });
                            }
                        }
                    }, 200);

                    alert('تم إضافة البنك الفرنسي بنجاح! تحقق من وحدة التحكم للتفاصيل.');

                } else {
                    console.error('❌ فشل في حفظ البنك الفرنسي');
                    alert('فشل في حفظ البنك الفرنسي');
                }

            } catch (error) {
                console.error('❌ خطأ في اختبار البنك الفرنسي:', error);
                alert('حدث خطأ: ' + error.message);
            }
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // ربط النموذج بدالة الحفظ
            const form = document.getElementById('add-account-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log('📝 تم إرسال النموذج، استدعاء saveNewAccount...');
                    saveNewAccount();
                });
                console.log('✅ تم ربط النموذج بدالة الحفظ');
            } else {
                console.error('❌ لم يتم العثور على النموذج');
            }

            // تحديث قائمة الحسابات الأب عند تغيير نوع الحساب
            const accountTypeSelect = document.getElementById('account-type');
            if (accountTypeSelect) {
                accountTypeSelect.addEventListener('change', function() {
                    loadParentAccounts();
                });
            }

            // إغلاق النافذة عند النقر خارجها
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeAddAccountModal();
                    }
                });
            }
        });

    </script>

    <!-- سكريبت النظام الاحترافي -->
    <script>
        // ===== وظائف النظام الاحترافي =====

        // متغير النظام الاحترافي
        let professionalChartOfAccounts = null;

        // دالة اختبار التبديل
        function testProfessionalToggle() {
            console.log('🧪 اختبار التبديل بين النظامين...');

            // اختبار التبديل للنظام الاحترافي
            console.log('1️⃣ التبديل للنظام الاحترافي...');
            switchToProfessionalView();

            setTimeout(() => {
                console.log('2️⃣ التحقق من النظام الاحترافي...');
                const professionalSystem = document.getElementById('professional-accounts-system');
                const isVisible = professionalSystem && professionalSystem.style.display === 'block';
                console.log(`النظام الاحترافي مرئي: ${isVisible ? '✅ نعم' : '❌ لا'}`);

                if (isVisible) {
                    alert('✅ النظام الاحترافي يعمل بشكل صحيح!');
                } else {
                    alert('❌ مشكلة في عرض النظام الاحترافي. تحقق من وحدة التحكم.');
                }
            }, 500);
        }

        // دالة تشخيص عناصر الصفحة
        function diagnosePage() {
            console.log('🔍 تشخيص عناصر الصفحة...');

            const elementsToCheck = [
                '.accounts-toolbar',
                '.accounts-display',
                '.accounts-table-modern',
                '#professional-accounts-system',
                '.chart-of-accounts-modern'
            ];

            elementsToCheck.forEach(selector => {
                const element = document.querySelector(selector);
                console.log(`${selector}: ${element ? '✅ موجود' : '❌ غير موجود'}`);
                if (element) {
                    console.log(`  - العرض: ${element.style.display || 'افتراضي'}`);
                    console.log(`  - الرؤية: ${element.style.visibility || 'افتراضي'}`);
                }
            });
        }

        // دالة التبديل إلى النظام الاحترافي
        function switchToProfessionalView() {
            console.log('🔄 التبديل إلى النظام الاحترافي...');

            // تشخيص أولي
            diagnosePage();

            // إخفاء عناصر النظام الكلاسيكي بطرق متعددة
            const elementsToHide = [
                '.accounts-toolbar',
                '.accounts-display',
                '.accounts-table-modern'
            ];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    elements.forEach(element => {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                    });
                    console.log(`✅ تم إخفاء ${elements.length} عنصر من: ${selector}`);
                } else {
                    console.log(`⚠️ لم يتم العثور على: ${selector}`);
                }
            });

            // محاولة إخفاء بطريقة أخرى - البحث عن الحاوي الرئيسي
            const chartContainer = document.querySelector('.chart-of-accounts-modern');
            if (chartContainer) {
                const children = chartContainer.children;
                for (let i = 1; i < children.length; i++) {
                    if (children[i].id !== 'professional-accounts-system') {
                        children[i].style.display = 'none';
                        console.log(`✅ تم إخفاء العنصر رقم ${i}`);
                    }
                }
            }

            // إظهار النظام الاحترافي
            const professionalSystem = document.getElementById('professional-accounts-system');
            if (professionalSystem) {
                professionalSystem.style.display = 'block';
                professionalSystem.style.visibility = 'visible';
                console.log('✅ تم إظهار النظام الاحترافي');

                // تهيئة النظام الاحترافي
                setTimeout(() => {
                    initializeProfessionalSystem();
                }, 100);
            } else {
                console.error('❌ لم يتم العثور على النظام الاحترافي');
                alert('خطأ: لم يتم العثور على النظام الاحترافي. يرجى إعادة تحميل الصفحة.');
            }
        }

        // دالة التبديل إلى النظام الكلاسيكي
        function switchToClassicView() {
            console.log('🔄 التبديل إلى النظام الكلاسيكي...');

            // إظهار عناصر النظام الكلاسيكي
            const elementsToShow = [
                '.accounts-toolbar',
                '.accounts-display',
                '.accounts-table-modern'
            ];

            elementsToShow.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    elements.forEach(element => {
                        element.style.display = 'block';
                        element.style.visibility = 'visible';
                    });
                    console.log(`✅ تم إظهار ${elements.length} عنصر من: ${selector}`);
                } else {
                    console.log(`⚠️ لم يتم العثور على: ${selector}`);
                }
            });

            // إظهار جميع العناصر في الحاوي الرئيسي
            const chartContainer = document.querySelector('.chart-of-accounts-modern');
            if (chartContainer) {
                const children = chartContainer.children;
                for (let i = 1; i < children.length; i++) {
                    if (children[i].id !== 'professional-accounts-system') {
                        children[i].style.display = 'block';
                        children[i].style.visibility = 'visible';
                        console.log(`✅ تم إظهار العنصر رقم ${i}`);
                    }
                }
            }

            // إخفاء النظام الاحترافي
            const professionalSystem = document.getElementById('professional-accounts-system');
            if (professionalSystem) {
                professionalSystem.style.display = 'none';
                professionalSystem.style.visibility = 'hidden';
                console.log('✅ تم إخفاء النظام الاحترافي');
            } else {
                console.error('❌ لم يتم العثور على النظام الاحترافي');
            }
        }

        // تهيئة النظام الاحترافي
        function initializeProfessionalSystem() {
            console.log('🚀 تهيئة النظام الاحترافي...');

            // إنشاء كائن النظام الاحترافي
            if (!professionalChartOfAccounts) {
                professionalChartOfAccounts = new ProfessionalChartOfAccounts();
            }

            // تحديث الإحصائيات
            updateProfessionalStats();

            // عرض الشجرة
            renderProfessionalTree();
        }

        // فئة النظام الاحترافي
        class ProfessionalChartOfAccounts {
            constructor() {
                this.accounts = this.loadProfessionalAccounts();
                this.currentEditId = null;
                this.storageKey = 'professionalChartOfAccounts';
            }

            loadProfessionalAccounts() {
                try {
                    // محاولة تحميل من النظام المركزي أولاً
                    const centralAccounts = localStorage.getItem('chartOfAccounts');
                    const monjizAccounts = localStorage.getItem('monjizAccounts');
                    const professionalAccounts = localStorage.getItem(this.storageKey);

                    console.log('🔄 تحميل الحسابات للنظام الاحترافي...');

                    // إعطاء الأولوية للنظام المركزي
                    if (centralAccounts) {
                        const accounts = JSON.parse(centralAccounts);
                        console.log('✅ تم تحميل الحسابات من النظام المركزي:', accounts.length, 'حساب');
                        return this.convertToHierarchical(accounts);
                    } else if (monjizAccounts) {
                        const accounts = JSON.parse(monjizAccounts);
                        console.log('✅ تم تحميل الحسابات من monjizAccounts:', accounts.length, 'حساب');
                        return this.convertToHierarchical(accounts);
                    } else if (professionalAccounts) {
                        console.log('✅ تم تحميل الحسابات من النظام الاحترافي');
                        return JSON.parse(professionalAccounts);
                    } else {
                        console.log('⚠️ لا توجد حسابات، إنشاء الحسابات الافتراضية');
                        return this.getDefaultProfessionalAccounts();
                    }
                } catch (error) {
                    console.error('❌ خطأ في تحميل الحسابات الاحترافية:', error);
                    return this.getDefaultProfessionalAccounts();
                }
            }

            // تحويل الحسابات المسطحة إلى هيكل هرمي
            convertToHierarchical(flatAccounts) {
                console.log('🔄 تحويل الحسابات إلى هيكل هرمي...');

                // إنشاء خريطة للحسابات
                const accountMap = {};
                flatAccounts.forEach(account => {
                    accountMap[account.code] = {
                        ...account,
                        children: []
                    };
                });

                // بناء الهيكل الهرمي
                const rootAccounts = [];
                flatAccounts.forEach(account => {
                    if (account.parentCode && accountMap[account.parentCode]) {
                        accountMap[account.parentCode].children.push(accountMap[account.code]);
                    } else {
                        rootAccounts.push(accountMap[account.code]);
                    }
                });

                console.log('✅ تم تحويل', flatAccounts.length, 'حساب إلى', rootAccounts.length, 'حساب جذر');
                return rootAccounts;
            }

            getDefaultProfessionalAccounts() {
                return [
                    {
                        id: 1,
                        code: '1',
                        name: 'الأصول',
                        type: 'assets',
                        parentId: null,
                        balance: 0,
                        level: 0,
                        children: [
                            {
                                id: 11,
                                code: '11',
                                name: 'الأصول المتداولة',
                                type: 'assets',
                                parentId: 1,
                                balance: 0,
                                level: 1,
                                children: [
                                    {
                                        id: 111,
                                        code: '1101',
                                        name: 'النقدية والبنوك',
                                        type: 'assets',
                                        parentId: 11,
                                        balance: 0,
                                        level: 2,
                                        children: []
                                    },
                                    {
                                        id: 112,
                                        code: '1102',
                                        name: 'العملاء والذمم المدينة',
                                        type: 'assets',
                                        parentId: 11,
                                        balance: 0,
                                        level: 2,
                                        children: []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        id: 2,
                        code: '2',
                        name: 'الخصوم',
                        type: 'liabilities',
                        parentId: null,
                        balance: 0,
                        level: 0,
                        children: []
                    },
                    {
                        id: 3,
                        code: '3',
                        name: 'حقوق الملكية',
                        type: 'equity',
                        parentId: null,
                        balance: 0,
                        level: 0,
                        children: []
                    },
                    {
                        id: 4,
                        code: '4',
                        name: 'الإيرادات',
                        type: 'revenue',
                        parentId: null,
                        balance: 0,
                        level: 0,
                        children: []
                    },
                    {
                        id: 5,
                        code: '5',
                        name: 'المصروفات',
                        type: 'expenses',
                        parentId: null,
                        balance: 0,
                        level: 0,
                        children: []
                    }
                ];
            }

            saveToStorage() {
                try {
                    // حفظ في النظام الاحترافي
                    localStorage.setItem(this.storageKey, JSON.stringify(this.accounts));

                    // تحويل إلى تنسيق مسطح وحفظ في النظام المركزي
                    const flatAccounts = this.flattenAccounts();
                    localStorage.setItem('chartOfAccounts', JSON.stringify(flatAccounts));
                    localStorage.setItem('monjizAccounts', JSON.stringify(flatAccounts));

                    console.log('✅ تم حفظ الحسابات في كلا النظامين:', flatAccounts.length, 'حساب');

                    // إرسال إشعار التحديث
                    if (window.BroadcastChannel) {
                        const channel = new BroadcastChannel('monjiz-updates');
                        channel.postMessage({
                            type: 'accounts-updated',
                            data: flatAccounts
                        });
                    }

                    // إشعار localStorage
                    localStorage.setItem('monjizDataUpdate', JSON.stringify({
                        type: 'accounts-updated',
                        timestamp: Date.now(),
                        data: flatAccounts
                    }));

                } catch (error) {
                    console.error('❌ خطأ في حفظ الحسابات الاحترافية:', error);
                }
            }

            flattenAccounts(accounts = this.accounts, result = [], parentCode = null) {
                accounts.forEach(account => {
                    // إنشاء نسخة مسطحة من الحساب
                    const flatAccount = {
                        id: account.id,
                        code: account.code,
                        name: account.name,
                        type: account.type,
                        balance: account.balance || 0,
                        level: account.level || 0,
                        parentCode: parentCode,
                        description: account.description || '',
                        isActive: account.isActive !== false,
                        createdAt: account.createdAt || new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };

                    result.push(flatAccount);

                    if (account.children && account.children.length > 0) {
                        this.flattenAccounts(account.children, result, account.code);
                    }
                });
                return result;
            }

            findAccountById(id, accounts = this.accounts) {
                for (const account of accounts) {
                    if (account.id === id) {
                        return account;
                    }
                    if (account.children) {
                        const found = this.findAccountById(id, account.children);
                        if (found) return found;
                    }
                }
                return null;
            }

            addNewAccount(accountData) {
                const newAccount = {
                    id: Date.now(),
                    ...accountData,
                    level: accountData.parentId ? this.getAccountLevel(accountData.parentId) + 1 : 0,
                    children: []
                };

                if (accountData.parentId) {
                    const parent = this.findAccountById(accountData.parentId);
                    if (parent) {
                        parent.children.push(newAccount);
                    }
                } else {
                    this.accounts.push(newAccount);
                }

                this.saveToStorage();
                return newAccount;
            }

            getAccountLevel(parentId) {
                const parent = this.findAccountById(parentId);
                return parent ? parent.level : 0;
            }
        }

        // تحديث الإحصائيات الاحترافية
        function updateProfessionalStats() {
            if (!professionalChartOfAccounts) return;

            const flatAccounts = professionalChartOfAccounts.flattenAccounts();
            const mainAccounts = professionalChartOfAccounts.accounts.length;
            const subAccounts = flatAccounts.length - mainAccounts;
            const totalBalance = flatAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);

            document.getElementById('professionalTotalAccounts').textContent = flatAccounts.length;
            document.getElementById('professionalMainAccounts').textContent = mainAccounts;
            document.getElementById('professionalSubAccounts').textContent = subAccounts;
            document.getElementById('professionalTotalBalance').textContent = formatCurrency(totalBalance);
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // عرض الشجرة الاحترافية
        function renderProfessionalTree() {
            console.log('🌳 عرض الشجرة الاحترافية...');

            if (!professionalChartOfAccounts) {
                console.log('❌ النظام الاحترافي غير مهيأ');
                return;
            }

            const container = document.getElementById('professionalAccountsTree');
            if (!container) {
                console.log('❌ حاوية الشجرة الاحترافية غير موجودة');
                return;
            }

            console.log('📊 عدد الحسابات في النظام الاحترافي:', professionalChartOfAccounts.accounts.length);

            if (professionalChartOfAccounts.accounts.length === 0) {
                container.innerHTML = `
                    <div class="empty-state-pro" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-folder-open" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3 style="margin-bottom: 10px; color: #495057;">لا توجد حسابات</h3>
                        <p>ابدأ بإضافة حساب جديد أو استيراد البيانات</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            // عرض الحسابات الجذر (التي ليس لها والد)
            professionalChartOfAccounts.accounts.forEach(account => {
                // التحقق من الحسابات الجذر بطرق مختلفة
                const isRoot = !account.parentId && !account.parentCode && !account.parent;

                if (isRoot) {
                    console.log('🌟 عرض حساب جذر:', account.code, '-', account.name);
                    container.appendChild(createProfessionalAccountNode(account));
                }
            });

            console.log('✅ تم عرض الشجرة الاحترافية');
        }

        // إنشاء عقدة حساب احترافية
        function createProfessionalAccountNode(account) {
            const node = document.createElement('div');
            node.className = `account-node-pro level-${account.level}`;
            node.dataset.accountId = account.id;

            const hasChildren = account.children && account.children.length > 0;
            const typeClass = `type-${account.type}`;
            const balanceFormatted = formatCurrency(account.balance || 0);

            node.innerHTML = `
                <div class="account-header-pro" onclick="toggleProfessionalAccount(${account.id})" style="padding: 15px 20px; display: flex; align-items: center; justify-content: space-between; cursor: pointer; user-select: none; border-bottom: 1px solid #f1f3f4; transition: all 0.3s ease;">
                    <div class="account-info-pro" style="display: flex; align-items: center; gap: 15px; flex: 1;">
                        ${hasChildren ? `<i class="fas fa-chevron-right expand-icon-pro" style="transition: transform 0.3s ease; color: #6c757d; font-size: 0.9rem;"></i>` : '<span style="width: 16px;"></span>'}
                        <div class="account-code-pro" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; min-width: 80px; text-align: center; font-size: 0.9rem;">${account.code}</div>
                        <div class="account-name-pro" style="font-weight: 600; color: #2c3e50; font-size: 1.1rem;">${account.name}</div>
                        <div class="account-type-pro ${typeClass}" style="padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; font-weight: bold;">${getTypeLabel(account.type)}</div>
                    </div>
                    <div class="account-balance-pro" style="font-weight: bold; color: #2c3e50; min-width: 120px; text-align: left;">${balanceFormatted}</div>
                    <div class="account-actions-pro" onclick="event.stopPropagation()" style="display: flex; gap: 8px;">
                        <button class="action-btn-pro view" onclick="viewProfessionalAccount(${account.id})" title="عرض" style="width: 32px; height: 32px; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; font-size: 0.9rem; background: #17a2b8; color: white;">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn-pro edit" onclick="editProfessionalAccount(${account.id})" title="تعديل" style="width: 32px; height: 32px; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; font-size: 0.9rem; background: #28a745; color: white;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn-pro add" onclick="addProfessionalSubAccount(${account.id})" title="إضافة حساب فرعي" style="width: 32px; height: 32px; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; font-size: 0.9rem; background: #6f42c1; color: white;">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            `;

            if (hasChildren) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'children-pro';
                childrenContainer.id = `children-pro-${account.id}`;
                childrenContainer.style.display = 'none';
                childrenContainer.style.background = '#f8f9fa';
                childrenContainer.style.borderTop = '1px solid #e9ecef';

                account.children.forEach(child => {
                    childrenContainer.appendChild(createProfessionalAccountNode(child));
                });

                node.appendChild(childrenContainer);
            }

            return node;
        }

        // الحصول على تسمية نوع الحساب
        function getTypeLabel(type) {
            const labels = {
                'assets': 'أصول',
                'liabilities': 'خصوم',
                'equity': 'حقوق ملكية',
                'revenue': 'إيرادات',
                'expenses': 'مصروفات'
            };
            return labels[type] || type;
        }

        // وظائف النظام الاحترافي
        function professionalAddAccount() {
            showProfessionalModal();
        }

        function professionalAddFrenchBank() {
            if (!professionalChartOfAccounts) {
                initializeProfessionalSystem();
            }

            const frenchBankData = {
                code: '1105',
                name: 'البنك الفرنسي',
                type: 'assets',
                parentId: 111, // تحت النقدية والبنوك
                balance: 50000,
                description: 'حساب البنك الفرنسي - اختبار'
            };

            // التحقق من عدم وجود الحساب مسبقاً
            const flatAccounts = professionalChartOfAccounts.flattenAccounts();
            const existingAccount = flatAccounts.find(acc => acc.code === frenchBankData.code);
            if (existingAccount) {
                alert('البنك الفرنسي موجود بالفعل في دليل الحسابات الاحترافي');
                return;
            }

            // إضافة الحساب
            professionalChartOfAccounts.addNewAccount(frenchBankData);

            // تحديث العرض
            updateProfessionalStats();
            renderProfessionalTree();

            // توسيع الشجرة لإظهار الحساب الجديد
            setTimeout(() => {
                toggleProfessionalAccount(1); // الأصول
                setTimeout(() => {
                    toggleProfessionalAccount(11); // الأصول المتداولة
                    setTimeout(() => {
                        toggleProfessionalAccount(111); // النقدية والبنوك
                    }, 100);
                }, 100);
            }, 100);

            alert('تم إضافة البنك الفرنسي بنجاح إلى النظام الاحترافي!');
        }

        function professionalExpandAll() {
            document.querySelectorAll('.children-pro').forEach(children => {
                children.style.display = 'block';
            });
            document.querySelectorAll('.account-node-pro').forEach(node => {
                node.classList.add('expanded-pro');
            });
            document.querySelectorAll('.expand-icon-pro').forEach(icon => {
                icon.style.transform = 'rotate(90deg)';
            });
        }

        function professionalCollapseAll() {
            document.querySelectorAll('.children-pro').forEach(children => {
                children.style.display = 'none';
            });
            document.querySelectorAll('.account-node-pro').forEach(node => {
                node.classList.remove('expanded-pro');
            });
            document.querySelectorAll('.expand-icon-pro').forEach(icon => {
                icon.style.transform = 'rotate(0deg)';
            });
        }

        function professionalExportAccounts() {
            if (!professionalChartOfAccounts) return;

            const data = JSON.stringify(professionalChartOfAccounts.accounts, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `professional-chart-of-accounts-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function toggleProfessionalAccount(accountId) {
            const node = document.querySelector(`[data-account-id="${accountId}"]`);
            const children = node.querySelector('.children-pro');
            const icon = node.querySelector('.expand-icon-pro');

            if (children) {
                if (children.style.display === 'none') {
                    children.style.display = 'block';
                    node.classList.add('expanded-pro');
                    if (icon) icon.style.transform = 'rotate(90deg)';
                } else {
                    children.style.display = 'none';
                    node.classList.remove('expanded-pro');
                    if (icon) icon.style.transform = 'rotate(0deg)';
                }
            }
        }

        function showProfessionalModal(parentId = null) {
            if (!professionalChartOfAccounts) {
                initializeProfessionalSystem();
            }

            professionalChartOfAccounts.currentEditId = null;
            document.getElementById('professionalModalTitle').textContent = parentId ? 'إضافة حساب فرعي' : 'إضافة حساب جديد';
            document.getElementById('professionalAccountForm').reset();

            populateProfessionalParentOptions();
            if (parentId) {
                document.getElementById('professionalParentAccount').value = parentId;
            }

            document.getElementById('professionalAccountModal').style.display = 'flex';
            document.getElementById('professionalAccountCode').focus();
        }

        function closeProfessionalModal() {
            document.getElementById('professionalAccountModal').style.display = 'none';
            if (professionalChartOfAccounts) {
                professionalChartOfAccounts.currentEditId = null;
            }
        }

        function populateProfessionalParentOptions() {
            if (!professionalChartOfAccounts) return;

            const select = document.getElementById('professionalParentAccount');
            select.innerHTML = '<option value="">حساب رئيسي</option>';

            professionalChartOfAccounts.flattenAccounts().forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = `${account.code} - ${account.name}`;
                select.appendChild(option);
            });
        }

        function viewProfessionalAccount(accountId) {
            if (!professionalChartOfAccounts) return;

            const account = professionalChartOfAccounts.findAccountById(accountId);
            if (account) {
                alert(`معلومات الحساب:

رقم الحساب: ${account.code}
اسم الحساب: ${account.name}
النوع: ${getTypeLabel(account.type)}
الرصيد: ${formatCurrency(account.balance || 0)}
المستوى: ${account.level + 1}
${account.description ? 'الوصف: ' + account.description : ''}`);
            }
        }

        function editProfessionalAccount(accountId) {
            if (!professionalChartOfAccounts) return;

            const account = professionalChartOfAccounts.findAccountById(accountId);
            if (account) {
                professionalChartOfAccounts.currentEditId = accountId;
                document.getElementById('professionalModalTitle').textContent = 'تعديل الحساب';

                document.getElementById('professionalAccountCode').value = account.code;
                document.getElementById('professionalAccountName').value = account.name;
                document.getElementById('professionalAccountType').value = account.type;
                document.getElementById('professionalParentAccount').value = account.parentId || '';
                document.getElementById('professionalAccountBalance').value = account.balance || 0;
                document.getElementById('professionalAccountDescription').value = account.description || '';

                populateProfessionalParentOptions();
                document.getElementById('professionalAccountModal').style.display = 'flex';
            }
        }

        function addProfessionalSubAccount(parentId) {
            showProfessionalModal(parentId);
        }

        // معالج إرسال النموذج الاحترافي
        document.addEventListener('DOMContentLoaded', function() {
            const professionalForm = document.getElementById('professionalAccountForm');
            if (professionalForm) {
                professionalForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveProfessionalAccount();
                });
            }
        });

        function saveProfessionalAccount() {
            if (!professionalChartOfAccounts) {
                initializeProfessionalSystem();
            }

            const form = document.getElementById('professionalAccountForm');
            const formData = new FormData(form);

            const accountData = {
                code: document.getElementById('professionalAccountCode').value.trim(),
                name: document.getElementById('professionalAccountName').value.trim(),
                type: document.getElementById('professionalAccountType').value,
                parentId: document.getElementById('professionalParentAccount').value ? parseInt(document.getElementById('professionalParentAccount').value) : null,
                balance: parseFloat(document.getElementById('professionalAccountBalance').value) || 0,
                description: document.getElementById('professionalAccountDescription').value || ''
            };

            // التحقق من صحة البيانات
            if (!accountData.code || !accountData.name || !accountData.type) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من عدم تكرار رقم الحساب
            const existingAccount = professionalChartOfAccounts.flattenAccounts().find(acc =>
                acc.code === accountData.code && acc.id !== professionalChartOfAccounts.currentEditId
            );

            if (existingAccount) {
                alert('رقم الحساب موجود بالفعل');
                return;
            }

            if (professionalChartOfAccounts.currentEditId) {
                // تحديث حساب موجود
                const account = professionalChartOfAccounts.findAccountById(professionalChartOfAccounts.currentEditId);
                if (account) {
                    Object.assign(account, accountData);
                    professionalChartOfAccounts.saveToStorage();
                }
            } else {
                // إضافة حساب جديد
                professionalChartOfAccounts.addNewAccount(accountData);
            }

            // تزامن مع النظام الكلاسيكي
            syncProfessionalToClassic(accountData);

            closeProfessionalModal();
            updateProfessionalStats();
            renderProfessionalTree();

            alert('تم حفظ الحساب بنجاح في النظامين!');
        }

        // دالة تزامن من النظام الاحترافي للكلاسيكي
        function syncProfessionalToClassic(accountData) {
            try {
                // تحميل الحسابات الكلاسيكية
                let classicAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // تحويل بيانات النظام الاحترافي للتنسيق الكلاسيكي
                const classicAccount = {
                    id: Date.now(),
                    code: accountData.code,
                    name: accountData.name,
                    type: accountData.type,
                    balance: accountData.balance || 0,
                    status: 'active',
                    level: calculateLevel(accountData.code),
                    parentCode: findParentCode(accountData.code),
                    createdAt: new Date().toISOString()
                };

                // التحقق من عدم التكرار
                const existingIndex = classicAccounts.findIndex(a => a.code === accountData.code);
                if (existingIndex !== -1) {
                    // تحديث الحساب الموجود
                    classicAccounts[existingIndex] = classicAccount;
                } else {
                    // إضافة حساب جديد
                    classicAccounts.push(classicAccount);
                }

                // حفظ في جميع مفاتيح النظام الكلاسيكي
                localStorage.setItem('chartOfAccounts', JSON.stringify(classicAccounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(classicAccounts));

                // تحديث النظام الكلاسيكي
                setTimeout(() => {
                    if (typeof loadAccountsFromCentralSystem === 'function') {
                        loadAccountsFromCentralSystem();
                    }
                }, 100);

                console.log('✅ تم تزامن الحساب مع النظام الكلاسيكي');

            } catch (error) {
                console.error('❌ خطأ في تزامن النظام الاحترافي مع الكلاسيكي:', error);
            }
        }

        // دالة حساب المستوى بناءً على الكود
        function calculateLevel(code) {
            if (!code) return 1;
            if (code.length <= 1) return 1;
            if (code.length <= 2) return 2;
            if (code.length <= 3) return 3;
            if (code.length <= 4) return 4;
            return 5;
        }

        // دالة العثور على كود الحساب الأب
        function findParentCode(code) {
            if (!code || code.length <= 1) return null;
            return code.substring(0, code.length - 1);
        }

        // دالة تزامن من النظام الكلاسيكي للاحترافي (مبسطة)
        function syncClassicToProfessional(accountData) {
            try {
                console.log('🔄 تزامن من الكلاسيكي للاحترافي:', accountData);

                // تحديث النظام الاحترافي بإعادة تحميل البيانات
                setTimeout(() => {
                    if (typeof initializeProfessionalSystem === 'function') {
                        initializeProfessionalSystem();
                        console.log('✅ تم تحديث النظام الاحترافي');
                    }
                }, 200);

            } catch (error) {
                console.error('❌ خطأ في تزامن النظام الكلاسيكي مع الاحترافي:', error);
            }
        }

        // دالة العثور على ID الحساب الأب من الكود
        function findParentIdFromCode(parentCode) {
            if (!parentCode || !professionalChartOfAccounts) return null;

            const flatAccounts = professionalChartOfAccounts.flattenAccounts();
            const parentAccount = flatAccounts.find(acc => acc.code === parentCode);
            return parentAccount ? parentAccount.id : null;
        }

        // إضافة الأنماط CSS للنظام الاحترافي
        const professionalStyles = `
            <style>
                .type-assets { background: #e3f2fd; color: #1976d2; }
                .type-liabilities { background: #fff3e0; color: #f57c00; }
                .type-equity { background: #e8f5e8; color: #388e3c; }
                .type-revenue { background: #e8f5e8; color: #2e7d32; }
                .type-expenses { background: #ffebee; color: #d32f2f; }

                .account-node-pro:hover {
                    background: #f8f9fa;
                }

                .action-btn-pro:hover {
                    transform: scale(1.1);
                }

                .children-pro .account-header-pro {
                    padding-right: 40px;
                }

                .level-1 .account-header-pro { padding-right: 40px; }
                .level-2 .account-header-pro { padding-right: 80px; }
                .level-3 .account-header-pro { padding-right: 120px; }
                .level-4 .account-header-pro { padding-right: 160px; }

                /* تحسينات البحث في النظام الاحترافي */
                .search-result {
                    border: 1px solid rgba(255,255,255,0.2);
                    margin: 8px 0;
                    border-radius: 8px;
                    background: rgba(255,255,255,0.05);
                    padding: 12px;
                    transition: all 0.3s ease;
                }

                .search-result:hover {
                    background: rgba(255,255,255,0.1);
                    border-color: rgba(255,255,255,0.4);
                    transform: translateX(-3px);
                }

                .search-result mark {
                    background: rgba(255,255,0,0.4);
                    color: #fff;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-weight: bold;
                }

                .account-details {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 8px;
                    font-size: 12px;
                    opacity: 0.8;
                }

                .account-type {
                    background: rgba(108,117,125,0.3);
                    padding: 3px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    text-transform: uppercase;
                }

                .account-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 5px;
                }

                .account-name {
                    font-weight: bold;
                    font-size: 14px;
                }

                .account-code {
                    background: rgba(0,123,255,0.2);
                    padding: 3px 8px;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    font-weight: bold;
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', professionalStyles);

        // ===== وظائف التكامل =====

        // مستمع تحديث الحسابات
        document.addEventListener('accountsUpdated', function(e) {
            console.log('🔄 تم تحديث الحسابات، إعادة تحميل الواجهة...');

            // تحديث النظام الكلاسيكي
            setTimeout(() => {
                loadAccountsFromCentralSystem();
            }, 100);

            // تحديث النظام الاحترافي إذا كان مفعلاً
            setTimeout(() => {
                if (professionalChartOfAccounts) {
                    console.log('🔄 تحديث النظام الاحترافي من النظام الكلاسيكي...');
                    professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                    updateProfessionalStats();
                    renderProfessionalTree();
                    console.log('✅ تم تحديث النظام الاحترافي');
                }
            }, 200);
        });

        // مراقبة التحديثات من النظام الكلاسيكي
        function watchForClassicUpdates() {
            console.log('🔄 تشغيل مراقبة التحديثات بين النظامين...');

            // مراقبة localStorage
            window.addEventListener('storage', function(e) {
                if (e.key === 'chartOfAccounts' || e.key === 'monjizAccounts') {
                    console.log('🔄 تم اكتشاف تحديث في النظام الكلاسيكي');
                    if (professionalChartOfAccounts) {
                        setTimeout(() => {
                            professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                            updateProfessionalStats();
                            renderProfessionalTree();
                            console.log('✅ تم تحديث النظام الاحترافي تلقائياً');
                        }, 100);
                    }
                }
            });

            // مراقبة BroadcastChannel
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.addEventListener('message', function(e) {
                    if (e.data.type === 'accounts-updated') {
                        console.log('🔄 تم استقبال إشعار تحديث الحسابات');
                        if (professionalChartOfAccounts) {
                            setTimeout(() => {
                                professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                                updateProfessionalStats();
                                renderProfessionalTree();
                                console.log('✅ تم تحديث النظام الاحترافي من الإشعار');
                            }, 100);
                        }
                    }
                });
            }
        }

        // مستمع إضافة العملاء
        document.addEventListener('customerAdded', function(e) {
            console.log('👤 تم إضافة عميل جديد، تحديث دليل الحسابات...');
            setTimeout(() => {
                loadAccountsFromCentralSystem();
                if (professionalChartOfAccounts) {
                    professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                    updateProfessionalStats();
                    renderProfessionalTree();
                }
            }, 300);
        });

        // مستمع إضافة الموردين
        document.addEventListener('supplierAdded', function(e) {
            console.log('🚚 تم إضافة مورد جديد، تحديث دليل الحسابات...');
            setTimeout(() => {
                loadAccountsFromCentralSystem();
                if (professionalChartOfAccounts) {
                    professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                    updateProfessionalStats();
                    renderProfessionalTree();
                }
            }, 300);
        });

        // دالة مزامنة مع رسالة تأكيد
        function syncAllAccountsWithConfirmation() {
            console.log('🔄 بدء المزامنة الشاملة...');

            if (window.integrationSystem) {
                const result = window.integrationSystem.fullSync();

                let message = 'تمت المزامنة الشاملة:\n';
                message += `• العملاء: ${result.customers} حساب جديد\n`;
                message += `• الموردين: ${result.suppliers} حساب جديد\n`;
                message += `• المجموع: ${result.total} حساب جديد`;

                alert(message);

                if (result.total > 0) {
                    // تحديث الواجهة
                    loadAccountsFromCentralSystem();
                }
            } else {
                alert('نظام التكامل غير متاح');
            }
        }

        // دالة تشخيص مع عرض النتائج
        function diagnoseIntegrationWithDisplay() {
            console.log('🔍 تشخيص حالة التكامل...');

            if (window.integrationSystem) {
                const diagnosis = window.integrationSystem.diagnose();

                let message = 'تشخيص التكامل:\n\n';
                message += `العملاء:\n`;
                message += `• المجموع: ${diagnosis.customers.total}\n`;
                message += `• لديهم حسابات: ${diagnosis.customers.withAccounts}\n`;
                message += `• مفقودة: ${diagnosis.customers.missing}\n\n`;

                message += `الموردين:\n`;
                message += `• المجموع: ${diagnosis.suppliers.total}\n`;
                message += `• لديهم حسابات: ${diagnosis.suppliers.withAccounts}\n`;
                message += `• مفقودة: ${diagnosis.suppliers.missing}\n\n`;

                message += `دليل الحسابات:\n`;
                message += `• المجموع: ${diagnosis.accounts.total}\n`;
                message += `• حسابات العملاء: ${diagnosis.accounts.customers}\n`;
                message += `• حسابات الموردين: ${diagnosis.accounts.suppliers}`;

                alert(message);

                // إذا كان هناك حسابات مفقودة، اعرض خيار المزامنة
                if (diagnosis.customers.missing > 0 || diagnosis.suppliers.missing > 0) {
                    if (confirm('هناك حسابات مفقودة. هل تريد تشغيل المزامنة الشاملة؟')) {
                        syncAllAccountsWithConfirmation();
                    }
                }
            } else {
                alert('نظام التكامل غير متاح');
            }
        }

        // دالة اختبار إضافة حساب جديد
        function addTestAccount() {
            const testAccount = {
                code: '1111-' + Date.now().toString().slice(-4),
                name: 'حساب تجريبي - ' + new Date().toLocaleTimeString('ar-SA'),
                type: 'assets',
                parentCode: '1111',
                balance: 0,
                description: 'حساب تجريبي لاختبار التزامن بين النظامين',
                isActive: true
            };

            // إضافة الحساب للنظام المركزي
            if (window.dataManager) {
                const saved = window.dataManager.addAccount(testAccount);
                if (saved) {
                    // إعادة تحميل فورية
                    setTimeout(() => {
                        loadAccountsFromCentralSystem();
                        if (professionalChartOfAccounts) {
                            professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                            updateProfessionalStats();
                            renderProfessionalTree();
                        }
                    }, 100);

                    alert(`تم إضافة حساب تجريبي بنجاح!\n\nالكود: ${saved.code}\nالاسم: ${saved.name}\n\nتحقق من النظام الاحترافي لرؤية التحديث.`);
                    console.log('✅ تم إضافة حساب تجريبي:', saved);
                } else {
                    alert('فشل في إضافة الحساب التجريبي!');
                }
            } else {
                alert('النظام المركزي غير متاح!');
            }
        }

        // دالة إعادة تحميل قوية للحسابات
        function forceReloadAccounts() {
            console.log('🔄 إعادة تحميل قوية للحسابات...');

            // إعادة تحميل النظام الكلاسيكي
            loadAccountsFromCentralSystem();

            // إعادة تحميل النظام الاحترافي
            if (professionalChartOfAccounts) {
                professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                updateProfessionalStats();
                renderProfessionalTree();
            }

            alert('تم إعادة تحميل جميع الحسابات بنجاح!');
        }

        // دالة اختبار بسيطة لإضافة حساب
        function testAddAccountDirect() {
            const testAccount = {
                id: Date.now(),
                code: '********',
                name: 'بنك سامبا - اختبار',
                type: 'assets',
                balance: 0,
                status: 'active',
                level: 5,
                parentCode: '110102',
                isActive: true,
                createdAt: new Date().toISOString()
            };

            console.log('🧪 اختبار إضافة حساب مباشر:', testAccount);

            try {
                // الحفظ المباشر في localStorage
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // التحقق من عدم التكرار
                if (accounts.some(a => a.code === testAccount.code)) {
                    // تحديث الكود إذا كان موجوداً
                    testAccount.code = '********' + Date.now().toString().slice(-3);
                }

                accounts.push(testAccount);

                // حفظ في كلا المفتاحين
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                console.log('✅ تم حفظ الحساب التجريبي في localStorage');

                // إرسال إشعارات
                if (window.BroadcastChannel) {
                    const channel = new BroadcastChannel('monjiz-updates');
                    channel.postMessage({
                        type: 'accounts-updated',
                        data: testAccount
                    });
                }

                // تحديث النظامين
                setTimeout(() => {
                    loadAccountsFromCentralSystem();
                    if (professionalChartOfAccounts) {
                        professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                        updateProfessionalStats();
                        renderProfessionalTree();
                    }
                }, 100);

                alert(`تم إضافة حساب اختبار بنجاح!\n\nالكود: ${testAccount.code}\nالاسم: ${testAccount.name}\n\nتحقق من النظامين الآن.`);

            } catch (error) {
                console.error('❌ خطأ في الاختبار:', error);
                alert('فشل في إضافة الحساب التجريبي!');
            }
        }

        // دالة إعادة تعيين الحسابات الافتراضية
        function resetToDefaultAccounts() {
            if (!confirm('هل أنت متأكد من إعادة تعيين دليل الحسابات إلى الحسابات الافتراضية؟\n\nسيتم حذف جميع الحسابات الحالية!')) {
                return;
            }

            console.log('🔄 إعادة تعيين الحسابات الافتراضية...');

            // إنشاء الحسابات الافتراضية الجديدة
            const defaultAccounts = createDefaultAccounts();

            // حفظ في جميع المفاتيح
            localStorage.setItem('chartOfAccounts', JSON.stringify(defaultAccounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(defaultAccounts));

            // إرسال إشعارات التحديث
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.postMessage({
                    type: 'accounts-reset',
                    data: defaultAccounts
                });
            }

            // تحديث النظامين
            loadAccountsFromCentralSystem();
            if (professionalChartOfAccounts) {
                professionalChartOfAccounts.accounts = professionalChartOfAccounts.loadProfessionalAccounts();
                updateProfessionalStats();
                renderProfessionalTree();
            }

            alert(`تم إعادة تعيين دليل الحسابات بنجاح!\n\nتم إنشاء ${defaultAccounts.length} حساب افتراضي منظم هرمياً:\n\n• الأصول → الأصول المتداولة → النقدية والبنوك → البنوك → بنك الراجحي\n• الخصوم → الخصوم المتداولة → الموردون\n• حقوق الملكية → رأس المال\n• الإيرادات → إيرادات المبيعات\n• المصروفات → المصروفات الإدارية → الرواتب`);

            console.log('✅ تم إعادة تعيين', defaultAccounts.length, 'حساب افتراضي');
        }

        // دالة تشخيص سريعة للحسابات
        function quickDiagnoseAccounts() {
            console.log('🔍 تشخيص سريع للحسابات...');

            // فحص localStorage
            const chartAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            const monjizAccounts = JSON.parse(localStorage.getItem('monjizAccounts')) || [];
            const professionalAccounts = JSON.parse(localStorage.getItem('professionalChartOfAccounts')) || [];

            console.log('📊 إحصائيات التخزين:');
            console.log(`   chartOfAccounts: ${chartAccounts.length} حساب`);
            console.log(`   monjizAccounts: ${monjizAccounts.length} حساب`);
            console.log(`   professionalChartOfAccounts: ${professionalAccounts.length} حساب`);

            // فحص النظام المركزي
            let centralAccounts = [];
            if (window.dataManager) {
                centralAccounts = window.dataManager.getAccounts() || [];
                console.log(`   النظام المركزي: ${centralAccounts.length} حساب`);
            } else {
                console.log('   النظام المركزي: غير متاح');
            }

            // فحص النظام الاحترافي
            if (professionalChartOfAccounts) {
                console.log(`   النظام الاحترافي: ${professionalChartOfAccounts.accounts.length} حساب`);
            } else {
                console.log('   النظام الاحترافي: غير مهيأ');
            }

            // عرض النتائج
            const report = `
🔍 تقرير تشخيص الحسابات:

📊 البيانات المحفوظة:
- chartOfAccounts: ${chartAccounts.length} حساب
- monjizAccounts: ${monjizAccounts.length} حساب
- professionalChartOfAccounts: ${professionalAccounts.length} حساب

🔧 الأنظمة النشطة:
- النظام المركزي: ${window.dataManager ? centralAccounts.length + ' حساب' : 'غير متاح'}
- النظام الاحترافي: ${professionalChartOfAccounts ? professionalChartOfAccounts.accounts.length + ' حساب' : 'غير مهيأ'}

💡 التوصيات:
${chartAccounts.length === 0 ? '⚠️ لا توجد حسابات في chartOfAccounts' : '✅ chartOfAccounts يحتوي على بيانات'}
${professionalChartOfAccounts && professionalChartOfAccounts.accounts.length === 0 ? '⚠️ النظام الاحترافي فارغ' : '✅ النظام الاحترافي يحتوي على بيانات'}
            `;

            alert(report);
            return {
                chartAccounts,
                monjizAccounts,
                professionalAccounts,
                centralAccounts,
                professionalSystem: professionalChartOfAccounts
            };
        }

        // ربط الدوال بالنطاق العام
        window.syncAllAccounts = syncAllAccountsWithConfirmation;
        window.diagnoseIntegration = diagnoseIntegrationWithDisplay;
        window.addTestAccount = addTestAccount;
        window.forceReloadAccounts = forceReloadAccounts;
        window.quickDiagnoseAccounts = quickDiagnoseAccounts;
        window.resetToDefaultAccounts = resetToDefaultAccounts;
        window.testAddAccountDirect = testAddAccountDirect;

        // رسائل وحدة التحكم
        console.log('💡 أوامر الاختبار المتاحة:');
        console.log('   addTestAccount() - إضافة حساب تجريبي لاختبار التزامن');
        console.log('   testAddAccountDirect() - اختبار إضافة حساب مباشر');
        console.log('   forceReloadAccounts() - إعادة تحميل قوية لجميع الحسابات');
        console.log('   quickDiagnoseAccounts() - تشخيص سريع لحالة الحسابات');
        console.log('   resetToDefaultAccounts() - إعادة تعيين الحسابات الافتراضية المنظمة');

        // دالة حفظ الحساب مبسطة جداً
        function saveAccountSimple() {
            // جمع البيانات
            const code = document.getElementById('account-code').value.trim();
            const name = document.getElementById('account-name').value.trim();
            const type = document.getElementById('account-type').value;
            const parentCode = document.getElementById('parent-account').value;

            // التحقق البسيط
            if (!code || !name || !type) {
                alert('يرجى ملء جميع الحقول المطلوبة!');
                return;
            }

            // حساب المستوى الصحيح
            let level = 1;
            if (parentCode) {
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                const parentAccount = accounts.find(a => a.code === parentCode);
                if (parentAccount) {
                    level = (parentAccount.level || 1) + 1;
                } else {
                    // إذا لم يوجد الحساب الأب، احسب المستوى من طول الكود
                    level = code.length;
                }
            }

            // إنشاء الحساب
            const newAccount = {
                id: Date.now(),
                code: code,
                name: name,
                type: type,
                balance: 0,
                status: 'active',
                level: level,
                parentCode: parentCode || null,
                createdAt: new Date().toISOString()
            };

            // حفظ مباشر
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // التحقق من التكرار
            if (accounts.some(a => a.code === code)) {
                alert('رقم الحساب موجود بالفعل!');
                return;
            }

            // إضافة الحساب
            accounts.push(newAccount);

            // حفظ في جميع المفاتيح
            localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
            localStorage.setItem('professionalChartOfAccounts', JSON.stringify(accounts));

            // تزامن مع النظام الاحترافي
            syncClassicToProfessional(newAccount);

            // إغلاق النافذة
            closeAddAccountModal();

            // تحديث النظام المناسب
            const unifiedSection = document.getElementById('unified-chart-section');
            if (unifiedSection && unifiedSection.style.display !== 'none') {
                // تحديث النظام الموحد
                setTimeout(() => {
                    loadUnifiedAccounts();
                }, 100);
                alert(`تم إضافة الحساب بنجاح في النظام الموحد!\n\nالكود: ${code}\nالاسم: ${name}`);
            } else {
                // إعادة تحميل للأنظمة الأخرى
                setTimeout(() => {
                    location.reload();
                }, 500);
                alert(`تم إضافة الحساب بنجاح!\n\nالكود: ${code}\nالاسم: ${name}\n\nسيتم إعادة تحميل الصفحة...`);
            }
        }

        // دالة اختبار إضافة بنك سامبا مباشرة
        function testAddSamaBank() {
            // إنشاء بنك سامبا
            const samaBank = {
                id: Date.now(),
                code: '11125',
                name: 'بنك سامبا',
                type: 'assets',
                balance: 0,
                status: 'active',
                level: 5,
                parentCode: '1112',
                createdAt: new Date().toISOString()
            };

            // تحميل الحسابات
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إزالة بنك سامبا إذا كان موجود (لتجنب التكرار)
            accounts = accounts.filter(a => a.code !== '11125');

            // إضافة بنك سامبا
            accounts.push(samaBank);

            // حفظ في جميع المفاتيح
            localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
            localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
            localStorage.setItem('professionalChartOfAccounts', JSON.stringify(accounts));

            // إعادة تحميل الصفحة لضمان الظهور
            alert('تم إضافة بنك سامبا بنجاح!\n\nسيتم إعادة تحميل الصفحة لعرض التحديث...');

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // دالة إعادة تعيين النظام بالكامل
        function resetSystemData() {
            if (confirm('هل أنت متأكد من إعادة تعيين النظام؟\n\nسيتم حذف جميع الحسابات المضافة والعودة للحسابات الافتراضية.')) {
                console.log('🔄 إعادة تعيين النظام...');

                // مسح جميع البيانات
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('monjizAccounts');
                localStorage.removeItem('professionalChartOfAccounts');

                console.log('✅ تم مسح جميع البيانات');

                // إعادة تحميل الصفحة لإعادة التهيئة
                alert('سيتم إعادة تحميل الصفحة لإعادة تهيئة النظام...');
                location.reload();
            }
        }

        // ========== النظام الموحد ==========

        // دالة التبديل للنظام الموحد
        function switchToUnifiedView() {
            console.log('🔄 التبديل للنظام الموحد...');

            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // إظهار النظام الموحد
            const unifiedSection = document.getElementById('unified-chart-section');
            if (unifiedSection) {
                unifiedSection.style.display = 'block';

                // تحميل البيانات
                loadUnifiedAccounts();

                console.log('✅ تم التبديل للنظام الموحد');
            } else {
                console.error('❌ قسم النظام الموحد غير موجود');
            }
        }

        // دالة التبديل للعرض الكلاسيكي
        function switchToClassicView() {
            console.log('🔄 التبديل للعرض الكلاسيكي...');

            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // إظهار النظام الكلاسيكي
            const classicSection = document.getElementById('chart-of-accounts');
            if (classicSection) {
                classicSection.style.display = 'block';

                // تحميل البيانات
                loadAccountsFromCentralSystem();

                console.log('✅ تم التبديل للعرض الكلاسيكي');
            }
        }

        // دالة تحميل الحسابات للنظام الموحد
        function loadUnifiedAccounts() {
            console.log('📊 تحميل الحسابات للنظام الموحد...');

            // تحميل الحسابات من localStorage
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            console.log('📋 تم تحميل', accounts.length, 'حساب');

            // تحديث الإحصائيات
            updateUnifiedStats(accounts);

            // بناء وعرض الشجرة
            renderUnifiedTree(accounts);
        }

        // دالة تحديث الإحصائيات
        function updateUnifiedStats(accounts) {
            const stats = {
                total: accounts.length,
                assets: accounts.filter(a => a.type === 'assets').length,
                liabilities: accounts.filter(a => a.type === 'liabilities').length,
                equity: accounts.filter(a => a.type === 'equity').length,
                revenue: accounts.filter(a => a.type === 'revenue').length,
                expenses: accounts.filter(a => a.type === 'expenses').length
            };

            // تحديث العناصر
            document.getElementById('unified-total-accounts').textContent = stats.total;
            document.getElementById('unified-assets-count').textContent = stats.assets;
            document.getElementById('unified-liabilities-count').textContent = stats.liabilities;
            document.getElementById('unified-equity-count').textContent = stats.equity;

            console.log('📈 تم تحديث الإحصائيات:', stats);
        }

        // دالة عرض الشجرة الموحدة
        function renderUnifiedTree(accounts) {
            console.log('🌳 بناء الشجرة الموحدة...');

            const treeContainer = document.getElementById('unified-accounts-tree');
            if (!treeContainer) {
                console.error('❌ حاوية الشجرة غير موجودة');
                return;
            }

            // مسح المحتوى الحالي
            treeContainer.innerHTML = '';

            // بناء الشجرة الهرمية
            const accountTree = buildAccountTree(accounts);

            // عرض الشجرة
            renderUnifiedAccountTree(accountTree, treeContainer, 0);

            console.log('✅ تم عرض الشجرة الموحدة');
        }

        // دالة عرض شجرة الحسابات الموحدة
        function renderUnifiedAccountTree(accountTree, container, level) {
            accountTree.forEach(account => {
                // إنشاء عنصر الحساب
                const accountItem = document.createElement('div');
                accountItem.className = 'unified-account-item';
                accountItem.style.marginRight = (level * 25) + 'px';

                // محتوى الحساب
                accountItem.innerHTML = `
                    <div class="unified-account-content">
                        <div class="unified-account-icon ${account.type}">
                            <i class="fas ${getAccountIcon(account.type)}"></i>
                        </div>
                        <div class="unified-account-details">
                            <div class="unified-account-name">${account.name}</div>
                            <span class="unified-account-code">${account.code}</span>
                        </div>
                        <div class="unified-account-actions">
                            <button class="unified-action-btn view" onclick="viewUnifiedAccount('${account.code}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="unified-action-btn edit" onclick="editUnifiedAccount('${account.code}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="unified-action-btn delete" onclick="deleteUnifiedAccount('${account.code}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(accountItem);

                // عرض الحسابات الفرعية
                if (account.children && account.children.length > 0) {
                    renderUnifiedAccountTree(account.children, container, level + 1);
                }
            });
        }

        // دالة الحصول على أيقونة الحساب
        function getAccountIcon(type) {
            const icons = {
                'assets': 'fa-chart-line',
                'liabilities': 'fa-credit-card',
                'equity': 'fa-wallet',
                'revenue': 'fa-arrow-up',
                'expenses': 'fa-arrow-down'
            };
            return icons[type] || 'fa-circle';
        }

        // دوال الإجراءات
        function viewUnifiedAccount(code) {
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            const account = accounts.find(a => a.code === code);
            if (account) {
                alert(`تفاصيل الحساب:\n\nالكود: ${account.code}\nالاسم: ${account.name}\nالنوع: ${account.type}\nالرصيد: ${account.balance || 0}`);
            }
        }

        function editUnifiedAccount(code) {
            alert('سيتم إضافة نافذة التعديل قريباً');
        }

        function deleteUnifiedAccount(code) {
            if (confirm(`هل أنت متأكد من حذف الحساب ${code}؟`)) {
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                accounts = accounts.filter(a => a.code !== code);

                // حفظ التحديث
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
                localStorage.setItem('professionalChartOfAccounts', JSON.stringify(accounts));

                // إعادة تحميل الشجرة
                loadUnifiedAccounts();

                alert('تم حذف الحساب بنجاح');
            }
        }

        function showUnifiedAddModal() {
            // استخدام نفس نافذة الإضافة الموجودة
            showAddAccountModal();
        }

        function exportUnifiedAccounts() {
            const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            const dataStr = JSON.stringify(accounts, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'unified-chart-of-accounts.json';
            link.click();
        }

        // ربط الدوال بالنطاق العام
        window.testAddSamaBank = testAddSamaBank;
        window.resetSystemData = resetSystemData;
        window.switchToUnifiedView = switchToUnifiedView;
        window.switchToClassicView = switchToClassicView;

        // تهيئة النظام مع الحفاظ على الحسابات الموجودة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تهيئة النظام مع الحفاظ على البيانات الموجودة...');

            // التحقق من وجود حسابات محفوظة
            const existingAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
            console.log('📊 الحسابات الموجودة:', existingAccounts.length);

            // إنشاء الحسابات الافتراضية فقط إذا لم تكن موجودة
            if (existingAccounts.length === 0) {
                console.log('⚠️ لا توجد حسابات، سيتم إنشاء الحسابات الافتراضية');
                // إنشاء الحسابات الافتراضية المنظمة
            const defaultAccounts = [
                // المستوى الأول - الحسابات الرئيسية
                { id: 1, code: '1', name: 'الأصول', type: 'assets', balance: 0, level: 1, parentCode: null, status: 'active', createdAt: new Date().toISOString() },
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', balance: 0, level: 1, parentCode: null, status: 'active', createdAt: new Date().toISOString() },
                { id: 3, code: '3', name: 'حقوق الملكية', type: 'equity', balance: 0, level: 1, parentCode: null, status: 'active', createdAt: new Date().toISOString() },
                { id: 4, code: '4', name: 'الإيرادات', type: 'revenue', balance: 0, level: 1, parentCode: null, status: 'active', createdAt: new Date().toISOString() },
                { id: 5, code: '5', name: 'المصروفات', type: 'expenses', balance: 0, level: 1, parentCode: null, status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثاني - الأصول
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', balance: 0, level: 2, parentCode: '1', status: 'active', createdAt: new Date().toISOString() },
                { id: 12, code: '12', name: 'الأصول الثابتة', type: 'assets', balance: 0, level: 2, parentCode: '1', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثالث - الأصول المتداولة
                { id: 111, code: '111', name: 'النقدية والبنوك', type: 'assets', balance: 0, level: 3, parentCode: '11', status: 'active', createdAt: new Date().toISOString() },
                { id: 112, code: '112', name: 'العملاء والذمم المدينة', type: 'assets', balance: 0, level: 3, parentCode: '11', status: 'active', createdAt: new Date().toISOString() },
                { id: 113, code: '113', name: 'المخزون', type: 'assets', balance: 0, level: 3, parentCode: '11', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الرابع - النقدية والبنوك
                { id: 1111, code: '1111', name: 'الصندوق', type: 'assets', balance: 0, level: 4, parentCode: '111', status: 'active', createdAt: new Date().toISOString() },
                { id: 1112, code: '1112', name: 'البنوك', type: 'assets', balance: 0, level: 4, parentCode: '111', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الخامس - البنوك
                { id: 11121, code: '11121', name: 'بنك الراجحي', type: 'assets', balance: 0, level: 5, parentCode: '1112', status: 'active', createdAt: new Date().toISOString() },
                { id: 11122, code: '11122', name: 'البنك الأهلي', type: 'assets', balance: 0, level: 5, parentCode: '1112', status: 'active', createdAt: new Date().toISOString() },
                { id: 11123, code: '11123', name: 'بنك الرياض', type: 'assets', balance: 0, level: 5, parentCode: '1112', status: 'active', createdAt: new Date().toISOString() },
                { id: 11124, code: '11124', name: 'بنك الإنماء', type: 'assets', balance: 0, level: 5, parentCode: '1112', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثاني - الخصوم
                { id: 21, code: '21', name: 'الخصوم المتداولة', type: 'liabilities', balance: 0, level: 2, parentCode: '2', status: 'active', createdAt: new Date().toISOString() },
                { id: 22, code: '22', name: 'الخصوم طويلة الأجل', type: 'liabilities', balance: 0, level: 2, parentCode: '2', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثالث - الخصوم المتداولة
                { id: 211, code: '211', name: 'الموردون والذمم الدائنة', type: 'liabilities', balance: 0, level: 3, parentCode: '21', status: 'active', createdAt: new Date().toISOString() },
                { id: 212, code: '212', name: 'المصروفات المستحقة', type: 'liabilities', balance: 0, level: 3, parentCode: '21', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثاني - حقوق الملكية
                { id: 31, code: '31', name: 'رأس المال', type: 'equity', balance: 0, level: 2, parentCode: '3', status: 'active', createdAt: new Date().toISOString() },
                { id: 32, code: '32', name: 'الأرباح المحتجزة', type: 'equity', balance: 0, level: 2, parentCode: '3', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثاني - الإيرادات
                { id: 41, code: '41', name: 'إيرادات المبيعات', type: 'revenue', balance: 0, level: 2, parentCode: '4', status: 'active', createdAt: new Date().toISOString() },
                { id: 42, code: '42', name: 'إيرادات أخرى', type: 'revenue', balance: 0, level: 2, parentCode: '4', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثاني - المصروفات
                { id: 51, code: '51', name: 'تكلفة البضاعة المباعة', type: 'expenses', balance: 0, level: 2, parentCode: '5', status: 'active', createdAt: new Date().toISOString() },
                { id: 52, code: '52', name: 'مصروفات التشغيل', type: 'expenses', balance: 0, level: 2, parentCode: '5', status: 'active', createdAt: new Date().toISOString() },

                // المستوى الثالث - مصروفات التشغيل
                { id: 521, code: '521', name: 'مصروفات الرواتب', type: 'expenses', balance: 0, level: 3, parentCode: '52', status: 'active', createdAt: new Date().toISOString() },
                { id: 522, code: '522', name: 'مصروفات الإيجار', type: 'expenses', balance: 0, level: 3, parentCode: '52', status: 'active', createdAt: new Date().toISOString() },
                { id: 523, code: '523', name: 'مصروفات الكهرباء والماء', type: 'expenses', balance: 0, level: 3, parentCode: '52', status: 'active', createdAt: new Date().toISOString() }
                ];

                // حفظ الحسابات الافتراضية في جميع المفاتيح
                localStorage.setItem('chartOfAccounts', JSON.stringify(defaultAccounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(defaultAccounts));
                localStorage.setItem('professionalChartOfAccounts', JSON.stringify(defaultAccounts));

                console.log('✅ تم حفظ', defaultAccounts.length, 'حساب افتراضي منظم');
            } else {
                console.log('✅ تم العثور على', existingAccounts.length, 'حساب محفوظ، سيتم الاحتفاظ بها');
            }

            // تحميل النظام الموحد
            setTimeout(() => {
                if (typeof loadUnifiedAccounts === 'function') {
                    loadUnifiedAccounts();
                }
            }, 500);

            console.log('✅ تم تهيئة النظام مع الحفاظ على البيانات الموجودة');
        });

    </script>

    <script src="js/data-manager.js"></script>
    <script src="js/accounting.js"></script>
</body>
</html>