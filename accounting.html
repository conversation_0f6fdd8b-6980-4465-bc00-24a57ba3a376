<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحسابات - نظام إدارة الأعمال</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accounting.css">
    <link rel="stylesheet" href="css/accounting-fix.css">
    <link rel="stylesheet" href="css/chart-of-accounts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* تنسيق الأيقونات حسب النمط المتبع في البرنامج */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
        }

        .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 10px !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        .action-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        /* تحسين الأيقونات */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
        }

        .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 10px !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        .action-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        /* CSS إضافي قوي لضمان تطبيق التنسيق */
        table .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 10px !important;
        }

        table .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        table .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        table .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        table .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        table .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
        }

        /* تنسيق خاص لجدول الحسابات */
        #accounts-table-body .action-btn {
            width: 26px !important;
            height: 26px !important;
            border-radius: 4px !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 10px !important;
        }

        #accounts-table-body .action-btn.view {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn.edit {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn.delete {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
        }

        #accounts-table-body .action-btn i {
            color: white !important;
            font-size: 10px !important;
        }

        /* تنسيق نافذة إضافة حساب جديد */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 30px;
        }

        #add-account-modal.active {
            display: flex !important;
        }

        /* تنسيق التبويبات */
        .nav-tab {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* تنسيق نافذة إضافة الحساب */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal-container {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row:last-child {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #28a745;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .primary-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .secondary-btn {
            background: #6c757d;
            color: white;
        }

        .secondary-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="products.html"><i class="fas fa-boxes"></i> المنتجات</a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html" class="active"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- صفحة الحسابات -->
    <section class="accounting-page">
        <div class="container">
            <div class="page-header">
                <h2><i class="fas fa-calculator"></i> إدارة الحسابات</h2>
                <div class="header-actions">
                    <button class="btn add-transaction-btn" onclick="showAddJournalEntryModal()"><i class="fas fa-plus-circle"></i> قيد جديد</button>
                    <button class="btn export-btn" onclick="exportAccountingData()"><i class="fas fa-file-export"></i> تصدير</button>
                </div>
            </div>

            <!-- قائمة التنقل الفرعية للحسابات -->
            <div class="accounting-nav">
                <div class="nav-tabs">
                    <button class="nav-tab" data-tab="overview">
                        <i class="fas fa-chart-pie"></i>
                        نظرة عامة
                    </button>
                    <button class="nav-tab active" data-tab="chart-of-accounts">
                        <i class="fas fa-book"></i>
                        دليل الحسابات
                    </button>
                    <button class="nav-tab" data-tab="journal-entries">
                        <i class="fas fa-journal-whills"></i>
                        قيد اليومية
                    </button>
                    <button class="nav-tab" data-tab="receipt-voucher">
                        <i class="fas fa-receipt"></i>
                        سند القبض
                    </button>
                    <button class="nav-tab" data-tab="payment-voucher">
                        <i class="fas fa-money-bill-wave"></i>
                        سند الصرف
                    </button>
                </div>
            </div>

            <!-- محتوى التبويبات -->
            <div class="tab-content">
                <!-- تبويب النظرة العامة -->
                <div class="tab-pane" id="overview-tab">
                    <!-- أزرار التصدير للتقارير -->
                    <div class="section-header">
                        <h3><i class="fas fa-chart-pie"></i> التقارير المالية</h3>
                        <div class="header-actions">
                            <div class="export-buttons">
                                <button class="btn secondary-btn" onclick="printFinancialReport()">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                                <button class="btn secondary-btn" onclick="exportFinancialReportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn secondary-btn" onclick="exportFinancialReportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- لوحة الإحصائيات المالية -->
                    <div class="financial-stats">
                <div class="stat-card income">
                    <div class="stat-icon"><i class="fas fa-arrow-down"></i></div>
                    <div class="stat-info">
                        <h3>إجمالي الإيرادات</h3>
                        <p class="stat-value">125,750.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card expenses">
                    <div class="stat-icon"><i class="fas fa-arrow-up"></i></div>
                    <div class="stat-info">
                        <h3>إجمالي المصروفات</h3>
                        <p class="stat-value">87,250.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card profit">
                    <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="stat-info">
                        <h3>صافي الربح</h3>
                        <p class="stat-value">38,500.00 ر.س</p>
                        <p class="stat-period">هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card balance">
                    <div class="stat-icon"><i class="fas fa-wallet"></i></div>
                    <div class="stat-info">
                        <h3>الرصيد الحالي</h3>
                        <p class="stat-value">215,350.00 ر.س</p>
                        <p class="stat-period">حتى تاريخه</p>
                    </div>
                </div>
            </div>

            <!-- الرسم البياني للإيرادات والمصروفات -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>الإيرادات والمصروفات</h3>
                    <div class="chart-period">
                        <select id="chart-period-select">
                            <option value="month">هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذه السنة</option>
                        </select>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="financial-chart"></canvas>
                </div>
            </div>

            <!-- أدوات البحث والتصفية -->
            <div class="search-filter-container">
                <div class="search-box">
                    <input type="text" placeholder="بحث عن معاملة..." id="transaction-search">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
                <div class="filter-options">
                    <select id="transaction-type-filter">
                        <option value="all">جميع المعاملات</option>
                        <option value="income">إيرادات</option>
                        <option value="expense">مصروفات</option>
                    </select>
                    <select id="date-filter">
                        <option value="all">كل الفترات</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="custom">تاريخ مخصص</option>
                    </select>
                    <button class="filter-btn" id="advanced-filter-btn">
                        <i class="fas fa-filter"></i> تصفية متقدمة
                    </button>
                </div>
            </div>

            <!-- تاريخ مخصص (مخفي بشكل افتراضي) -->
            <div class="custom-date-filter" id="custom-date-filter" style="display: none;">
                <div class="date-range">
                    <div class="date-input">
                        <label for="start-date">من تاريخ:</label>
                        <input type="date" id="start-date">
                    </div>
                    <div class="date-input">
                        <label for="end-date">إلى تاريخ:</label>
                        <input type="date" id="end-date">
                    </div>
                    <button class="apply-date-btn">تطبيق</button>
                </div>
            </div>

            <!-- جدول المعاملات المالية -->
            <div class="table-container">
                <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم المعاملة</th>
                            <th>الوصف</th>
                            <th>الفئة</th>
                            <th>النوع</th>
                            <th>المبلغ</th>
                            <th>الحساب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transactions-table-body">
                        <!-- سيتم إضافة صفوف المعاملات هنا عبر JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- نظام التنقل الموحد -->
            <div id="pagination"></div>
                </div>

                <!-- تبويب دليل الحسابات -->
                <div class="tab-pane active" id="chart-of-accounts-tab">
                    <div class="chart-of-accounts-modern">
                        <!-- رأس القسم -->
                        <div class="section-header-modern">
                            <div class="header-title">
                                <h3><i class="fas fa-book"></i> دليل الحسابات</h3>
                                <p>إدارة شاملة لجميع الحسابات المالية</p>
                            </div>
                            <div class="header-actions">
                                <button id="add-account-btn" class="btn-modern primary" onclick="showAddAccountModal();">
                                    <i class="fas fa-plus"></i> حساب جديد
                                </button>
                                <button class="btn-modern secondary" onclick="exportAccounts()">
                                    <i class="fas fa-download"></i> تصدير
                                </button>
                            </div>
                        </div>

                        <!-- أدوات البحث والتصفية -->
                        <div class="accounts-toolbar">
                            <div class="search-section">
                                <div class="search-box-modern">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="accounts-search" placeholder="البحث في الحسابات...">
                                </div>
                            </div>
                            <div class="filter-section">
                                <select id="account-type-filter" class="filter-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="assets">الأصول</option>
                                    <option value="liabilities">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                    <option value="revenue">الإيرادات</option>
                                    <option value="expenses">المصروفات</option>
                                </select>
                                <select id="account-status-filter" class="filter-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <!-- عرض الحسابات بتصميم حديث -->
                        <div class="accounts-display">


                            <!-- جدول تفاصيل الحسابات -->
                            <div class="accounts-table-modern">
                                <div class="table-header">
                                    <h4>تفاصيل الحسابات</h4>
                                    <div class="table-actions">
                                        <button class="btn-icon" onclick="toggleTableView()" title="تبديل العرض">
                                            <i class="fas fa-th-list"></i>
                                        </button>
                                        <button class="btn-icon" onclick="printAccountsTable()" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="table-container">
                                    <table class="modern-table" id="accounts-table">
                                        <thead>
                                            <tr>
                                                <th>الرقم</th>
                                                <th>اسم الحساب</th>
                                                <th>النوع</th>
                                                <th>الرصيد</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="accounts-table-body">
                                            <!-- شجرة الحسابات -->
                                            <tr>
                                                <td><strong>1</strong></td>
                                                <td><strong>الأصول</strong></td>
                                                <td><span class="badge badge-primary">أصول</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal" style="display: flex !important; gap: 6px !important; justify-content: center !important;">
                                                        <button class="action-btn view" onclick="viewAccount('1')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('1')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 30px;"><strong>11</strong></td>
                                                <td style="padding-right: 30px;"><strong>الأصول المتداولة</strong></td>
                                                <td><span class="badge badge-primary">أصول متداولة</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('11')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('11')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 60px;"><strong>11030</strong></td>
                                                <td style="padding-right: 60px;"><strong>العملاء</strong></td>
                                                <td><span class="badge badge-info">عملاء</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('11030')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('11030')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 90px;">********</td>
                                                <td style="padding-right: 90px;">حافظ</td>
                                                <td><span class="badge badge-success">عميل</span></td>
                                                <td>0.00 ر.س</td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('********')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('********')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn delete" onclick="deleteAccount('********')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 90px;">********</td>
                                                <td style="padding-right: 90px;">حمدا</td>
                                                <td><span class="badge badge-success">عميل</span></td>
                                                <td>0.00 ر.س</td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('********')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('********')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                        <button class="action-btn delete" onclick="deleteAccount('********')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                                            <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- الخصوم والموردون -->
                                            <tr>
                                                <td><strong>2</strong></td>
                                                <td><strong>الخصوم</strong></td>
                                                <td><span class="badge badge-warning">خصوم</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('2')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('2')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding-right: 60px;"><strong>21030</strong></td>
                                                <td style="padding-right: 60px;"><strong>الموردون</strong></td>
                                                <td><span class="badge badge-warning">موردين</span></td>
                                                <td><strong>0.00 ر.س</strong></td>
                                                <td><span class="status-badge active">نشط</span></td>
                                                <td>
                                                    <div class="action-buttons-horizontal">
                                                        <button class="action-btn view" onclick="viewAccount('21030')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="action-btn edit" onclick="editAccount('21030')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- الأقسام المالية -->
            <div class="financial-sections">
                <div class="section-header">
                    <h3>الأقسام المالية</h3>
                    <button class="btn-sm add-category-btn"><i class="fas fa-plus"></i> إضافة فئة</button>
                </div>
                <div class="categories-container">
                    <div class="category-column">
                        <h4>فئات الإيرادات</h4>
                        <ul class="category-list" id="income-categories">
                            <!-- سيتم إضافة فئات الإيرادات هنا عبر JavaScript -->
                        </ul>
                    </div>
                    <div class="category-column">
                        <h4>فئات المصروفات</h4>
                        <ul class="category-list" id="expense-categories">
                            <!-- سيتم إضافة فئات المصروفات هنا عبر JavaScript -->
                        </ul>
                    </div>
                </div>
                </div>

                <!-- تبويب قيد اليومية -->
                <div class="tab-pane" id="journal-entries-tab">
                    <div class="journal-entries-section">
                        <div class="section-header">
                            <h3><i class="fas fa-journal-whills"></i> قيد اليومية</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-journal-entry-btn" onclick="showAddJournalEntryModal()">
                                    <i class="fas fa-plus"></i> قيد جديد
                                </button>
                                <div class="export-buttons">
                                    <button class="btn secondary-btn" onclick="printJournalEntries()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                    <button class="btn secondary-btn" onclick="exportJournalEntriesToExcel()">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </button>
                                    <button class="btn secondary-btn" onclick="exportJournalEntriesToPDF()">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جدول قيود اليومية -->
                        <div class="journal-entries-table-container">
                            <table class="journal-entries-table">
                                <thead>
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>البيان</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="journal-entries-table-body">
                                    <tr>
                                        <td>JE-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>قيد افتتاحي</td>
                                        <td><strong>10,000.00 ر.س</strong></td>
                                        <td><strong>10,000.00 ر.س</strong></td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewJournalEntry('JE-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editJournalEntry('JE-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteJournalEntry('JE-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>JE-2023-002</td>
                                        <td>2023-12-02</td>
                                        <td>مشتريات</td>
                                        <td><strong>5,000.00 ر.س</strong></td>
                                        <td><strong>5,000.00 ر.س</strong></td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewJournalEntry('JE-2023-002')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editJournalEntry('JE-2023-002')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteJournalEntry('JE-2023-002')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب سند القبض -->
                <div class="tab-pane" id="receipt-voucher-tab">
                    <div class="receipt-voucher-section">
                        <div class="section-header">
                            <h3><i class="fas fa-receipt"></i> سند القبض</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-receipt-btn" onclick="showAddReceiptVoucherModal()">
                                    <i class="fas fa-plus"></i> سند قبض جديد
                                </button>
                            </div>
                        </div>

                        <!-- جدول سندات القبض -->
                        <div class="receipt-vouchers-table-container">
                            <table class="receipt-vouchers-table">
                                <thead>
                                    <tr>
                                        <th>رقم السند</th>
                                        <th>التاريخ</th>
                                        <th>المستلم من</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="receipt-vouchers-table-body">
                                    <tr>
                                        <td>RC-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>أحمد محمد</td>
                                        <td><strong>2,500.00 ر.س</strong></td>
                                        <td>دفعة من العميل</td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewReceiptVoucher('RC-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editReceiptVoucher('RC-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deleteReceiptVoucher('RC-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب سند الصرف -->
                <div class="tab-pane" id="payment-voucher-tab">
                    <div class="payment-voucher-section">
                        <div class="section-header">
                            <h3><i class="fas fa-money-bill-wave"></i> سند الصرف</h3>
                            <div class="header-actions">
                                <button class="btn primary-btn add-payment-btn" onclick="showAddPaymentVoucherModal()">
                                    <i class="fas fa-plus"></i> سند صرف جديد
                                </button>
                            </div>
                        </div>

                        <!-- جدول سندات الصرف -->
                        <div class="payment-vouchers-table-container">
                            <table class="payment-vouchers-table">
                                <thead>
                                    <tr>
                                        <th>رقم السند</th>
                                        <th>التاريخ</th>
                                        <th>المدفوع إلى</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="payment-vouchers-table-body">
                                    <tr>
                                        <td>PV-2023-001</td>
                                        <td>2023-12-01</td>
                                        <td>شركة الكهرباء</td>
                                        <td><strong>1,200.00 ر.س</strong></td>
                                        <td>دفع فاتورة الكهرباء</td>
                                        <td>
                                            <div class="action-buttons-horizontal">
                                                <button class="action-btn view" onclick="viewPaymentVoucher('PV-2023-001')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-btn edit" onclick="editPaymentVoucher('PV-2023-001')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn delete" onclick="deletePaymentVoucher('PV-2023-001')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- نافذة إضافة حساب جديد -->
    <div id="add-account-modal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button class="close-btn" onclick="closeAddAccountModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-code">رقم الحساب *</label>
                            <input type="text" id="account-code" name="account-code" required
                                   placeholder="مثال: 1101" maxlength="10">
                        </div>
                        <div class="form-group">
                            <label for="account-name">اسم الحساب *</label>
                            <input type="text" id="account-name" name="account-name" required
                                   placeholder="مثال: البنك الأهلي">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-type">نوع الحساب *</label>
                            <select id="account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">أصول</option>
                                <option value="liabilities">خصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">إيرادات</option>
                                <option value="expenses">مصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent-account">الحساب الأب (اختياري)</label>
                            <select id="parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-balance">الرصيد الافتتاحي</label>
                            <input type="number" id="account-balance" name="account-balance"
                                   value="0" step="0.01" min="0">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn secondary-btn" onclick="closeAddAccountModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حساب جديد - قديمة -->
    <div class="modal" id="add-account-modal-old">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button class="close-modal" id="close-add-account-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-code">رقم الحساب *</label>
                            <input type="text" id="account-code" name="account-code" required
                                   placeholder="مثال: 1001" maxlength="10">
                            <small class="form-help">يجب أن يكون رقم الحساب فريداً</small>
                        </div>
                        <div class="form-group">
                            <label for="account-name">اسم الحساب *</label>
                            <input type="text" id="account-name" name="account-name" required
                                   placeholder="مثال: النقدية في الصندوق">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-type">نوع الحساب *</label>
                            <select id="account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">الأصول</option>
                                <option value="liabilities">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expenses">المصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent-account">الحساب الأب</label>
                            <select id="parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                                <!-- سيتم ملء الخيارات عبر JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="account-balance">الرصيد الافتتاحي</label>
                            <input type="number" id="account-balance" name="account-balance"
                                   step="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="account-status">حالة الحساب</label>
                            <select id="account-status" name="account-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="account-description">وصف الحساب</label>
                        <textarea id="account-description" name="account-description"
                                  rows="3" placeholder="وصف اختياري للحساب"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ الحساب
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-account">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة قيد جديد -->
    <div class="modal" id="add-journal-entry-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-journal-whills"></i> إضافة قيد جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-journal-entry-form">
                    <div class="form-group">
                        <label for="entry-date">التاريخ:</label>
                        <input type="date" id="entry-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="entry-description">البيان:</label>
                        <textarea id="entry-description" name="description" rows="3" placeholder="وصف القيد..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="debit-account">الحساب المدين:</label>
                        <select id="debit-account" name="debitAccount" required>
                            <option value="">اختر الحساب المدين</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="credit-account">الحساب الدائن:</label>
                        <select id="credit-account" name="creditAccount" required>
                            <option value="">اختر الحساب الدائن</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="entry-amount">المبلغ:</label>
                        <input type="number" id="entry-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ القيد
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-journal-entry">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة سند قبض -->
    <div class="modal" id="add-receipt-voucher-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-receipt"></i> إضافة سند قبض جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-receipt-voucher-form">
                    <div class="form-group">
                        <label for="receipt-date">التاريخ:</label>
                        <input type="date" id="receipt-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="received-from">المستلم من:</label>
                        <input type="text" id="received-from" name="receivedFrom" placeholder="اسم العميل أو الجهة..." required>
                    </div>
                    <div class="form-group">
                        <label for="receipt-amount">المبلغ:</label>
                        <input type="number" id="receipt-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="receipt-description">البيان:</label>
                        <textarea id="receipt-description" name="description" rows="3" placeholder="وصف سند القبض..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ السند
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-receipt-voucher">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة سند صرف -->
    <div class="modal" id="add-payment-voucher-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill-wave"></i> إضافة سند صرف جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-payment-voucher-form">
                    <div class="form-group">
                        <label for="payment-date">التاريخ:</label>
                        <input type="date" id="payment-date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="paid-to">المدفوع إلى:</label>
                        <input type="text" id="paid-to" name="paidTo" placeholder="اسم المورد أو الجهة..." required>
                    </div>
                    <div class="form-group">
                        <label for="payment-amount">المبلغ:</label>
                        <input type="number" id="payment-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="payment-description">البيان:</label>
                        <textarea id="payment-description" name="description" rows="3" placeholder="وصف سند الصرف..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ السند
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-add-payment-voucher">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حساب -->
    <div class="modal" id="edit-account-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> تعديل الحساب</h3>
                <button class="close-modal" id="close-edit-account-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-account-form">
                    <!-- نفس حقول النموذج السابق مع قيم محملة -->
                    <input type="hidden" id="edit-account-id" name="account-id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-code">رقم الحساب *</label>
                            <input type="text" id="edit-account-code" name="account-code" required
                                   maxlength="10">
                        </div>
                        <div class="form-group">
                            <label for="edit-account-name">اسم الحساب *</label>
                            <input type="text" id="edit-account-name" name="account-name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-type">نوع الحساب *</label>
                            <select id="edit-account-type" name="account-type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">الأصول</option>
                                <option value="liabilities">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expenses">المصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-parent-account">الحساب الأب</label>
                            <select id="edit-parent-account" name="parent-account">
                                <option value="">حساب رئيسي</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-account-balance">الرصيد الحالي</label>
                            <input type="number" id="edit-account-balance" name="account-balance"
                                   step="0.01" readonly>
                            <small class="form-help">الرصيد الحالي للقراءة فقط</small>
                        </div>
                        <div class="form-group">
                            <label for="edit-account-status">حالة الحساب</label>
                            <select id="edit-account-status" name="account-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-account-description">وصف الحساب</label>
                        <textarea id="edit-account-description" name="account-description"
                                  rows="3"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <button type="button" class="btn secondary-btn" id="cancel-edit-account">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حساب جديد - بسيطة وفعالة -->
    <div id="new-account-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
        <div style="background: white; border-radius: 12px; width: 500px; max-width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <!-- رأس النافذة -->
            <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; font-size: 18px;"><i class="fas fa-plus-circle"></i> إضافة حساب جديد</h3>
                <button onclick="closeNewAccountModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div style="padding: 25px;">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">رقم الحساب *</label>
                    <input type="text" id="new-account-code" placeholder="مثال: 1101" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;" maxlength="10">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">اسم الحساب *</label>
                    <input type="text" id="new-account-name" placeholder="مثال: البنك الأهلي" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">نوع الحساب *</label>
                    <select id="new-account-type" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; background: white;">
                        <option value="">اختر نوع الحساب</option>
                        <option value="assets">أصول</option>
                        <option value="liabilities">خصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">إيرادات</option>
                        <option value="expenses">مصروفات</option>
                    </select>
                </div>

                <div style="margin-bottom: 25px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">الرصيد الافتتاحي</label>
                    <input type="number" id="new-account-balance" placeholder="0.00" value="0" step="0.01" min="0" style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                </div>

                <!-- أزرار الحفظ والإلغاء -->
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="closeNewAccountModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button onclick="saveNewAccountSimple()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold; box-shadow: 0 3px 10px rgba(40,167,69,0.3);">
                        <i class="fas fa-save"></i> حفظ الحساب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- القدم -->
    <footer class="main-footer">
        <div class="container">
            <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة الأعمال</p>
        </div>
    </footer>

    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

    <script src="js/data-manager.js"></script>
    <script src="js/main.js"></script>
    <script src="js/accounting.js"></script>
    <script src="js/accounting-fix.js"></script>
    <script src="js/modal-fix.js"></script>
    <script src="js/chart-of-accounts-data.js"></script>
    <script src="js/chart-of-accounts-modern.js"></script>

    <!-- سكريبت إضافي لإصلاح الأيقونات -->
    <script>
        // تطبيق التنسيق على جميع الأيقونات عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('تطبيق تنسيق الأيقونات عند تحميل الصفحة...');

            // تنسيق جميع الأيقونات
            function fixAllIcons() {
                // تحديد جميع الأزرار
                const allButtons = document.querySelectorAll('.action-btn');

                // تطبيق التنسيق على كل زر
                allButtons.forEach(btn => {
                    // تنسيق الزر
                    btn.style.width = '26px';
                    btn.style.height = '26px';
                    btn.style.borderRadius = '4px';
                    btn.style.border = 'none';
                    btn.style.display = 'flex';
                    btn.style.alignItems = 'center';
                    btn.style.justifyContent = 'center';
                    btn.style.margin = '0';
                    btn.style.padding = '0';

                    // تنسيق حسب النوع
                    if (btn.classList.contains('view')) {
                        btn.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('edit')) {
                        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('delete')) {
                        btn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                        btn.style.color = 'white';
                    }

                    // تنسيق الأيقونة
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.color = 'white';
                        icon.style.fontSize = '10px';
                    }
                });

                // تنسيق حاويات الأزرار
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    container.style.display = 'flex';
                    container.style.flexDirection = 'row';
                    container.style.gap = '6px';
                    container.style.justifyContent = 'center';
                });
            }

            // تطبيق التنسيق عند التحميل
            fixAllIcons();

            // تطبيق التنسيق بعد تأخير
            setTimeout(fixAllIcons, 500);
            setTimeout(fixAllIcons, 1000);
            setTimeout(fixAllIcons, 2000);

            // تطبيق التنسيق عند النقر على أي تبويب
            const allTabs = document.querySelectorAll('.tab-link, .nav-link, .account-type-filter');
            allTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    setTimeout(fixAllIcons, 100);
                    setTimeout(fixAllIcons, 500);
                });
            });

            // تطبيق التنسيق كل ثانية
            setInterval(fixAllIcons, 1000);
        });
    </script>

    <script>
        // دالة تحميل الحسابات من النظام المركزي
        function loadAccountsFromCentralSystem() {
            console.log('تحميل الحسابات من النظام المركزي...');

            // تحميل الحسابات من localStorage
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إضافة الحسابات الافتراضية إذا لم تكن موجودة
            if (accounts.length === 0) {
                accounts = createDefaultAccounts();
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
            }

            // إذا لم تكن هناك حسابات، إنشاء الحسابات الأساسية
            if (accounts.length === 0) {
                // إنشاء الحسابات الأساسية
                const basicAccounts = [
                    // الأصول
                    {
                        id: 1,
                        code: '1',
                        name: 'الأصول',
                        type: 'assets',
                        level: 1,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 11,
                        code: '11',
                        name: 'الأصول المتداولة',
                        type: 'assets',
                        subType: 'current_assets',
                        parentCode: '1',
                        level: 2,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 1103,
                        code: '1103',
                        name: 'المدينون',
                        type: 'assets',
                        subType: 'current_assets',
                        parentCode: '11',
                        level: 3,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 11030,
                        code: '11030',
                        name: 'العملاء',
                        type: 'assets',
                        subType: 'current_assets',
                        category: 'customers',
                        parentCode: '1103',
                        level: 3,
                        balance: 0,
                        status: 'active'
                    },
                    // الخصوم
                    {
                        id: 2,
                        code: '2',
                        name: 'الخصوم',
                        type: 'liabilities',
                        level: 1,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 21030,
                        code: '21030',
                        name: 'الموردون',
                        type: 'liabilities',
                        subType: 'current_liabilities',
                        category: 'suppliers',
                        parentCode: '2',
                        level: 2,
                        balance: 0,
                        status: 'active'
                    }
                ];

                // إضافة الحسابات الأساسية
                accounts = basicAccounts;

                // حفظ الحسابات في localStorage
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                console.log('تم إنشاء الحسابات الأساسية');
            }

            console.log('الحسابات المحملة:', accounts);

            // تحديث جدول الحسابات
            updateAccountsTable(accounts);

            // تطبيق تنسيق الأيقونات بعد تحميل الحسابات
            setTimeout(function() {
                // تنسيق الأيقونات في دليل الحسابات
                const accountsButtons = document.querySelectorAll('#accounts-table-body .action-btn');
                accountsButtons.forEach(btn => {
                    if (btn.classList.contains('view')) {
                        btn.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('edit')) {
                        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                        btn.style.color = 'white';
                    } else if (btn.classList.contains('delete')) {
                        btn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                        btn.style.color = 'white';
                    }

                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.color = 'white';
                    }
                });
            }, 500);

            if (!window.dataManager) {
                console.error('النظام المركزي غير متاح');
                return;
            }

            // تحميل الحسابات من localStorage أولاً
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // إذا لم تكن هناك حسابات في localStorage، حاول تحميلها من النظام المركزي
            if (accounts.length === 0 && window.dataManager) {
                accounts = window.dataManager.getAccounts() || [];
            }

            console.log('الحسابات المحملة:', accounts);

            // تحديث جدول الحسابات
            updateAccountsTable(accounts);
        }

        // دالة تحديث جدول الحسابات
        function updateAccountsTable(accounts) {
            const tableBody = document.getElementById('accounts-table-body');
            if (!tableBody) {
                console.error('جدول الحسابات غير موجود');
                return;
            }

            // مسح الجدول الحالي
            tableBody.innerHTML = '';

            // ترتيب الحسابات حسب الكود
            accounts.sort((a, b) => a.code.localeCompare(b.code));

            // بناء شجرة الحسابات
            const accountTree = buildAccountTree(accounts);

            // عرض الحسابات بشكل شجرة
            renderAccountTree(accountTree, tableBody);

            console.log(`تم عرض ${accounts.length} حساب في الجدول`);
        }

        // دالة بناء شجرة الحسابات
        function buildAccountTree(accounts) {
            // إنشاء قاموس للحسابات
            const accountMap = {};
            accounts.forEach(account => {
                accountMap[account.code] = { ...account, children: [] };
            });

            // بناء الشجرة
            const rootAccounts = [];
            accounts.forEach(account => {
                if (account.parentCode && accountMap[account.parentCode]) {
                    // إضافة الحساب كابن للحساب الأب
                    accountMap[account.parentCode].children.push(accountMap[account.code]);
                } else {
                    // إضافة الحساب كحساب جذر
                    rootAccounts.push(accountMap[account.code]);
                }
            });

            return rootAccounts;
        }

        // دالة عرض شجرة الحسابات
        function renderAccountTree(accountTree, tableBody, level = 0) {
            accountTree.forEach(account => {
                const row = document.createElement('tr');

                // تحديد المسافة البادئة حسب المستوى
                const paddingRight = level * 30;

                // تحديد نوع الحساب
                let accountTypeLabel = '';
                let badgeClass = '';

                if (account.category === 'customers') {
                    accountTypeLabel = 'عميل';
                    badgeClass = 'badge-success';
                } else if (account.category === 'suppliers') {
                    accountTypeLabel = 'مورد';
                    badgeClass = 'badge-warning';
                } else {
                    switch (account.type) {
                        case 'assets':
                            accountTypeLabel = 'أصول';
                            badgeClass = 'badge-primary';
                            break;
                        case 'liabilities':
                            accountTypeLabel = 'خصوم';
                            badgeClass = 'badge-warning';
                            break;
                        case 'equity':
                            accountTypeLabel = 'حقوق الملكية';
                            badgeClass = 'badge-info';
                            break;
                        case 'revenue':
                            accountTypeLabel = 'إيرادات';
                            badgeClass = 'badge-success';
                            break;
                        case 'expenses':
                            accountTypeLabel = 'مصروفات';
                            badgeClass = 'badge-danger';
                            break;
                        default:
                            accountTypeLabel = account.type;
                            badgeClass = 'badge-secondary';
                    }
                }

                // إضافة أيقونة الشجرة
                const treeIcon = level > 0 ? '<i class="fas fa-level-up-alt fa-rotate-90" style="margin-left: 8px; color: #aaa;"></i>' : '';

                // تحديد ما إذا كان الحساب رئيسيًا (له أبناء)
                const isParent = account.children && account.children.length > 0;
                const nameStyle = isParent ? 'font-weight: bold;' : '';

                row.innerHTML = `
                    <td style="padding-right: ${paddingRight}px;"><strong>${account.code}</strong></td>
                    <td style="padding-right: ${paddingRight}px; ${nameStyle}">${treeIcon} ${account.name}</td>
                    <td><span class="badge ${badgeClass}">${accountTypeLabel}</span></td>
                    <td>${(account.balance || 0).toFixed(2)} ر.س</td>
                    <td><span class="status-badge ${account.status || 'active'}">${account.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons-horizontal" style="display: flex !important; gap: 6px !important; justify-content: center !important;">
                            <button class="action-btn view" onclick="viewAccount('${account.code}')" title="عرض" style="background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-eye" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn edit" onclick="editAccount('${account.code}')" title="تعديل" style="background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-edit" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteAccount('${account.code}')" title="حذف" style="background: linear-gradient(135deg, #dc3545, #c82333) !important; color: white !important; width: 26px !important; height: 26px !important; border-radius: 4px !important; border: none !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 10px !important;">
                                <i class="fas fa-trash" style="color: white !important; font-size: 10px !important;"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);

                // عرض الحسابات الفرعية بشكل متسلسل
                if (account.children && account.children.length > 0) {
                    renderAccountTree(account.children, tableBody, level + 1);
                }
            });
        }

        // دالة مساعدة لتحديد نوع الحساب
        function getAccountTypeBadge(type) {
            const badges = {
                'assets': 'primary',
                'liabilities': 'warning',
                'equity': 'info',
                'revenue': 'success',
                'expenses': 'danger'
            };
            return badges[type] || 'secondary';
        }

        // دالة مساعدة لتحديد اسم نوع الحساب
        function getAccountTypeLabel(type) {
            const labels = {
                'assets': 'أصول',
                'liabilities': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expenses': 'مصروفات',
                'customers': 'عميل',
                'suppliers': 'مورد'
            };
            return labels[type] || type;
        }

        // دالة إنشاء الحسابات الافتراضية
        function createDefaultAccounts() {
            return [
                // الأصول الرئيسية
                { id: 1, code: '1', name: 'الأصول', type: 'assets', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 11, code: '11', name: 'الأصول المتداولة', type: 'assets', balance: 0, status: 'active', level: 2, parentCode: '1', createdAt: new Date().toISOString() },
                { id: 1101, code: '1101', name: 'النقدية', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '11', createdAt: new Date().toISOString() },
                { id: 110101, code: '110101', name: 'الصندوق', type: 'assets', balance: 0, status: 'active', level: 4, parentCode: '1101', createdAt: new Date().toISOString() },
                { id: 110102, code: '110102', name: 'البنك الأهلي', type: 'assets', balance: 0, status: 'active', level: 4, parentCode: '1101', createdAt: new Date().toISOString() },
                { id: 1102, code: '1102', name: 'العملاء', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '11', createdAt: new Date().toISOString() },

                // الأصول الثابتة
                { id: 12, code: '12', name: 'الأصول الثابتة', type: 'assets', balance: 0, status: 'active', level: 2, parentCode: '1', createdAt: new Date().toISOString() },
                { id: 1201, code: '1201', name: 'المباني', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '12', createdAt: new Date().toISOString() },
                { id: 1202, code: '1202', name: 'المعدات', type: 'assets', balance: 0, status: 'active', level: 3, parentCode: '12', createdAt: new Date().toISOString() },

                // الخصوم الرئيسية
                { id: 2, code: '2', name: 'الخصوم', type: 'liabilities', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 21, code: '21', name: 'الخصوم المتداولة', type: 'liabilities', balance: 0, status: 'active', level: 2, parentCode: '2', createdAt: new Date().toISOString() },
                { id: 2101, code: '2101', name: 'الموردون', type: 'liabilities', balance: 0, status: 'active', level: 3, parentCode: '21', createdAt: new Date().toISOString() },
                { id: 2102, code: '2102', name: 'المصروفات المستحقة', type: 'liabilities', balance: 0, status: 'active', level: 3, parentCode: '21', createdAt: new Date().toISOString() },

                // حقوق الملكية
                { id: 3, code: '3', name: 'حقوق الملكية', type: 'equity', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 31, code: '31', name: 'رأس المال', type: 'equity', balance: 0, status: 'active', level: 2, parentCode: '3', createdAt: new Date().toISOString() },
                { id: 32, code: '32', name: 'الأرباح المحتجزة', type: 'equity', balance: 0, status: 'active', level: 2, parentCode: '3', createdAt: new Date().toISOString() },

                // الإيرادات
                { id: 4, code: '4', name: 'الإيرادات', type: 'revenue', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 41, code: '41', name: 'إيرادات المبيعات', type: 'revenue', balance: 0, status: 'active', level: 2, parentCode: '4', createdAt: new Date().toISOString() },
                { id: 42, code: '42', name: 'إيرادات أخرى', type: 'revenue', balance: 0, status: 'active', level: 2, parentCode: '4', createdAt: new Date().toISOString() },

                // المصروفات
                { id: 5, code: '5', name: 'المصروفات', type: 'expenses', balance: 0, status: 'active', level: 1, parentCode: null, createdAt: new Date().toISOString() },
                { id: 51, code: '51', name: 'تكلفة البضاعة المباعة', type: 'expenses', balance: 0, status: 'active', level: 2, parentCode: '5', createdAt: new Date().toISOString() },
                { id: 52, code: '52', name: 'المصروفات الإدارية', type: 'expenses', balance: 0, status: 'active', level: 2, parentCode: '5', createdAt: new Date().toISOString() },
                { id: 5201, code: '5201', name: 'الرواتب', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() },
                { id: 5202, code: '5202', name: 'الإيجار', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() },
                { id: 5203, code: '5203', name: 'الكهرباء والماء', type: 'expenses', balance: 0, status: 'active', level: 3, parentCode: '52', createdAt: new Date().toISOString() }
            ];
        }

        // دالة التحقق من صحة رقم الحساب
        function validateAccountCode(code, parentCode = null) {
            // التحقق من أن الرقم ليس فارغًا
            if (!code || code.trim() === '') {
                return { valid: false, message: 'رقم الحساب مطلوب' };
            }

            // التحقق من أن الرقم يحتوي على أرقام فقط
            if (!/^\d+$/.test(code)) {
                return { valid: false, message: 'رقم الحساب يجب أن يحتوي على أرقام فقط' };
            }

            // التحقق من طول الرقم
            if (code.length > 10) {
                return { valid: false, message: 'رقم الحساب لا يجب أن يزيد عن 10 أرقام' };
            }

            // التحقق من التسلسل الهرمي إذا كان هناك حساب أب
            if (parentCode) {
                if (!code.startsWith(parentCode)) {
                    return { valid: false, message: `رقم الحساب يجب أن يبدأ برقم الحساب الأب: ${parentCode}` };
                }

                if (code.length <= parentCode.length) {
                    return { valid: false, message: 'رقم الحساب الفرعي يجب أن يكون أطول من رقم الحساب الأب' };
                }
            }

            return { valid: true, message: 'رقم الحساب صحيح' };
        }

        function getAccountTypeText(type) {
            const types = {
                'assets': 'الأصول',
                'liabilities': 'الخصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'الإيرادات',
                'expenses': 'المصروفات'
            };
            return types[type] || 'غير محدد';
        }

        // تحميل الحسابات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تحميل الصفحة...');

            // تحميل الحسابات من النظام المركزي
            loadAccountsFromCentralSystem();

            // إضافة مستمعي الأحداث للتبويبات
            setupTabNavigation();

            // تطبيق التنسيق كل ثانية للتأكد من التطبيق
            setInterval(forceHorizontalButtons, 1000);
        });

        // دالة إعداد التنقل بين التبويبات
        function setupTabNavigation() {
            console.log('إعداد التنقل بين التبويبات...');

            // إضافة مستمعي الأحداث للتبويبات
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    console.log('تم النقر على التبويب:', tabId);

                    // إزالة الفئة النشطة من جميع التبويبات
                    document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');

                    // إظهار المحتوى المناسب
                    const tabContent = document.getElementById(tabId + '-tab');
                    if (tabContent) {
                        tabContent.classList.add('active');
                        console.log('تم تفعيل المحتوى:', tabId + '-tab');
                    } else {
                        console.error('لم يتم العثور على محتوى التبويب:', tabId + '-tab');
                    }

                    // إذا كان التبويب هو دليل الحسابات، قم بتحميل الحسابات
                    if (tabId === 'chart-of-accounts') {
                        setTimeout(() => {
                            loadAccountsFromCentralSystem();
                        }, 100);
                    }
                });
            });

            // إضافة مستمع حدث لزر إضافة الحساب
            const addAccountBtn = document.getElementById('add-account-btn');
            if (addAccountBtn) {
                console.log('تم العثور على زر إضافة الحساب');
                addAccountBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('تم النقر على زر إضافة الحساب من خلال addEventListener');
                    showAddAccountModal();
                });
            } else {
                console.error('لم يتم العثور على زر إضافة الحساب');
            }
        }



        // إجبار الأيقونات على النمط الصحيح
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق التنسيق على جميع الأيقونات
            function forceHorizontalButtons() {
                console.log('تطبيق تنسيق الأيقونات...');

                // تنسيق الحاويات
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    container.style.setProperty('display', 'flex', 'important');
                    container.style.setProperty('flex-direction', 'row', 'important');
                    container.style.setProperty('flex-wrap', 'nowrap', 'important');
                    container.style.setProperty('gap', '6px', 'important');
                    container.style.setProperty('justify-content', 'center', 'important');
                    container.style.setProperty('align-items', 'center', 'important');
                    container.style.setProperty('padding', '3px', 'important');
                });

                // تنسيق الأزرار
                const buttons = document.querySelectorAll('.action-btn');
                buttons.forEach(btn => {
                    btn.style.setProperty('width', '26px', 'important');
                    btn.style.setProperty('height', '26px', 'important');
                    btn.style.setProperty('border-radius', '4px', 'important');
                    btn.style.setProperty('border', 'none', 'important');
                    btn.style.setProperty('display', 'flex', 'important');
                    btn.style.setProperty('align-items', 'center', 'important');
                    btn.style.setProperty('justify-content', 'center', 'important');
                    btn.style.setProperty('cursor', 'pointer', 'important');
                    btn.style.setProperty('transition', 'all 0.2s ease', 'important');
                    btn.style.setProperty('font-size', '10px', 'important');
                    btn.style.setProperty('margin', '0', 'important');
                    btn.style.setProperty('padding', '0', 'important');

                    if (btn.classList.contains('view')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8, #138496)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    } else if (btn.classList.contains('edit')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #28a745, #20c997)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    } else if (btn.classList.contains('delete')) {
                        btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545, #c82333)', 'important');
                        btn.style.setProperty('color', 'white', 'important');
                    }

                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.style.setProperty('color', 'white', 'important');
                        icon.style.setProperty('font-size', '10px', 'important');
                    }

                    // إضافة تأثيرات التحويم
                    btn.addEventListener('mouseenter', function() {
                        this.style.setProperty('transform', 'translateY(-2px)', 'important');
                        this.style.setProperty('box-shadow', '0 4px 12px rgba(0,0,0,0.15)', 'important');
                    });

                    btn.addEventListener('mouseleave', function() {
                        this.style.setProperty('transform', 'translateY(0)', 'important');
                        this.style.setProperty('box-shadow', 'none', 'important');
                    });
                });
            }

            // تطبيق التنسيق عند تحميل الصفحة
            forceHorizontalButtons();

            // إعادة تطبيق التنسيق عند تحديث المحتوى
            const observer = new MutationObserver(forceHorizontalButtons);
            observer.observe(document.body, { childList: true, subtree: true });

            // تطبيق التنسيق عند النقر على تبويب دليل الحسابات
            const accountsTabLinks = document.querySelectorAll('.nav-tab');
            accountsTabLinks.forEach(link => {
                link.addEventListener('click', function() {
                    console.log('تم النقر على تبويب:', this.getAttribute('data-tab'));

                    // إزالة الفئة النشطة من جميع التبويبات
                    document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');
                    const targetTab = this.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetTab + '-tab');
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }

                    // إعادة تحميل الحسابات عند فتح تبويب دليل الحسابات
                    if (targetTab === 'chart-of-accounts') {
                        setTimeout(() => {
                            loadAccountsFromCentralSystem();
                        }, 100);
                    }

                    // تأخير قليل للسماح بتحديث المحتوى
                    setTimeout(forceHorizontalButtons, 100);
                    setTimeout(forceHorizontalButtons, 500);
                });
            });

            // تطبيق التنسيق عند تغيير عرض الحسابات
            const accountTypeFilters = document.querySelectorAll('.account-type-filter');
            accountTypeFilters.forEach(filter => {
                filter.addEventListener('click', function() {
                    // تأخير قليل للسماح بتحديث المحتوى
                    setTimeout(forceHorizontalButtons, 100);
                    setTimeout(forceHorizontalButtons, 500);
                });
            });

            // تطبيق التنسيق كل ثانية للتأكد من التطبيق
            setInterval(forceHorizontalButtons, 1000);
        });
















        // دالة للحصول على تسمية نوع الحساب
        function getAccountTypeLabel(type) {
            const types = {
                'assets': 'أصول',
                'liabilities': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expenses': 'مصروفات'
            };
            return types[type] || type;
        }

        // دالة فتح نافذة إضافة حساب جديد
        function showAddAccountModal() {
            console.log('فتح نافذة إضافة حساب جديد');
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.classList.add('active');
                loadParentAccounts();

                // مسح النموذج
                document.getElementById('add-account-form').reset();

                // التركيز على أول حقل
                document.getElementById('account-code').focus();
            }
        }

        // دالة إغلاق نافذة إضافة حساب جديد
        function closeAddAccountModal() {
            console.log('إغلاق نافذة إضافة حساب جديد');
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }

        // دالة تحميل الحسابات الأب
        function loadParentAccounts() {
            const parentSelect = document.getElementById('parent-account');
            const accountTypeSelect = document.getElementById('account-type');

            if (!parentSelect) return;

            // تحميل الحسابات الحالية
            let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

            // تصفية الحسابات حسب النوع المحدد
            const selectedType = accountTypeSelect.value;
            if (selectedType) {
                accounts = accounts.filter(account => account.type === selectedType);
            }

            // مسح الخيارات الحالية
            parentSelect.innerHTML = '<option value="">حساب رئيسي</option>';

            // إضافة الحسابات كخيارات
            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.code;

                // إضافة مسافات بادئة حسب المستوى
                const indent = '  '.repeat((account.level || 1) - 1);
                option.textContent = `${indent}${account.code} - ${account.name}`;

                parentSelect.appendChild(option);
            });
        }

        // دالة حفظ الحساب الجديد
        function saveNewAccount() {
            console.log('حفظ حساب جديد');

            // جمع البيانات من النموذج
            const code = document.getElementById('account-code').value.trim();
            const name = document.getElementById('account-name').value.trim();
            const type = document.getElementById('account-type').value;
            const parentCode = document.getElementById('parent-account').value;
            const balance = parseFloat(document.getElementById('account-balance').value) || 0;

            // التحقق من صحة البيانات
            if (!code) {
                alert('يرجى إدخال رقم الحساب');
                return;
            }

            if (!name) {
                alert('يرجى إدخال اسم الحساب');
                return;
            }

            if (!type) {
                alert('يرجى اختيار نوع الحساب');
                return;
            }

            // تحديد مستوى الحساب
            let level = 1;
            let parentAccountData = null;

            if (parentCode) {
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                parentAccountData = accounts.find(acc => acc.code === parentCode);
                if (parentAccountData) {
                    level = (parentAccountData.level || 1) + 1;
                }
            }

            // إنشاء كائن الحساب
            const account = {
                id: Date.now(),
                code: code,
                name: name,
                type: type,
                balance: balance,
                status: 'active',
                level: level,
                parentCode: parentCode || null,
                parentName: parentAccountData ? parentAccountData.name : null,
                createdAt: new Date().toISOString()
            };

            try {
                // تحميل الحسابات الحالية
                let accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];

                // التحقق من عدم تكرار رقم الحساب
                if (accounts.some(a => a.code === code)) {
                    alert('رقم الحساب موجود بالفعل. يرجى استخدام رقم آخر.');
                    return;
                }

                // إضافة الحساب الجديد
                accounts.push(account);

                // ترتيب الحسابات حسب الكود
                accounts.sort((a, b) => a.code.localeCompare(b.code));

                // حفظ الحسابات
                localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                // إغلاق النافذة
                closeAddAccountModal();

                // عرض رسالة نجاح
                alert(`تم إضافة الحساب بنجاح!\n\nرقم الحساب: ${code}\nاسم الحساب: ${name}\nالنوع: ${getAccountTypeLabel(type)}\nالمستوى: ${level}${parentAccountData ? '\nالحساب الأب: ' + parentAccountData.name : ''}`);

                // تحديث الجدول
                updateAccountsTable(accounts);

            } catch (error) {
                console.error('خطأ في حفظ الحساب:', error);
                alert('حدث خطأ أثناء حفظ الحساب. يرجى المحاولة مرة أخرى.');
            }
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // ربط النموذج بدالة الحفظ
            const form = document.getElementById('add-account-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveNewAccount();
                });
            }

            // تحديث قائمة الحسابات الأب عند تغيير نوع الحساب
            const accountTypeSelect = document.getElementById('account-type');
            if (accountTypeSelect) {
                accountTypeSelect.addEventListener('change', function() {
                    loadParentAccounts();
                });
            }

            // إغلاق النافذة عند النقر خارجها
            const modal = document.getElementById('add-account-modal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeAddAccountModal();
                    }
                });
            }
        });
    </script>
</body>
</html>