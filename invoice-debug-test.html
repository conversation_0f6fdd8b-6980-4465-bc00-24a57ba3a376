<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة حفظ الفاتورة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #dc3545;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(220,53,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        .debug-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #dc3545;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة حفظ الفاتورة</h1>

        <!-- إعداد البيانات -->
        <div class="test-section">
            <h2>🛠️ إعداد البيانات للاختبار</h2>
            <button class="btn success" onclick="setupTestData()">إعداد بيانات تجريبية</button>
            <button class="btn primary" onclick="checkCurrentData()">فحص البيانات الحالية</button>
            <div id="setup-result"></div>
        </div>

        <!-- تشخيص المشكلة -->
        <div class="test-section">
            <h2>🔍 تشخيص المشكلة</h2>
            <button class="btn" onclick="simulateInvoiceCreation()">محاكاة إنشاء فاتورة</button>
            <button class="btn primary" onclick="openSalesWithDebug()">فتح المبيعات مع التشخيص</button>
            <div id="debug-result"></div>
        </div>

        <!-- سجل التشخيص -->
        <div class="test-section">
            <h2>📋 سجل التشخيص</h2>
            <button class="btn primary" onclick="clearLog()">مسح السجل</button>
            <div id="debug-log" class="debug-log">جاري انتظار عمليات التشخيص...</div>
        </div>

        <!-- خطوات الحل -->
        <div class="test-section">
            <h2>💡 خطوات الحل المقترحة</h2>
            <div class="step-list">
                <h3>للتأكد من حل المشكلة:</h3>
                <ol>
                    <li><strong>اضغط "إعداد بيانات تجريبية"</strong> - لضمان وجود منتجات وعملاء</li>
                    <li><strong>اضغط "محاكاة إنشاء فاتورة"</strong> - لاختبار العملية محلياً</li>
                    <li><strong>اضغط "فتح المبيعات مع التشخيص"</strong> - لفتح الصفحة مع console مفتوح</li>
                    <li><strong>في صفحة المبيعات:</strong>
                        <ul>
                            <li>افتح Developer Tools (F12)</li>
                            <li>اذهب لتبويب Console</li>
                            <li>اضغط "فاتورة جديدة"</li>
                            <li>اختر عميل ومنتج</li>
                            <li>تأكد من ملء الكمية والسعر</li>
                            <li>اضغط "حفظ الفاتورة"</li>
                            <li>راقب الرسائل في Console</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            updateDebugDisplay();
            console.log(logEntry);
        }

        function updateDebugDisplay() {
            document.getElementById('debug-log').textContent = debugLog.join('\n');
        }

        function clearLog() {
            debugLog = [];
            updateDebugDisplay();
        }

        // إعداد بيانات تجريبية
        function setupTestData() {
            log('🛠️ بدء إعداد البيانات التجريبية...');
            
            // إضافة منتجات
            const testProducts = [
                { id: 'prod-1', name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000, quantity: 10, minStock: 2 },
                { id: 'prod-2', name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2 },
                { id: 'prod-3', name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5 }
            ];

            localStorage.setItem('monjizProducts', JSON.stringify(testProducts));
            log(`✅ تم إضافة ${testProducts.length} منتج`);

            // إضافة عملاء
            const testCustomers = [
                { id: 'cust-1', name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { id: 'cust-2', name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' }
            ];

            localStorage.setItem('monjizCustomers', JSON.stringify(testCustomers));
            log(`✅ تم إضافة ${testCustomers.length} عميل`);

            localStorage.setItem('monjizDataUpdate', Date.now().toString());
            log('✅ تم إرسال إشعار التحديث');

            showResult('setup-result', '✅ تم إعداد البيانات التجريبية بنجاح!<br>📦 3 منتجات + 👥 2 عميل', 'success');
        }

        // فحص البيانات الحالية
        function checkCurrentData() {
            log('🔍 فحص البيانات الحالية...');
            
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            log(`📦 المنتجات: ${products.length}`);
            products.forEach((product, index) => {
                log(`  ${index + 1}. ${product.name} (${product.id}) - ${product.price} ر.س`);
            });
            
            log(`👥 العملاء: ${customers.length}`);
            customers.forEach((customer, index) => {
                log(`  ${index + 1}. ${customer.name} (${customer.id}) - ${customer.type}`);
            });

            let result = `<div class="info">📊 البيانات الحالية:</div>`;
            result += `<p>📦 المنتجات: ${products.length}</p>`;
            result += `<p>👥 العملاء: ${customers.length}</p>`;
            
            if (products.length > 0 && customers.length > 0) {
                result += '<div class="success">✅ البيانات جاهزة للاختبار</div>';
            } else {
                result += '<div class="error">❌ البيانات غير كافية - اضغط "إعداد بيانات تجريبية"</div>';
            }
            
            document.getElementById('setup-result').innerHTML = result;
        }

        // محاكاة إنشاء فاتورة
        function simulateInvoiceCreation() {
            log('🧪 بدء محاكاة إنشاء فاتورة...');
            
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            if (products.length === 0 || customers.length === 0) {
                log('❌ لا توجد بيانات كافية للمحاكاة');
                showResult('debug-result', '❌ يرجى إعداد البيانات التجريبية أولاً', 'error');
                return;
            }

            // محاكاة بيانات الفاتورة
            const selectedCustomer = customers[0];
            const selectedProduct = products[0];
            const quantity = 2;
            const price = selectedProduct.price;

            log(`👤 العميل المختار: ${selectedCustomer.name} (${selectedCustomer.id})`);
            log(`📦 المنتج المختار: ${selectedProduct.name} (${selectedProduct.id})`);
            log(`🔢 الكمية: ${quantity}`);
            log(`💰 السعر: ${price} ر.س`);
            log(`💰 الإجمالي: ${quantity * price} ر.س`);

            // محاكاة التحقق من صحة البيانات
            const mockProducts = [{
                productId: selectedProduct.id,
                productName: selectedProduct.name,
                quantity: quantity,
                price: price,
                total: quantity * price
            }];

            log(`✅ تم تجميع ${mockProducts.length} منتج للفاتورة`);

            if (mockProducts.length > 0) {
                log('✅ التحقق من صحة البيانات: نجح');
                
                const subtotal = mockProducts.reduce((sum, product) => sum + product.total, 0);
                const tax = subtotal * 0.15;
                const total = subtotal + tax;

                log(`💰 المجموع الفرعي: ${subtotal} ر.س`);
                log(`💰 الضريبة (15%): ${tax} ر.س`);
                log(`💰 الإجمالي النهائي: ${total} ر.س`);

                const invoice = {
                    id: 'INV-TEST-' + Date.now(),
                    date: new Date().toLocaleDateString('ar-SA'),
                    customerId: selectedCustomer.id,
                    customerName: selectedCustomer.name,
                    products: mockProducts,
                    subtotal: subtotal,
                    tax: tax,
                    total: total
                };

                log('✅ تم إنشاء الفاتورة بنجاح');
                log(`📄 رقم الفاتورة: ${invoice.id}`);

                showResult('debug-result', '✅ المحاكاة نجحت!<br>الفاتورة يمكن إنشاؤها بدون مشاكل<br>💡 المشكلة قد تكون في واجهة المستخدم', 'success');
            } else {
                log('❌ فشل في تجميع المنتجات');
                showResult('debug-result', '❌ فشلت المحاكاة - مشكلة في تجميع المنتجات', 'error');
            }
        }

        // فتح المبيعات مع التشخيص
        function openSalesWithDebug() {
            log('🚀 فتح صفحة المبيعات...');
            log('💡 تأكد من فتح Developer Tools (F12) ومراقبة Console');
            
            window.open('sales.html', '_blank');
            
            showResult('debug-result', '🚀 تم فتح صفحة المبيعات<br>💡 افتح Developer Tools (F12) وراقب Console عند إنشاء الفاتورة', 'info');
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            log('🔍 بدء تشخيص مشكلة حفظ الفاتورة');
            checkCurrentData();
        });
    </script>
</body>
</html>
