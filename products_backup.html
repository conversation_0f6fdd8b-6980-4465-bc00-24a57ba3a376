<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الأعمال - المنتجات والمخزون</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accounting.css">
    <link rel="stylesheet" href="css/accounting-fix.css">
    <link rel="stylesheet" href="css/chart-of-accounts.css">
    <link rel="stylesheet" href="css/action-buttons-horizontal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* تنسيق متناسق مع باقي الصفحات */
        .products-management-modern {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px 0;
        }
        
        .section-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title h3 {
            margin: 0 0 5px 0;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .header-title p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn-modern {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        /* البطاقات الإحصائية */
        .stats-section-modern {
            padding: 25px;
            background: #f8f9fa;
        }
        
        .stats-grid-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .stat-card-modern {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.3s ease;
        }
        
        .stat-card-modern:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon-modern {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .stat-card-modern.primary .stat-icon-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-card-modern.success .stat-icon-modern {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .stat-card-modern.warning .stat-icon-modern {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        }
        
        .stat-card-modern.danger .stat-icon-modern {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .stat-content-modern h4 {
            margin: 0 0 5px 0;
            color: #495057;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .stat-value-modern {
            font-size: 1.8rem;
            font-weight: 700;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .stat-change-modern {
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stat-change-modern.positive {
            color: #28a745;
        }
        
        .stat-change-modern.negative {
            color: #dc3545;
        }
        
        /* أدوات البحث والتصفية */
        .search-filters-modern {
            padding: 20px 25px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box-modern {
            position: relative;
            flex: 1;
            min-width: 250px;
        }
        
        .search-box-modern i {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .input-modern {
            width: 100%;
            padding: 10px 40px 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }
        
        .input-modern:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .filters-modern {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-select-modern {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background: white;
            font-size: 0.85rem;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }
        
        .filter-select-modern:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #6c757d;
            color: #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        /* جدول المنتجات */
        .table-container-modern {
            background: white;
            border-radius: 0 0 12px 12px;
            overflow: hidden;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }
        
        .modern-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 10px;
            text-align: right;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }
        
        .modern-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        
        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            min-width: 60px;
            display: inline-block;
        }
        
        .badge.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .badge.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .badge.danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .badge.primary {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        /* تحسينات للأيقونات الأفقية */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
            width: 100% !important;
            min-width: 120px !important;
        }
        
        .action-buttons-horizontal .action-btn {
            width: 30px !important;
            height: 30px !important;
            min-width: 30px !important;
            max-width: 30px !important;
            border: none !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 12px !important;
            margin: 0 !important;
            padding: 0 !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
        }
        
        .action-buttons-horizontal .action-btn.edit {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            color: white !important;
        }
        
        .action-buttons-horizontal .action-btn.view {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            color: white !important;
        }
        
        .action-buttons-horizontal .action-btn.delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
        }
        
        .action-buttons-horizontal .action-btn:hover {
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3) !important;
        }

        /* أنماط النوافذ المنبثقة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content-modern {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header-modern h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body-modern {
            padding: 20px;
        }

        .form-modern {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .select-modern, .textarea-modern {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.2s;
        }

        .select-modern:focus, .textarea-modern:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        /* تحسينات إضافية */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .dropdown-menu a {
            display: block;
            padding: 10px 15px;
            color: #495057;
            text-decoration: none;
            transition: background 0.2s;
        }

        .dropdown-menu a:hover {
            background: #f8f9fa;
        }

        .table-footer-modern {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-info-modern {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .pagination-modern {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .page-btn:hover {
            background: #e9ecef;
        }

        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* أنماط معاينة الفئة */
        .category-preview {
            padding: 15px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            text-align: center;
            margin-top: 5px;
        }

        .category-preview .badge {
            font-size: 1rem;
            padding: 8px 15px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .category-preview .badge i {
            font-size: 1.1rem;
        }

        /* ألوان إضافية للفئات */
        .badge.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .badge.secondary {
            background-color: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        /* تحسينات للنموذج */
        .form-group label {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-group label::before {
            content: '';
            width: 3px;
            height: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        /* تحسينات للحقول */
        .input-modern:focus, .select-modern:focus, .textarea-modern:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* رسائل التحقق */
        .field-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.8rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .success-message {
            color: #28a745;
            font-size: 0.8rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* أنماط نافذة عرض المنتج */
        .product-details-view {
            max-height: 70vh;
            overflow-y: auto;
        }

        .details-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .details-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .detail-value {
            font-weight: 600;
            color: #212529;
        }

        .detail-value.price {
            color: #28a745;
            font-size: 1.1rem;
        }

        .detail-value.cost {
            color: #ffc107;
        }

        .detail-value.profit {
            color: #17a2b8;
        }

        .detail-value.percentage {
            color: #6f42c1;
        }

        .detail-value.quantity {
            color: #fd7e14;
        }

        .detail-value.total {
            color: #dc3545;
            font-size: 1.1rem;
        }

        .detail-value.code {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.85rem;
        }

        .description-text {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            line-height: 1.6;
            color: #495057;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .quick-stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-left: 4px solid;
        }

        .quick-stat-item.success {
            border-left-color: #28a745;
        }

        .quick-stat-item.info {
            border-left-color: #17a2b8;
        }

        .quick-stat-item.warning {
            border-left-color: #ffc107;
        }

        .quick-stat-item i {
            font-size: 1.5rem;
            opacity: 0.7;
        }

        .quick-stat-content {
            display: flex;
            flex-direction: column;
        }

        .quick-stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #212529;
        }

        .quick-stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* أنماط نافذة تعديل المنتج */
        .product-stats-edit {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .stat-label {
            display: block;
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            font-weight: 700;
            font-size: 1rem;
        }

        .stat-value.profit {
            color: #28a745;
        }

        .stat-value.percentage {
            color: #17a2b8;
        }

        .stat-value.total {
            color: #dc3545;
        }

        /* تحسينات للنوافذ المنبثقة */
        .modal-content-modern {
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-overlay {
            animation: modalFadeIn 0.3s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* تحسينات للأزرار */
        .btn-modern:hover {
            transform: translateY(-1px);
        }

        .btn-modern:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="suppliers.html"><i class="fas fa-user-tie"></i> الموردين</a></li>
                    <li><a href="products.html" class="active"><i class="fas fa-boxes"></i> المخزون</a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- محتوى الصفحة -->
    <main class="main-content">
        <div class="container">
            <!-- قسم إدارة المنتجات والمخزون -->
            <div class="products-management-modern">
                <!-- رأس القسم -->
                <div class="section-header-modern">
                    <div class="header-title">
                        <h3><i class="fas fa-boxes"></i> إدارة المنتجات والمخزون</h3>
                        <p>إدارة شاملة للمنتجات ومراقبة المخزون والأجهزة الإلكترونية</p>
                    </div>
                    <div class="header-actions">
                        <div class="dropdown">
                            <button class="btn-modern btn-secondary dropdown-toggle" onclick="toggleDropdown('print-export-dropdown')">
                                <i class="fas fa-print"></i>
                                طباعة وتصدير
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="print-export-dropdown">
                                <a href="#" onclick="printProducts()"><i class="fas fa-print"></i> طباعة التقرير</a>
                                <a href="#" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> تصدير Excel</a>
                                <a href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf"></i> تصدير PDF</a>
                            </div>
                        </div>
                        <button class="btn-modern btn-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i>
                            منتج جديد
                        </button>
                        <button class="btn-modern btn-secondary" onclick="showAddCategoryModal()">
                            <i class="fas fa-plus-circle"></i>
                            فئة جديدة
                        </button>
                    </div>
                </div>

                <!-- البطاقات الإحصائية -->
                <div class="stats-section-modern">
                    <div class="stats-grid-modern">
                        <div class="stat-card-modern primary">
                            <div class="stat-icon-modern">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>إجمالي المنتجات</h4>
                                <div class="stat-value-modern" id="total-products">25</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +5 هذا الشهر
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern success">
                            <div class="stat-icon-modern">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>منتجات متوفرة</h4>
                                <div class="stat-value-modern" id="available-products">18</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +3 هذا الأسبوع
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern warning">
                            <div class="stat-icon-modern">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>منتجات منخفضة</h4>
                                <div class="stat-value-modern" id="low-stock-products">4</div>
                                <div class="stat-change-modern negative">
                                    <i class="fas fa-arrow-down"></i> تحتاج تجديد
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern danger">
                            <div class="stat-icon-modern">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>منتجات نافدة</h4>
                                <div class="stat-value-modern" id="out-of-stock-products">3</div>
                                <div class="stat-change-modern negative">
                                    <i class="fas fa-arrow-down"></i> تحتاج طلب فوري
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات البحث والتصفية -->
                <div class="search-filters-modern">
                    <div class="search-box-modern">
                        <i class="fas fa-search"></i>
                        <input type="text" id="product-search" placeholder="البحث في المنتجات..." class="input-modern">
                        <div class="search-suggestions" id="search-suggestions"></div>
                    </div>
                    <div class="filters-modern">
                        <select id="category-filter" class="filter-select-modern">
                            <option value="all">جميع الفئات</option>
                            <option value="electronics">أجهزة إلكترونية</option>
                            <option value="computers">حاسوب ولوازمه</option>
                            <option value="phones">هواتف ذكية</option>
                            <option value="accessories">إكسسوارات</option>
                        </select>
                        <select id="status-filter" class="filter-select-modern">
                            <option value="all">جميع الحالات</option>
                            <option value="available">متوفر</option>
                            <option value="low">منخفض</option>
                            <option value="out">نافد</option>
                        </select>
                        <button id="reset-filter" class="btn-modern btn-outline">
                            <i class="fas fa-times"></i> مسح التصفية
                        </button>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-container-modern">
                    <table class="modern-table products-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> رقم المنتج</th>
                                <th><i class="fas fa-tag"></i> اسم المنتج</th>
                                <th><i class="fas fa-list"></i> الفئة</th>
                                <th><i class="fas fa-barcode"></i> الرمز</th>
                                <th><i class="fas fa-dollar-sign"></i> السعر</th>
                                <th><i class="fas fa-coins"></i> التكلفة</th>
                                <th><i class="fas fa-warehouse"></i> الكمية</th>
                                <th><i class="fas fa-exclamation-triangle"></i> الحد الأدنى</th>
                                <th><i class="fas fa-info-circle"></i> الحالة</th>
                                <th><i class="fas fa-cogs"></i> الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-list">
                            <!-- أجهزة إلكترونية -->
                            <tr>
                                <td><strong>PROD-001</strong></td>
                                <td>لابتوب Dell Inspiron 15</td>
                                <td><span class="badge primary">حاسوب ولوازمه</span></td>
                                <td>DELL-INS-15-3000</td>
                                <td><strong>2,500 ر.س</strong></td>
                                <td>2,100 ر.س</td>
                                <td><strong>15</strong></td>
                                <td>5</td>
                                <td><span class="badge success">متوفر</span></td>
                                <td>
                                    <div class="action-buttons-horizontal">
                                        <button class="action-btn edit" onclick="editProduct('PROD-001')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn view" onclick="viewProduct('PROD-001')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteProduct('PROD-001')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PROD-002</strong></td>
                                <td>iPhone 15 Pro Max</td>
                                <td><span class="badge primary">هواتف ذكية</span></td>
                                <td>APPLE-IP15-PM-256</td>
                                <td><strong>4,200 ر.س</strong></td>
                                <td>3,800 ر.س</td>
                                <td><strong>8</strong></td>
                                <td>3</td>
                                <td><span class="badge success">متوفر</span></td>
                                <td>
                                    <div class="action-buttons-horizontal">
                                        <button class="action-btn edit" onclick="editProduct('PROD-002')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn view" onclick="viewProduct('PROD-002')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteProduct('PROD-002')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PROD-003</strong></td>
                                <td>Samsung Galaxy S24 Ultra</td>
                                <td><span class="badge primary">هواتف ذكية</span></td>
                                <td>SAM-GS24-U-512</td>
                                <td><strong>3,800 ر.س</strong></td>
                                <td>3,400 ر.س</td>
                                <td><strong>12</strong></td>
                                <td>5</td>
                                <td><span class="badge success">متوفر</span></td>
                                <td>
                                    <div class="action-buttons-horizontal">
                                        <button class="action-btn edit" onclick="editProduct('PROD-003')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn view" onclick="viewProduct('PROD-003')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteProduct('PROD-003')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PROD-004</strong></td>
                                <td>MacBook Air M3</td>
                                <td><span class="badge primary">حاسوب ولوازمه</span></td>
                                <td>APPLE-MBA-M3-512</td>
                                <td><strong>5,200 ر.س</strong></td>
                                <td>4,800 ر.س</td>
                                <td><strong>6</strong></td>
                                <td>3</td>
                                <td><span class="badge success">متوفر</span></td>
                                <td>
                                    <div class="action-buttons-horizontal">
                                        <button class="action-btn edit" onclick="editProduct('PROD-004')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn view" onclick="viewProduct('PROD-004')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteProduct('PROD-004')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PROD-005</strong></td>
                                <td>iPad Pro 12.9 M4</td>
                                <td><span class="badge primary">أجهزة إلكترونية</span></td>
                                <td>APPLE-IPP-M4-1TB</td>
                                <td><strong>4,500 ر.س</strong></td>
                                <td>4,100 ر.س</td>
                                <td><strong>4</strong></td>
                                <td>2</td>
                                <td><span class="badge warning">منخفض</span></td>
                                <td>
                                    <div class="action-buttons-horizontal">
                                        <button class="action-btn edit" onclick="editProduct('PROD-005')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn view" onclick="viewProduct('PROD-005')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteProduct('PROD-005')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- تذييل الجدول -->
                    <div class="table-footer-modern">
                        <div class="results-info-modern">
                            عرض <span class="results-count">5</span> من <span>25</span> نتيجة
                        </div>
                        <div class="pagination-modern">
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn next">التالي <i class="fas fa-chevron-left"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- تذييل الصفحة -->
    <footer class="main-footer">
        <div class="container">
            <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة الأعمال</p>
        </div>
    </footer>

    <!-- ربط ملف JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/products.js"></script>

    <script>
        // إجبار الأيقونات على أن تكون أفقية
        document.addEventListener('DOMContentLoaded', function() {
            function forceHorizontalButtons() {
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    // إجبار الـ flexbox في صف واحد
                    container.style.setProperty('display', 'flex', 'important');
                    container.style.setProperty('flex-direction', 'row', 'important');
                    container.style.setProperty('flex-wrap', 'nowrap', 'important');
                    container.style.setProperty('gap', '6px', 'important');
                    container.style.setProperty('justify-content', 'center', 'important');
                    container.style.setProperty('align-items', 'center', 'important');
                    container.style.setProperty('padding', '3px', 'important');
                    container.style.setProperty('width', '100%', 'important');
                    container.style.setProperty('min-width', '120px', 'important');

                    // تطبيق الأنماط على الأزرار
                    const buttons = container.querySelectorAll('.action-btn');
                    buttons.forEach((btn, index) => {
                        btn.style.setProperty('display', 'flex', 'important');
                        btn.style.setProperty('width', '30px', 'important');
                        btn.style.setProperty('height', '30px', 'important');
                        btn.style.setProperty('min-width', '30px', 'important');
                        btn.style.setProperty('max-width', '30px', 'important');
                        btn.style.setProperty('margin', '0', 'important');
                        btn.style.setProperty('padding', '0', 'important');
                        btn.style.setProperty('border-radius', '6px', 'important');
                        btn.style.setProperty('align-items', 'center', 'important');
                        btn.style.setProperty('justify-content', 'center', 'important');
                        btn.style.setProperty('border', 'none', 'important');
                        btn.style.setProperty('cursor', 'pointer', 'important');
                        btn.style.setProperty('font-size', '12px', 'important');
                        btn.style.setProperty('flex-shrink', '0', 'important');
                        btn.style.setProperty('flex-grow', '0', 'important');

                        // الألوان
                        if (btn.classList.contains('edit')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('view')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('delete')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        }
                    });
                });
            }

            // تطبيق التنسيق فوراً
            forceHorizontalButtons();

            // إعادة تطبيق التنسيق كل ثانية للتأكد
            setInterval(forceHorizontalButtons, 1000);
        });

        // وظائف إدارة المنتجات
        // تحميل المنتجات من localStorage أو استخدام البيانات الافتراضية
        let productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [
            {
                id: 1,
                name: 'لابتوب ديل XPS 13',
                category: 'إلكترونيات',
                code: 'DELL-XPS13',
                price: 3500,
                cost: 2800,
                quantity: 15,
                minStock: 5,
                description: 'لابتوب عالي الأداء للأعمال'
            },
            {
                id: 2,
                name: 'هاتف آيفون 15',
                category: 'إلكترونيات',
                code: 'IPHONE-15',
                price: 4200,
                cost: 3500,
                quantity: 8,
                minStock: 3,
                description: 'أحدث هاتف ذكي من آبل'
            },
            {
                id: 3,
                name: 'مكتب خشبي فاخر',
                category: 'أثاث',
                code: 'DESK-LUX',
                price: 1800,
                cost: 1200,
                quantity: 12,
                minStock: 2,
                description: 'مكتب خشبي عالي الجودة'
            },
            {
                id: 4,
                name: 'كرسي مكتبي جلد',
                category: 'أثاث',
                code: 'CHAIR-LEATHER',
                price: 650,
                cost: 400,
                quantity: 25,
                minStock: 5,
                description: 'كرسي مكتبي مريح من الجلد الطبيعي'
            },
            {
                id: 5,
                name: 'طابعة ليزر HP',
                category: 'إلكترونيات',
                code: 'HP-LASER',
                price: 1200,
                cost: 900,
                quantity: 6,
                minStock: 2,
                description: 'طابعة ليزر عالية السرعة'
            }
        ];

        // دالة حفظ المنتجات في localStorage
        function saveProductsToStorage() {
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            console.log('تم حفظ المنتجات في localStorage:', productsData.length);
        }

        // دالة إشعار الصفحات الأخرى بالتحديث
        function notifyOtherPagesOfUpdate() {
            // إرسال حدث تحديث للصفحات الأخرى
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            // إرسال رسالة للنوافذ الأخرى
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.postMessage({ type: 'products-updated', timestamp: Date.now() });
            }
        }

        function showAddProductModal() {
            console.log('فتح نافذة إضافة منتج جديد...');

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content-modern" style="max-width: 800px;">
                    <div class="modal-header-modern">
                        <h3><i class="fas fa-plus"></i> إضافة منتج جديد</h3>
                        <button class="close-btn" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body-modern">
                        <form class="form-modern" id="new-product-form" onsubmit="return false;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>رقم المنتج</label>
                                    <input type="text" class="input-modern" value="PROD-006" readonly>
                                </div>
                                <div class="form-group">
                                    <label>اسم المنتج</label>
                                    <input type="text" class="input-modern" placeholder="اسم المنتج" id="product-name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>الفئة</label>
                                    <select class="select-modern" id="product-category">
                                        <option value="">اختر الفئة</option>
                                        <option value="electronics">أجهزة إلكترونية</option>
                                        <option value="computers">حاسوب ولوازمه</option>
                                        <option value="phones">هواتف ذكية</option>
                                        <option value="accessories">إكسسوارات</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>رمز المنتج</label>
                                    <input type="text" class="input-modern" placeholder="رمز المنتج" id="product-code">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>سعر البيع</label>
                                    <input type="number" class="input-modern" placeholder="0.00" step="0.01" id="product-price">
                                </div>
                                <div class="form-group">
                                    <label>سعر التكلفة</label>
                                    <input type="number" class="input-modern" placeholder="0.00" step="0.01" id="product-cost">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>الكمية الحالية</label>
                                    <input type="number" class="input-modern" placeholder="0" min="0" id="product-quantity">
                                </div>
                                <div class="form-group">
                                    <label>الحد الأدنى</label>
                                    <input type="number" class="input-modern" placeholder="0" min="0" id="product-min-stock">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>وصف المنتج</label>
                                <textarea class="textarea-modern" rows="3" placeholder="وصف المنتج..." id="product-description"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-modern btn-primary" onclick="saveProduct()">
                                    <i class="fas fa-save"></i> حفظ المنتج
                                </button>
                                <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // منع إرسال النموذج عند الضغط على Enter
            const form = document.getElementById('new-product-form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                return false;
            });

            // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modal.querySelector('.close-btn'));
                }
            });
        }

        function editProduct(productId) {
            console.log('تعديل المنتج:', productId);

            // الحصول على بيانات المنتج (محاكاة)
            const productData = getProductData(productId);

            if (!productData) {
                alert('لم يتم العثور على المنتج');
                return;
            }

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal edit-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 700px;
                    width: 90%;
                    max-height: 85vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-edit"></i>
                            تعديل المنتج
                        </h3>
                        <button onclick="closeProductEditModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px; max-height: 60vh; overflow-y: auto;">
                        <form id="editProductForm" class="form-modern">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-hashtag" style="color: #ffc107;"></i>
                                        رقم المنتج
                                    </label>
                                    <input type="text" value="${productData.id}" readonly style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        background: #f8f9fa;
                                        color: #6c757d;
                                    ">
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-tag" style="color: #ffc107;"></i>
                                        اسم المنتج
                                    </label>
                                    <input type="text" id="editProductName" value="${productData.name}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-list" style="color: #ffc107;"></i>
                                        الفئة
                                    </label>
                                    <select id="editProductCategory" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        background: white;
                                        cursor: pointer;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                        <option value="electronics" ${productData.category === 'electronics' ? 'selected' : ''}>أجهزة إلكترونية</option>
                                        <option value="computers" ${productData.category === 'computers' ? 'selected' : ''}>حاسوب ولوازمه</option>
                                        <option value="phones" ${productData.category === 'phones' ? 'selected' : ''}>هواتف ذكية</option>
                                        <option value="accessories" ${productData.category === 'accessories' ? 'selected' : ''}>إكسسوارات</option>
                                    </select>
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-barcode" style="color: #ffc107;"></i>
                                        رمز المنتج
                                    </label>
                                    <input type="text" id="editProductCode" value="${productData.code}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                        font-family: monospace;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-dollar-sign" style="color: #ffc107;"></i>
                                        سعر البيع
                                    </label>
                                    <input type="number" id="editProductPrice" value="${productData.price}" step="0.01" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-coins" style="color: #ffc107;"></i>
                                        سعر التكلفة</label>
                                    <input type="number" id="editProductCost" value="${productData.cost}" step="0.01" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-warehouse" style="color: #ffc107;"></i>
                                        الكمية الحالية
                                    </label>
                                    <input type="number" id="editProductQuantity" value="${productData.quantity}" min="0" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>

                                <div>
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                                        الحد الأدنى
                                    </label>
                                    <input type="number" id="editProductMinStock" value="${productData.minStock}" min="0" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-align-left" style="color: #ffc107;"></i>
                                    وصف المنتج
                                </label>
                                <textarea id="editProductDescription" rows="3" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    transition: border-color 0.3s ease;
                                    resize: vertical;
                                    min-height: 80px;
                                " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'" placeholder="وصف تفصيلي للمنتج...">${productData.description || ''}</textarea>
                            </div>

                            <div style="
                                background: #f8f9fa;
                                padding: 15px;
                                border-radius: 8px;
                                margin-bottom: 20px;
                                border-left: 4px solid #ffc107;
                            ">
                                <h5 style="margin: 0 0 10px 0; color: #ffc107;">إحصائيات المنتج:</h5>
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                                    <div style="text-align: center;">
                                        <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">الربح المتوقع</p>
                                        <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #28a745;" id="editProfitMargin">${(productData.price - productData.cost).toFixed(2)} ر.س</p>
                                    </div>
                                    <div style="text-align: center;">
                                        <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">نسبة الربح</p>
                                        <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #17a2b8;" id="editProfitPercentage">${((productData.price - productData.cost) / productData.cost * 100).toFixed(1)}%</p>
                                    </div>
                                    <div style="text-align: center;">
                                        <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">قيمة المخزون</p>
                                        <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #6f42c1;" id="editStockValue">${(productData.cost * productData.quantity).toFixed(2)} ر.س</p>
                                    </div>
                                </div>
                            </div>

                            <div style="
                                display: flex;
                                gap: 12px;
                                justify-content: flex-end;
                                padding-top: 20px;
                                border-top: 1px solid #e9ecef;
                                margin-top: 20px;
                            ">
                                <button type="button" onclick="closeProductEditModal()" style="
                                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>

                                <button type="submit" id="updateProductBtn" style="
                                    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    <i class="fas fa-save"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            const form = document.getElementById('editProductForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                updateProduct(productId);
            });

            window.closeProductEditModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProductEditModal();
                }
            });

            // إضافة مستمعات لتحديث الإحصائيات
            addEditProductListeners();
        }

        function viewProduct(productId) {
            console.log('عرض المنتج:', productId);

            // الحصول على بيانات المنتج (محاكاة)
            const productData = getProductData(productId);

            if (!productData) {
                alert('لم يتم العثور على المنتج');
                return;
            }

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal view-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-eye"></i>
                            تفاصيل المنتج
                        </h3>
                        <button onclick="closeProductViewModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>
                    <div style="padding: 25px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">رقم المنتج</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${productData.id}</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">اسم المنتج</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${productData.name}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">الفئة</label>
                                <p style="margin: 5px 0 0 0;">
                                    <span class="badge ${productData.categoryColor}">${productData.categoryName}</span>
                                </p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">رمز المنتج</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-family: monospace; background: #f8f9fa; padding: 5px 10px; border-radius: 4px; display: inline-block;">${productData.code}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">سعر البيع</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.2rem; font-weight: 600; color: #28a745;">${productData.price} ر.س</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">سعر التكلفة</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; color: #dc3545;">${productData.cost} ر.س</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">الربح المتوقع</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #17a2b8;">${(productData.price - productData.cost).toFixed(2)} ر.س</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">نسبة الربح</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #ffc107;">${((productData.price - productData.cost) / productData.cost * 100).toFixed(1)}%</p>
                            </div>
                        </div>


                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">الكمية الحالية</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${productData.quantity} قطعة</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">الحد الأدنى</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem;">${productData.minStock} قطعة</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">حالة المخزون</label>
                                <p style="margin: 5px 0 0 0;">
                                    <span class="badge ${productData.statusColor}">${productData.status}</span>
                                </p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">قيمة المخزون</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600; color: #6f42c1;">${(productData.cost * productData.quantity).toFixed(2)} ر.س</p>
                            </div>
                        </div>


                        ${productData.description ? `
                        <div style="margin-bottom: 20px;">
                            <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">وصف المنتج</label>
                            <p style="margin: 5px 0 0 0; font-size: 1rem; line-height: 1.5; background: #f8f9fa; padding: 15px; border-radius: 8px;">${productData.description}</p>
                        </div>
                        ` : ''}

                        <div style="display: flex; gap: 10px; justify-content: flex-end; padding-top: 20px; border-top: 1px solid #e9ecef;">
                            <button onclick="closeProductViewModal(); editProduct('${productId}')" style="
                                background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button onclick="closeProductViewModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة أنماط CSS للانيميشن
            if (!document.querySelector('#products-modal-animations')) {
                const style = document.createElement('style');
                style.id = 'products-modal-animations';
                style.textContent = `
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    @keyframes slideIn {
                        from { opacity: 0; transform: translateY(-50px) scale(0.9); }
                        to { opacity: 1; transform: translateY(0) scale(1); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(modal);

            window.closeProductViewModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProductViewModal();
                }
            });
        }

        function getProductData(productId) {
            // محاكاة قاعدة البيانات للمنتجات
            const products = {
                'PROD-001': {
                    id: 'PROD-001',
                    name: 'لابتوب Dell Inspiron 15',
                    category: 'computers',
                    categoryName: 'حاسوب ولوازمه',
                    categoryColor: 'primary',
                    code: 'DELL-INS-15-3000',
                    price: 2500,
                    cost: 2100,
                    quantity: 15,
                    minStock: 5,
                    status: 'متوفر',
                    statusColor: 'success',
                    description: 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5 وذاكرة 8GB RAM وقرص صلب SSD 256GB'
                },
                'PROD-002': {
                    id: 'PROD-002',
                    name: 'iPhone 15 Pro Max',
                    category: 'phones',
                    categoryName: 'هواتف ذكية',
                    categoryColor: 'primary',
                    code: 'APPLE-IP15-PM-256',
                    price: 4200,
                    cost: 3800,
                    quantity: 8,
                    minStock: 3,
                    status: 'متوفر',
                    statusColor: 'success',
                    description: 'iPhone 15 Pro Max بسعة 256GB مع كاميرا Pro وشاشة Super Retina XDR'
                },
                'PROD-003': {
                    id: 'PROD-003',
                    name: 'Samsung Galaxy S24 Ultra',
                    category: 'phones',
                    categoryName: 'هواتف ذكية',
                    categoryColor: 'primary',
                    code: 'SAM-GS24-U-512',
                    price: 3800,
                    cost: 3400,
                    quantity: 12,
                    minStock: 5,
                    status: 'متوفر',
                    statusColor: 'success',
                    description: 'Samsung Galaxy S24 Ultra بسعة 512GB مع قلم S Pen وكاميرا 200MP'
                },
                'PROD-004': {
                    id: 'PROD-004',
                    name: 'MacBook Air M3',
                    category: 'computers',
                    categoryName: 'حاسوب ولوازمه',
                    categoryColor: 'primary',
                    code: 'APPLE-MBA-M3-512',
                    price: 5200,
                    cost: 4800,
                    quantity: 6,
                    minStock: 3,
                    status: 'متوفر',
                    statusColor: 'success',
                    description: 'MacBook Air مع معالج Apple M3 وذاكرة 16GB وقرص SSD 512GB'
                },
                'PROD-005': {
                    id: 'PROD-005',
                    name: 'iPad Pro 12.9 M4',
                    category: 'electronics',
                    categoryName: 'أجهزة إلكترونية',
                    categoryColor: 'primary',
                    code: 'APPLE-IPP-M4-1TB',
                    price: 4500,
                    cost: 4100,
                    quantity: 4,
                    minStock: 2,
                    status: 'منخفض',
                    statusColor: 'warning',
                    description: 'iPad Pro 12.9 بوصة مع معالج M4 وسعة 1TB وشاشة Liquid Retina XDR'
                }
            };

            return products[productId] || null;
        }

        function updateProduct(productId) {
            const name = document.getElementById('editProductName').value.trim();
            const category = document.getElementById('editProductCategory').value;
            const code = document.getElementById('editProductCode').value.trim();
            const price = parseFloat(document.getElementById('editProductPrice').value);
            const cost = parseFloat(document.getElementById('editProductCost').value);
            const quantity = parseInt(document.getElementById('editProductQuantity').value);
            const minStock = parseInt(document.getElementById('editProductMinStock').value);
            const description = document.getElementById('editProductDescription').value.trim();

            // التحقق من صحة البيانات
            if (!name || !category || !code || !price || !cost || quantity < 0 || minStock < 0) {
                alert('يرجى ملء جميع الحقول المطلوبة بقيم صحيحة');
                return;
            }

            if (price <= cost) {
                alert('سعر البيع يجب أن يكون أكبر من سعر التكلفة');
                return;
            }

            // محاكاة تحديث المنتج
            console.log('تحديث المنتج:', {
                id: productId,
                name: name,
                category: category,
                code: code,
                price: price,
                cost: cost,
                quantity: quantity,
                minStock: minStock,
                description: description
            });

            // إغلاق النافذة
            closeProductEditModal();

            // تحديث الجدول (محاكاة)
            updateProductInTable(productId, {
                name: name,
                category: category,
                code: code,
                price: price,
                cost: cost,
                quantity: quantity,
                minStock: minStock
            });

            alert('تم تحديث المنتج بنجاح');
        }

        function updateProductInTable(productId, data) {
            // محاكاة تحديث الجدول
            console.log('تحديث الجدول للمنتج:', productId, data);
            // يمكن إضافة منطق تحديث الجدول هنا
        }

        function addEditProductListeners() {
            // إضافة مستمعات لتحديث الإحصائيات في نموذج التعديل
            const priceInput = document.getElementById('edit-product-price');
            const costInput = document.getElementById('edit-product-cost');
            const quantityInput = document.getElementById('edit-product-quantity');

            function updateEditStats() {
                const price = parseFloat(priceInput?.value) || 0;
                const cost = parseFloat(costInput?.value) || 0;
                const quantity = parseInt(quantityInput?.value) || 0;

                const profit = price - cost;
                const profitPercentage = cost > 0 ? (profit / cost * 100) : 0;
                const stockValue = cost * quantity;

                const profitElement = document.getElementById('edit-profit-margin');
                const percentageElement = document.getElementById('edit-profit-percentage');
                const stockValueElement = document.getElementById('edit-stock-value');

                if (profitElement) profitElement.textContent = profit.toFixed(2) + ' ر.س';
                if (percentageElement) percentageElement.textContent = profitPercentage.toFixed(1) + '%';
                if (stockValueElement) stockValueElement.textContent = stockValue.toFixed(2) + ' ر.س';
            }

            if (priceInput) priceInput.addEventListener('input', updateEditStats);
            if (costInput) costInput.addEventListener('input', updateEditStats);
            if (quantityInput) quantityInput.addEventListener('input', updateEditStats);
        }

        function deleteProduct(productId) {
            console.log('حذف المنتج:', productId);

            const productData = getProductData(productId);
            if (!productData) {
                alert('لم يتم العثور على المنتج');
                return;
            }

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal delete-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-radius: 12px 12px 0 0;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            تأكيد الحذف
                        </h3>
                        <button onclick="closeProductDeleteModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="
                                width: 80px;
                                height: 80px;
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 15px auto;
                                color: white;
                                font-size: 2rem;
                            ">
                                <i class="fas fa-trash"></i>
                            </div>
                            <h4 style="margin: 0 0 10px 0; color: #dc3545;">هل أنت متأكد من حذف هذا المنتج؟</h4>
                            <p style="margin: 0; color: #6c757d;">هذا الإجراء لا يمكن التراجع عنه!</p>
                        </div>

                        <div style="
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border-left: 4px solid #dc3545;
                        ">
                            <h5 style="margin: 0 0 10px 0; color: #dc3545;">تفاصيل المنتج:</h5>
                            <p style="margin: 5px 0; color: #333;"><strong>رقم المنتج:</strong> ${productData.id}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>اسم المنتج:</strong> ${productData.name}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>الفئة:</strong> ${productData.categoryName}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>الكمية:</strong> ${productData.quantity} قطعة</p>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button onclick="closeProductDeleteModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>

                            <button onclick="confirmDeleteProduct('${productId}')" style="
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-trash"></i>
                                نعم، احذف المنتج
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeProductDeleteModal = function() {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            };

            window.confirmDeleteProduct = function(productId) {
                // حذف الصف من الجدول مع تأثير
                const rows = document.querySelectorAll('tbody tr');
                for (let row of rows) {
                    const deleteBtn = row.querySelector(`button[onclick*="deleteProduct('${productId}')"]`);
                    if (deleteBtn) {
                        row.style.animation = 'slideOutRow 0.5s ease';
                        row.style.background = '#ffebee';
                        setTimeout(() => {
                            row.remove();
                        }, 500);
                        break;
                    }
                }

                // إغلاق النافذة
                closeProductDeleteModal();

                // عرض رسالة نجاح
                setTimeout(() => {
                    alert(`تم حذف المنتج "${productData.name}" بنجاح`);
                }, 600);
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProductDeleteModal();
                }
            });
        }

        function removeProductFromTable(productId) {
            // محاكاة إزالة المنتج من الجدول
            console.log('إزالة المنتج من الجدول:', productId);
            // يمكن إضافة منطق إزالة الصف من الجدول هنا
        }

        function saveProduct() {
            console.log('بدء حفظ المنتج...');
            const name = document.getElementById('product-name').value;
            const category = document.getElementById('product-category').value;
            const code = document.getElementById('product-code').value;
            const price = parseFloat(document.getElementById('product-price').value);
            const cost = parseFloat(document.getElementById('product-cost').value);
            const quantity = parseInt(document.getElementById('product-quantity').value) || 0;
            const minStock = parseInt(document.getElementById('product-min-stock').value) || 0;
            const description = document.getElementById('product-description').value || '';

            console.log('بيانات المنتج:', { name, category, code, price, cost, quantity, minStock, description });

            if (!name || !category || !code || !price || !cost) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إنشاء منتج جديد
            const newProduct = {
                id: Date.now(), // استخدام timestamp كمعرف فريد
                name: name,
                category: category,
                code: code,
                price: price,
                cost: cost,
                quantity: quantity,
                minStock: minStock,
                description: description
            };

            // إضافة المنتج للمصفوفة
            productsData.push(newProduct);

            // حفظ في localStorage
            saveProductsToStorage();

            // إشعار الصفحات الأخرى بالتحديث
            notifyOtherPagesOfUpdate();

            // إضافة المنتج للجدول
            addProductToTable(newProduct);

            // إغلاق النافذة
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                closeModal(modal.querySelector('.close-btn'));
            }

            alert('تم إضافة المنتج بنجاح: ' + name);
        }

        // دالة إضافة المنتج للجدول
        function addProductToTable(product) {
            console.log('محاولة إضافة المنتج للجدول:', product);
            const tbody = document.querySelector('#products-list') ||
                         document.querySelector('.products-table tbody') ||
                         document.querySelector('#products-table tbody') ||
                         document.querySelector('table tbody');

            console.log('تم العثور على الجدول:', tbody);
            if (tbody) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${product.code}</td>
                    <td>${product.name}</td>
                    <td>${product.category}</td>
                    <td>${product.price} ر.س</td>
                    <td>${product.quantity}</td>
                    <td><span class="status-${product.quantity > product.minStock ? 'good' : 'warning'}">${product.quantity > product.minStock ? 'متوفر' : 'منخفض'}</span></td>
                    <td class="actions-cell">
                        <div class="actions-horizontal">
                            <button class="btn-action btn-view" onclick="viewProduct(${product.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-action btn-edit" onclick="editProduct(${product.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-action btn-delete" onclick="deleteProduct(${product.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            }
        }

        function closeModal(closeBtn) {
            const modal = closeBtn.closest('.modal-overlay');
            if (modal) {
                document.body.style.overflow = 'auto';
                modal.remove();
            }
        }

        function showAddCategoryModal() {
            console.log('فتح نافذة إضافة فئة جديدة...');

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content-modern" style="max-width: 500px;">
                    <div class="modal-header-modern">
                        <h3><i class="fas fa-plus-circle"></i> إضافة فئة جديدة</h3>
                        <button class="close-btn" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body-modern">
                        <form class="form-modern" id="new-category-form">
                            <div class="form-group">
                                <label><i class="fas fa-tag"></i> اسم الفئة</label>
                                <input type="text" class="input-modern" placeholder="اسم الفئة الجديدة" id="category-name" required oninput="updateCategoryPreview(); validateCategoryName()">
                                <div class="error-message" id="name-error" style="display: none;">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <span></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label><i class="fas fa-code"></i> رمز الفئة</label>
                                <input type="text" class="input-modern" placeholder="رمز الفئة (مثل: ELEC)" id="category-code" required oninput="validateCategoryCode()" maxlength="10">
                                <div class="error-message" id="code-error" style="display: none;">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <span></span>
                                </div>
                                <div class="success-message" id="code-success" style="display: none;">
                                    <i class="fas fa-check-circle"></i>
                                    <span>رمز الفئة صحيح</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>وصف الفئة</label>
                                <textarea class="textarea-modern" rows="3" placeholder="وصف مختصر للفئة..." id="category-description"></textarea>
                            </div>

                            <div class="form-group">
                                <label>لون الفئة</label>
                                <select class="select-modern" id="category-color">
                                    <option value="primary">أزرق (Primary)</option>
                                    <option value="success">أخضر (Success)</option>
                                    <option value="warning">أصفر (Warning)</option>
                                    <option value="danger">أحمر (Danger)</option>
                                    <option value="info">فيروزي (Info)</option>
                                    <option value="secondary">رمادي (Secondary)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>أيقونة الفئة</label>
                                <select class="select-modern" id="category-icon" onchange="updateCategoryPreview()">
                                    <option value="fas fa-laptop">💻 لابتوب</option>
                                    <option value="fas fa-mobile-alt">📱 هاتف</option>
                                    <option value="fas fa-tablet-alt">📟 تابلت</option>
                                    <option value="fas fa-headphones">🎧 سماعات</option>
                                    <option value="fas fa-keyboard">⌨️ لوحة مفاتيح</option>
                                    <option value="fas fa-mouse">🖱️ ماوس</option>
                                    <option value="fas fa-tv">📺 شاشة</option>
                                    <option value="fas fa-camera">📷 كاميرا</option>
                                    <option value="fas fa-gamepad">🎮 ألعاب</option>
                                    <option value="fas fa-microchip">🔧 قطع غيار</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>معاينة الفئة</label>
                                <div class="category-preview" id="category-preview">
                                    <span class="badge primary" id="preview-badge">
                                        <i class="fas fa-laptop" id="preview-icon"></i>
                                        <span id="preview-name">اسم الفئة</span>
                                    </span>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-modern btn-primary" onclick="saveCategory()">
                                    <i class="fas fa-save"></i> حفظ الفئة
                                </button>
                                <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة وظيفة إغلاق عند الضغط على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modal.querySelector('.close-btn'));
                }
            });

            // التركيز على حقل اسم الفئة
            setTimeout(() => {
                const nameInput = document.getElementById('category-name');
                if (nameInput) {
                    nameInput.focus();
                }
            }, 100);
        }

        function saveCategory() {
            const name = document.getElementById('category-name').value.trim();
            const code = document.getElementById('category-code').value.trim().toUpperCase();
            const description = document.getElementById('category-description').value.trim();
            const color = document.getElementById('category-color').value;
            const icon = document.getElementById('category-icon').value;

            // التحقق من صحة البيانات باستخدام الوظائف الجديدة
            const isNameValid = validateCategoryName();
            const isCodeValid = validateCategoryCode();

            if (!isNameValid || !isCodeValid) {
                // التركيز على أول حقل خاطئ
                if (!isNameValid) {
                    document.getElementById('category-name').focus();
                } else if (!isCodeValid) {
                    document.getElementById('category-code').focus();
                }
                return;
            }

            // محاكاة حفظ الفئة
            console.log('حفظ فئة جديدة:', {
                name: name,
                code: code,
                description: description,
                color: color,
                icon: icon
            });

            // إضافة الفئة إلى قائمة التصفية
            const categoryFilter = document.getElementById('category-filter');
            const productCategorySelect = document.getElementById('product-category');

            if (categoryFilter) {
                const option = document.createElement('option');
                option.value = code.toLowerCase();
                option.textContent = name;
                categoryFilter.appendChild(option);
            }

            // إضافة الفئة إلى نموذج إضافة المنتج إذا كان موجوداً
            if (productCategorySelect) {
                const option = document.createElement('option');
                option.value = code.toLowerCase();
                option.textContent = name;
                productCategorySelect.appendChild(option);
            }

            // إغلاق النافذة
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                closeModal(modal.querySelector('.close-btn'));
            }

            // رسالة نجاح
            alert(`تم إضافة الفئة "${name}" بنجاح!\nرمز الفئة: ${code}`);

            // تحديث الإحصائيات (محاكاة)
            updateCategoryStats();
        }

        function updateCategoryStats() {
            // محاكاة تحديث الإحصائيات
            console.log('تحديث إحصائيات الفئات...');

            // يمكن إضافة منطق تحديث الإحصائيات هنا
            // مثل إعادة حساب عدد المنتجات في كل فئة
        }

        function updateCategoryPreview() {
            const name = document.getElementById('category-name')?.value || 'اسم الفئة';
            const color = document.getElementById('category-color')?.value || 'primary';
            const icon = document.getElementById('category-icon')?.value || 'fas fa-laptop';

            const previewBadge = document.getElementById('preview-badge');
            const previewIcon = document.getElementById('preview-icon');
            const previewName = document.getElementById('preview-name');

            if (previewBadge && previewIcon && previewName) {
                // تحديث اللون
                previewBadge.className = `badge ${color}`;

                // تحديث الأيقونة
                previewIcon.className = icon;

                // تحديث النص
                previewName.textContent = name || 'اسم الفئة';
            }
        }

        function validateCategoryName() {
            const nameInput = document.getElementById('category-name');
            const errorDiv = document.getElementById('name-error');
            const name = nameInput?.value.trim();

            if (!name) {
                showFieldError(nameInput, errorDiv, 'اسم الفئة مطلوب');
                return false;
            } else if (name.length < 2) {
                showFieldError(nameInput, errorDiv, 'اسم الفئة يجب أن يكون أكثر من حرفين');
                return false;
            } else if (name.length > 50) {
                showFieldError(nameInput, errorDiv, 'اسم الفئة طويل جداً (الحد الأقصى 50 حرف)');
                return false;
            } else {
                hideFieldError(nameInput, errorDiv);
                return true;
            }
        }

        function validateCategoryCode() {
            const codeInput = document.getElementById('category-code');
            const errorDiv = document.getElementById('code-error');
            const successDiv = document.getElementById('code-success');
            const code = codeInput?.value.trim().toUpperCase();

            // تحديث القيمة بالأحرف الكبيرة
            if (codeInput) {
                codeInput.value = code;
            }

            if (!code) {
                showFieldError(codeInput, errorDiv, 'رمز الفئة مطلوب');
                hideFieldSuccess(successDiv);
                return false;
            } else if (code.length < 2) {
                showFieldError(codeInput, errorDiv, 'رمز الفئة يجب أن يكون أكثر من حرف واحد');
                hideFieldSuccess(successDiv);
                return false;
            } else if (code.length > 10) {
                showFieldError(codeInput, errorDiv, 'رمز الفئة طويل جداً (الحد الأقصى 10 أحرف)');
                hideFieldSuccess(successDiv);
                return false;
            } else if (!/^[A-Z0-9]+$/.test(code)) {
                showFieldError(codeInput, errorDiv, 'رمز الفئة يجب أن يحتوي على أحرف إنجليزية وأرقام فقط');
                hideFieldSuccess(successDiv);
                return false;
            } else {
                hideFieldError(codeInput, errorDiv);
                showFieldSuccess(successDiv);
                return true;
            }
        }

        function showFieldError(input, errorDiv, message) {
            if (input) {
                input.classList.add('field-error');
            }
            if (errorDiv) {
                errorDiv.querySelector('span').textContent = message;
                errorDiv.style.display = 'flex';
            }
        }

        function hideFieldError(input, errorDiv) {
            if (input) {
                input.classList.remove('field-error');
            }
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        function showFieldSuccess(successDiv) {
            if (successDiv) {
                successDiv.style.display = 'flex';
            }
        }

        function hideFieldSuccess(successDiv) {
            if (successDiv) {
                successDiv.style.display = 'none';
            }
        }

        // إضافة مستمعات الأحداث للمعاينة المباشرة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مستمع لتغيير اسم الفئة
            document.addEventListener('input', function(e) {
                if (e.target && e.target.id === 'category-name') {
                    updateCategoryPreview();
                }
            });

            // إضافة مستمع لتغيير لون الفئة
            document.addEventListener('change', function(e) {
                if (e.target && (e.target.id === 'category-color' || e.target.id === 'category-icon')) {
                    updateCategoryPreview();
                }
            });
        });

        function printProducts() {
            window.print();
        }

        function exportToExcel() {
            alert('سيتم تصدير البيانات إلى Excel');
        }

        function exportToPDF() {
            alert('سيتم تصدير البيانات إلى PDF');
        }

        function toggleDropdown(id) {
            const dropdown = document.getElementById(id);
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        }

        // إضافة أنماط CSS للانيميشن عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.querySelector('#products-animations')) {
                const style = document.createElement('style');
                style.id = 'products-animations';
                style.textContent = `
                    @keyframes slideOutRow {
                        from { opacity: 1; transform: translateX(0); }
                        to { opacity: 0; transform: translateX(-50px); }
                    }
                `;
                document.head.appendChild(style);
            }
        });
    </script>
</body>
</html>
