<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العملاء - نظام إدارة الأعمال</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accounting.css">
    <link rel="stylesheet" href="css/accounting-fix.css">
    <link rel="stylesheet" href="css/chart-of-accounts.css">
    <link rel="stylesheet" href="css/action-buttons-horizontal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        /* أنماط القائمة المنسدلة */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 8px;
            border: 1px solid #ddd;
            top: 100%;
            right: 0;
        }

        .dropdown-menu a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }

        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }

        .dropdown-menu a i {
            margin-left: 8px;
            width: 16px;
        }

        .dropdown-toggle {
            cursor: pointer;
        }

        /* أنماط التنقل بين الصفحات */
        .table-footer-modern {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            border-radius: 0 0 12px 12px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .results-info-modern {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
        }

        .pagination-modern {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .page-btn {
            background: white;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        .page-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .page-btn.disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .page-btn.disabled:hover {
            background: #f8f9fa;
            transform: none;
        }

        .page-btn.prev,
        .page-btn.next {
            padding: 8px 16px;
            font-weight: 600;
        }

        .page-btn i {
            margin: 0 4px;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .table-footer-modern {
                flex-direction: column;
                gap: 15px;
            }

            .pagination-modern {
                justify-content: center;
            }

            .page-btn {
                min-width: 35px;
                height: 35px;
                padding: 6px 10px;
                font-size: 13px;
            }
        }

        /* إجبار الأيقونات على أن تكون في صف واحد أفقي */
        .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 6px !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 3px !important;
            width: 100% !important;
            min-width: 120px !important;
            box-sizing: border-box !important;
        }

        .action-buttons-horizontal .action-btn {
            width: 30px !important;
            height: 30px !important;
            min-width: 30px !important;
            max-width: 30px !important;
            border: none !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s ease !important;
            font-size: 12px !important;
            margin: 0 !important;
            padding: 0 !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
            float: none !important;
            position: static !important;
        }

        .action-buttons-horizontal .action-btn.edit {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.edit:hover {
            background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(255, 193, 7, 0.4) !important;
        }

        .action-buttons-horizontal .action-btn.view {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.view:hover {
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(23, 162, 184, 0.4) !important;
        }

        .action-buttons-horizontal .action-btn.delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
        }

        .action-buttons-horizontal .action-btn.delete:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
            transform: translateY(-1px) scale(1.05) !important;
            box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4) !important;
        }

        /* تحسينات إضافية للجدول */
        .modern-table td {
            padding: 8px 6px;
            vertical-align: middle;
        }

        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* CSS إضافي قوي جداً لضمان الأيقونات الأفقية */
        table .action-buttons-horizontal {
            display: flex !important;
            flex-direction: row !important;
        }

        table .action-buttons-horizontal .action-btn {
            display: inline-block !important;
            float: left !important;
            margin-left: 5px !important;
        }

        /* إزالة أي CSS قد يسبب العرض العمودي */
        .action-btn {
            display: inline-block !important;
            vertical-align: middle !important;
        }

        /* طريقة بديلة لضمان الترتيب الأفقي */
        .action-buttons-horizontal .action-btn:nth-child(1) { order: 1; }
        .action-buttons-horizontal .action-btn:nth-child(2) { order: 2; }
        .action-buttons-horizontal .action-btn:nth-child(3) { order: 3; }

        /* إجبار الـ flexbox */
        .action-buttons-horizontal {
            white-space: nowrap !important;
        }

        .action-buttons-horizontal .action-btn {
            white-space: nowrap !important;
            min-width: 30px !important;
        }

        /* CSS إضافي قوي لمنع الانكسار */
        .action-buttons-horizontal {
            overflow: visible !important;
            min-height: 40px !important;
        }

        /* إجبار عرض الخلية */
        .modern-table td:last-child {
            width: 130px !important;
            min-width: 130px !important;
            max-width: 130px !important;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="main-header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>نظام إدارة الأعمال</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sales.html"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
                    <li><a href="purchases.html"><i class="fas fa-truck"></i> المشتريات</a></li>
                    <li><a href="customers.html" class="active"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="suppliers.html"><i class="fas fa-user-tie"></i> الموردين</a></li>
                    <li><a href="products.html"><i class="fas fa-boxes"></i> المخزون</a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                    <li><a href="accounting.html"><i class="fas fa-calculator"></i> الحسابات</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- محتوى الصفحة -->
    <main class="main-content">
        <div class="container">
            <!-- قسم إدارة العملاء -->
            <div class="products-management-modern">
                <!-- رأس القسم -->
                <div class="section-header-modern">
                    <div class="header-title">
                        <h3><i class="fas fa-users"></i> إدارة العملاء</h3>
                        <p>إدارة بيانات العملاء والحسابات والمتابعة</p>
                    </div>
                    <div class="header-actions">
                        <div class="dropdown">
                            <button class="btn-modern btn-secondary dropdown-toggle" onclick="toggleDropdown('print-export-dropdown')">
                                <i class="fas fa-print"></i>
                                طباعة وتصدير
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="print-export-dropdown">
                                <a href="#" onclick="printCustomers()"><i class="fas fa-print"></i> طباعة التقرير</a>
                                <a href="#" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> تصدير Excel</a>
                                <a href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf"></i> تصدير PDF</a>
                            </div>
                        </div>
                        <button class="btn-modern btn-primary add-customer-btn" onclick="showAddCustomerModal()">
                            <i class="fas fa-plus"></i>
                            عميل جديد
                        </button>
                    </div>
                </div>

                <!-- البطاقات الإحصائية -->
                <div class="stats-section-modern">
                    <div class="stats-grid-modern">
                        <div class="stat-card-modern primary">
                            <div class="stat-icon-modern">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>إجمالي العملاء</h4>
                                <div class="stat-value-modern" id="total-customers">248</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +18 هذا الشهر
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern success">
                            <div class="stat-icon-modern">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>عملاء جدد</h4>
                                <div class="stat-value-modern" id="new-customers">18</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +25% هذا الشهر
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern warning">
                            <div class="stat-icon-modern">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>إجمالي المديونية</h4>
                                <div class="stat-value-modern" id="total-debt">125,480 <span>ر.س</span></div>
                                <div class="stat-change-modern negative">
                                    <i class="fas fa-arrow-down"></i> -8% من الشهر الماضي
                                </div>
                            </div>
                        </div>
                        <div class="stat-card-modern info">
                            <div class="stat-icon-modern">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-content-modern">
                                <h4>عملاء نشطون</h4>
                                <div class="stat-value-modern" id="active-customers">186</div>
                                <div class="stat-change-modern positive">
                                    <i class="fas fa-arrow-up"></i> +12% هذا الشهر
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات البحث والتصفية -->
                <div class="search-filters-modern">
                    <div class="search-box-modern">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="البحث عن عميل..." class="input-modern">
                        <div class="search-suggestions" id="search-suggestions"></div>
                    </div>
                    <div class="filters-modern">
                        <select id="customer-type-filter" class="filter-select-modern">
                            <option value="">جميع الأنواع</option>
                            <option value="individual">أفراد</option>
                            <option value="company">شركات</option>
                        </select>
                        <select id="balance-filter" class="filter-select-modern">
                            <option value="">جميع الأرصدة</option>
                            <option value="positive">رصيد موجب</option>
                            <option value="negative">رصيد سالب</option>
                            <option value="zero">رصيد صفر</option>
                        </select>
                        <button class="btn-modern btn-outline" id="clear-filters">
                            <i class="fas fa-times"></i>
                            مسح التصفية
                        </button>
                    </div>
                </div>

            <!-- جدول العملاء -->
            <div class="table-container-modern">
                <table class="modern-table customers-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-hashtag"></i> الرقم</th>
                            <th><i class="fas fa-user"></i> الاسم</th>
                            <th><i class="fas fa-tag"></i> النوع</th>
                            <th><i class="fas fa-phone"></i> رقم الهاتف</th>
                            <th><i class="fas fa-envelope"></i> البريد الإلكتروني</th>
                            <th><i class="fas fa-money-bill-wave"></i> الرصيد</th>
                            <th><i class="fas fa-cogs"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>001</strong></td>
                            <td>أحمد محمد علي</td>
                            <td><span class="badge primary">فرد</span></td>
                            <td>+966501234567</td>
                            <td><EMAIL></td>
                            <td><strong class="positive">+2,450 ر.س</strong></td>
                            <td>
                                <div class="action-buttons-horizontal">
                                    <button class="action-btn edit" onclick="editCustomer(1)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="viewCustomer(1)" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="deleteCustomer(1)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>002</strong></td>
                            <td>شركة النور للتجارة</td>
                            <td><span class="badge success">شركة</span></td>
                            <td>+966112345678</td>
                            <td><EMAIL></td>
                            <td><strong class="negative">-1,250 ر.س</strong></td>
                            <td>
                                <div class="action-buttons-horizontal">
                                    <button class="action-btn edit" onclick="editCustomer(2)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="viewCustomer(2)" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="deleteCustomer(2)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- تذييل الجدول -->
                <div class="table-footer-modern">
                    <div class="results-info-modern">
                        عرض <span id="customers-start-result">1</span> - <span id="customers-end-result">10</span> من <span id="customers-total-results">0</span> نتيجة
                    </div>
                    <div class="pagination-modern" id="customers-pagination-container">
                        <button class="page-btn prev" id="customers-prev-btn" onclick="changeCustomersPage('prev')">
                            <i class="fas fa-chevron-right"></i> السابق
                        </button>
                        <button class="page-btn active" onclick="changeCustomersPage(1)">1</button>
                        <button class="page-btn" onclick="changeCustomersPage(2)">2</button>
                        <button class="page-btn" onclick="changeCustomersPage(3)">3</button>
                        <button class="page-btn next" id="customers-next-btn" onclick="changeCustomersPage('next')">
                            التالي <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- القدم -->
    <footer class="main-footer">
        <div class="container">
            <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة الأعمال</p>
        </div>
    </footer>

    <!-- <script src="js/customers.js"></script> -->

    <script>
        // إجبار الأيقونات على أن تكون أفقية
        document.addEventListener('DOMContentLoaded', function() {
            function forceHorizontalButtons() {
                const containers = document.querySelectorAll('.action-buttons-horizontal');
                containers.forEach(container => {
                    // إجبار الـ flexbox في صف واحد
                    container.style.setProperty('display', 'flex', 'important');
                    container.style.setProperty('flex-direction', 'row', 'important');
                    container.style.setProperty('flex-wrap', 'nowrap', 'important');
                    container.style.setProperty('gap', '6px', 'important');
                    container.style.setProperty('justify-content', 'center', 'important');
                    container.style.setProperty('align-items', 'center', 'important');
                    container.style.setProperty('padding', '3px', 'important');
                    container.style.setProperty('width', '100%', 'important');
                    container.style.setProperty('min-width', '120px', 'important');

                    // تطبيق الأنماط على الأزرار
                    const buttons = container.querySelectorAll('.action-btn');
                    buttons.forEach((btn, index) => {
                        btn.style.setProperty('display', 'flex', 'important');
                        btn.style.setProperty('width', '30px', 'important');
                        btn.style.setProperty('height', '30px', 'important');
                        btn.style.setProperty('min-width', '30px', 'important');
                        btn.style.setProperty('max-width', '30px', 'important');
                        btn.style.setProperty('margin', '0', 'important');
                        btn.style.setProperty('padding', '0', 'important');
                        btn.style.setProperty('border-radius', '6px', 'important');
                        btn.style.setProperty('align-items', 'center', 'important');
                        btn.style.setProperty('justify-content', 'center', 'important');
                        btn.style.setProperty('border', 'none', 'important');
                        btn.style.setProperty('cursor', 'pointer', 'important');
                        btn.style.setProperty('font-size', '12px', 'important');
                        btn.style.setProperty('flex-shrink', '0', 'important');
                        btn.style.setProperty('flex-grow', '0', 'important');

                        // الألوان
                        if (btn.classList.contains('edit')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('view')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        } else if (btn.classList.contains('delete')) {
                            btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)', 'important');
                            btn.style.setProperty('color', 'white', 'important');
                        }
                    });
                });
            }

            // تطبيق التنسيق فوراً
            forceHorizontalButtons();

            // إعادة تطبيق التنسيق كل ثانية للتأكد
            setInterval(forceHorizontalButtons, 1000);

            // تطبيق التنسيق عند إضافة عناصر جديدة
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && node.tagName === 'TR') {
                                setTimeout(() => {
                                    forceHorizontalButtonsForNewRow(node);
                                }, 100);
                            }
                        });
                    }
                });
            });

            // مراقبة التغييرات في الجدول
            const tableBody = document.querySelector('tbody');
            if (tableBody) {
                observer.observe(tableBody, { childList: true, subtree: true });
            }
        });

        // إضافة وظيفة نافذة النجاح المبسطة مباشرة
        function showCustomerSuccessMessage(customerData) {
            // إنشاء النافذة
            const successModal = document.createElement('div');
            successModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            successModal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    padding: 30px;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        color: #28a745;
                        font-size: 4rem;
                        margin-bottom: 20px;
                    ">
                        ✅
                    </div>

                    <h3 style="
                        color: #28a745;
                        margin: 0 0 15px 0;
                        font-size: 1.5rem;
                    ">تم الحفظ بنجاح!</h3>

                    <div style="
                        background: #f8fff9;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 20px 0;
                        border-left: 4px solid #28a745;
                        text-align: right;
                    ">
                        <p style="margin: 5px 0; color: #333;"><strong>اسم العميل:</strong> ${customerData.name}</p>
                        <p style="margin: 5px 0; color: #333;"><strong>رقم العميل:</strong> ${customerData.id}</p>
                        <p style="margin: 5px 0; color: #333;"><strong>نوع العميل:</strong> ${customerData.type === 'company' ? 'شركة' : 'فرد'}</p>
                        <p style="margin: 5px 0; color: #333;"><strong>رقم الهاتف:</strong> ${customerData.phone}</p>
                    </div>

                    <h4 style="color: #333; margin: 20px 0 15px 0;">ماذا تريد أن تفعل الآن؟</h4>

                    <div style="
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 10px;
                        margin-top: 20px;
                    ">
                        <button onclick="closeAndAddAnother()" style="
                            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                            color: white;
                            border: none;
                            padding: 12px 15px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: transform 0.2s;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            ➕ إضافة عميل آخر
                        </button>

                        <button onclick="closeSuccessWindow()" style="
                            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                            color: white;
                            border: none;
                            padding: 12px 15px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: transform 0.2s;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            ✅ إنهاء
                        </button>
                    </div>
                </div>
            `;

            // إضافة الأنماط للانيميشن
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { opacity: 0; transform: translateY(-50px) scale(0.9); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
            `;
            document.head.appendChild(style);

            // إضافة النافذة للصفحة
            document.body.appendChild(successModal);

            // إضافة الوظائف العامة
            window.closeSuccessWindow = function() {
                if (document.body.contains(successModal)) {
                    document.body.removeChild(successModal);
                }
                if (document.head.contains(style)) {
                    document.head.removeChild(style);
                }
            };

            window.closeAndAddAnother = function() {
                closeSuccessWindow();
                // إعادة فتح نافذة إضافة عميل
                if (typeof showAddCustomerModal === 'function') {
                    showAddCustomerModal();
                } else {
                    alert('سيتم إضافة وظيفة إضافة عميل جديد قريباً');
                }
            };

            // إغلاق عند النقر خارج النافذة
            successModal.addEventListener('click', function(e) {
                if (e.target === successModal) {
                    closeSuccessWindow();
                }
            });

            // إغلاق تلقائي بعد 10 ثوانٍ
            setTimeout(() => {
                if (document.body.contains(successModal)) {
                    closeSuccessWindow();
                }
            }, 10000);
        }

        // وظائف العملاء البسيطة
        // تحميل العملاء من localStorage أو استخدام البيانات الافتراضية
        let customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [
            {
                id: 1,
                name: 'أحمد محمد علي',
                type: 'individual',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'الرياض، حي العليا',
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                name: 'شركة النور التجارية',
                type: 'company',
                phone: '+966112345678',
                email: '<EMAIL>',
                address: 'جدة، حي الروضة',
                createdAt: '2024-01-10'
            }
        ];
        let nextCustomerId = 3;

        // تحديد العملاء الموجودين في الجدول عند تحميل الصفحة
        function initializeExistingCustomers() {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 6) {
                    // استخراج البيانات من الجدول
                    const customerIdText = cells[0].textContent.trim();
                    const customerId = customerIdText.includes('CUST-') ?
                        parseInt(customerIdText.replace('CUST-', '').replace(/^0+/, '')) :
                        (index + 1);

                    const customerName = cells[1].textContent.trim();
                    const customerTypeText = cells[2].textContent.trim();
                    const customerType = customerTypeText === 'شركة' ? 'company' : 'individual';
                    const customerPhone = cells[3].textContent.trim();
                    const customerEmail = cells[4].textContent.trim();

                    // التحقق من عدم وجود العميل في المصفوفة
                    const existingCustomer = customersData.find(c => c.id === customerId);
                    if (!existingCustomer && customerName && customerPhone) {
                        customersData.push({
                            id: customerId,
                            name: customerName,
                            type: customerType,
                            phone: customerPhone,
                            email: customerEmail !== '-' ? customerEmail : '',
                            address: '',
                            createdAt: new Date().toLocaleDateString('ar-SA')
                        });

                        // تحديث nextCustomerId
                        if (customerId >= nextCustomerId) {
                            nextCustomerId = customerId + 1;
                        }
                    }
                }
            });

            console.log('تم تحميل العملاء الموجودين:', customersData);
        }

        // دالة حفظ العملاء في localStorage
        function saveCustomersToStorage() {
            localStorage.setItem('monjizCustomers', JSON.stringify(customersData));
            console.log('تم حفظ العملاء في localStorage:', customersData.length);
        }

        // دالة إضافة العميل لدليل الحسابات
        function addCustomerToAccounts(customer) {
            try {
                // تحميل الحسابات الحالية
                const accounts = JSON.parse(localStorage.getItem('monjizAccounts')) || [];

                // إضافة الحسابات الأساسية للشجرة إذا لم تكن موجودة
                const basicAccounts = [
                    {
                        id: 1,
                        code: '1',
                        name: 'الأصول',
                        type: 'assets',
                        level: 1,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 11,
                        code: '11',
                        name: 'الأصول المتداولة',
                        type: 'assets',
                        subType: 'current_assets',
                        parentCode: '1',
                        level: 2,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 1103,
                        code: '1103',
                        name: 'المدينون',
                        type: 'assets',
                        subType: 'current_assets',
                        parentCode: '11',
                        level: 3,
                        balance: 0,
                        status: 'active'
                    },
                    {
                        id: 11030,
                        code: '11030',
                        name: 'العملاء',
                        type: 'assets',
                        subType: 'current_assets',
                        category: 'customers',
                        parentCode: '1103',
                        level: 3,
                        balance: 0,
                        status: 'active'
                    }
                ];

                // إضافة الحسابات الأساسية إذا لم تكن موجودة
                basicAccounts.forEach(basicAccount => {
                    const exists = accounts.find(acc => acc.code === basicAccount.code);
                    if (!exists) {
                        accounts.push(basicAccount);
                    }
                });

                // إنشاء حساب العميل في الشجرة الصحيحة
                const customerAccount = {
                    id: Date.now(),
                    code: `11030${String(customer.id).padStart(3, '0')}`, // ********, ********, etc.
                    name: customer.name,
                    type: 'assets',
                    subType: 'current_assets',
                    category: 'customers',
                    parentCode: '11030', // رمز حساب العملاء الرئيسي
                    parentName: 'العملاء',
                    balance: 0,
                    status: 'active',
                    linkedType: 'customer',
                    linkedId: customer.id,
                    level: 4, // المستوى في الشجرة: الأصول(1) → الأصول المتداولة(2) → العملاء(3) → العميل المحدد(4)
                    createdAt: new Date().toISOString()
                };

                // إضافة الحساب
                accounts.push(customerAccount);

                // حفظ الحسابات المحدثة
                localStorage.setItem('monjizAccounts', JSON.stringify(accounts));

                console.log(`تم إضافة حساب العميل "${customer.name}" في دليل الحسابات`);
            } catch (error) {
                console.error('خطأ في إضافة حساب العميل:', error);
            }
        }

        // دالة إشعار الصفحات الأخرى بالتحديث
        function notifyOtherPagesOfUpdate() {
            // إرسال حدث تحديث للصفحات الأخرى
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            // إرسال رسالة للنوافذ الأخرى
            if (window.BroadcastChannel) {
                const channel = new BroadcastChannel('monjiz-updates');
                channel.postMessage({ type: 'customers-updated', timestamp: Date.now() });
            }
        }

        // متغيرات التنقل بين صفحات العملاء
        let currentCustomerPage = 1;
        const customersPerPage = 10;

        // دالة الانتقال لصفحة معينة من العملاء
        function goToCustomerPage(pageNumber) {
            console.log('الانتقال لصفحة العملاء:', pageNumber);
            currentCustomerPage = pageNumber;

            // تحديث الأزرار النشطة
            const pageButtons = document.querySelectorAll('.pagination-btn:not(:last-child)');
            pageButtons.forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.textContent) === pageNumber) {
                    btn.classList.add('active');
                }
            });

            // تحديث عرض العملاء
            updateCustomersDisplay();

            // تحديث معلومات النتائج
            updateCustomerResultsInfo();
        }

        // دالة الانتقال للصفحة التالية من العملاء
        function nextCustomerPage() {
            const totalPages = Math.ceil(customersData.length / customersPerPage);
            if (currentCustomerPage < totalPages) {
                goToCustomerPage(currentCustomerPage + 1);
            }
        }

        // دالة تحديث عرض العملاء حسب الصفحة
        function updateCustomersDisplay() {
            const tbody = document.querySelector('.customers-table tbody');
            if (!tbody) return;

            // حساب العملاء للصفحة الحالية
            const startIndex = (currentCustomerPage - 1) * customersPerPage;
            const endIndex = startIndex + customersPerPage;
            const pageCustomers = customersData.slice(startIndex, endIndex);

            // مسح الجدول (عدا العناوين)
            const rows = tbody.querySelectorAll('tr');
            rows.forEach(row => {
                if (!row.querySelector('th')) {
                    row.remove();
                }
            });

            // إضافة عملاء الصفحة الحالية
            pageCustomers.forEach(customer => {
                addCustomerToTable(customer);
            });

            console.log(`عرض العملاء ${startIndex + 1}-${Math.min(endIndex, customersData.length)} من ${customersData.length}`);
        }

        // دالة تحديث معلومات نتائج العملاء
        function updateCustomerResultsInfo() {
            const customersStart = document.querySelector('.customers-start');
            const customersEnd = document.querySelector('.customers-end');
            const customersTotal = document.querySelector('.customers-total');

            if (customersStart && customersEnd && customersTotal) {
                const startIndex = (currentCustomerPage - 1) * customersPerPage;
                const endIndex = Math.min(startIndex + customersPerPage, customersData.length);

                customersStart.textContent = startIndex + 1;
                customersEnd.textContent = endIndex;
                customersTotal.textContent = customersData.length;
            }
        }

        // دالة الانتقال للصفحة السابقة من العملاء
        function previousCustomerPage() {
            if (currentCustomerPage > 1) {
                goToCustomerPage(currentCustomerPage - 1);
            }
        }

        // تحميل العملاء من localStorage عند تحميل الصفحة
        function loadCustomersOnPageLoad() {
            console.log('تحميل العملاء من localStorage...');
            const savedCustomers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];

            if (savedCustomers.length > 0) {
                console.log('تم العثور على', savedCustomers.length, 'عميل محفوظ');

                // تحديث مصفوفة العملاء
                customersData.length = 0; // مسح المصفوفة الحالية
                customersData.push(...savedCustomers); // إضافة العملاء المحفوظين

                // تحديث عرض العملاء مع التنقل
                currentCustomerPage = 1; // العودة للصفحة الأولى
                updateCustomersDisplay();
                updateCustomerResultsInfo();

                console.log('تم تحميل العملاء في الجدول مع نظام التنقل');
            } else {
                console.log('لا يوجد عملاء محفوظين');
            }
        }

        // تشغيل التحميل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeExistingCustomers, 500);
            setTimeout(loadCustomersOnPageLoad, 100);
        });

        function showAddCustomerModal() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div class="modal-content" style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 85vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div class="modal-header" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-user-plus"></i>
                            إضافة عميل جديد
                        </h3>
                        <button onclick="closeModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div class="modal-body" style="padding: 25px; max-height: 60vh; overflow-y: auto;">

                    <form id="customerForm" class="form-modern">
                        <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-user" style="color: #667eea;"></i>
                                    اسم العميل
                                </label>
                                <input type="text" id="customerName" required style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'">
                            </div>

                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-building" style="color: #667eea;"></i>
                                    نوع العميل
                                </label>
                                <select id="customerType" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    background: white;
                                    cursor: pointer;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'">
                                    <option value="individual">فرد</option>
                                    <option value="company">شركة</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-phone" style="color: #667eea;"></i>
                                    رقم الهاتف
                                </label>
                                <input type="tel" id="customerPhone" required style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'">
                            </div>

                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-envelope" style="color: #667eea;"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" id="customerEmail" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'">
                            </div>
                        </div>

                        <div class="form-group" style="margin-bottom: 20px;">
                            <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                <i class="fas fa-map-marker-alt" style="color: #667eea;"></i>
                                العنوان
                            </label>
                            <textarea id="customerAddress" rows="3" style="
                                width: 100%;
                                padding: 12px;
                                border: 2px solid #e9ecef;
                                border-radius: 8px;
                                font-size: 14px;
                                box-sizing: border-box;
                                resize: vertical;
                                transition: border-color 0.3s ease;
                            " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'"></textarea>
                        </div>

                        <div class="form-actions" style="
                            display: flex;
                            gap: 12px;
                            justify-content: flex-end;
                            padding-top: 20px;
                            border-top: 1px solid #e9ecef;
                            margin-top: 20px;
                        ">
                            <button type="button" onclick="closeModal()" class="btn-modern btn-secondary" style="
                                background: linear-gradient(135deg, #6c757d 0%, #**********%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(108, 117, 125, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>

                            <button type="submit" id="saveBtn" class="btn-modern btn-primary" style="
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-save"></i>
                                حفظ العميل
                            </button>
                        </div>
                    </form>
                    </div>
                    </form>
                </div>
            `;

            // إضافة أنماط CSS للانيميشن
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { opacity: 0; transform: translateY(-50px) scale(0.9); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(modal);

            // معالجة النموذج
            const form = document.getElementById('customerForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                saveCustomer();
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            window.closeModal = function() {
                document.body.removeChild(modal);
            };
        }

        function saveCustomer() {
            console.log('بدء حفظ العميل...');
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.innerHTML;

            // إظهار رسالة التحميل
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            saveBtn.disabled = true;

            // جمع البيانات
            const customerData = {
                id: nextCustomerId++,
                name: document.getElementById('customerName').value,
                type: document.getElementById('customerType').value,
                phone: document.getElementById('customerPhone').value,
                email: document.getElementById('customerEmail').value,
                address: document.getElementById('customerAddress').value,
                createdAt: new Date().toLocaleDateString('ar-SA')
            };

            console.log('بيانات العميل الجديد:', customerData);

            // محاكاة عملية الحفظ
            setTimeout(() => {
                try {
                    console.log('بدء عملية حفظ العميل...');
                    // إضافة العميل للمصفوفة
                    customersData.push(customerData);
                    console.log('تم إضافة العميل للمصفوفة. العدد الحالي:', customersData.length);

                    // حفظ في localStorage
                    saveCustomersToStorage();

                    // إضافة العميل لدليل الحسابات
                    console.log('إضافة العميل لدليل الحسابات:', customerData);
                    addCustomerToAccounts(customerData);

                    // إشعار الصفحات الأخرى بالتحديث
                    notifyOtherPagesOfUpdate();

                    // إضافة العميل للجدول
                    addCustomerToTable(customerData);

                    // إغلاق النافذة
                    closeModal();

                    // عرض رسالة النجاح
                    showSuccessMessage(customerData);

                } catch (error) {
                    console.error('خطأ في الحفظ:', error);
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                    alert('حدث خطأ أثناء الحفظ. يرجى المحاولة مرة أخرى.');
                }
            }, 1000);
        }

        function addCustomerToTable(customer) {
            console.log('محاولة إضافة العميل للجدول:', customer);
            // البحث عن الجدول في الصفحة
            const tbody = document.querySelector('.customers-table tbody');

            console.log('تم العثور على جدول العملاء:', tbody);
            if (!tbody) {
                console.log('لم يتم العثور على جدول العملاء');
                return;
            }

            const row = document.createElement('tr');
            row.style.animation = 'slideInRow 0.5s ease';
            row.innerHTML = `
                <td><strong>CUST-${String(customer.id).padStart(3, '0')}</strong></td>
                <td><strong>${customer.name}</strong></td>
                <td><span class="badge ${customer.type === 'company' ? 'primary' : 'secondary'}">${customer.type === 'company' ? 'شركة' : 'فرد'}</span></td>
                <td>${customer.phone}</td>
                <td>${customer.email || '-'}</td>
                <td><span class="badge success">0 ريال</span></td>
                <td>
                    <div class="action-buttons-horizontal" style="
                        display: flex !important;
                        flex-direction: row !important;
                        flex-wrap: nowrap !important;
                        gap: 6px !important;
                        justify-content: center !important;
                        align-items: center !important;
                        padding: 3px !important;
                        width: 100% !important;
                        min-width: 120px !important;
                    ">
                        <button class="action-btn edit" onclick="editCustomer(${customer.id})" title="تعديل" style="
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            width: 32px !important;
                            height: 32px !important;
                            border-radius: 6px !important;
                            border: none !important;
                            cursor: pointer !important;
                            font-size: 14px !important;
                            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
                            color: white !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            transition: all 0.2s ease !important;
                        ">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view" onclick="viewCustomer(${customer.id})" title="عرض" style="
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            width: 32px !important;
                            height: 32px !important;
                            border-radius: 6px !important;
                            border: none !important;
                            cursor: pointer !important;
                            font-size: 14px !important;
                            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
                            color: white !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            transition: all 0.2s ease !important;
                        ">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteCustomer(${customer.id})" title="حذف" style="
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            width: 32px !important;
                            height: 32px !important;
                            border-radius: 6px !important;
                            border: none !important;
                            cursor: pointer !important;
                            font-size: 14px !important;
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
                            color: white !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            transition: all 0.2s ease !important;
                        ">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            // إضافة الصف في المقدمة
            tbody.insertBefore(row, tbody.firstChild);

            // إضافة انيميشن للصف الجديد
            if (!document.querySelector('#row-animations')) {
                const style = document.createElement('style');
                style.id = 'row-animations';
                style.textContent = `
                    @keyframes slideInRow {
                        from { opacity: 0; transform: translateX(50px); background: #e8f5e8; }
                        to { opacity: 1; transform: translateX(0); background: transparent; }
                    }
                    @keyframes slideOutRow {
                        from { opacity: 1; transform: translateX(0); }
                        to { opacity: 0; transform: translateX(-50px); }
                    }
                `;
                document.head.appendChild(style);
            }

            // تطبيق تنسيق الأيقونات الأفقية فوراً
            setTimeout(() => {
                forceHorizontalButtonsForNewRow(row);
            }, 50);

            console.log('تم إضافة العميل للجدول:', customer.name);
        }

        // وظيفة خاصة لتنسيق الأيقونات في الصف الجديد
        function forceHorizontalButtonsForNewRow(row) {
            const container = row.querySelector('.action-buttons-horizontal');
            if (!container) return;

            // تطبيق الأنماط بقوة
            container.style.setProperty('display', 'flex', 'important');
            container.style.setProperty('flex-direction', 'row', 'important');
            container.style.setProperty('flex-wrap', 'nowrap', 'important');
            container.style.setProperty('gap', '6px', 'important');
            container.style.setProperty('justify-content', 'center', 'important');
            container.style.setProperty('align-items', 'center', 'important');
            container.style.setProperty('padding', '3px', 'important');
            container.style.setProperty('width', '100%', 'important');
            container.style.setProperty('min-width', '120px', 'important');

            // تطبيق الأنماط على الأزرار
            const buttons = container.querySelectorAll('.action-btn');
            buttons.forEach((btn, index) => {
                btn.style.setProperty('display', 'flex', 'important');
                btn.style.setProperty('align-items', 'center', 'important');
                btn.style.setProperty('justify-content', 'center', 'important');
                btn.style.setProperty('width', '32px', 'important');
                btn.style.setProperty('height', '32px', 'important');
                btn.style.setProperty('border-radius', '6px', 'important');
                btn.style.setProperty('border', 'none', 'important');
                btn.style.setProperty('cursor', 'pointer', 'important');
                btn.style.setProperty('font-size', '14px', 'important');
                btn.style.setProperty('transition', 'all 0.2s ease', 'important');
                btn.style.setProperty('margin', '0', 'important');
                btn.style.setProperty('padding', '0', 'important');

                // تطبيق الألوان حسب النوع
                if (btn.classList.contains('edit')) {
                    btn.style.setProperty('background', 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)', 'important');
                    btn.style.setProperty('color', 'white', 'important');
                } else if (btn.classList.contains('view')) {
                    btn.style.setProperty('background', 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)', 'important');
                    btn.style.setProperty('color', 'white', 'important');
                } else if (btn.classList.contains('delete')) {
                    btn.style.setProperty('background', 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)', 'important');
                    btn.style.setProperty('color', 'white', 'important');
                }

                // إضافة تأثيرات التحويم
                btn.addEventListener('mouseenter', function() {
                    this.style.setProperty('transform', 'translateY(-2px)', 'important');
                    this.style.setProperty('box-shadow', '0 4px 12px rgba(0,0,0,0.15)', 'important');
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.setProperty('transform', 'translateY(0)', 'important');
                    this.style.setProperty('box-shadow', 'none', 'important');
                });
            });

            console.log('تم تطبيق تنسيق الأيقونات الأفقية على الصف الجديد');
        }

        function showSuccessMessage(customer) {
            // استخدام النافذة الاحترافية بدلاً من confirm
            showCustomerSuccessMessage(customer);
        }

        function editCustomer(id) {
            console.log('محاولة تعديل العميل رقم:', id);
            console.log('العملاء المتاحين:', customersData);

            let customer = customersData.find(c => c.id == id);

            // إذا لم يتم العثور على العميل، حاول استخراجه من الجدول
            if (!customer) {
                customer = extractCustomerFromTable(id);
                if (customer) {
                    customersData.push(customer);
                }
            }

            if (!customer) {
                alert('لم يتم العثور على العميل رقم: ' + id);
                return;
            }

            // فتح نافذة التعديل مع البيانات الحالية
            showEditCustomerModal(customer);
        }

        function viewCustomer(id) {
            console.log('محاولة عرض العميل رقم:', id);
            console.log('العملاء المتاحين:', customersData);

            let customer = customersData.find(c => c.id == id);

            // إذا لم يتم العثور على العميل، حاول استخراجه من الجدول
            if (!customer) {
                customer = extractCustomerFromTable(id);
                if (customer) {
                    customersData.push(customer);
                }
            }

            if (!customer) {
                alert('لم يتم العثور على العميل رقم: ' + id);
                return;
            }

            // عرض تفاصيل العميل
            showViewCustomerModal(customer);
        }

        // وظيفة لاستخراج بيانات العميل من الجدول
        function extractCustomerFromTable(id) {
            const rows = document.querySelectorAll('tbody tr');
            for (let row of rows) {
                const editBtn = row.querySelector(`button[onclick*="editCustomer(${id})"]`) ||
                              row.querySelector(`button[onclick*="viewCustomer(${id})"]`) ||
                              row.querySelector(`button[onclick*="deleteCustomer(${id})"]`);

                if (editBtn) {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 6) {
                        const customerIdText = cells[0].textContent.trim();
                        const extractedId = customerIdText.includes('CUST-') ?
                            parseInt(customerIdText.replace('CUST-', '').replace(/^0+/, '')) :
                            parseInt(customerIdText);

                        if (extractedId == id) {
                            const customerName = cells[1].textContent.trim();
                            const customerTypeText = cells[2].textContent.trim();
                            const customerType = customerTypeText === 'شركة' ? 'company' : 'individual';
                            const customerPhone = cells[3].textContent.trim();
                            const customerEmail = cells[4].textContent.trim();

                            return {
                                id: extractedId,
                                name: customerName,
                                type: customerType,
                                phone: customerPhone,
                                email: customerEmail !== '-' ? customerEmail : '',
                                address: '',
                                createdAt: new Date().toLocaleDateString('ar-SA')
                            };
                        }
                    }
                }
            }
            return null;
        }

        function deleteCustomer(id) {
            console.log('محاولة حذف العميل رقم:', id);
            console.log('العملاء المتاحين:', customersData);

            let customer = customersData.find(c => c.id == id);

            // إذا لم يتم العثور على العميل، حاول استخراجه من الجدول
            if (!customer) {
                customer = extractCustomerFromTable(id);
                if (customer) {
                    customersData.push(customer);
                }
            }

            if (!customer) {
                alert('لم يتم العثور على العميل رقم: ' + id);
                return;
            }

            // إنشاء نافذة تأكيد مخصصة
            showDeleteConfirmModal(customer);
        }

        function showDeleteConfirmModal(customer) {
            const modal = document.createElement('div');
            modal.className = 'modal delete-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-radius: 12px 12px 0 0;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            تأكيد الحذف
                        </h3>
                        <button onclick="closeDeleteModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="
                                width: 80px;
                                height: 80px;
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 15px auto;
                                color: white;
                                font-size: 2rem;
                            ">
                                <i class="fas fa-trash"></i>
                            </div>
                            <h4 style="margin: 0 0 10px 0; color: #dc3545;">هل أنت متأكد من حذف هذا العميل؟</h4>
                            <p style="margin: 0; color: #6c757d;">هذا الإجراء لا يمكن التراجع عنه!</p>
                        </div>

                        <div style="
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border-left: 4px solid #dc3545;
                        ">
                            <h5 style="margin: 0 0 10px 0; color: #dc3545;">تفاصيل العميل:</h5>
                            <p style="margin: 5px 0; color: #333;"><strong>الاسم:</strong> ${customer.name}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>الرقم:</strong> CUST-${String(customer.id).padStart(3, '0')}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>النوع:</strong> ${customer.type === 'company' ? 'شركة' : 'فرد'}</p>
                            <p style="margin: 5px 0; color: #333;"><strong>الهاتف:</strong> ${customer.phone}</p>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button onclick="closeDeleteModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #**********%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>

                            <button onclick="confirmDelete(${customer.id})" style="
                                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-trash"></i>
                                نعم، احذف العميل
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeDeleteModal = function() {
                document.body.removeChild(modal);
            };

            window.confirmDelete = function(customerId) {
                // حذف العميل من المصفوفة
                const index = customersData.findIndex(c => c.id == customerId);
                if (index > -1) {
                    const deletedCustomer = customersData[index];
                    customersData.splice(index, 1);

                    // حفظ التغييرات في localStorage
                    saveCustomersToStorage();

                    // حذف الصف من الجدول مع تأثير
                    const rows = document.querySelectorAll('tbody tr');
                    for (let row of rows) {
                        const deleteBtn = row.querySelector(`button[onclick*="deleteCustomer(${customerId})"]`);
                        if (deleteBtn) {
                            row.style.animation = 'slideOutRow 0.5s ease';
                            row.style.background = '#ffebee';
                            setTimeout(() => {
                                row.remove();
                            }, 500);
                            break;
                        }
                    }

                    // إغلاق النافذة
                    closeDeleteModal();

                    // عرض رسالة نجاح
                    setTimeout(() => {
                        alert(`تم حذف العميل "${deletedCustomer.name}" بنجاح`);
                    }, 600);
                }
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeDeleteModal();
                }
            });
        }

        function showEditCustomerModal(customer) {
            const modal = document.createElement('div');
            modal.className = 'modal edit-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div class="modal-content" style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 85vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div class="modal-header" style="
                        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-edit"></i>
                            تعديل العميل
                        </h3>
                        <button onclick="closeEditModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div class="modal-body" style="padding: 25px; max-height: 60vh; overflow-y: auto;">
                        <form id="editCustomerForm" class="form-modern">
                            <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-user" style="color: #ffc107;"></i>
                                        اسم العميل
                                    </label>
                                    <input type="text" id="editCustomerName" value="${customer.name}" required style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>

                                <div class="form-group">
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-building" style="color: #ffc107;"></i>
                                        نوع العميل
                                    </label>
                                    <select id="editCustomerType" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        background: white;
                                        cursor: pointer;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                        <option value="individual" ${customer.type === 'individual' ? 'selected' : ''}>فرد</option>
                                        <option value="company" ${customer.type === 'company' ? 'selected' : ''}>شركة</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-phone" style="color: #ffc107;"></i>
                                        رقم الهاتف
                                    </label>
                                    <input type="tel" id="editCustomerPhone" value="${customer.phone}" required style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>

                                <div class="form-group">
                                    <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                        <i class="fas fa-envelope" style="color: #ffc107;"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" id="editCustomerEmail" value="${customer.email || ''}" style="
                                        width: 100%;
                                        padding: 12px;
                                        border: 2px solid #e9ecef;
                                        border-radius: 8px;
                                        font-size: 14px;
                                        box-sizing: border-box;
                                        transition: border-color 0.3s ease;
                                    " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">
                                </div>
                            </div>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center; gap: 5px; margin-bottom: 8px; font-weight: 600; color: #495057;">
                                    <i class="fas fa-map-marker-alt" style="color: #ffc107;"></i>
                                    العنوان
                                </label>
                                <textarea id="editCustomerAddress" rows="3" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 2px solid #e9ecef;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    box-sizing: border-box;
                                    resize: vertical;
                                    transition: border-color 0.3s ease;
                                " onfocus="this.style.borderColor='#ffc107'" onblur="this.style.borderColor='#e9ecef'">${customer.address || ''}</textarea>
                            </div>

                            <div class="form-actions" style="
                                display: flex;
                                gap: 12px;
                                justify-content: flex-end;
                                padding-top: 20px;
                                border-top: 1px solid #e9ecef;
                                margin-top: 20px;
                            ">
                                <button type="button" onclick="closeEditModal()" class="btn-modern btn-secondary" style="
                                    background: linear-gradient(135deg, #6c757d 0%, #**********%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(108, 117, 125, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>

                                <button type="submit" id="updateBtn" class="btn-modern btn-primary" style="
                                    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: 600;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    transition: all 0.3s ease;
                                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(255, 193, 7, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                    <i class="fas fa-save"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            const form = document.getElementById('editCustomerForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                updateCustomer(customer.id);
            });

            window.closeEditModal = function() {
                document.body.removeChild(modal);
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeEditModal();
                }
            });
        }

        function updateCustomer(customerId) {
            const updateBtn = document.getElementById('updateBtn');
            const originalText = updateBtn.innerHTML;

            // إظهار رسالة التحميل
            updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            updateBtn.disabled = true;

            // جمع البيانات المحدثة
            const updatedData = {
                name: document.getElementById('editCustomerName').value,
                type: document.getElementById('editCustomerType').value,
                phone: document.getElementById('editCustomerPhone').value,
                email: document.getElementById('editCustomerEmail').value,
                address: document.getElementById('editCustomerAddress').value
            };

            // محاكاة عملية التحديث
            setTimeout(() => {
                try {
                    // العثور على العميل في المصفوفة وتحديثه
                    const customerIndex = customersData.findIndex(c => c.id == customerId);
                    if (customerIndex > -1) {
                        customersData[customerIndex] = { ...customersData[customerIndex], ...updatedData };

                        // حفظ التغييرات في localStorage
                        saveCustomersToStorage();

                        // تحديث الصف في الجدول
                        updateCustomerInTable(customersData[customerIndex]);

                        // إغلاق النافذة
                        closeEditModal();

                        // عرض رسالة نجاح
                        alert(`تم تحديث بيانات العميل "${updatedData.name}" بنجاح!`);
                    } else {
                        throw new Error('لم يتم العثور على العميل');
                    }

                } catch (error) {
                    console.error('خطأ في التحديث:', error);
                    updateBtn.innerHTML = originalText;
                    updateBtn.disabled = false;
                    alert('حدث خطأ أثناء تحديث البيانات. يرجى المحاولة مرة أخرى.');
                }
            }, 1000);
        }

        function updateCustomerInTable(customer) {
            // العثور على الصف في الجدول
            const rows = document.querySelectorAll('tbody tr');
            for (let row of rows) {
                const editBtn = row.querySelector(`button[onclick*="editCustomer(${customer.id})"]`);
                if (editBtn) {
                    // تحديث محتوى الصف
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 6) {
                        cells[1].innerHTML = `<strong>${customer.name}</strong>`;
                        cells[2].innerHTML = `<span class="badge ${customer.type === 'company' ? 'primary' : 'secondary'}">${customer.type === 'company' ? 'شركة' : 'فرد'}</span>`;
                        cells[3].textContent = customer.phone;
                        cells[4].textContent = customer.email || '-';

                        // إضافة تأثير بصري للتحديث
                        row.style.background = '#e8f5e8';
                        setTimeout(() => {
                            row.style.background = '';
                        }, 2000);
                    }
                    break;
                }
            }
        }

        function showViewCustomerModal(customer) {
            const modal = document.createElement('div');
            modal.className = 'modal view-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                        color: white;
                        padding: 20px 25px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 1.3rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-eye"></i>
                            تفاصيل العميل
                        </h3>
                        <button onclick="closeViewModal()" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: background 0.2s;
                        " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='none'">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 25px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">رقم العميل</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">CUST-${String(customer.id).padStart(3, '0')}</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">اسم العميل</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem; font-weight: 600;">${customer.name}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">نوع العميل</label>
                                <p style="margin: 5px 0 0 0;">
                                    <span class="badge ${customer.type === 'company' ? 'primary' : 'secondary'}">
                                        ${customer.type === 'company' ? 'شركة' : 'فرد'}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">رقم الهاتف</label>
                                <p style="margin: 5px 0 0 0; font-size: 1.1rem;">${customer.phone}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">البريد الإلكتروني</label>
                                <p style="margin: 5px 0 0 0; font-size: 1rem;">${customer.email || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">تاريخ الإضافة</label>
                                <p style="margin: 5px 0 0 0; font-size: 1rem;">${customer.createdAt}</p>
                            </div>
                        </div>

                        ${customer.address ? `
                        <div style="margin-bottom: 20px;">
                            <label style="font-weight: 600; color: #6c757d; font-size: 0.9rem;">العنوان</label>
                            <p style="margin: 5px 0 0 0; font-size: 1rem; line-height: 1.5;">${customer.address}</p>
                        </div>
                        ` : ''}

                        <div style="display: flex; gap: 10px; justify-content: flex-end; padding-top: 20px; border-top: 1px solid #e9ecef;">
                            <button onclick="closeViewModal(); editCustomer(${customer.id})" style="
                                background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button onclick="closeViewModal()" style="
                                background: linear-gradient(135deg, #6c757d 0%, #**********%);
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            ">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeViewModal = function() {
                document.body.removeChild(modal);
            };

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeViewModal();
                }
            });
        }

        // متغيرات التنقل للعملاء
        let customersCurrentPage = 1;
        let customersItemsPerPage = 10;
        let customersTotalItems = 0;
        let customersAllData = [];

        // دالة تحديث عرض بيانات العملاء حسب الصفحة
        function updateCustomersDataDisplay() {
            const startIndex = (customersCurrentPage - 1) * customersItemsPerPage;
            const endIndex = startIndex + customersItemsPerPage;
            const pageData = customersAllData.slice(startIndex, endIndex);

            // تحديث الجدول
            updateCustomersTableDisplay(pageData);

            // تحديث معلومات النتائج
            updateCustomersResultsInfo();

            // تحديث أزرار التنقل
            updateCustomersPaginationButtons();
        }

        // دالة تحديث عرض جدول العملاء
        function updateCustomersTableDisplay(data) {
            const tbody = document.querySelector('.customers-table tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                            لا توجد بيانات عملاء للعرض
                        </td>
                    </tr>
                `;
                return;
            }

            data.forEach((customer, index) => {
                const globalIndex = (customersCurrentPage - 1) * customersItemsPerPage + index + 1;
                const row = `
                    <tr>
                        <td>${globalIndex}</td>
                        <td>${customer.name || '-'}</td>
                        <td>${customer.type || '-'}</td>
                        <td>${customer.phone || '-'}</td>
                        <td>${customer.email || '-'}</td>
                        <td>${customer.balance || '0.00'}</td>
                        <td>
                            <div class="action-buttons-horizontal">
                                <button class="btn-action btn-view" onclick="viewCustomer(${customer.id})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit" onclick="editCustomer(${customer.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete" onclick="deleteCustomer(${customer.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // دالة تحديث معلومات نتائج العملاء
        function updateCustomersResultsInfo() {
            const startResult = customersTotalItems === 0 ? 0 : (customersCurrentPage - 1) * customersItemsPerPage + 1;
            const endResult = Math.min(customersCurrentPage * customersItemsPerPage, customersTotalItems);

            document.getElementById('customers-start-result').textContent = startResult;
            document.getElementById('customers-end-result').textContent = endResult;
            document.getElementById('customers-total-results').textContent = customersTotalItems;
        }

        // دالة تحديث أزرار التنقل للعملاء
        function updateCustomersPaginationButtons() {
            const totalPages = Math.ceil(customersTotalItems / customersItemsPerPage);
            const paginationContainer = document.getElementById('customers-pagination-container');

            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }

            paginationContainer.style.display = 'flex';

            let paginationHTML = '';

            // زر السابق
            const prevDisabled = customersCurrentPage === 1 ? 'disabled' : '';
            paginationHTML += `
                <button class="page-btn prev ${prevDisabled}" onclick="changeCustomersPage('prev')">
                    <i class="fas fa-chevron-right"></i> السابق
                </button>
            `;

            // أزرار الصفحات
            const startPage = Math.max(1, customersCurrentPage - 2);
            const endPage = Math.min(totalPages, customersCurrentPage + 2);

            if (startPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="changeCustomersPage(1)">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="page-dots">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === customersCurrentPage ? 'active' : '';
                paginationHTML += `<button class="page-btn ${activeClass}" onclick="changeCustomersPage(${i})">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `<span class="page-dots">...</span>`;
                }
                paginationHTML += `<button class="page-btn" onclick="changeCustomersPage(${totalPages})">${totalPages}</button>`;
            }

            // زر التالي
            const nextDisabled = customersCurrentPage === totalPages ? 'disabled' : '';
            paginationHTML += `
                <button class="page-btn next ${nextDisabled}" onclick="changeCustomersPage('next')">
                    التالي <i class="fas fa-chevron-left"></i>
                </button>
            `;

            paginationContainer.innerHTML = paginationHTML;
        }

        // دالة تغيير صفحة العملاء
        function changeCustomersPage(page) {
            const totalPages = Math.ceil(customersTotalItems / customersItemsPerPage);

            if (page === 'prev') {
                if (customersCurrentPage > 1) {
                    customersCurrentPage--;
                }
            } else if (page === 'next') {
                if (customersCurrentPage < totalPages) {
                    customersCurrentPage++;
                }
            } else if (typeof page === 'number') {
                if (page >= 1 && page <= totalPages) {
                    customersCurrentPage = page;
                }
            }

            updateCustomersDataDisplay();
        }

        // دالة تحميل بيانات العملاء
        function loadCustomersData() {
            // تحميل بيانات العملاء من localStorage
            customersAllData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            customersTotalItems = customersAllData.length;
            customersCurrentPage = 1;

            updateCustomersDataDisplay();
        }

        // دالة إظهار/إخفاء القائمة المنسدلة
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        }

        // إغلاق القائمة المنسدلة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach(dropdown => {
                if (!dropdown.parentElement.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });
        });

        // دوال الطباعة والتصدير
        function printCustomers() {
            console.log('طباعة تقرير العملاء...');

            // الحصول على بيانات العملاء
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];

            if (customers.length === 0) {
                alert('لا توجد بيانات عملاء للطباعة');
                return;
            }

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');

            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير العملاء</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                            font-size: 24px;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: right;
                            font-size: 12px;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                            color: #333;
                        }
                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                        .footer {
                            margin-top: 30px;
                            text-align: center;
                            color: #666;
                            font-size: 10px;
                            border-top: 1px solid #ddd;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير العملاء</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p>عدد العملاء: ${customers.length}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>النوع</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            customers.forEach((customer, index) => {
                htmlContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${customer.name || '-'}</td>
                        <td>${customer.type || '-'}</td>
                        <td>${customer.phone || '-'}</td>
                        <td>${customer.email || '-'}</td>
                        <td>${customer.address || '-'}</td>
                        <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام منجز لإدارة الأعمال</p>
                        <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };

            console.log('تم إنشاء تقرير طباعة للعملاء');
        }

        function exportToExcel() {
            console.log('تصدير العملاء إلى Excel...');

            // الحصول على بيانات العملاء
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];

            if (customers.length === 0) {
                alert('لا توجد بيانات عملاء للتصدير');
                return;
            }

            // إنشاء محتوى CSV
            let csvContent = '\uFEFFاسم العميل,النوع,رقم الهاتف,البريد الإلكتروني,العنوان,تاريخ الإضافة\n';

            customers.forEach(customer => {
                const row = [
                    customer.name || '',
                    customer.type || '',
                    customer.phone || '',
                    customer.email || '',
                    customer.address || '',
                    new Date(customer.createdAt).toLocaleDateString('ar-SA')
                ].join(',');
                csvContent += row + '\n';
            });

            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `عملاء_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('تم تصدير', customers.length, 'عميل إلى Excel');
        }

        function exportToPDF() {
            console.log('تصدير العملاء إلى PDF...');

            // الحصول على بيانات العملاء
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];

            if (customers.length === 0) {
                alert('لا توجد بيانات عملاء للتصدير');
                return;
            }

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');

            let htmlContent = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير العملاء</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 12px;
                            text-align: right;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                            color: #333;
                        }
                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                        .footer {
                            margin-top: 30px;
                            text-align: center;
                            color: #666;
                            font-size: 12px;
                            border-top: 1px solid #ddd;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير العملاء</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p>عدد العملاء: ${customers.length}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>النوع</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            customers.forEach((customer, index) => {
                htmlContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${customer.name || '-'}</td>
                        <td>${customer.type || '-'}</td>
                        <td>${customer.phone || '-'}</td>
                        <td>${customer.email || '-'}</td>
                        <td>${customer.address || '-'}</td>
                        <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                    </tr>
                `;
            });

            htmlContent += `
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام منجز لإدارة الأعمال</p>
                        <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };

            console.log('تم إنشاء تقرير PDF للعملاء');
        }

        // تحميل بيانات العملاء عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomersData();

            // إضافة بعض البيانات التجريبية إذا لم تكن موجودة
            addSampleCustomersIfEmpty();
        });

        // إضافة بعض البيانات التجريبية للعملاء إذا لم تكن موجودة
        function addSampleCustomersIfEmpty() {
            const existingData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            if (existingData.length === 0) {
                const sampleData = [
                    {
                        id: 1,
                        name: 'شركة النور للتجارة',
                        type: 'شركة',
                        phone: '+966112345678',
                        email: '<EMAIL>',
                        address: 'الرياض - شارع العليا',
                        balance: '15000.00',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        name: 'مؤسسة الأمل',
                        type: 'مؤسسة',
                        phone: '+966501234567',
                        email: '<EMAIL>',
                        address: 'جدة - شارع التحلية',
                        balance: '8500.00',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        name: 'أحمد محمد',
                        type: 'فرد',
                        phone: '+966551234567',
                        email: '<EMAIL>',
                        address: 'الدمام - حي النزهة',
                        balance: '2300.00',
                        createdAt: new Date().toISOString()
                    }
                ];

                // إضافة المزيد من البيانات للاختبار
                for (let i = 4; i <= 25; i++) {
                    sampleData.push({
                        id: i,
                        name: `عميل رقم ${i}`,
                        type: ['شركة', 'مؤسسة', 'فرد'][Math.floor(Math.random() * 3)],
                        phone: `+96650${String(i).padStart(7, '0')}`,
                        email: `customer${i}@example.com`,
                        address: `عنوان العميل رقم ${i}`,
                        balance: (Math.random() * 10000).toFixed(2),
                        createdAt: new Date().toISOString()
                    });
                }

                localStorage.setItem('monjizCustomers', JSON.stringify(sampleData));
            }
        }
    </script>
</body>
</html>
