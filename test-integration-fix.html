<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التكامل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn.success { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
        .btn.danger { background: rgba(220, 53, 69, 0.3); border-color: #dc3545; }
        .btn.info { background: rgba(23, 162, 184, 0.3); border-color: #17a2b8; }
        
        .result {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .log {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
            border-left: 3px solid;
        }
        
        .log.success { 
            background: rgba(40, 167, 69, 0.2); 
            border-color: #28a745;
            color: #90ee90;
        }
        .log.error { 
            background: rgba(220, 53, 69, 0.2); 
            border-color: #dc3545;
            color: #ffb3b3;
        }
        .log.info { 
            background: rgba(23, 162, 184, 0.2); 
            border-color: #17a2b8;
            color: #87ceeb;
        }

        .navigation {
            text-align: center;
            margin: 20px 0;
        }

        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            padding: 8px 16px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .navigation a:hover {
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-tools"></i> اختبار إصلاح التكامل</h1>
        
        <div class="navigation">
            <a href="accounting.html" target="_blank"><i class="fas fa-calculator"></i> دليل الحسابات</a>
            <a href="customers.html" target="_blank"><i class="fas fa-users"></i> العملاء</a>
            <a href="suppliers.html" target="_blank"><i class="fas fa-truck"></i> الموردين</a>
        </div>

        <!-- اختبار إضافة العملاء -->
        <div class="test-section">
            <h2><i class="fas fa-user-plus"></i> اختبار إضافة العملاء</h2>
            <button class="btn success" onclick="testAddCustomerDirect()">
                <i class="fas fa-plus"></i> إضافة عميل مباشر
            </button>
            <button class="btn info" onclick="checkCustomerAccounts()">
                <i class="fas fa-search"></i> فحص حسابات العملاء
            </button>
        </div>

        <!-- اختبار إضافة الموردين -->
        <div class="test-section">
            <h2><i class="fas fa-truck-loading"></i> اختبار إضافة الموردين</h2>
            <button class="btn success" onclick="testAddSupplierDirect()">
                <i class="fas fa-plus"></i> إضافة مورد مباشر
            </button>
            <button class="btn info" onclick="checkSupplierAccounts()">
                <i class="fas fa-search"></i> فحص حسابات الموردين
            </button>
        </div>

        <!-- اختبار النظام المركزي -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار النظام المركزي</h2>
            <button class="btn info" onclick="testDataManager()">
                <i class="fas fa-cogs"></i> اختبار DataManager
            </button>
            <button class="btn success" onclick="testIntegrationSystem()">
                <i class="fas fa-link"></i> اختبار نظام التكامل
            </button>
        </div>

        <!-- إدارة البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> إدارة البيانات</h2>
            <button class="btn info" onclick="showAllData()">
                <i class="fas fa-eye"></i> عرض جميع البيانات
            </button>
            <button class="btn danger" onclick="clearAllData()">
                <i class="fas fa-trash"></i> مسح جميع البيانات
            </button>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h2>
            <div class="result" id="testResults">
                <div class="log info">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="js/data-manager.js"></script>
    <script src="js/integration-system.js"></script>
    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-SA')}:</strong> ${message}`;
            results.appendChild(logDiv);
            results.scrollTop = results.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function testAddCustomerDirect() {
            log('👤 اختبار إضافة عميل مباشر...', 'info');
            
            try {
                if (!window.dataManager) {
                    log('❌ النظام المركزي غير متاح', 'error');
                    return;
                }

                const testCustomer = {
                    name: `عميل اختبار ${Date.now()}`,
                    type: 'شركة',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    address: 'عنوان اختبار'
                };

                log(`📝 إضافة العميل: ${testCustomer.name}`, 'info');
                const savedCustomer = window.dataManager.addCustomer(testCustomer);
                
                if (savedCustomer) {
                    log(`✅ تم إضافة العميل بنجاح: ID=${savedCustomer.id}`, 'success');
                    
                    // التحقق من إضافة الحساب
                    setTimeout(() => {
                        const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                        const customerAccount = accounts.find(acc => acc.linkedId === savedCustomer.id && acc.linkedType === 'customer');
                        
                        if (customerAccount) {
                            log(`✅ تم إنشاء حساب العميل: ${customerAccount.code} - ${customerAccount.name}`, 'success');
                        } else {
                            log(`❌ لم يتم إنشاء حساب العميل`, 'error');
                        }
                    }, 500);
                } else {
                    log('❌ فشل في إضافة العميل', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار العميل: ${error.message}`, 'error');
            }
        }

        function testAddSupplierDirect() {
            log('🚚 اختبار إضافة مورد مباشر...', 'info');
            
            try {
                if (!window.dataManager) {
                    log('❌ النظام المركزي غير متاح', 'error');
                    return;
                }

                const testSupplier = {
                    name: `مورد اختبار ${Date.now()}`,
                    type: 'شركة',
                    phone: '+************',
                    email: '<EMAIL>',
                    address: 'عنوان مورد'
                };

                log(`📝 إضافة المورد: ${testSupplier.name}`, 'info');
                const savedSupplier = window.dataManager.addSupplier(testSupplier);
                
                if (savedSupplier) {
                    log(`✅ تم إضافة المورد بنجاح: ID=${savedSupplier.id}`, 'success');
                    
                    // التحقق من إضافة الحساب
                    setTimeout(() => {
                        const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                        const supplierAccount = accounts.find(acc => acc.linkedId === savedSupplier.id && acc.linkedType === 'supplier');
                        
                        if (supplierAccount) {
                            log(`✅ تم إنشاء حساب المورد: ${supplierAccount.code} - ${supplierAccount.name}`, 'success');
                        } else {
                            log(`❌ لم يتم إنشاء حساب المورد`, 'error');
                        }
                    }, 500);
                } else {
                    log('❌ فشل في إضافة المورد', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار المورد: ${error.message}`, 'error');
            }
        }

        function checkCustomerAccounts() {
            log('🔍 فحص حسابات العملاء...', 'info');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                log(`📊 العملاء: ${customers.length}`, 'info');
                
                customers.forEach(customer => {
                    const expectedCode = `11030${String(customer.id).padStart(3, '0')}`;
                    const account = accounts.find(acc => acc.code === expectedCode);
                    
                    if (account) {
                        log(`✅ ${customer.name} → ${account.code}`, 'success');
                    } else {
                        log(`❌ ${customer.name} → حساب مفقود (${expectedCode})`, 'error');
                    }
                });
                
            } catch (error) {
                log(`❌ خطأ في فحص حسابات العملاء: ${error.message}`, 'error');
            }
        }

        function checkSupplierAccounts() {
            log('🔍 فحص حسابات الموردين...', 'info');
            
            try {
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                log(`📊 الموردين: ${suppliers.length}`, 'info');
                
                suppliers.forEach(supplier => {
                    const expectedCode = `21030${String(supplier.id).padStart(3, '0')}`;
                    const account = accounts.find(acc => acc.code === expectedCode);
                    
                    if (account) {
                        log(`✅ ${supplier.name} → ${account.code}`, 'success');
                    } else {
                        log(`❌ ${supplier.name} → حساب مفقود (${expectedCode})`, 'error');
                    }
                });
                
            } catch (error) {
                log(`❌ خطأ في فحص حسابات الموردين: ${error.message}`, 'error');
            }
        }

        function testDataManager() {
            log('🔧 اختبار النظام المركزي...', 'info');
            
            try {
                if (window.dataManager) {
                    log('✅ النظام المركزي متاح', 'success');
                    
                    const customers = window.dataManager.getCustomers();
                    const suppliers = window.dataManager.getSuppliers();
                    const accounts = window.dataManager.getAccounts();
                    
                    log(`📊 العملاء: ${customers.length}`, 'info');
                    log(`📊 الموردين: ${suppliers.length}`, 'info');
                    log(`📊 الحسابات: ${accounts.length}`, 'info');
                } else {
                    log('❌ النظام المركزي غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في النظام المركزي: ${error.message}`, 'error');
            }
        }

        function testIntegrationSystem() {
            log('🔗 اختبار نظام التكامل...', 'info');
            
            try {
                if (window.integrationSystem) {
                    log('✅ نظام التكامل متاح', 'success');
                    
                    const diagnosis = window.integrationSystem.diagnose();
                    log(`📊 العملاء: ${diagnosis.customers.total} (${diagnosis.customers.missing} مفقود)`, 'info');
                    log(`📊 الموردين: ${diagnosis.suppliers.total} (${diagnosis.suppliers.missing} مفقود)`, 'info');
                    
                    if (diagnosis.customers.missing > 0 || diagnosis.suppliers.missing > 0) {
                        log('🔄 تشغيل المزامنة الشاملة...', 'info');
                        const result = window.integrationSystem.fullSync();
                        log(`✅ المزامنة مكتملة: ${result.total} حساب جديد`, 'success');
                    }
                } else {
                    log('❌ نظام التكامل غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في نظام التكامل: ${error.message}`, 'error');
            }
        }

        function showAllData() {
            log('👁️ عرض جميع البيانات...', 'info');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const accounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                
                log(`📦 العملاء: ${customers.length}`, 'info');
                customers.slice(0, 3).forEach(customer => {
                    log(`  • ${customer.name} (ID: ${customer.id})`, 'info');
                });
                
                log(`📦 الموردين: ${suppliers.length}`, 'info');
                suppliers.slice(0, 3).forEach(supplier => {
                    log(`  • ${supplier.name} (ID: ${supplier.id})`, 'info');
                });
                
                log(`📦 الحسابات: ${accounts.length}`, 'info');
                const customerAccounts = accounts.filter(acc => acc.category === 'customers');
                const supplierAccounts = accounts.filter(acc => acc.category === 'suppliers');
                
                log(`  • حسابات العملاء: ${customerAccounts.length}`, 'info');
                log(`  • حسابات الموردين: ${supplierAccounts.length}`, 'info');
                
            } catch (error) {
                log(`❌ خطأ في عرض البيانات: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizSuppliers');
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('professionalChartOfAccounts');
                
                log('🗑️ تم حذف جميع البيانات', 'info');
            }
        }

        // تشغيل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل صفحة اختبار الإصلاح', 'success');
            
            // اختبار الأنظمة
            setTimeout(() => {
                testDataManager();
                testIntegrationSystem();
            }, 1000);
        });
    </script>
</body>
</html>
