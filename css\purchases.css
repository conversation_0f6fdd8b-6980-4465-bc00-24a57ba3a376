/* تنسيقات خاصة بصفحة المشتريات */

/* تنسيق رأس الصفحة - استخدام الأنماط من style.css */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.page-title h2 {
    color: var(--dark-color);
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.page-title h2 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.page-title p {
    color: var(--gray-color);
    font-size: 14px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.add-purchase-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-purchase-btn:hover {
    background-color: var(--secondary-color);
}

.add-supplier-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-supplier-btn:hover {
    background-color: #218838;
}

/* تنسيق بطاقات الإحصائيات - استخدام الأنماط من style.css */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 36px;
    margin-left: 15px;
    opacity: 0.8;
}

.purchase-total .stat-icon {
    color: var(--primary-color);
}

.invoice-count .stat-icon {
    color: var(--warning-color);
}

.supplier-count .stat-icon {
    color: var(--success-color);
}

.product-count .stat-icon {
    color: var(--secondary-color);
}

.stat-info h3 {
    color: var(--gray-color);
    font-size: 16px;
    margin-bottom: 5px;
    margin-top: 0;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: var(--dark-color);
}

.stat-period {
    font-size: 14px;
    color: var(--gray-color);
    margin-top: 5px;
}

/* تنسيق قسم البحث والتصفية - استخدام الأنماط من style.css */
.search-filter-section {
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 14px;
    color: var(--gray-color);
}

.form-select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
    min-width: 150px;
}

.reset-btn {
    background-color: var(--light-color);
    color: var(--dark-color);
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    align-self: flex-end;
}

.reset-btn:hover {
    background-color: #e9ecef;
}

/* تنسيق جدول المشتريات - استخدام الأنماط من style.css */
.data-table-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.no-data {
    text-align: center;
    color: var(--gray-color);
    padding: 20px;
}

/* تنسيق حالة الفاتورة - استخدام الأنماط من style.css */
.invoice-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* تنسيق أزرار الإجراءات - استخدام الأنماط من style.css */
.action-btn {
    background: none;
    border: none;
    padding: 5px 8px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
}

.view-btn {
    color: var(--primary-color);
}

.view-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.edit-btn {
    color: var(--warning-color);
}

.edit-btn:hover {
    background-color: var(--warning-color);
    color: white;
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* تنسيق تذييل الجدول - استخدام الأنماط من style.css */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--light-color);
    border-top: 1px solid #eee;
}

.results-info {
    color: var(--gray-color);
    font-size: 14px;
}

.pagination {
    display: flex;
    gap: 5px;
}

.pagination a {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: var(--dark-color);
    transition: var(--transition);
}

.pagination a:hover,
.pagination a.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}


/* تنسيق النافذة المنبثقة - استخدام الأنماط من style.css */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 50px auto;
    padding: 0;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 900px;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-header {
    padding: 20px;
    background-color: var(--light-color);
    border-bottom: 1px solid #eee;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.close {
    color: var(--gray-color);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--dark-color);
}

.modal-body {
    padding: 20px;
}

/* تنسيق استجابي - استخدام الأنماط من style.css */
@media (max-width: 992px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-options {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .data-table {
        font-size: 14px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 10px;
    }

    .table-footer {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .page-actions {
        flex-direction: column;
        width: 100%;
    }

    .primary-btn,
    .secondary-btn,
    .add-purchase-btn,
    .export-btn {
        width: 100%;
        justify-content: center;
    }

    .filter-options {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .form-select {
        width: 100%;
    }
}

/* أنماط النوافذ المنبثقة للموردين */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
}

.modal-content-modern {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    margin: auto;
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header-modern {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header-modern.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.modal-header-modern h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body-modern {
    padding: 30px;
}

.modal-footer-modern {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    border-radius: 0 0 12px 12px;
    background: #f8f9fa;
}

.form-modern {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-row .form-group.full-width {
    grid-column: 1 / -1;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.input-modern,
.select-modern,
.textarea-modern {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: white;
}

.input-modern:focus,
.select-modern:focus,
.textarea-modern:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    background: #f8fff9;
}

.textarea-modern {
    resize: vertical;
    min-height: 80px;
}

.success-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.success-details p {
    margin: 8px 0;
    color: #2c3e50;
}

.success-details strong {
    color: #28a745;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .modal-content-modern {
        width: 95%;
        margin: 20px auto;
        max-height: 95vh;
    }

    .modal-body-modern {
        padding: 20px;
    }

    .modal-footer-modern {
        padding: 15px 20px;
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .btn-modern {
        width: 100%;
        justify-content: center;
    }
}