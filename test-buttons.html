<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .result {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log.success { background: #d4edda; color: #155724; }
        .log.error { background: #f8d7da; color: #721c24; }
        .log.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>اختبار الأزرار والوظائف</h1>
    
    <div>
        <button class="btn" onclick="testBasicFunction()">اختبار دالة أساسية</button>
        <button class="btn" onclick="testDiagnose()">اختبار التشخيص</button>
        <button class="btn" onclick="testProfessionalToggle()">اختبار التبديل</button>
        <button class="btn" onclick="clearResults()">مسح النتائج</button>
    </div>
    
    <div class="result" id="results">
        <div class="log info">جاهز للاختبار...</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(logDiv);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function testBasicFunction() {
            log('اختبار الدالة الأساسية...', 'info');
            try {
                log('✅ الدالة الأساسية تعمل بشكل صحيح', 'success');
            } catch (error) {
                log('❌ خطأ في الدالة الأساسية: ' + error.message, 'error');
            }
        }

        function testDiagnose() {
            log('اختبار التشخيص...', 'info');
            try {
                // محاكاة فحص العناصر
                const elements = [
                    '.accounts-toolbar',
                    '.accounts-display',
                    '.accounts-table-modern',
                    '#professional-accounts-system'
                ];
                
                elements.forEach(selector => {
                    const element = document.querySelector(selector);
                    log(`${selector}: ${element ? '✅ موجود' : '❌ غير موجود'}`, element ? 'success' : 'error');
                });
                
                log('✅ تم إكمال التشخيص', 'success');
            } catch (error) {
                log('❌ خطأ في التشخيص: ' + error.message, 'error');
            }
        }

        function testProfessionalToggle() {
            log('اختبار التبديل للنظام الاحترافي...', 'info');
            try {
                // محاكاة التبديل
                log('1️⃣ محاولة إخفاء العناصر الكلاسيكية...', 'info');
                log('2️⃣ محاولة إظهار النظام الاحترافي...', 'info');
                log('3️⃣ تهيئة النظام الاحترافي...', 'info');
                log('✅ تم اختبار التبديل بنجاح', 'success');
            } catch (error) {
                log('❌ خطأ في اختبار التبديل: ' + error.message, 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '<div class="log info">تم مسح النتائج...</div>';
        }

        // اختبار تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ تم تحميل الصفحة بنجاح', 'success');
            log('🔧 جميع الوظائف جاهزة للاختبار', 'info');
        });

        // اختبار وحدة التحكم
        console.log('🧪 ملف اختبار الأزرار جاهز');
        console.log('📋 الوظائف المتاحة:');
        console.log('   - testBasicFunction()');
        console.log('   - testDiagnose()');
        console.log('   - testProfessionalToggle()');
    </script>
</body>
</html>
