<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخطوة 1: إظهار قائمة المنتجات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .step-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #28a745;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #28a745;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .step-number {
            background: #28a745;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-left: 10px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1><span class="step-number">1</span> إظهار قائمة المنتجات التجريبية</h1>

        <!-- الهدف -->
        <div class="step-section">
            <h2>🎯 الهدف من هذه الخطوة</h2>
            <div class="highlight">
                <p><strong>المطلوب:</strong> إظهار 25 منتج تجريبي في قائمة المنتجات بالمخزون</p>
                <p><strong>المشكلة الحالية:</strong> القائمة فارغة رغم وجود البيانات</p>
            </div>
        </div>

        <!-- فحص البيانات الحالية -->
        <div class="step-section">
            <h2>🔍 فحص البيانات الحالية</h2>
            <div class="action-buttons">
                <button class="btn" onclick="checkCurrentData()">📊 فحص البيانات الموجودة</button>
                <button class="btn btn-danger" onclick="clearData()">🗑️ مسح البيانات القديمة</button>
            </div>
            <div id="data-status"></div>
        </div>

        <!-- إضافة البيانات التجريبية -->
        <div class="step-section">
            <h2>➕ إضافة البيانات التجريبية</h2>
            <div class="action-buttons">
                <button class="btn" onclick="addSampleProducts()">📦 إضافة 25 منتج تجريبي</button>
            </div>
            <div id="add-status"></div>
        </div>

        <!-- اختبار النتيجة -->
        <div class="step-section">
            <h2>🧪 اختبار النتيجة</h2>
            <div class="action-buttons">
                <button class="btn" onclick="testProductsPage()">🚀 فتح صفحة المنتجات</button>
            </div>
            <div id="test-result"></div>
        </div>

        <!-- النتيجة المتوقعة -->
        <div class="step-section">
            <h2>✅ النتيجة المتوقعة</h2>
            <div class="info">
                <h3>يجب أن تجد في صفحة المنتجات:</h3>
                <ul>
                    <li><strong>✅ 25 منتج تجريبي</strong> يظهرون في الجدول</li>
                    <li><strong>✅ العداد يظهر:</strong> "عرض 1 - 10 من 25 منتج"</li>
                    <li><strong>✅ التنقل يعمل:</strong> 3 صفحات (10 منتجات لكل صفحة)</li>
                    <li><strong>✅ أسماء المنتجات:</strong> "منتج رقم 1" إلى "منتج رقم 25"</li>
                    <li><strong>✅ فئات متنوعة:</strong> إلكترونيات، ملابس، أدوات منزلية، كتب</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // فحص البيانات الحالية
        function checkCurrentData() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            let statusHtml = '';
            if (productsData.length === 0) {
                statusHtml = `
                    <div class="info">
                        📊 <strong>فحص البيانات:</strong><br><br>
                        ❌ <strong>لا توجد بيانات منتجات</strong><br>
                        📦 عدد المنتجات: 0<br>
                        💾 مفتاح التخزين: monjizProducts<br><br>
                        💡 <strong>اضغط "إضافة 25 منتج تجريبي"</strong>
                    </div>
                `;
            } else {
                statusHtml = `
                    <div class="success">
                        📊 <strong>البيانات موجودة!</strong><br><br>
                        📦 عدد المنتجات: ${productsData.length}<br>
                        📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br><br>
                        <strong>أول 3 منتجات:</strong><br>
                        ${productsData.slice(0, 3).map((p, i) => 
                            `${i + 1}. ${p.name} - ${p.category} - ${p.price} ر.س`
                        ).join('<br>')}<br><br>
                        ✅ <strong>البيانات جاهزة! اختبر صفحة المنتجات</strong>
                    </div>
                `;
            }
            
            document.getElementById('data-status').innerHTML = statusHtml;
        }

        // مسح البيانات القديمة
        function clearData() {
            localStorage.removeItem('monjizProducts');
            document.getElementById('data-status').innerHTML = `
                <div class="success">
                    🗑️ <strong>تم مسح البيانات القديمة!</strong><br><br>
                    ✅ تم حذف جميع المنتجات من localStorage<br>
                    💡 <strong>الآن اضغط "إضافة 25 منتج تجريبي"</strong>
                </div>
            `;
        }

        // إضافة منتجات تجريبية
        function addSampleProducts() {
            const productsData = [];
            
            // إضافة 25 منتج تجريبي
            for (let i = 1; i <= 25; i++) {
                const quantity = Math.floor(Math.random() * 100) + 1;
                const minQuantity = Math.floor(quantity * 0.2) + 1;
                const price = parseFloat((Math.random() * 1000 + 50).toFixed(2));
                const cost = parseFloat((Math.random() * 500 + 25).toFixed(2));
                
                productsData.push({
                    id: i,
                    code: `PRD-${String(i).padStart(3, '0')}`,
                    name: `منتج رقم ${i}`,
                    category: ['إلكترونيات', 'ملابس', 'أدوات منزلية', 'كتب'][Math.floor(Math.random() * 4)],
                    price: price,
                    cost: cost,
                    quantity: quantity,
                    minQuantity: minQuantity,
                    unit: ['قطعة', 'كيلو', 'متر', 'لتر'][Math.floor(Math.random() * 4)],
                    description: `وصف المنتج رقم ${i}`,
                    createdAt: new Date().toISOString()
                });
            }
            
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            document.getElementById('add-status').innerHTML = `
                <div class="success">
                    ➕ <strong>تم إضافة المنتجات التجريبية بنجاح!</strong><br><br>
                    📦 عدد المنتجات: ${productsData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(productsData.length / 10)}<br>
                    💰 نطاق الأسعار: 50 - 1050 ر.س<br>
                    🏷️ الفئات: إلكترونيات، ملابس، أدوات منزلية، كتب<br><br>
                    ✅ <strong>البيانات جاهزة! اختبر صفحة المنتجات الآن</strong>
                </div>
            `;
        }

        // اختبار صفحة المنتجات
        function testProductsPage() {
            window.open('products.html', '_blank');
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    🚀 <strong>تم فتح صفحة المنتجات!</strong><br><br>
                    
                    <strong>تحقق من:</strong><br>
                    ✅ ظهور 25 منتج في الجدول<br>
                    ✅ العداد: "عرض 1 - 10 من 25 منتج"<br>
                    ✅ التنقل بين 3 صفحات<br>
                    ✅ أسماء المنتجات: "منتج رقم 1" إلى "منتج رقم 25"<br>
                    ✅ فئات متنوعة في كل منتج<br><br>
                    
                    💡 <strong>إذا ظهرت المنتجات = الخطوة 1 مكتملة!</strong><br>
                    ❌ <strong>إذا لم تظهر = أعلمني فوراً</strong>
                </div>
            `;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            document.getElementById('data-status').innerHTML = `
                <div class="info">
                    🔍 <strong>ابدأ بفحص البيانات الحالية</strong><br><br>
                    📊 اضغط "فحص البيانات الموجودة" لمعرفة الوضع الحالي<br>
                    💡 ثم اتبع الخطوات حسب النتيجة
                </div>
            `;
        });
    </script>
</body>
</html>
