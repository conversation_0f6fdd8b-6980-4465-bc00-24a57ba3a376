<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الحسابات الاحترافي - منجز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .toolbar {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn.danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .btn.warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #333; }
        .btn.info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }

        .content {
            padding: 30px;
        }

        .accounts-tree {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .tree-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        .tree-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .account-node {
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .account-node:hover {
            background: #f8f9fa;
        }

        .account-header {
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            user-select: none;
        }

        .account-info {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
        }

        .account-code {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 80px;
            text-align: center;
            font-size: 0.9rem;
        }

        .account-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .account-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .type-assets { background: #e3f2fd; color: #1976d2; }
        .type-liabilities { background: #fff3e0; color: #f57c00; }
        .type-equity { background: #e8f5e8; color: #388e3c; }
        .type-revenue { background: #e8f5e8; color: #2e7d32; }
        .type-expenses { background: #ffebee; color: #d32f2f; }

        .account-balance {
            font-weight: bold;
            color: #2c3e50;
            min-width: 120px;
            text-align: left;
        }

        .account-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn.view { background: #17a2b8; color: white; }
        .action-btn.edit { background: #28a745; color: white; }
        .action-btn.delete { background: #dc3545; color: white; }
        .action-btn.add { background: #6f42c1; color: white; }

        .expand-icon {
            transition: transform 0.3s ease;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .expanded .expand-icon {
            transform: rotate(90deg);
        }

        .children {
            display: none;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .children.show {
            display: block;
        }

        .child-account {
            padding-right: 40px;
            border-bottom: 1px solid #e9ecef;
        }

        .child-account:last-child {
            border-bottom: none;
        }

        .level-1 { padding-right: 40px; }
        .level-2 { padding-right: 80px; }
        .level-3 { padding-right: 120px; }
        .level-4 { padding-right: 160px; }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-header h3 {
            color: #2c3e50;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f8f9fa;
            color: #dc3545;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #6c757d;
            font-size: 1rem;
        }

        .search-box {
            position: relative;
            flex: 1;
            max-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .account-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .account-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-sitemap"></i> دليل الحسابات الاحترافي</h1>
            <p>نظام شجرة حسابات متقدم ومنظم بشكل احترافي</p>
        </div>

        <div class="toolbar">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="البحث في الحسابات...">
                <i class="fas fa-search"></i>
            </div>
            
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn success" onclick="showAddAccountModal()">
                    <i class="fas fa-plus"></i> إضافة حساب
                </button>
                <button class="btn info" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt"></i> توسيع الكل
                </button>
                <button class="btn warning" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt"></i> طي الكل
                </button>
                <button class="btn" onclick="exportAccounts()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="btn danger" onclick="resetAccounts()">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </button>
                <button class="btn success" onclick="addFrenchBank()" style="background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);">
                    <i class="fas fa-university"></i> إضافة البنك الفرنسي
                </button>
                <button class="btn info" onclick="testProfessionalSystem()" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                    <i class="fas fa-vial"></i> اختبار النظام
                </button>
            </div>
        </div>

        <div class="content">
            <!-- إحصائيات -->
            <div class="stats">
                <div class="stat-card">
                    <h3 id="totalAccounts">0</h3>
                    <p>إجمالي الحسابات</p>
                </div>
                <div class="stat-card">
                    <h3 id="mainAccounts">0</h3>
                    <p>الحسابات الرئيسية</p>
                </div>
                <div class="stat-card">
                    <h3 id="subAccounts">0</h3>
                    <p>الحسابات الفرعية</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalBalance">0</h3>
                    <p>إجمالي الأرصدة</p>
                </div>
            </div>

            <!-- شجرة الحسابات -->
            <div class="accounts-tree">
                <div class="tree-header">
                    <i class="fas fa-tree"></i> شجرة الحسابات
                </div>
                <div class="tree-container" id="accountsTree">
                    <div class="empty-state">
                        <i class="fas fa-folder-open"></i>
                        <h3>لا توجد حسابات</h3>
                        <p>ابدأ بإضافة حساب جديد</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل حساب -->
    <div class="modal" id="accountModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة حساب جديد</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="accountForm">
                <div class="form-group">
                    <label for="accountCode">رقم الحساب *</label>
                    <input type="text" id="accountCode" required placeholder="مثال: 1101">
                </div>
                
                <div class="form-group">
                    <label for="accountName">اسم الحساب *</label>
                    <input type="text" id="accountName" required placeholder="مثال: البنك الأهلي">
                </div>
                
                <div class="form-group">
                    <label for="accountType">نوع الحساب *</label>
                    <select id="accountType" required>
                        <option value="">اختر نوع الحساب</option>
                        <option value="assets">الأصول</option>
                        <option value="liabilities">الخصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">الإيرادات</option>
                        <option value="expenses">المصروفات</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="parentAccount">الحساب الأب</label>
                    <select id="parentAccount">
                        <option value="">حساب رئيسي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="accountBalance">الرصيد الافتتاحي</label>
                    <input type="number" id="accountBalance" step="0.01" value="0" placeholder="0.00">
                </div>
                
                <div class="form-group">
                    <label for="accountDescription">الوصف</label>
                    <input type="text" id="accountDescription" placeholder="وصف اختياري للحساب">
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn success">حفظ الحساب</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/chart-of-accounts-professional.js"></script>
</body>
</html>
