<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .print-btn {
            background: #3498db;
        }
        .print-btn:hover {
            background: #2980b9;
        }
        .quick-print-btn {
            background: #27ae60;
        }
        .quick-print-btn:hover {
            background: #229954;
        }
        .test-content {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            background: #f9f9f9;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .test-table {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .test-table th, .test-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .test-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .enhanced-table tbody tr:hover {
            background: #e3f2fd !important;
        }

        /* تنسيق المبالغ والتواريخ */
        .amount {
            font-weight: 600;
            color: #2c3e50;
        }
        .amount.positive {
            color: #27ae60;
        }
        .amount.negative {
            color: #e74c3c;
        }
        .date-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
            background: #f0f8ff;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media print {
            .test-buttons, .no-print {
                display: none !important;
            }
            body {
                margin: 0 !important;
                padding: 15mm !important;
                font-size: 11px !important;
                background: white !important;
                color: black !important;
            }
            .test-container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .test-content h2 {
                color: #000 !important;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
            }
            .test-table {
                box-shadow: none !important;
                border: 2px solid #000 !important;
            }
            .test-table thead {
                background: #e0e0e0 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .test-table th {
                background: #e0e0e0 !important;
                color: #000 !important;
                border: 1px solid #000 !important;
                -webkit-print-color-adjust: exact;
            }
            .test-table td {
                border: 1px solid #666 !important;
                color: #000 !important;
            }
            .test-table tbody tr:nth-child(even) {
                background: #f9f9f9 !important;
                -webkit-print-color-adjust: exact;
            }
            .test-table tfoot {
                background: #e0e0e0 !important;
                -webkit-print-color-adjust: exact;
            }
            .test-table tfoot td {
                border: 2px solid #000 !important;
                font-weight: bold !important;
                color: #000 !important;
            }
            .amount {
                color: #000 !important;
                font-weight: bold !important;
            }
            .date-cell {
                background: #f5f5f5 !important;
                color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖨️ اختبار وظائف الطباعة</h1>
        <p class="no-print">هذه صفحة اختبار لوظائف الطباعة المختلفة. جرب الأزرار أدناه:</p>
        
        <div class="test-buttons no-print">
            <button class="test-btn print-btn" onclick="testPrintWindow()">
                <i class="fas fa-external-link-alt"></i> طباعة في نافذة جديدة
            </button>
            <button class="test-btn quick-print-btn" onclick="testQuickPrint()">
                <i class="fas fa-bolt"></i> طباعة سريعة
            </button>
            <button class="test-btn print-btn" onclick="testDirectPrint()">
                <i class="fas fa-print"></i> طباعة مباشرة
            </button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="test-content">
            <h2>📊 كشف حساب تجريبي</h2>
            <div style="margin: 15px 0;">
                <strong>اسم العميل:</strong> أحمد محمد علي<br>
                <strong>رقم الحساب:</strong> 12345<br>
                <strong>الفترة:</strong> من 2023-06-01 إلى 2023-06-30<br>
                <strong>تاريخ الطباعة:</strong> <span id="print-date"></span>
            </div>
            
            <table class="test-table enhanced-table">
                <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <tr>
                        <th style="padding: 12px; text-align: center;">التاريخ</th>
                        <th style="padding: 12px; text-align: center;">البيان</th>
                        <th style="padding: 12px; text-align: center;">مدين</th>
                        <th style="padding: 12px; text-align: center;">دائن</th>
                        <th style="padding: 12px; text-align: center;">الرصيد</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="date-cell">2023-06-01</span></td>
                        <td>رصيد مدور</td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td><span class="amount">5,000.00</span></td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td><span class="date-cell">2023-06-05</span></td>
                        <td>فاتورة رقم 001</td>
                        <td><span class="amount positive">1,500.00</span></td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td><span class="amount">6,500.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="date-cell">2023-06-10</span></td>
                        <td>سداد نقدي</td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td><span class="amount negative">2,000.00</span></td>
                        <td><span class="amount">4,500.00</span></td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td><span class="date-cell">2023-06-15</span></td>
                        <td>فاتورة رقم 002</td>
                        <td><span class="amount positive">800.00</span></td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td><span class="amount">5,300.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="date-cell">2023-06-20</span></td>
                        <td>سداد شيك</td>
                        <td style="color: #999; font-style: italic;">لا يوجد</td>
                        <td><span class="amount negative">1,000.00</span></td>
                        <td><span class="amount">4,300.00</span></td>
                    </tr>
                </tbody>
                <tfoot style="background: #e9ecef; font-weight: bold;">
                    <tr>
                        <td colspan="2" style="font-weight: bold; color: #2c3e50;">الإجمالي</td>
                        <td style="font-weight: bold; color: #2c3e50;"><span class="amount">2,300.00</span></td>
                        <td style="font-weight: bold; color: #2c3e50;"><span class="amount">3,000.00</span></td>
                        <td style="font-weight: bold; color: #2c3e50;"><span class="amount">4,300.00</span></td>
                    </tr>
                </tfoot>
            </table>
            
            <div style="margin-top: 30px; text-align: center; color: #666;">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الأعمال</p>
            </div>
        </div>
    </div>

    <script>
        // تحديث تاريخ الطباعة
        document.getElementById('print-date').textContent = new Date().toLocaleDateString('ar-SA') + ' - ' + new Date().toLocaleTimeString('ar-SA');
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }
        
        // اختبار الطباعة في نافذة جديدة
        function testPrintWindow() {
            try {
                const content = document.querySelector('.test-content').outerHTML;
                const printWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');
                
                if (!printWindow) {
                    showStatus('تم حظر النوافذ المنبثقة. يرجى السماح بها وإعادة المحاولة.', 'error');
                    return;
                }
                
                const printContent = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <title>طباعة - كشف حساب تجريبي</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            th { background: #f0f0f0; }
                            .print-controls { text-align: center; margin: 20px; padding: 20px; background: #f0f0f0; }
                            .print-btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
                            @media print { .print-controls { display: none; } }
                        </style>
                    </head>
                    <body>
                        <div class="print-controls">
                            <button class="print-btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="print-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                        ${content}
                    </body>
                    </html>
                `;
                
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.focus();
                
                showStatus('تم فتح نافذة الطباعة بنجاح!', 'success');
                
            } catch (error) {
                showStatus('خطأ في فتح نافذة الطباعة: ' + error.message, 'error');
            }
        }
        
        // اختبار الطباعة السريعة
        function testQuickPrint() {
            try {
                window.print();
                showStatus('تم إرسال الصفحة للطباعة!', 'success');
            } catch (error) {
                showStatus('خطأ في الطباعة: ' + error.message, 'error');
            }
        }
        
        // اختبار الطباعة المباشرة
        function testDirectPrint() {
            try {
                // إخفاء الأزرار مؤقتاً
                const buttons = document.querySelector('.test-buttons');
                const originalDisplay = buttons.style.display;
                buttons.style.display = 'none';
                
                window.print();
                
                // إعادة إظهار الأزرار
                setTimeout(() => {
                    buttons.style.display = originalDisplay;
                }, 1000);
                
                showStatus('تم إرسال المحتوى للطباعة!', 'success');
            } catch (error) {
                showStatus('خطأ في الطباعة: ' + error.message, 'error');
            }
        }
        
        // اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                testQuickPrint();
            }
        });
    </script>
</body>
</html>
