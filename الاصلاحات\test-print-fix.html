<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الطباعة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .page-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .page-card .icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .demo-print {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .demo-print h3 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .demo-table th,
        .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
            font-size: 12px;
        }
        .demo-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .demo-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ اختبار إصلاح الطباعة</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>عند اختيار الطباعة تظهر ورقة بيضاء بدون البيانات المختارة</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>طباعة ورقة بيضاء فارغة</li>
                        <li>محاولة إخفاء عناصر غير موجودة</li>
                        <li>البحث عن عناصر بأسماء خاطئة</li>
                        <li>عدم عرض البيانات الفعلية</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>نافذة جديدة مع تقرير كامل</li>
                        <li>عرض البيانات الفعلية من localStorage</li>
                        <li>تنسيق احترافي للطباعة</li>
                        <li>جدول منسق مع جميع البيانات</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الحل المطبق -->
        <div class="test-section">
            <h2>🛠️ الحل المطبق</h2>
            <div class="highlight">
                <h3>تم تغيير آلية الطباعة من:</h3>
                <p><strong>طباعة الصفحة الحالية</strong> → <strong>إنشاء نافذة جديدة مع تقرير مخصص</strong></p>
            </div>
            
            <div class="step-list">
                <h3>خطوات الحل الجديد:</h3>
                <ol>
                    <li><strong>قراءة البيانات:</strong> من localStorage للصفحة المحددة</li>
                    <li><strong>إنشاء HTML:</strong> تقرير كامل مع البيانات</li>
                    <li><strong>نافذة جديدة:</strong> فتح نافذة منفصلة للطباعة</li>
                    <li><strong>تنسيق احترافي:</strong> CSS مخصص للطباعة</li>
                    <li><strong>طباعة تلقائية:</strong> فتح نافذة الطباعة تلقائياً</li>
                    <li><strong>إغلاق تلقائي:</strong> إغلاق النافذة بعد الطباعة</li>
                </ol>
            </div>
        </div>

        <!-- عرض توضيحي للتقرير -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للتقرير</h2>
            <div class="highlight">
                <h3>هكذا سيبدو التقرير المطبوع:</h3>
            </div>
            
            <div class="demo-print">
                <h3>تقرير العملاء</h3>
                <p style="text-align: center; color: #666;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                <p style="text-align: center; color: #666;">عدد العملاء: 3</p>
                
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العميل</th>
                            <th>النوع</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>تاريخ الإضافة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>شركة النور للتجارة</td>
                            <td>شركة</td>
                            <td>+966112345678</td>
                            <td><EMAIL></td>
                            <td>الرياض - شارع العليا</td>
                            <td>2024-01-15</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>مؤسسة الأمل</td>
                            <td>مؤسسة</td>
                            <td>+966501234567</td>
                            <td><EMAIL></td>
                            <td>جدة - شارع التحلية</td>
                            <td>2024-01-16</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>أحمد محمد</td>
                            <td>فرد</td>
                            <td>+966551234567</td>
                            <td><EMAIL></td>
                            <td>الدمام - حي النزهة</td>
                            <td>2024-01-17</td>
                        </tr>
                    </tbody>
                </table>
                
                <p style="text-align: center; color: #666; font-size: 10px; margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px;">
                    تم إنشاء هذا التقرير بواسطة نظام منجز لإدارة الأعمال<br>
                    تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                </p>
            </div>
        </div>

        <!-- الصفحات المصلحة -->
        <div class="test-section">
            <h2>📄 الصفحات المصلحة</h2>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="icon">🛒</div>
                    <h3>صفحة المشتريات</h3>
                    <p>تم إصلاح الطباعة لعرض بيانات المشتريات</p>
                    <button class="btn" onclick="openPurchasesPage()">فتح الصفحة</button>
                </div>
                <div class="page-card">
                    <div class="icon">👥</div>
                    <h3>صفحة العملاء</h3>
                    <p>تم إصلاح الطباعة لعرض بيانات العملاء</p>
                    <button class="btn" onclick="openCustomersPage()">فتح الصفحة</button>
                </div>
            </div>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار الطباعة المصلحة:</h3>
                <p>سنختبر الطباعة في كلا الصفحتين للتأكد من عرض البيانات بشكل صحيح</p>
            </div>
            
            <button class="btn" onclick="startPrintTest()">🚀 بدء اختبار الطباعة</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار الطباعة:</h3>
                <ol>
                    <li><strong>إضافة البيانات:</strong> اضغط "إضافة بيانات تجريبية"</li>
                    <li><strong>فتح صفحة المشتريات:</strong> اضغط "فتح الصفحة"</li>
                    <li><strong>اختبار الطباعة:</strong> اضغط "طباعة وتصدير" ← "طباعة التقرير"</li>
                    <li><strong>التحقق:</strong> يجب أن تفتح نافذة جديدة مع تقرير المشتريات</li>
                    <li><strong>فتح صفحة العملاء:</strong> كرر نفس الاختبار</li>
                    <li><strong>التحقق:</strong> يجب أن تفتح نافذة جديدة مع تقرير العملاء</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="addTestData()">📊 إضافة بيانات تجريبية</button>
            <button class="btn" onclick="openPurchasesPage()">🛒 فتح صفحة المشتريات</button>
            <button class="btn" onclick="openCustomersPage()">👥 فتح صفحة العملاء</button>
            <button class="btn" onclick="testPrintDemo()">🖨️ عرض توضيحي للطباعة</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن:</h3>
                <ul>
                    <li><strong>نافذة جديدة:</strong> تفتح نافذة منفصلة للطباعة</li>
                    <li><strong>البيانات الفعلية:</strong> تظهر جميع البيانات من localStorage</li>
                    <li><strong>تنسيق احترافي:</strong> جدول منسق مع عنوان وتاريخ</li>
                    <li><strong>طباعة تلقائية:</strong> تفتح نافذة الطباعة تلقائياً</li>
                    <li><strong>إغلاق تلقائي:</strong> تغلق النافذة بعد الطباعة</li>
                    <li><strong>دعم العربية:</strong> تنسيق صحيح من اليمين لليسار</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // إضافة بيانات تجريبية
        function addTestData() {
            // إضافة بيانات مشتريات
            const purchases = [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    date: new Date().toISOString(),
                    supplier: 'شركة التقنية المتقدمة',
                    amount: '5000',
                    payment: 'نقدي',
                    status: 'مكتمل',
                    notes: 'شراء أجهزة حاسوب',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    date: new Date().toISOString(),
                    supplier: 'مؤسسة الإمداد الشامل',
                    amount: '3500',
                    payment: 'تحويل بنكي',
                    status: 'قيد المعالجة',
                    notes: 'شراء أثاث مكتبي',
                    createdAt: new Date().toISOString()
                }
            ];
            localStorage.setItem('monjizPurchases', JSON.stringify(purchases));

            // إضافة بيانات عملاء
            const customers = [
                {
                    id: 1,
                    name: 'شركة النور للتجارة',
                    type: 'شركة',
                    phone: '+966112345678',
                    email: '<EMAIL>',
                    address: 'الرياض - شارع العليا',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'مؤسسة الأمل',
                    type: 'مؤسسة',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    address: 'جدة - شارع التحلية',
                    createdAt: new Date().toISOString()
                }
            ];
            localStorage.setItem('monjizCustomers', JSON.stringify(customers));

            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة البيانات التجريبية!</strong><br><br>
                    📊 تم إضافة 2 مشترى و 2 عميل<br>
                    💾 البيانات محفوظة في localStorage<br>
                    🧪 يمكنك الآن اختبار الطباعة<br><br>
                    💡 <strong>افتح الصفحات لاختبار الطباعة!</strong>
                </div>
            `);
        }

        // بدء اختبار الطباعة
        function startPrintTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار الطباعة المصلحة!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ أضف البيانات التجريبية<br>
                    2️⃣ افتح صفحة المشتريات<br>
                    3️⃣ اضغط "طباعة وتصدير" ← "طباعة التقرير"<br>
                    4️⃣ تحقق من فتح نافذة جديدة مع البيانات<br>
                    5️⃣ افتح صفحة العملاء وكرر الاختبار<br><br>
                    
                    <strong>🎯 اضغط "إضافة بيانات تجريبية" أولاً!</strong>
                </div>
            `);
        }

        // فتح صفحة المشتريات
        function openPurchasesPage() {
            window.open('purchases.html', '_blank');
            showResult('🛒 تم فتح صفحة المشتريات<br>💡 اضغط "طباعة وتصدير" ← "طباعة التقرير" لاختبار الطباعة!', 'info');
        }

        // فتح صفحة العملاء
        function openCustomersPage() {
            window.open('customers.html', '_blank');
            showResult('👥 تم فتح صفحة العملاء<br>💡 اضغط "طباعة وتصدير" ← "طباعة التقرير" لاختبار الطباعة!', 'info');
        }

        // عرض توضيحي للطباعة
        function testPrintDemo() {
            showResult(`
                <div class="info">
                    🖨️ <strong>عرض توضيحي للطباعة:</strong><br><br>
                    
                    <strong>🔧 الآلية الجديدة:</strong><br>
                    • قراءة البيانات من localStorage<br>
                    • إنشاء HTML مخصص للتقرير<br>
                    • فتح نافذة جديدة للطباعة<br>
                    • تنسيق احترافي مع CSS<br>
                    • طباعة تلقائية وإغلاق النافذة<br><br>
                    
                    <strong>📋 محتوى التقرير:</strong><br>
                    • عنوان التقرير<br>
                    • تاريخ التقرير وعدد السجلات<br>
                    • جدول منسق مع جميع البيانات<br>
                    • تذييل مع معلومات النظام<br><br>
                    
                    ✅ <strong>لا مزيد من الأوراق البيضاء!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🖨️ <strong>تم إصلاح مشكلة الطباعة بنجاح!</strong><br><br>
                    ✅ نافذة جديدة مع تقرير كامل<br>
                    ✅ عرض البيانات الفعلية من localStorage<br>
                    ✅ تنسيق احترافي للطباعة<br>
                    ✅ طباعة تلقائية وإغلاق النافذة<br>
                    ✅ دعم العربية مع تنسيق RTL<br>
                    ✅ جدول منسق مع جميع البيانات<br><br>
                    🧪 <strong>اضغط "بدء اختبار الطباعة" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
