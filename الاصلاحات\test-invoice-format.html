<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق الفاتورة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(86,171,47,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(79,172,254,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .demo-invoice {
            background: white;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار تنسيق الفاتورة</h1>

        <!-- المشاكل المصلحة -->
        <div class="test-section">
            <h2>✅ المشاكل المصلحة</h2>
            <div class="highlight">
                <h3>تم إصلاح مشكلتين:</h3>
                <ol>
                    <li><strong>رقم التسلسل:</strong> من INV-175247001075 إلى INV-001, INV-002, INV-003</li>
                    <li><strong>تنسيق التاريخ:</strong> من الهجري إلى الميلادي dd/mm/yyyy</li>
                </ol>
            </div>
        </div>

        <!-- مقارنة التنسيق -->
        <div class="test-section">
            <h2>📊 مقارنة التنسيق</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>العنصر</th>
                        <th>قبل الإصلاح</th>
                        <th>بعد الإصلاح</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>رقم الفاتورة</td>
                        <td>INV-175247001075</td>
                        <td>INV-001, INV-002, INV-003</td>
                        <td>✅ تم الإصلاح</td>
                    </tr>
                    <tr>
                        <td>تنسيق التاريخ</td>
                        <td>١٤٤٧/١/١٩ هـ (هجري)</td>
                        <td>19/01/2025 (ميلادي)</td>
                        <td>✅ تم الإصلاح</td>
                    </tr>
                    <tr>
                        <td>ترقيم تلقائي</td>
                        <td>عشوائي</td>
                        <td>تسلسلي متزايد</td>
                        <td>✅ محسن</td>
                    </tr>
                    <tr>
                        <td>سهولة القراءة</td>
                        <td>صعب</td>
                        <td>سهل ومفهوم</td>
                        <td>✅ محسن</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- اختبار الفاتورة -->
        <div class="test-section">
            <h2>🧪 اختبار الفاتورة الجديدة</h2>
            <button class="btn" onclick="openSalesPage()">فتح صفحة المبيعات للاختبار</button>
            <button class="btn" onclick="simulateInvoiceGeneration()">محاكاة توليد الفاتورة</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار:</h3>
                <ol>
                    <li><strong>اضغط "فتح صفحة المبيعات"</strong></li>
                    <li><strong>اضغط "فاتورة جديدة"</strong></li>
                    <li><strong>املأ بيانات الفاتورة:</strong>
                        <ul>
                            <li>اختر عميل</li>
                            <li>أضف منتج</li>
                            <li>أدخل الكمية</li>
                        </ul>
                    </li>
                    <li><strong>احفظ الفاتورة</strong></li>
                    <li><strong>تحقق من:</strong>
                        <ul>
                            <li>رقم الفاتورة: INV-001 (أو الرقم التالي)</li>
                            <li>التاريخ: dd/mm/yyyy ميلادي</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- عرض توضيحي -->
        <div class="test-section">
            <h2>📋 عرض توضيحي للتنسيق الجديد</h2>
            <div class="demo-invoice">
                <h3>🧾 فاتورة جديدة - التنسيق المحسن</h3>
                <div id="demo-invoice-content">
                    <p><strong>رقم الفاتورة:</strong> <span id="demo-invoice-number">INV-001</span></p>
                    <p><strong>التاريخ:</strong> <span id="demo-invoice-date">19/01/2025</span></p>
                    <p><strong>العميل:</strong> شركة التجارة المتقدمة</p>
                    <p><strong>المنتجات:</strong> 2</p>
                    <p><strong>المبلغ الإجمالي:</strong> 3,600.00 ر.س</p>
                </div>
            </div>
        </div>

        <!-- التفاصيل التقنية -->
        <div class="test-section">
            <h2>🔧 التفاصيل التقنية</h2>
            <div class="info">
                <h3>التغييرات المطبقة في الكود:</h3>
                <ol>
                    <li><strong>رقم الفاتورة:</strong>
                        <ul>
                            <li>قبل: <code>id: 'INV-' + Date.now()</code></li>
                            <li>بعد: <code>id: generateSimpleInvoiceNumber()</code></li>
                            <li>النتيجة: INV-001, INV-002, INV-003...</li>
                        </ul>
                    </li>
                    <li><strong>تنسيق التاريخ:</strong>
                        <ul>
                            <li>قبل: <code>toLocaleDateString('ar-SA')</code> (هجري)</li>
                            <li>بعد: <code>toLocaleDateString('en-GB')</code> (ميلادي)</li>
                            <li>النتيجة: dd/mm/yyyy</li>
                        </ul>
                    </li>
                    <li><strong>دالة الترقيم:</strong>
                        <ul>
                            <li>تقرأ الفواتير المحفوظة</li>
                            <li>تجد أعلى رقم موجود</li>
                            <li>تضيف 1 للرقم التالي</li>
                            <li>تنسق بـ 3 أرقام: 001, 002, 003</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="success">
                ✅ <strong>بعد الإصلاح:</strong><br><br>
                🔢 <strong>رقم الفاتورة:</strong> INV-001, INV-002, INV-003...<br>
                📅 <strong>التاريخ:</strong> 19/01/2025 (ميلادي)<br>
                📋 <strong>سهولة القراءة:</strong> أرقام بسيطة ومفهومة<br>
                🔄 <strong>الترقيم:</strong> تسلسلي ومنطقي
            </div>
        </div>
    </div>

    <script>
        // فتح صفحة المبيعات
        function openSalesPage() {
            window.open('sales.html', '_blank');
            showResult('🚀 تم فتح صفحة المبيعات<br>💡 اضغط "فاتورة جديدة" واختبر التنسيق الجديد<br>🔍 تحقق من رقم الفاتورة والتاريخ', 'info');
        }

        // محاكاة توليد الفاتورة
        function simulateInvoiceGeneration() {
            // محاكاة دالة توليد رقم الفاتورة
            function generateSimpleInvoiceNumber() {
                const savedInvoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
                let maxNumber = 0;
                
                savedInvoices.forEach(invoice => {
                    if (invoice.id && invoice.id.startsWith('INV-')) {
                        const numberPart = invoice.id.replace('INV-', '');
                        const number = parseInt(numberPart);
                        if (!isNaN(number) && number > maxNumber) {
                            maxNumber = number;
                        }
                    }
                });
                
                const nextNumber = maxNumber + 1;
                return 'INV-' + nextNumber.toString().padStart(3, '0');
            }

            // توليد رقم فاتورة جديد
            const newInvoiceNumber = generateSimpleInvoiceNumber();
            
            // تنسيق التاريخ الحالي
            const currentDate = new Date().toLocaleDateString('en-GB');
            
            // تحديث العرض التوضيحي
            document.getElementById('demo-invoice-number').textContent = newInvoiceNumber;
            document.getElementById('demo-invoice-date').textContent = currentDate;
            
            showResult(`✅ <strong>تم توليد فاتورة جديدة!</strong><br><br>🔢 <strong>رقم الفاتورة:</strong> ${newInvoiceNumber}<br>📅 <strong>التاريخ:</strong> ${currentDate}<br><br>💡 <strong>لاحظ التنسيق الجديد البسيط والواضح!</strong>`, 'success');
        }

        // عرض النتائج
        function showResult(message, type) {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحديث العرض التوضيحي عند تحميل الصفحة
        window.addEventListener('load', function() {
            simulateInvoiceGeneration();
            showResult('🔧 <strong>تم إصلاح تنسيق الفاتورة!</strong><br><br>✅ رقم الفاتورة: من INV-175247001075 إلى INV-001<br>✅ التاريخ: من الهجري إلى الميلادي<br><br>🧪 <strong>اضغط الأزرار أعلاه لاختبار التحسينات!</strong>', 'info');
        });
    </script>
</body>
</html>
