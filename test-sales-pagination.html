<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التنقل في المبيعات - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(102,126,234,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }
        .success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,184,148,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(9,132,227,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .highlight {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .comparison-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-box, .after-box {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .before-box {
            background: #ffebee;
            border-left: 5px solid #f44336;
        }
        .after-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .features-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .features-table tr:hover {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #dc3545; }
        .demo-counter {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .demo-counter h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .old-counter {
            background: #ffebee;
            color: #d32f2f;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #ffcdd2;
        }
        .new-counter {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #c8e6c9;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🛒 اختبار إصلاح التنقل في المبيعات</h1>

        <!-- المشكلة والحل -->
        <div class="test-section">
            <h2>🎯 المشكلة المصلحة</h2>
            <div class="highlight">
                <h3>المشكلة:</h3>
                <p><strong>صفحة المبيعات لا يوجد بها التنقل بين الصفحات وتوجد في الأعلى "عرض 5 من أصل 5 فاتورة" علماً أن القائمة تشمل أكثر من 10</strong></p>
            </div>
            
            <div class="comparison-box">
                <div class="before-box">
                    <h4>❌ قبل الإصلاح:</h4>
                    <ul>
                        <li>لا يوجد تنقل بين الصفحات</li>
                        <li>عداد خاطئ في الأعلى "عرض 5 من أصل 5"</li>
                        <li>لا يمكن رؤية جميع الفواتير</li>
                        <li>عدم وجود أنماط CSS للتنقل</li>
                        <li>عدم وجود دوال JavaScript للتنقل</li>
                    </ul>
                </div>
                <div class="after-box">
                    <h4>✅ بعد الإصلاح:</h4>
                    <ul>
                        <li>تنقل كامل بين الصفحات في الأسفل</li>
                        <li>عداد صحيح "عرض 1-10 من 25 فاتورة"</li>
                        <li>إمكانية رؤية جميع الفواتير</li>
                        <li>أنماط CSS احترافية للتنقل</li>
                        <li>دوال JavaScript كاملة وفعالة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- مقارنة العدادات -->
        <div class="test-section">
            <h2>📊 مقارنة العدادات</h2>
            <div class="demo-counter">
                <h3>مقارنة العدادات</h3>
                
                <div class="old-counter">
                    <strong>❌ العداد القديم (في الأعلى):</strong><br>
                    <i class="fas fa-info-circle"></i> عرض 5 من أصل 5 فاتورة
                </div>
                
                <div class="new-counter">
                    <strong>✅ العداد الجديد (في الأسفل مع التنقل):</strong><br>
                    عرض 1 - 10 من 25 فاتورة
                </div>
            </div>
        </div>

        <!-- الإصلاحات المطبقة -->
        <div class="test-section">
            <h2>🛠️ الإصلاحات المطبقة</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أنماط CSS للتنقل</td>
                        <td>تصميم احترافي ومتجاوب للتنقل</td>
                        <td><span class="status-indicator status-fixed"></span>مضافة</td>
                    </tr>
                    <tr>
                        <td>HTML للتنقل</td>
                        <td>تذييل جدول مع أزرار التنقل</td>
                        <td><span class="status-indicator status-fixed"></span>مضاف</td>
                    </tr>
                    <tr>
                        <td>دوال JavaScript</td>
                        <td>دوال كاملة لإدارة التنقل</td>
                        <td><span class="status-indicator status-fixed"></span>مضافة</td>
                    </tr>
                    <tr>
                        <td>بيانات تجريبية</td>
                        <td>25 فاتورة للاختبار</td>
                        <td><span class="status-indicator status-fixed"></span>مضافة</td>
                    </tr>
                    <tr>
                        <td>تحديث العداد</td>
                        <td>عداد صحيح في مكان التنقل</td>
                        <td><span class="status-indicator status-fixed"></span>محديث</td>
                    </tr>
                    <tr>
                        <td>إزالة العداد القديم</td>
                        <td>إزالة العداد الخاطئ من الأعلى</td>
                        <td><span class="status-indicator status-fixed"></span>تم</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار التنقل في المبيعات:</h3>
                <p>سنختبر التنقل الجديد في صفحة المبيعات للتأكد من عمله بشكل صحيح</p>
            </div>
            
            <button class="btn" onclick="startSalesTest()">🚀 بدء اختبار المبيعات</button>
            <div id="test-result"></div>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <h2>📋 خطوات الاختبار</h2>
            <div class="step-list">
                <h3>خطوات اختبار التنقل في المبيعات:</h3>
                <ol>
                    <li><strong>فتح صفحة المبيعات:</strong> اضغط "فتح صفحة المبيعات"</li>
                    <li><strong>التحقق من البيانات:</strong> يجب أن تجد 25 فاتورة تجريبية</li>
                    <li><strong>البحث عن التنقل:</strong> انتقل لأسفل الجدول</li>
                    <li><strong>التحقق من العداد:</strong> يجب أن تجد "عرض 1-10 من 25 فاتورة"</li>
                    <li><strong>اختبار التنقل:</strong> جرب الانتقال للصفحة 2 و 3</li>
                    <li><strong>اختبار الأزرار:</strong> جرب "السابق" و "التالي"</li>
                    <li><strong>التحقق من البيانات:</strong> تأكد من تغير البيانات في كل صفحة</li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openSalesPage()">🛒 فتح صفحة المبيعات</button>
            <button class="btn" onclick="checkSalesData()">📊 فحص بيانات المبيعات</button>
            <button class="btn" onclick="addMoreSalesData()">➕ إضافة المزيد من البيانات</button>
            <button class="btn" onclick="clearSalesData()">🗑️ مسح البيانات التجريبية</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاح يجب أن تجد:</h3>
                <ul>
                    <li><strong>تنقل في الأسفل:</strong> أزرار التنقل أسفل جدول الفواتير</li>
                    <li><strong>عداد صحيح:</strong> "عرض 1-10 من 25 فاتورة" بدلاً من "عرض 5 من أصل 5"</li>
                    <li><strong>صفحات متعددة:</strong> 3 صفحات (10 فواتير لكل صفحة)</li>
                    <li><strong>تنقل فعال:</strong> الانتقال بين الصفحات 1، 2، 3 يعمل</li>
                    <li><strong>أزرار ذكية:</strong> "السابق" و "التالي" تتعطل عند الحاجة</li>
                    <li><strong>بيانات متغيرة:</strong> بيانات مختلفة في كل صفحة</li>
                    <li><strong>تصميم احترافي:</strong> تنسيق جميل ومتسق</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // بدء اختبار المبيعات
        function startSalesTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء اختبار التنقل في المبيعات!</strong><br><br>
                    
                    <strong>خطوات الاختبار:</strong><br>
                    1️⃣ افتح صفحة المبيعات<br>
                    2️⃣ تحقق من وجود 25 فاتورة تجريبية<br>
                    3️⃣ انتقل لأسفل الجدول<br>
                    4️⃣ ابحث عن التنقل والعداد الجديد<br>
                    5️⃣ جرب الانتقال بين الصفحات<br>
                    6️⃣ اختبر أزرار "السابق" و "التالي"<br><br>
                    
                    <strong>🎯 اضغط "فتح صفحة المبيعات" للبدء!</strong>
                </div>
            `);
        }

        // فتح صفحة المبيعات
        function openSalesPage() {
            window.open('sales.html', '_blank');
            showResult('🛒 تم فتح صفحة المبيعات<br>💡 انتقل لأسفل الجدول لرؤية التنقل الجديد!', 'info');
        }

        // فحص بيانات المبيعات
        function checkSalesData() {
            const salesData = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            showResult(`
                <div class="info">
                    📊 <strong>فحص بيانات المبيعات:</strong><br><br>
                    
                    <strong>📈 الإحصائيات:</strong><br>
                    🛒 عدد الفواتير: ${salesData.length}<br>
                    🗄️ مفتاح التخزين: monjizInvoices<br>
                    📝 تنسيق البيانات: JSON<br><br>
                    
                    <strong>📋 التنقل المتوقع:</strong><br>
                    📄 عدد الصفحات: ${Math.ceil(salesData.length / 10)}<br>
                    📊 فواتير لكل صفحة: 10<br>
                    🔢 الصفحة الأخيرة: ${salesData.length % 10} فواتير<br><br>
                    
                    ${salesData.length >= 10 ? 
                        '✅ <strong>البيانات كافية لاختبار التنقل!</strong>' :
                        '❌ <strong>أضف المزيد من البيانات لاختبار التنقل</strong>'
                    }
                </div>
            `);
        }

        // إضافة المزيد من البيانات
        function addMoreSalesData() {
            const existingData = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const additionalData = [];
            
            for (let i = existingData.length + 1; i <= existingData.length + 15; i++) {
                additionalData.push({
                    id: `INV-${String(i).padStart(3, '0')}`,
                    date: new Date().toISOString().split('T')[0],
                    customerName: `عميل إضافي رقم ${i}`,
                    productCount: Math.floor(Math.random() * 10) + 1,
                    total: (Math.random() * 5000 + 500).toFixed(2),
                    paymentMethod: ['نقدي', 'بطاقة ائتمان', 'تحويل بنكي'][Math.floor(Math.random() * 3)],
                    status: ['مدفوعة', 'معلقة'][Math.floor(Math.random() * 2)],
                    createdAt: new Date().toISOString()
                });
            }
            
            const allData = [...existingData, ...additionalData];
            localStorage.setItem('monjizInvoices', JSON.stringify(allData));
            
            showResult(`
                <div class="success">
                    ✅ <strong>تم إضافة المزيد من البيانات!</strong><br><br>
                    📊 تم إضافة 15 فاتورة إضافية<br>
                    🔢 إجمالي الفواتير: ${allData.length}<br>
                    📄 عدد الصفحات: ${Math.ceil(allData.length / 10)}<br><br>
                    💡 <strong>افتح صفحة المبيعات لرؤية التنقل المحديث!</strong>
                </div>
            `);
        }

        // مسح البيانات التجريبية
        function clearSalesData() {
            localStorage.removeItem('monjizInvoices');
            showResult('🗑️ تم مسح جميع بيانات المبيعات التجريبية', 'info');
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🛒 <strong>تم إصلاح التنقل في صفحة المبيعات بنجاح!</strong><br><br>
                    ✅ أنماط CSS احترافية للتنقل<br>
                    ✅ HTML محديث مع تذييل الجدول<br>
                    ✅ دوال JavaScript كاملة وفعالة<br>
                    ✅ بيانات تجريبية (25 فاتورة)<br>
                    ✅ عداد صحيح في مكان التنقل<br>
                    ✅ إزالة العداد الخاطئ من الأعلى<br>
                    ✅ تنقل في وسط الصفحة (أسفل الجدول)<br><br>
                    🧪 <strong>اضغط "بدء اختبار المبيعات" للبدء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
