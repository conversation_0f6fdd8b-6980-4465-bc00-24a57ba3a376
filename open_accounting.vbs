' VBScript to open accounting.html directly
' Created to fix accounting management functions issue

Option Explicit

Dim objShell, objFSO, strCurrentDir, strAccountingPath

' Create file system object
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get the current directory (where this script is located)
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Build the path to accounting.html
strAccountingPath = objFSO.BuildPath(strCurrentDir, "accounting.html")

' Check if the file exists
If objFSO.FileExists(strAccountingPath) Then
    ' Create shell object
    Set objShell = CreateObject("WScript.Shell")
    
    ' Open the accounting.html file with the default browser
    objShell.Run """" & strAccountingPath & """", 1, False
    
    ' Clean up
    Set objShell = Nothing
Else
    ' Display error message if the file doesn't exist
    MsgBox "لم يتم العثور على ملف صفحة الحسابات في: " & strAccountingPath, vbExclamation, "خطأ"
End If

' Clean up
Set objFSO = Nothing