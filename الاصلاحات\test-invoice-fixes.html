<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات الفاتورة - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #ff9a9e;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(255,154,158,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,154,158,0.4);
        }
        .success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(86,171,47,0.3);
            font-size: 18px;
            text-align: center;
        }
        .info {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(79,172,254,0.3);
            font-size: 16px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #ff9a9e;
            border-bottom: 3px solid #ff9a9e;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .fixes-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .fixes-table th,
        .fixes-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .fixes-table th {
            background: #ff9a9e;
            color: white;
            font-weight: bold;
        }
        .fixes-table tr:hover {
            background: #f8f9fa;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .highlight {
            background: #fff0f0;
            border: 1px solid #ffcccb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            vertical-align: middle;
        }
        .status-fixed { background: #28a745; }
        .status-pending { background: #ffc107; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاحات الفاتورة الشاملة</h1>

        <!-- المشاكل المصلحة -->
        <div class="test-section">
            <h2>✅ المشاكل المصلحة</h2>
            <table class="fixes-table">
                <thead>
                    <tr>
                        <th>المشكلة</th>
                        <th>قبل الإصلاح</th>
                        <th>بعد الإصلاح</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>رقم التسلسل</td>
                        <td>INV-175247001075</td>
                        <td>INV-001, INV-002, INV-003</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                    <tr>
                        <td>تنسيق التاريخ</td>
                        <td>١٤٤٧/١/١٩ هـ (هجري)</td>
                        <td>19/01/2025 (ميلادي)</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                    <tr>
                        <td>حفظ الفاتورة</td>
                        <td>تختفي عند التحديث</td>
                        <td>تُحفظ في localStorage</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                    <tr>
                        <td>تحميل الفواتير</td>
                        <td>لا تظهر عند التحديث</td>
                        <td>تُحمل تلقائياً</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                    <tr>
                        <td>التقارير</td>
                        <td>لا تظهر الفواتير</td>
                        <td>تقرأ من localStorage</td>
                        <td><span class="status-indicator status-fixed"></span>مصلح</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الاختبار الشامل -->
        <div class="test-section">
            <h2>🧪 الاختبار الشامل</h2>
            <div class="highlight">
                <h3>🎯 اختبار كامل للنظام:</h3>
                <p>سنختبر جميع الإصلاحات في تسلسل منطقي للتأكد من عمل النظام بشكل صحيح</p>
            </div>
            
            <button class="btn" onclick="startComprehensiveTest()">🚀 بدء الاختبار الشامل</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار الشامل:</h3>
                <ol>
                    <li><strong>اختبار إنشاء فاتورة جديدة:</strong>
                        <ul>
                            <li>فتح صفحة المبيعات</li>
                            <li>إنشاء فاتورة جديدة</li>
                            <li>التحقق من رقم التسلسل (INV-001)</li>
                            <li>التحقق من التاريخ الميلادي</li>
                        </ul>
                    </li>
                    <li><strong>اختبار الحفظ والتحميل:</strong>
                        <ul>
                            <li>حفظ الفاتورة</li>
                            <li>إعادة تحميل الصفحة</li>
                            <li>التحقق من ظهور الفاتورة</li>
                        </ul>
                    </li>
                    <li><strong>اختبار التقارير:</strong>
                        <ul>
                            <li>فتح صفحة التقارير</li>
                            <li>التحقق من ظهور بيانات المبيعات</li>
                            <li>التحقق من تقارير العملاء</li>
                        </ul>
                    </li>
                    <li><strong>اختبار الترقيم التسلسلي:</strong>
                        <ul>
                            <li>إنشاء فاتورة ثانية</li>
                            <li>التحقق من الرقم INV-002</li>
                            <li>إنشاء فاتورة ثالثة</li>
                            <li>التحقق من الرقم INV-003</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <button class="btn" onclick="openSalesPage()">📊 فتح صفحة المبيعات</button>
            <button class="btn" onclick="openReportsPage()">📈 فتح صفحة التقارير</button>
            <button class="btn" onclick="checkLocalStorage()">💾 فحص البيانات المحفوظة</button>
            <button class="btn" onclick="simulateInvoiceCreation()">🧾 محاكاة إنشاء فاتورة</button>
        </div>

        <!-- النتائج المتوقعة -->
        <div class="test-section">
            <h2>🎯 النتائج المتوقعة</h2>
            <div class="info">
                <h3>✅ بعد الإصلاحات يجب أن:</h3>
                <ul>
                    <li><strong>رقم الفاتورة:</strong> INV-001, INV-002, INV-003... (بسيط ومتسلسل)</li>
                    <li><strong>التاريخ:</strong> dd/mm/yyyy ميلادي (19/01/2025)</li>
                    <li><strong>الحفظ:</strong> الفواتير تُحفظ في localStorage</li>
                    <li><strong>التحميل:</strong> الفواتير تظهر عند إعادة تحميل الصفحة</li>
                    <li><strong>التقارير:</strong> تقرأ البيانات من localStorage</li>
                    <li><strong>الاستمرارية:</strong> البيانات لا تختفي</li>
                </ul>
            </div>
        </div>

        <!-- معلومات تقنية -->
        <div class="test-section">
            <h2>🔧 معلومات تقنية</h2>
            <div class="highlight">
                <h3>الإصلاحات المطبقة:</h3>
                <ol>
                    <li><strong>دالة generateSimpleInvoiceNumber():</strong> توليد أرقام بسيطة ومتسلسلة</li>
                    <li><strong>تنسيق التاريخ:</strong> استخدام dd/mm/yyyy بدلاً من الهجري</li>
                    <li><strong>دالة loadInvoicesFromStorage():</strong> تحميل الفواتير عند بدء الصفحة</li>
                    <li><strong>دالة updateSalesDataFromStorage():</strong> تحديث بيانات التقارير</li>
                    <li><strong>تحسين الحفظ:</strong> إضافة console.log للتشخيص</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // بدء الاختبار الشامل
        function startComprehensiveTest() {
            showResult(`
                <div class="success">
                    🚀 <strong>بدء الاختبار الشامل!</strong><br><br>
                    
                    <strong>المرحلة 1: اختبار إنشاء الفاتورة</strong><br>
                    1️⃣ افتح صفحة المبيعات<br>
                    2️⃣ اضغط "فاتورة جديدة"<br>
                    3️⃣ تحقق من رقم الفاتورة: INV-001<br>
                    4️⃣ تحقق من التاريخ الميلادي<br><br>
                    
                    <strong>المرحلة 2: اختبار الحفظ</strong><br>
                    5️⃣ املأ بيانات الفاتورة واحفظها<br>
                    6️⃣ أعد تحميل الصفحة (F5)<br>
                    7️⃣ تحقق من ظهور الفاتورة<br><br>
                    
                    <strong>المرحلة 3: اختبار التقارير</strong><br>
                    8️⃣ افتح صفحة التقارير<br>
                    9️⃣ تحقق من ظهور بيانات المبيعات<br><br>
                    
                    <strong>🎯 اضغط الأزرار أدناه لبدء كل مرحلة!</strong>
                </div>
            `);
        }

        // فتح صفحة المبيعات
        function openSalesPage() {
            window.open('sales.html', '_blank');
            showResult('📊 تم فتح صفحة المبيعات<br>💡 اضغط "فاتورة جديدة" واختبر الإصلاحات', 'info');
        }

        // فتح صفحة التقارير
        function openReportsPage() {
            window.open('reports.html', '_blank');
            showResult('📈 تم فتح صفحة التقارير<br>💡 تحقق من ظهور بيانات المبيعات الحقيقية', 'info');
        }

        // فحص البيانات المحفوظة
        function checkLocalStorage() {
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            showResult(`
                <div class="info">
                    💾 <strong>البيانات المحفوظة في localStorage:</strong><br><br>
                    🧾 <strong>الفواتير:</strong> ${invoices.length} فاتورة<br>
                    👥 <strong>العملاء:</strong> ${customers.length} عميل<br>
                    📦 <strong>المنتجات:</strong> ${products.length} منتج<br><br>
                    ${invoices.length > 0 ? `<strong>آخر فاتورة:</strong> ${invoices[invoices.length - 1].id}` : '<strong>لا توجد فواتير محفوظة</strong>'}
                </div>
            `);
        }

        // محاكاة إنشاء فاتورة
        function simulateInvoiceCreation() {
            // محاكاة دالة توليد رقم الفاتورة
            function generateSimpleInvoiceNumber() {
                const savedInvoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
                let maxNumber = 0;
                
                savedInvoices.forEach(invoice => {
                    if (invoice.id && invoice.id.startsWith('INV-')) {
                        const numberPart = invoice.id.replace('INV-', '');
                        const number = parseInt(numberPart);
                        if (!isNaN(number) && number > maxNumber) {
                            maxNumber = number;
                        }
                    }
                });
                
                const nextNumber = maxNumber + 1;
                return 'INV-' + nextNumber.toString().padStart(3, '0');
            }

            // توليد رقم فاتورة جديد
            const newInvoiceNumber = generateSimpleInvoiceNumber();
            
            // تنسيق التاريخ الحالي
            const currentDate = new Date();
            const day = currentDate.getDate().toString().padStart(2, '0');
            const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
            const year = currentDate.getFullYear();
            const formattedDate = `${day}/${month}/${year}`;
            
            showResult(`
                <div class="success">
                    🧾 <strong>محاكاة إنشاء فاتورة جديدة:</strong><br><br>
                    🔢 <strong>رقم الفاتورة:</strong> ${newInvoiceNumber}<br>
                    📅 <strong>التاريخ:</strong> ${formattedDate}<br>
                    💾 <strong>ستُحفظ في:</strong> localStorage<br><br>
                    ✅ <strong>التنسيق صحيح ومطابق للمطلوب!</strong>
                </div>
            `);
        }

        // عرض النتائج
        function showResult(message, type = 'info') {
            document.getElementById('test-result').innerHTML = `<div class="${type}">${message}</div>`;
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            showResult(`
                <div class="info">
                    🔧 <strong>تم تطبيق جميع الإصلاحات!</strong><br><br>
                    ✅ رقم التسلسل: INV-001, INV-002, INV-003...<br>
                    ✅ التاريخ: ميلادي dd/mm/yyyy<br>
                    ✅ الحفظ: في localStorage<br>
                    ✅ التحميل: تلقائي عند بدء الصفحة<br>
                    ✅ التقارير: تقرأ البيانات الحقيقية<br><br>
                    🧪 <strong>اضغط "بدء الاختبار الشامل" للتحقق من كل شيء!</strong>
                </div>
            `);
        });
    </script>
</body>
</html>
