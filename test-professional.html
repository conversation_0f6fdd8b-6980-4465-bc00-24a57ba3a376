<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الاحترافي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn.primary { background: rgba(0, 123, 255, 0.3); border-color: #007bff; }
        .btn.success { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
        .btn.warning { background: rgba(255, 193, 7, 0.3); border-color: #ffc107; }
        .btn.info { background: rgba(23, 162, 184, 0.3); border-color: #17a2b8; }
        
        .status-box {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .navigation {
            text-align: center;
            margin: 30px 0;
        }
        
        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 15px 25px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
            font-size: 16px;
        }
        
        .navigation a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-list li {
            background: rgba(255,255,255,0.05);
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 4px solid rgba(255,255,255,0.3);
        }
        
        .highlight {
            background: rgba(255, 255, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-tree"></i> اختبار النظام الاحترافي</h1>
        
        <div class="navigation">
            <a href="customers.html" target="_blank">
                <i class="fas fa-users"></i> العملاء
            </a>
            <a href="suppliers.html" target="_blank">
                <i class="fas fa-truck"></i> الموردين
            </a>
            <a href="accounting.html" target="_blank">
                <i class="fas fa-calculator"></i> دليل الحسابات
            </a>
        </div>

        <!-- خطوات الاختبار -->
        <div class="test-section">
            <div class="section-title">
                <i class="fas fa-list-ol"></i>
                خطوات اختبار النظام الاحترافي
            </div>
            <ul class="step-list">
                <li><strong>1.</strong> أضف عميل جديد من صفحة العملاء</li>
                <li><strong>2.</strong> أضف مورد جديد من صفحة الموردين</li>
                <li><strong>3.</strong> افتح دليل الحسابات واذهب للنظام الاحترافي</li>
                <li><strong>4.</strong> تحقق من ظهور العميل والمورد في الشجرة</li>
                <li><strong>5.</strong> جرب البحث الفوري من أول حرف</li>
            </ul>
        </div>

        <!-- اختبار البحث -->
        <div class="test-section">
            <div class="section-title">
                <i class="fas fa-search"></i>
                اختبار البحث الفوري
            </div>
            <p>في النظام الاحترافي، جرب البحث عن:</p>
            <ul class="step-list">
                <li><span class="highlight">عميل</span> - للبحث عن العملاء</li>
                <li><span class="highlight">مورد</span> - للبحث عن الموردين</li>
                <li><span class="highlight">1103</span> - للبحث برقم الحساب</li>
                <li><span class="highlight">بنك</span> - للبحث عن الحسابات البنكية</li>
            </ul>
        </div>

        <!-- أدوات الاختبار -->
        <div class="test-section">
            <div class="section-title">
                <i class="fas fa-tools"></i>
                أدوات الاختبار
            </div>
            <button class="btn primary" onclick="addTestData()">
                <i class="fas fa-plus"></i> إضافة بيانات تجريبية
            </button>
            <button class="btn info" onclick="checkProfessionalSystem()">
                <i class="fas fa-search"></i> فحص النظام الاحترافي
            </button>
            <button class="btn success" onclick="testSearch()">
                <i class="fas fa-search-plus"></i> اختبار البحث
            </button>
            <button class="btn warning" onclick="clearData()">
                <i class="fas fa-broom"></i> مسح البيانات
            </button>
        </div>

        <!-- نتائج الاختبار -->
        <div class="test-section">
            <div class="section-title">
                <i class="fas fa-clipboard-check"></i>
                نتائج الاختبار
            </div>
            <div class="status-box" id="testResults">
                اضغط على "فحص النظام الاحترافي" لبدء الاختبار...
            </div>
        </div>

        <!-- نصائح -->
        <div class="test-section">
            <div class="section-title">
                <i class="fas fa-lightbulb"></i>
                نصائح مهمة
            </div>
            <ul class="step-list">
                <li><strong>البحث الفوري:</strong> يعمل من أول حرف تكتبه</li>
                <li><strong>التحديث التلقائي:</strong> النظام يتحدث تلقائياً عند إضافة عملاء/موردين</li>
                <li><strong>الشجرة التفاعلية:</strong> يمكن طي وتوسيع الحسابات</li>
                <li><strong>البحث الذكي:</strong> يبحث في الأسماء والأرقام والفئات</li>
            </ul>
        </div>
    </div>

    <script src="js/data-manager.js"></script>
    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            
            results.innerHTML += `${icon} ${timestamp}: ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        function addTestData() {
            log('إضافة بيانات تجريبية...', 'info');
            
            if (!window.dataManager) {
                log('النظام المركزي غير متاح', 'error');
                return;
            }

            // إضافة عميل تجريبي
            const testCustomer = {
                name: `عميل تجريبي ${Date.now()}`,
                type: 'شركة',
                phone: '+966501234567',
                email: '<EMAIL>'
            };

            const customer = window.dataManager.addCustomer(testCustomer);
            if (customer) {
                log(`تم إضافة العميل: ${customer.name}`, 'success');
            }

            // إضافة مورد تجريبي
            const testSupplier = {
                name: `مورد تجريبي ${Date.now()}`,
                type: 'شركة',
                phone: '+966502345678',
                email: '<EMAIL>'
            };

            const supplier = window.dataManager.addSupplier(testSupplier);
            if (supplier) {
                log(`تم إضافة المورد: ${supplier.name}`, 'success');
            }

            setTimeout(() => {
                log('تم إضافة البيانات التجريبية بنجاح', 'success');
            }, 1000);
        }

        function checkProfessionalSystem() {
            log('فحص النظام الاحترافي...', 'info');
            
            try {
                const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
                const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
                const basicAccounts = JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
                const professionalAccounts = JSON.parse(localStorage.getItem('professionalChartOfAccounts')) || [];

                log(`العملاء: ${customers.length}`, 'info');
                log(`الموردين: ${suppliers.length}`, 'info');
                log(`الحسابات الأساسية: ${basicAccounts.length}`, 'info');
                log(`الحسابات الاحترافية: ${professionalAccounts.length}`, 'info');

                // فحص حسابات العملاء
                const customerAccounts = basicAccounts.filter(acc => acc.category === 'customers');
                log(`حسابات العملاء: ${customerAccounts.length}`, customerAccounts.length > 0 ? 'success' : 'error');

                // فحص حسابات الموردين
                const supplierAccounts = basicAccounts.filter(acc => acc.category === 'suppliers');
                log(`حسابات الموردين: ${supplierAccounts.length}`, supplierAccounts.length > 0 ? 'success' : 'error');

                if (customerAccounts.length > 0) {
                    customerAccounts.slice(0, 3).forEach(acc => {
                        log(`  • ${acc.name} (${acc.code})`, 'info');
                    });
                }

                if (supplierAccounts.length > 0) {
                    supplierAccounts.slice(0, 3).forEach(acc => {
                        log(`  • ${acc.name} (${acc.code})`, 'info');
                    });
                }

            } catch (error) {
                log(`خطأ في الفحص: ${error.message}`, 'error');
            }
        }

        function testSearch() {
            log('اختبار البحث...', 'info');
            log('افتح دليل الحسابات → النظام الاحترافي', 'info');
            log('جرب البحث عن: عميل، مورد، 1103، بنك', 'info');
            log('البحث يعمل من أول حرف تكتبه', 'success');
        }

        function clearData() {
            if (confirm('هل تريد مسح جميع البيانات التجريبية؟')) {
                localStorage.removeItem('monjizCustomers');
                localStorage.removeItem('monjizSuppliers');
                localStorage.removeItem('chartOfAccounts');
                localStorage.removeItem('monjizAccounts');
                localStorage.removeItem('professionalChartOfAccounts');
                
                log('تم مسح جميع البيانات', 'info');
                document.getElementById('testResults').innerHTML = 'تم مسح البيانات...\n';
            }
        }

        // فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار النظام الاحترافي', 'success');
            setTimeout(checkProfessionalSystem, 1000);
        });
    </script>
</body>
</html>
