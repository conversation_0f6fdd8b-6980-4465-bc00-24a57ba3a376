// نظام إدارة البيانات المركزي - ربط جميع الأقسام
// هذا الملف يدير الربط بين المبيعات والمشتريات والمخزون والعملاء والموردين

class DataManager {
    constructor() {
        this.initializeData();
        this.setupEventListeners();
    }

    // تهيئة البيانات الأساسية
    initializeData() {
        // تهيئة المنتجات الأساسية إذا لم تكن موجودة
        if (!localStorage.getItem('monjizProducts')) {
            const defaultProducts = [
                {
                    id: 1,
                    name: 'أرز بسمتي - كيس 5 كيلو',
                    barcode: 'RICE-BASMATI-5KG',
                    category: 'مواد غذائية',
                    purchasePrice: 35.00,
                    salePrice: 50.00,
                    quantity: 100,
                    minQuantity: 20,
                    status: 'active',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'سكر أبيض - كيس 2 كيلو',
                    barcode: 'SUGAR-WHITE-2KG',
                    category: 'مواد غذائية',
                    purchasePrice: 18.00,
                    salePrice: 25.00,
                    quantity: 80,
                    minQuantity: 15,
                    status: 'active',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'زيت طبخ - عبوة 1 لتر',
                    barcode: 'OIL-COOK-1L',
                    category: 'مواد غذائية',
                    purchasePrice: 12.00,
                    salePrice: 15.00,
                    quantity: 60,
                    minQuantity: 10,
                    status: 'active',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 4,
                    name: 'لابتوب Dell',
                    barcode: 'LAPTOP-DELL-001',
                    category: 'إلكترونيات',
                    purchasePrice: 1800.00,
                    salePrice: 2000.00,
                    quantity: 5,
                    minQuantity: 2,
                    status: 'active',
                    createdAt: new Date().toISOString()
                }
            ];
            this.saveProducts(defaultProducts);
        }

        // تهيئة العملاء الأساسيين
        if (!localStorage.getItem('monjizCustomers')) {
            const defaultCustomers = [
                {
                    id: 1,
                    name: 'أحمد محمد',
                    phone: '0501234567',
                    email: '<EMAIL>',
                    address: 'الرياض، المملكة العربية السعودية',
                    createdAt: new Date().toISOString()
                }
            ];
            this.saveCustomers(defaultCustomers);
        }

        // تهيئة الموردين الأساسيين
        if (!localStorage.getItem('monjizSuppliers')) {
            const defaultSuppliers = [
                {
                    id: 1,
                    name: 'شركة الأغذية المتحدة',
                    phone: '0112345678',
                    email: '<EMAIL>',
                    type: 'مواد غذائية',
                    createdAt: new Date().toISOString()
                }
            ];
            this.saveSuppliers(defaultSuppliers);
        }

        // تهيئة الفئات الأساسية
        if (!localStorage.getItem('monjizCategories')) {
            const defaultCategories = [
                {
                    id: 1,
                    name: 'مواد غذائية',
                    code: 'food',
                    description: 'المواد الغذائية والمشروبات',
                    color: '#28a745',
                    icon: 'fas fa-utensils',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'إلكترونيات',
                    code: 'electronics',
                    description: 'الأجهزة الإلكترونية والتقنية',
                    color: '#007bff',
                    icon: 'fas fa-laptop',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'ملابس',
                    code: 'clothing',
                    description: 'الملابس والأزياء',
                    color: '#6f42c1',
                    icon: 'fas fa-tshirt',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 4,
                    name: 'منزلية',
                    code: 'home',
                    description: 'الأدوات المنزلية والديكور',
                    color: '#fd7e14',
                    icon: 'fas fa-home',
                    createdAt: new Date().toISOString()
                }
            ];
            this.saveCategories(defaultCategories);
        }

        // تهيئة المبيعات والمشتريات
        if (!localStorage.getItem('monjizSales')) {
            localStorage.setItem('monjizSales', JSON.stringify([]));
        }
        if (!localStorage.getItem('monjizPurchases')) {
            localStorage.setItem('monjizPurchases', JSON.stringify([]));
        }
    }

    // إعداد مستمعي الأحداث للتحديثات
    setupEventListeners() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key && e.key.startsWith('monjiz')) {
                this.notifyPageUpdate(e.key);
            }
        });
    }

    // === إدارة المنتجات ===
    getProducts() {
        return JSON.parse(localStorage.getItem('monjizProducts')) || [];
    }

    saveProducts(products) {
        localStorage.setItem('monjizProducts', JSON.stringify(products));
        this.notifyPageUpdate('monjizProducts');
    }

    addProduct(product) {
        const products = this.getProducts();
        const newId = Math.max(...products.map(p => p.id), 0) + 1;
        const newProduct = {
            ...product,
            id: newId,
            createdAt: new Date().toISOString()
        };
        products.push(newProduct);
        this.saveProducts(products);
        return newProduct;
    }

    updateProduct(productId, updates) {
        const products = this.getProducts();
        const index = products.findIndex(p => p.id === productId);
        if (index !== -1) {
            products[index] = { ...products[index], ...updates };
            this.saveProducts(products);
            return products[index];
        }
        return null;
    }

    // تحديث كمية المنتج مع تسجيل الحركة
    updateProductQuantity(productId, quantityChange, operation = 'add', reference = '', referenceType = 'manual') {
        const products = this.getProducts();
        const product = products.find(p => p.id === productId);
        if (product) {
            const oldQuantity = product.quantity;

            if (operation === 'add') {
                product.quantity += quantityChange;
            } else if (operation === 'subtract') {
                product.quantity = Math.max(0, product.quantity - quantityChange);
            }

            // تسجيل حركة المخزون
            this.addStockMovement({
                productId: productId,
                productName: product.name,
                productCode: product.code || product.id,
                movementType: operation === 'add' ? 'in' : 'out',
                quantity: quantityChange,
                oldQuantity: oldQuantity,
                newQuantity: product.quantity,
                reference: reference,
                referenceType: referenceType, // sale, purchase, adjustment, manual
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA'),
                createdAt: new Date().toISOString()
            });

            this.saveProducts(products);
            console.log(`تم تحديث كمية المنتج ${product.name}: ${oldQuantity} → ${product.quantity}`);
            return product;
        }
        return null;
    }

    // إضافة حركة مخزون
    addStockMovement(movement) {
        const movements = this.getStockMovements();
        const newId = Math.max(...movements.map(m => m.id), 0) + 1;
        const newMovement = {
            ...movement,
            id: newId,
            createdAt: movement.createdAt || new Date().toISOString()
        };

        movements.push(newMovement);
        this.saveStockMovements(movements);
        return newMovement;
    }

    // الحصول على حركات المخزون
    getStockMovements() {
        return JSON.parse(localStorage.getItem('monjizStockMovements')) || [];
    }

    // حفظ حركات المخزون
    saveStockMovements(movements) {
        localStorage.setItem('monjizStockMovements', JSON.stringify(movements));
        this.notifyPageUpdate('monjizStockMovements');
    }

    // ===== إدارة الحسابات =====

    // الحصول على الحسابات
    getAccounts() {
        return JSON.parse(localStorage.getItem('monjizAccounts')) ||
               JSON.parse(localStorage.getItem('chartOfAccounts')) || [];
    }

    // حفظ الحسابات
    saveAccounts(accounts) {
        localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
        localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
        this.notifyPageUpdate('monjizAccounts');
        this.notifyPageUpdate('chartOfAccounts');
    }

    // إضافة حساب جديد
    addAccount(account) {
        const accounts = this.getAccounts();

        // التحقق من عدم تكرار رقم الحساب
        if (accounts.some(a => a.code === account.code)) {
            console.error('رقم الحساب موجود بالفعل:', account.code);
            return null;
        }

        // إنشاء ID جديد
        const newId = Math.max(...accounts.map(a => a.id), 0) + 1;

        // إنشاء الحساب الجديد
        const newAccount = {
            ...account,
            id: newId,
            createdAt: account.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isActive: account.isActive !== false
        };

        // إضافة الحساب
        accounts.push(newAccount);

        // حفظ الحسابات
        this.saveAccounts(accounts);

        console.log('✅ تم إضافة حساب جديد:', newAccount.code, '-', newAccount.name);
        return newAccount;
    }

    // تحديث حساب موجود
    updateAccount(accountCode, updates) {
        const accounts = this.getAccounts();
        const accountIndex = accounts.findIndex(a => a.code === accountCode);

        if (accountIndex === -1) {
            console.error('الحساب غير موجود:', accountCode);
            return null;
        }

        // تحديث الحساب
        accounts[accountIndex] = {
            ...accounts[accountIndex],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        // حفظ الحسابات
        this.saveAccounts(accounts);

        console.log('✅ تم تحديث الحساب:', accountCode);
        return accounts[accountIndex];
    }

    // حذف حساب
    deleteAccount(accountCode) {
        const accounts = this.getAccounts();
        const filteredAccounts = accounts.filter(a => a.code !== accountCode);

        if (filteredAccounts.length === accounts.length) {
            console.error('الحساب غير موجود:', accountCode);
            return false;
        }

        // حفظ الحسابات بعد الحذف
        this.saveAccounts(filteredAccounts);

        console.log('✅ تم حذف الحساب:', accountCode);
        return true;
    }

    // === إدارة المبيعات ===
    getSales() {
        // البحث في كلا المفتاحين للتوافق
        const sales = JSON.parse(localStorage.getItem('monjizSales')) || [];
        const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];

        // دمج البيانات من المفتاحين
        const allSales = [...sales, ...invoices];

        // إزالة المكررات بناءً على ID
        const uniqueSales = allSales.filter((sale, index, self) =>
            index === self.findIndex(s => s.id === sale.id)
        );

        return uniqueSales;
    }

    saveSales(sales) {
        // حفظ في كلا المفتاحين للتوافق
        localStorage.setItem('monjizSales', JSON.stringify(sales));
        localStorage.setItem('monjizInvoices', JSON.stringify(sales));
        this.notifyPageUpdate('monjizSales');
        this.notifyPageUpdate('monjizInvoices');
    }

    addSale(sale) {
        const sales = this.getSales();

        // استخدام ID الموجود أو إنشاء جديد
        const newSale = {
            ...sale,
            createdAt: sale.createdAt || new Date().toISOString()
        };

        // تحديث المخزون (تقليل الكميات)
        if (sale.products) {
            sale.products.forEach(product => {
                this.updateProductQuantity(
                    product.productId,
                    product.quantity,
                    'subtract',
                    `فاتورة مبيعات ${newSale.id}`,
                    'sale'
                );
            });
        }

        sales.push(newSale);
        this.saveSales(sales);

        console.log('تم حفظ الفاتورة في النظام المركزي:', newSale.id);
        return newSale;
    }

    // === إدارة المشتريات ===
    getPurchases() {
        return JSON.parse(localStorage.getItem('monjizPurchases')) || [];
    }

    savePurchases(purchases) {
        localStorage.setItem('monjizPurchases', JSON.stringify(purchases));
        this.notifyPageUpdate('monjizPurchases');
    }

    addPurchase(purchase) {
        const purchases = this.getPurchases();
        const newId = Math.max(...purchases.map(p => p.id), 0) + 1;
        const newPurchase = {
            ...purchase,
            id: newId,
            createdAt: new Date().toISOString()
        };
        
        // تحديث المخزون (زيادة الكميات)
        if (purchase.items) {
            purchase.items.forEach(item => {
                this.updateProductQuantity(
                    item.productId,
                    item.quantity,
                    'add',
                    `فاتورة مشتريات ${newPurchase.id}`,
                    'purchase'
                );
            });
        }
        
        purchases.push(newPurchase);
        this.savePurchases(purchases);
        return newPurchase;
    }

    // === إدارة العملاء ===
    getCustomers() {
        return JSON.parse(localStorage.getItem('monjizCustomers')) || [];
    }

    saveCustomers(customers) {
        localStorage.setItem('monjizCustomers', JSON.stringify(customers));
        this.notifyPageUpdate('monjizCustomers');
    }

    addCustomer(customer) {
        const customers = this.getCustomers();
        const newId = Math.max(...customers.map(c => c.id), 0) + 1;
        const newCustomer = {
            ...customer,
            id: newId,
            createdAt: new Date().toISOString()
        };
        customers.push(newCustomer);
        this.saveCustomers(customers);

        // إضافة حساب العميل تلقائياً في دليل الحسابات
        this.addCustomerAccount(newCustomer);
        console.log(`تم إضافة حساب العميل "${newCustomer.name}" في دليل الحسابات`);

        // إرسال إشعار للواجهات
        setTimeout(() => {
            document.dispatchEvent(new CustomEvent('customerAdded', { detail: newCustomer }));
            document.dispatchEvent(new CustomEvent('accountsUpdated', { detail: { type: 'customer', data: newCustomer } }));
        }, 100);

        return newCustomer;
    }

    // === إدارة الموردين ===
    getSuppliers() {
        return JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
    }

    saveSuppliers(suppliers) {
        localStorage.setItem('monjizSuppliers', JSON.stringify(suppliers));
        this.notifyPageUpdate('monjizSuppliers');
    }

    addSupplier(supplier) {
        const suppliers = this.getSuppliers();
        const newId = Math.max(...suppliers.map(s => s.id), 0) + 1;
        const newSupplier = {
            ...supplier,
            id: newId,
            createdAt: new Date().toISOString()
        };
        suppliers.push(newSupplier);
        this.saveSuppliers(suppliers);

        // إضافة حساب المورد تلقائياً في دليل الحسابات
        this.addSupplierAccount(newSupplier);
        console.log(`تم إضافة حساب المورد "${newSupplier.name}" في دليل الحسابات`);

        // إرسال إشعار للواجهات
        setTimeout(() => {
            document.dispatchEvent(new CustomEvent('supplierAdded', { detail: newSupplier }));
            document.dispatchEvent(new CustomEvent('accountsUpdated', { detail: { type: 'supplier', data: newSupplier } }));
        }, 100);

        return newSupplier;
    }

    updateSupplier(supplierId, updates) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(s => s.id === supplierId);
        if (index !== -1) {
            suppliers[index] = { ...suppliers[index], ...updates, updatedAt: new Date().toISOString() };
            this.saveSuppliers(suppliers);
            return suppliers[index];
        }
        return null;
    }

    // === إدارة الفئات ===
    getCategories() {
        return JSON.parse(localStorage.getItem('monjizCategories')) || [];
    }

    saveCategories(categories) {
        localStorage.setItem('monjizCategories', JSON.stringify(categories));
        this.notifyPageUpdate('monjizCategories');
    }

    addCategory(category) {
        const categories = this.getCategories();
        const newId = Math.max(...categories.map(c => c.id), 0) + 1;
        const newCategory = {
            ...category,
            id: newId,
            createdAt: new Date().toISOString()
        };
        categories.push(newCategory);
        this.saveCategories(categories);
        return newCategory;
    }

    updateCategory(categoryId, updates) {
        const categories = this.getCategories();
        const index = categories.findIndex(c => c.id === categoryId);
        if (index !== -1) {
            categories[index] = { ...categories[index], ...updates };
            this.saveCategories(categories);
            return categories[index];
        }
        return null;
    }

    deleteCategory(categoryId) {
        const categories = this.getCategories();
        const index = categories.findIndex(c => c.id === categoryId);
        if (index !== -1) {
            const deletedCategory = categories.splice(index, 1)[0];
            this.saveCategories(categories);
            return deletedCategory;
        }
        return null;
    }

    // === إشعار الصفحات بالتحديثات ===
    notifyPageUpdate(dataType) {
        // إرسال حدث مخصص للصفحات الأخرى
        const event = new CustomEvent('monjizDataUpdate', {
            detail: { dataType, timestamp: new Date().toISOString() }
        });
        window.dispatchEvent(event);
    }

    // === تقارير وإحصائيات ===
    getInventoryReport() {
        const products = this.getProducts();
        return {
            totalProducts: products.length,
            lowStockProducts: products.filter(p => p.quantity <= p.minQuantity),
            totalValue: products.reduce((sum, p) => sum + (p.quantity * p.salePrice), 0),
            outOfStockProducts: products.filter(p => p.quantity === 0)
        };
    }

    getSalesReport(startDate, endDate) {
        const sales = this.getSales();
        const filteredSales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate >= new Date(startDate) && saleDate <= new Date(endDate);
        });
        
        return {
            totalSales: filteredSales.length,
            totalRevenue: filteredSales.reduce((sum, sale) => sum + sale.total, 0),
            averageSale: filteredSales.length > 0 ? 
                filteredSales.reduce((sum, sale) => sum + sale.total, 0) / filteredSales.length : 0
        };
    }

    getPurchasesReport(startDate, endDate) {
        const purchases = this.getPurchases();
        const filteredPurchases = purchases.filter(purchase => {
            const purchaseDate = new Date(purchase.createdAt);
            return purchaseDate >= new Date(startDate) && purchaseDate <= new Date(endDate);
        });
        
        return {
            totalPurchases: filteredPurchases.length,
            totalCost: filteredPurchases.reduce((sum, purchase) => sum + purchase.total, 0),
            averagePurchase: filteredPurchases.length > 0 ?
                filteredPurchases.reduce((sum, purchase) => sum + purchase.total, 0) / filteredPurchases.length : 0
        };
    }

    // === إدارة الحسابات ===
    getAccounts() {
        // قراءة من كلا المفتاحين للتوافق
        return JSON.parse(localStorage.getItem('chartOfAccounts')) ||
               JSON.parse(localStorage.getItem('monjizAccounts')) ||
               this.getDefaultAccounts();
    }

    saveAccounts(accounts) {
        // حفظ في كلا المفتاحين للتوافق
        localStorage.setItem('chartOfAccounts', JSON.stringify(accounts));
        localStorage.setItem('monjizAccounts', JSON.stringify(accounts));
        this.notifyPageUpdate('monjizAccounts');
    }

    getDefaultAccounts() {
        return [
            { id: 1, code: '1001', name: 'النقدية', type: 'assets', balance: 0, status: 'active' },
            { id: 2, code: '1002', name: 'البنك', type: 'assets', balance: 0, status: 'active' },
            { id: 3, code: '2001', name: 'الموردون', type: 'liabilities', balance: 0, status: 'active' },
            { id: 4, code: '1003', name: 'العملاء', type: 'assets', balance: 0, status: 'active' },
            { id: 5, code: '4001', name: 'المبيعات', type: 'revenue', balance: 0, status: 'active' },
            { id: 6, code: '5001', name: 'المشتريات', type: 'expenses', balance: 0, status: 'active' }
        ];
    }

    addAccount(account) {
        const accounts = this.getAccounts();
        const newId = Math.max(...accounts.map(a => a.id), 0) + 1;
        const newAccount = {
            ...account,
            id: newId,
            createdAt: new Date().toISOString()
        };
        accounts.push(newAccount);
        this.saveAccounts(accounts);
        return newAccount;
    }

    // إضافة حساب عميل تلقائياً
    addCustomerAccount(customer) {
        const account = {
            code: `11030${String(customer.id).padStart(3, '0')}`,
            name: customer.name,
            type: 'assets',
            subType: 'current_assets',
            category: 'customers',
            parentCode: '11030',
            parentName: 'العملاء',
            balance: 0,
            status: 'active',
            linkedType: 'customer',
            linkedId: customer.id,
            level: 4,
            autoCreated: true
        };

        // التأكد من وجود الحسابات الأساسية
        this.ensureBasicCustomerAccounts();

        return this.addAccount(account);
    }

    // إضافة حساب مورد تلقائياً
    addSupplierAccount(supplier) {
        const account = {
            code: `21030${String(supplier.id).padStart(3, '0')}`,
            name: supplier.name,
            type: 'liabilities',
            subType: 'current_liabilities',
            category: 'suppliers',
            parentCode: '21030',
            parentName: 'الموردون',
            balance: 0,
            status: 'active',
            linkedType: 'supplier',
            linkedId: supplier.id,
            level: 3,
            autoCreated: true
        };

        // التأكد من وجود الحسابات الأساسية
        this.ensureBasicSupplierAccounts();

        return this.addAccount(account);
    }

    // التأكد من وجود الحسابات الأساسية للعملاء
    ensureBasicCustomerAccounts() {
        const accounts = this.getAccounts();
        const basicAccounts = [
            { code: '1', name: 'الأصول', type: 'assets', level: 1, parentCode: null },
            { code: '11', name: 'الأصول المتداولة', type: 'assets', level: 2, parentCode: '1' },
            { code: '1103', name: 'الذمم المدينة', type: 'assets', level: 3, parentCode: '11' },
            { code: '11030', name: 'العملاء', type: 'assets', level: 3, parentCode: '1103' }
        ];

        let needsSave = false;
        basicAccounts.forEach(basicAccount => {
            const exists = accounts.find(acc => acc.code === basicAccount.code);
            if (!exists) {
                accounts.push({
                    ...basicAccount,
                    id: Date.now() + Math.random(),
                    balance: 0,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    autoCreated: true
                });
                needsSave = true;
            }
        });

        if (needsSave) {
            this.saveAccounts(accounts);
        }
    }

    // التأكد من وجود الحسابات الأساسية للموردين
    ensureBasicSupplierAccounts() {
        const accounts = this.getAccounts();
        const basicAccounts = [
            { code: '2', name: 'الخصوم', type: 'liabilities', level: 1, parentCode: null },
            { code: '21', name: 'الخصوم المتداولة', type: 'liabilities', level: 2, parentCode: '2' },
            { code: '2103', name: 'الذمم الدائنة', type: 'liabilities', level: 3, parentCode: '21' },
            { code: '21030', name: 'الموردون', type: 'liabilities', level: 3, parentCode: '2103' }
        ];

        let needsSave = false;
        basicAccounts.forEach(basicAccount => {
            const exists = accounts.find(acc => acc.code === basicAccount.code);
            if (!exists) {
                accounts.push({
                    ...basicAccount,
                    id: Date.now() + Math.random(),
                    balance: 0,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    autoCreated: true
                });
                needsSave = true;
            }
        });

        if (needsSave) {
            this.saveAccounts(accounts);
        }
    }

    // === إدارة الإشعارات ===
    notifyPageUpdate(dataType) {
        // يمكن استخدامها لاحقاً لإشعار الصفحات بالتحديثات
        console.log(`تم تحديث البيانات: ${dataType}`);

        // إرسال حدث تحديث الحسابات
        if (dataType === 'monjizCustomers' || dataType === 'monjizSuppliers') {
            const event = new CustomEvent('accountsUpdated', {
                detail: { dataType, timestamp: new Date().toISOString() }
            });
            document.dispatchEvent(event);
        }
    }
}

// إنشاء مثيل عام من مدير البيانات
window.dataManager = new DataManager();

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
}
