// JavaScript file to open launcher.html
// Save this file with .js extension and run it with Node.js
// Updated to handle Arabic character issue and provide better error handling

const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Get the current directory
const currentDir = __dirname;

// Build the path to launcher.html
const launcherPath = path.join(currentDir, 'launcher.html');

/**
 * Opens the launcher using the most appropriate method for the current platform
 */
function openLauncher() {
    // Check if the file exists
    if (!fs.existsSync(launcherPath)) {
        console.error(`Launcher file not found at: ${launcherPath}`);
        return;
    }
    
    console.log(`Opening launcher at: ${launcherPath}`);
    
    // Try multiple methods to open the file
    tryOpenWithDefaultMethod()
        .catch(() => tryOpenWithAlternativeMethod())
        .catch(error => {
            console.error('All methods failed to open the launcher.');
            console.error(`Error details: ${error.message}`);
            console.error('Please try one of the following alternatives:');
            console.error('1. Use open_launcher.vbs (recommended for Windows)');
            console.error('2. Use open_launcher.ps1 (PowerShell script)');
            console.error('3. Open launcher.html directly from your file explorer');
        });
}

/**
 * Tries to open the launcher using the default method for the current platform
 */
function tryOpenWithDefaultMethod() {
    return new Promise((resolve, reject) => {
        const command = process.platform === 'win32' ? 
            `start "" "${launcherPath}"` : 
            process.platform === 'darwin' ? 
                `open "${launcherPath}"` : 
                `xdg-open "${launcherPath}"`;
        
        exec(command, (error) => {
            if (error) {
                console.error(`Default method failed: ${error.message}`);
                reject(error);
                return;
            }
            console.log('Launcher opened successfully with default method!');
            resolve();
        });
    });
}

/**
 * Tries to open the launcher using an alternative method
 */
function tryOpenWithAlternativeMethod() {
    return new Promise((resolve, reject) => {
        console.log('Trying alternative method...');
        
        let command;
        let args;
        
        if (process.platform === 'win32') {
            // On Windows, try using the start command directly
            command = 'cmd.exe';
            args = ['/c', 'start', '', launcherPath];
        } else if (process.platform === 'darwin') {
            // On macOS, try using the open command directly
            command = 'open';
            args = [launcherPath];
        } else {
            // On Linux, try using xdg-open directly
            command = 'xdg-open';
            args = [launcherPath];
        }
        
        const childProcess = spawn(command, args, { stdio: 'inherit' });
        
        childProcess.on('error', (error) => {
            console.error(`Alternative method failed: ${error.message}`);
            reject(error);
        });
        
        childProcess.on('close', (code) => {
            if (code !== 0) {
                console.error(`Alternative method exited with code ${code}`);
                reject(new Error(`Process exited with code ${code}`));
                return;
            }
            console.log('Launcher opened successfully with alternative method!');
            resolve();
        });
    });
}

// Start the process
openLauncher();