<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق الفواتير - منجز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #e74c3c;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(231,76,60,0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231,76,60,0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 5px rgba(40,167,69,0.3);
        }
        .btn.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(40,167,69,0.3);
        }
        .error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        }
        .info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(23,162,184,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #e74c3c;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .comparison-table th {
            background: #e74c3c;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        .format-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .step-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-list ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 اختبار تنسيق الفواتير</h1>

        <!-- وصف المشكلة -->
        <div class="test-section">
            <h2>❌ المشكلة المكتشفة</h2>
            <div class="format-box">
                <h3>مشكلة التنسيق:</h3>
                <p><strong>الفاتورة الجديدة تظهر بتنسيق مختلف عن الفواتير السابقة</strong></p>
                
                <h4>المشاكل المحددة:</h4>
                <ul>
                    <li>❌ <strong>عدد الأعمدة مختلف:</strong> الفاتورة الجديدة تحتوي على أعمدة أقل</li>
                    <li>❌ <strong>الأيقونات مختلفة:</strong> ألوان وترتيب مختلف</li>
                    <li>❌ <strong>التنسيق العام:</strong> لا يتطابق مع الفواتير الموجودة</li>
                    <li>❌ <strong>عدد المنتجات مفقود:</strong> لا يظهر عدد المنتجات في الفاتورة</li>
                </ul>
            </div>
        </div>

        <!-- الحل المطبق -->
        <div class="test-section">
            <h2>✅ الحل المطبق</h2>
            <div class="format-box">
                <h3>التحسينات المطبقة:</h3>
                <ol>
                    <li><strong>إضافة عمود عدد المنتجات:</strong> <code>${invoice.products ? invoice.products.length : 1}</code></li>
                    <li><strong>تطبيق نفس تنسيق رقم الفاتورة:</strong> <code>&lt;strong&gt;${invoice.id}&lt;/strong&gt;</code></li>
                    <li><strong>تطبيق نفس تنسيق الإجمالي:</strong> <code>&lt;strong class="positive"&gt;${invoice.total.toFixed(2)} ر.س&lt;/strong&gt;</code></li>
                    <li><strong>تطبيق نفس تنسيق طريقة الدفع:</strong> <code>&lt;span class="badge success"&gt;نقداً&lt;/span&gt;</code></li>
                    <li><strong>تطبيق نفس تنسيق الحالة:</strong> <code>&lt;span class="badge primary"&gt;مكتملة&lt;/span&gt;</code></li>
                    <li><strong>ترتيب الأيقونات:</strong> تعديل، عرض، حذف (نفس ترتيب الفواتير الأصلية)</li>
                </ol>
            </div>
        </div>

        <!-- مقارنة التنسيق -->
        <div class="test-section">
            <h2>📊 مقارنة التنسيق</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>العنصر</th>
                        <th>التنسيق الأصلي</th>
                        <th>التنسيق الجديد (بعد الإصلاح)</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>رقم الفاتورة</td>
                        <td>&lt;strong&gt;INV-001&lt;/strong&gt;</td>
                        <td>&lt;strong&gt;${invoice.id}&lt;/strong&gt;</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>التاريخ</td>
                        <td>2024-01-15</td>
                        <td>${invoice.date}</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>العميل</td>
                        <td>أحمد محمد علي</td>
                        <td>${invoice.customerName}</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>عدد المنتجات</td>
                        <td>5</td>
                        <td>${invoice.products.length}</td>
                        <td>✅ مُضاف</td>
                    </tr>
                    <tr>
                        <td>الإجمالي</td>
                        <td>&lt;strong class="positive"&gt;2,450 ر.س&lt;/strong&gt;</td>
                        <td>&lt;strong class="positive"&gt;${invoice.total} ر.س&lt;/strong&gt;</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>طريقة الدفع</td>
                        <td>&lt;span class="badge success"&gt;نقداً&lt;/span&gt;</td>
                        <td>&lt;span class="badge success"&gt;نقداً&lt;/span&gt;</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>الحالة</td>
                        <td>&lt;span class="badge primary"&gt;مكتملة&lt;/span&gt;</td>
                        <td>&lt;span class="badge primary"&gt;مكتملة&lt;/span&gt;</td>
                        <td>✅ متطابق</td>
                    </tr>
                    <tr>
                        <td>الإجراءات</td>
                        <td>تعديل، عرض، حذف</td>
                        <td>تعديل، عرض، حذف</td>
                        <td>✅ متطابق</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- إعداد الاختبار -->
        <div class="test-section">
            <h2>🛠️ إعداد الاختبار</h2>
            <button class="btn success" onclick="setupTestData()">إعداد بيانات الاختبار</button>
            <button class="btn primary" onclick="checkCurrentData()">فحص البيانات الحالية</button>
            <div id="setup-result"></div>
        </div>

        <!-- اختبار التنسيق -->
        <div class="test-section">
            <h2>🧪 اختبار التنسيق</h2>
            <button class="btn" onclick="testInvoiceFormatting()">اختبار تنسيق الفاتورة</button>
            <button class="btn primary" onclick="openSalesForTest()">فتح المبيعات للاختبار</button>
            <div id="test-result"></div>
            
            <div class="step-list">
                <h3>خطوات الاختبار:</h3>
                <ol>
                    <li><strong>اضغط "إعداد بيانات الاختبار"</strong> لضمان وجود منتجات وعملاء</li>
                    <li><strong>اضغط "فتح المبيعات للاختبار"</strong></li>
                    <li><strong>في صفحة المبيعات:</strong>
                        <ul>
                            <li>اضغط "فاتورة جديدة"</li>
                            <li>اختر عميل ومنتجات</li>
                            <li>اضغط "حفظ الفاتورة"</li>
                            <li><strong>تحقق من الجدول:</strong> الفاتورة الجديدة يجب أن تتطابق مع التنسيق الأصلي</li>
                        </ul>
                    </li>
                    <li><strong>تحقق من:</strong>
                        <ul>
                            <li>✅ رقم الفاتورة بخط عريض</li>
                            <li>✅ عدد المنتجات يظهر صحيحاً</li>
                            <li>✅ الإجمالي بخط عريض ولون أخضر</li>
                            <li>✅ طريقة الدفع مع badge أخضر</li>
                            <li>✅ الحالة مع badge أزرق</li>
                            <li>✅ الأيقونات بنفس الترتيب والألوان</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // إعداد بيانات الاختبار
        function setupTestData() {
            const testProducts = [
                { id: 'format-test-1', name: 'جهاز عرض محمول', category: 'electronics', code: 'PROJ-001', price: 2500, cost: 2000, quantity: 10, minStock: 2 },
                { id: 'format-test-2', name: 'لابتوب Dell XPS 15', category: 'computers', code: 'DELL-XPS15', price: 4500, cost: 3800, quantity: 8, minStock: 2 },
                { id: 'format-test-3', name: 'كيبورد ميكانيكي', category: 'accessories', code: 'MECH-KB', price: 350, cost: 250, quantity: 15, minStock: 5 }
            ];

            localStorage.setItem('monjizProducts', JSON.stringify(testProducts));

            const testCustomers = [
                { id: 'format-cust-1', name: 'مطعم توباز', type: 'company', phone: '+966501234567', email: '<EMAIL>' },
                { id: 'format-cust-2', name: 'أحمد محمد العلي', type: 'individual', phone: '+966503456789', email: '<EMAIL>' }
            ];

            localStorage.setItem('monjizCustomers', JSON.stringify(testCustomers));
            localStorage.setItem('monjizDataUpdate', Date.now().toString());

            showResult('setup-result', '✅ تم إعداد بيانات الاختبار بنجاح!<br>📦 3 منتجات للاختبار<br>👥 2 عميل للاختبار<br>🔄 تم إرسال إشعار التحديث', 'success');
        }

        // فحص البيانات الحالية
        function checkCurrentData() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            
            let result = '<div class="info">📊 البيانات الحالية:</div>';
            result += `<p><strong>المنتجات:</strong> ${products.length} عنصر</p>`;
            result += `<p><strong>العملاء:</strong> ${customers.length} عنصر</p>`;
            result += `<p><strong>الفواتير:</strong> ${invoices.length} عنصر</p>`;
            
            if (products.length >= 2 && customers.length >= 1) {
                result += '<div class="success">✅ البيانات كافية لاختبار التنسيق</div>';
            } else {
                result += '<div class="error">❌ البيانات غير كافية - اضغط "إعداد بيانات الاختبار"</div>';
            }
            
            document.getElementById('setup-result').innerHTML = result;
        }

        // اختبار تنسيق الفاتورة
        function testInvoiceFormatting() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            if (products.length < 2 || customers.length < 1) {
                showResult('test-result', '❌ تحتاج منتجين وعميل واحد على الأقل - اضغط "إعداد بيانات الاختبار"', 'error');
                return;
            }

            // محاكاة إنشاء فاتورة
            const mockInvoice = {
                id: 'INV-TEST-' + Date.now(),
                date: new Date().toLocaleDateString('ar-SA'),
                customerName: customers[0].name,
                products: [
                    { id: products[0].id, name: products[0].name, quantity: 2, price: products[0].price },
                    { id: products[1].id, name: products[1].name, quantity: 1, price: products[1].price }
                ],
                total: (products[0].price * 2) + products[1].price
            };

            let result = '<div class="info">🧪 محاكاة تنسيق الفاتورة:</div>';
            result += '<h4>بيانات الفاتورة التجريبية:</h4>';
            result += `<p><strong>رقم الفاتورة:</strong> ${mockInvoice.id}</p>`;
            result += `<p><strong>التاريخ:</strong> ${mockInvoice.date}</p>`;
            result += `<p><strong>العميل:</strong> ${mockInvoice.customerName}</p>`;
            result += `<p><strong>عدد المنتجات:</strong> ${mockInvoice.products.length}</p>`;
            result += `<p><strong>الإجمالي:</strong> ${mockInvoice.total.toFixed(2)} ر.س</p>`;
            
            result += '<h4>التنسيق المتوقع:</h4>';
            result += '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">';
            result += `&lt;td&gt;&lt;strong&gt;${mockInvoice.id}&lt;/strong&gt;&lt;/td&gt;<br>`;
            result += `&lt;td&gt;${mockInvoice.date}&lt;/td&gt;<br>`;
            result += `&lt;td&gt;${mockInvoice.customerName}&lt;/td&gt;<br>`;
            result += `&lt;td&gt;${mockInvoice.products.length}&lt;/td&gt;<br>`;
            result += `&lt;td&gt;&lt;strong class="positive"&gt;${mockInvoice.total.toFixed(2)} ر.س&lt;/strong&gt;&lt;/td&gt;<br>`;
            result += `&lt;td&gt;&lt;span class="badge success"&gt;نقداً&lt;/span&gt;&lt;/td&gt;<br>`;
            result += `&lt;td&gt;&lt;span class="badge primary"&gt;مكتملة&lt;/span&gt;&lt;/td&gt;<br>`;
            result += `&lt;td&gt;[أيقونات الإجراءات]&lt;/td&gt;`;
            result += '</div>';
            
            result += '<div class="success">✅ هذا هو التنسيق المتوقع للفاتورة الجديدة</div>';
            
            document.getElementById('test-result').innerHTML = result;
        }

        // فتح المبيعات للاختبار
        function openSalesForTest() {
            window.open('sales.html', '_blank');
            showResult('test-result', '🚀 تم فتح صفحة المبيعات<br>💡 أنشئ فاتورة جديدة وتحقق من التنسيق<br>🎯 يجب أن تتطابق مع الفواتير الموجودة', 'info');
        }

        // دالة مساعدة لعرض النتائج
        function showResult(elementId, message, type) {
            document.getElementById(elementId).innerHTML = `<div class="${type}">${message}</div>`;
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            checkCurrentData();
        });
    </script>
</body>
</html>
