<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوظائف</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>اختبار وظائف منجز</h1>
    
    <div class="test-container">
        <h2>اختبار المنتجات</h2>
        <button class="btn" onclick="testAddProduct()">إضافة منتج تجريبي</button>
        <button class="btn" onclick="testLoadProducts()">تحميل المنتجات</button>
        <button class="btn" onclick="clearProducts()">مسح المنتجات</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-container">
        <h2>اختبار العملاء</h2>
        <button class="btn" onclick="testAddCustomer()">إضافة عميل تجريبي</button>
        <button class="btn" onclick="testLoadCustomers()">تحميل العملاء</button>
        <button class="btn" onclick="clearCustomers()">مسح العملاء</button>
        <div id="customers-result" class="result"></div>
    </div>

    <div class="test-container">
        <h2>اختبار localStorage</h2>
        <button class="btn" onclick="checkLocalStorage()">فحص localStorage</button>
        <button class="btn" onclick="clearAllData()">مسح جميع البيانات</button>
        <div id="storage-result" class="result"></div>
    </div>

    <script>
        // اختبار إضافة منتج
        function testAddProduct() {
            const testProduct = {
                id: Date.now(),
                name: 'جهاز عرض محمول - اختبار',
                category: 'electronics',
                code: 'PROJ-TEST-001',
                price: 2500,
                cost: 2000,
                quantity: 10,
                minStock: 2,
                description: 'جهاز عرض محمول للاختبار'
            };

            // تحميل المنتجات الحالية
            let productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            
            // إضافة المنتج الجديد
            productsData.push(testProduct);
            
            // حفظ في localStorage
            localStorage.setItem('monjizProducts', JSON.stringify(productsData));
            
            document.getElementById('products-result').textContent = 
                `تم إضافة المنتج بنجاح!\nاسم المنتج: ${testProduct.name}\nالرمز: ${testProduct.code}\nالسعر: ${testProduct.price} ر.س\nإجمالي المنتجات: ${productsData.length}`;
        }

        // اختبار تحميل المنتجات
        function testLoadProducts() {
            const productsData = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            let result = `عدد المنتجات المحفوظة: ${productsData.length}\n\n`;
            
            if (productsData.length > 0) {
                result += 'قائمة المنتجات:\n';
                productsData.forEach((product, index) => {
                    result += `${index + 1}. ${product.name} (${product.code}) - ${product.price} ر.س\n`;
                });
            } else {
                result += 'لا توجد منتجات محفوظة';
            }
            
            document.getElementById('products-result').textContent = result;
        }

        // اختبار إضافة عميل
        function testAddCustomer() {
            const testCustomer = {
                id: Date.now(),
                name: 'مطعم توباز - اختبار',
                type: 'company',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية',
                createdAt: new Date().toLocaleDateString('ar-SA')
            };

            // تحميل العملاء الحاليين
            let customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            
            // إضافة العميل الجديد
            customersData.push(testCustomer);
            
            // حفظ في localStorage
            localStorage.setItem('monjizCustomers', JSON.stringify(customersData));
            
            document.getElementById('customers-result').textContent = 
                `تم إضافة العميل بنجاح!\nاسم العميل: ${testCustomer.name}\nالنوع: ${testCustomer.type}\nالهاتف: ${testCustomer.phone}\nإجمالي العملاء: ${customersData.length}`;
        }

        // اختبار تحميل العملاء
        function testLoadCustomers() {
            const customersData = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            let result = `عدد العملاء المحفوظين: ${customersData.length}\n\n`;
            
            if (customersData.length > 0) {
                result += 'قائمة العملاء:\n';
                customersData.forEach((customer, index) => {
                    result += `${index + 1}. ${customer.name} (${customer.type}) - ${customer.phone}\n`;
                });
            } else {
                result += 'لا يوجد عملاء محفوظين';
            }
            
            document.getElementById('customers-result').textContent = result;
        }

        // فحص localStorage
        function checkLocalStorage() {
            const products = JSON.parse(localStorage.getItem('monjizProducts')) || [];
            const customers = JSON.parse(localStorage.getItem('monjizCustomers')) || [];
            const invoices = JSON.parse(localStorage.getItem('monjizInvoices')) || [];
            const suppliers = JSON.parse(localStorage.getItem('monjizSuppliers')) || [];
            
            let result = 'حالة localStorage:\n\n';
            result += `المنتجات: ${products.length} عنصر\n`;
            result += `العملاء: ${customers.length} عنصر\n`;
            result += `الفواتير: ${invoices.length} عنصر\n`;
            result += `الموردين: ${suppliers.length} عنصر\n\n`;
            
            result += 'تفاصيل البيانات:\n';
            if (products.length > 0) {
                result += `آخر منتج: ${products[products.length - 1].name}\n`;
            }
            if (customers.length > 0) {
                result += `آخر عميل: ${customers[customers.length - 1].name}\n`;
            }
            
            document.getElementById('storage-result').textContent = result;
        }

        // مسح المنتجات
        function clearProducts() {
            localStorage.removeItem('monjizProducts');
            document.getElementById('products-result').textContent = 'تم مسح جميع المنتجات';
        }

        // مسح العملاء
        function clearCustomers() {
            localStorage.removeItem('monjizCustomers');
            document.getElementById('customers-result').textContent = 'تم مسح جميع العملاء';
        }

        // مسح جميع البيانات
        function clearAllData() {
            localStorage.removeItem('monjizProducts');
            localStorage.removeItem('monjizCustomers');
            localStorage.removeItem('monjizInvoices');
            localStorage.removeItem('monjizSuppliers');
            localStorage.removeItem('monjizDataUpdate');
            
            document.getElementById('storage-result').textContent = 'تم مسح جميع البيانات من localStorage';
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            checkLocalStorage();
            testLoadProducts();
            testLoadCustomers();
        });
    </script>
</body>
</html>
