@echo off
REM Batch file to open accounting.html directly
REM Created to fix accounting management functions issue

setlocal

REM Get the current directory (where this batch file is located)
set "CURRENT_DIR=%~dp0"

REM Remove trailing backslash
set "CURRENT_DIR=%CURRENT_DIR:~0,-1%"

REM Build the path to accounting.html
set "ACCOUNTING_PATH=%CURRENT_DIR%\accounting.html"

REM Check if the file exists
if exist "%ACCOUNTING_PATH%" (
    echo فتح صفحة الحسابات من: %ACCOUNTING_PATH%
    
    REM Open the accounting.html file with the default browser
    start "" "%ACCOUNTING_PATH%"
    
    if %ERRORLEVEL% EQU 0 (
        echo تم فتح صفحة الحسابات بنجاح!
    ) else (
        echo خطأ في فتح صفحة الحسابات
        echo الرجاء محاولة فتح accounting.html مباشرة من مستكشف الملفات
    )
) else (
    echo لم يتم العثور على ملف صفحة الحسابات في: %ACCOUNTING_PATH%
)

endlocal