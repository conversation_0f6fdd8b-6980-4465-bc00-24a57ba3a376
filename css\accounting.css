/* تنسيقات صفحة الحسابات والصفحات المتوافقة */

/* رأس الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

/* تنسيقات البحث والتصفية المحسنة */
.search-filters-modern {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box-modern {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box-modern i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 2;
}

.filters-modern {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

/* تنسيقات الجدول المحسن */
.table-container-modern {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.modern-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-table thead th {
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    border: none;
    position: relative;
}

.modern-table thead th i {
    margin-left: 8px;
    opacity: 0.8;
}

.modern-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: #f8f9ff;
}

.modern-table tbody td {
    padding: 12px;
    text-align: right;
    vertical-align: middle;
}

/* تذييل الجدول المحسن */
.table-footer-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.results-info-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.results-info-modern i {
    color: #3498db;
}

.pagination-modern {
    display: flex;
    gap: 5px;
}

.pagination-btn {
    background: white;
    border: 1px solid #ddd;
    color: #666;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #3498db;
    color: #3498db;
}

.pagination-btn.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

.pagination-btn i {
    margin-right: 5px;
}

/* رسالة عدم وجود بيانات */
.no-data-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-data-message i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
    display: block;
}

.no-data-message p {
    font-size: 16px;
    margin: 0;
}

.page-header h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-header h2 i {
    color: #3498db;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.add-transaction-btn {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.add-transaction-btn:hover {
    background-color: #219653;
}

.export-btn {
    background-color: #9b59b6;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.export-btn:hover {
    background-color: #8e44ad;
}

/* لوحة الإحصائيات المالية */
.financial-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
}

.income .stat-icon {
    background-color: #e8f5e9;
    color: #27ae60;
}

.expenses .stat-icon {
    background-color: #ffebee;
    color: #e74c3c;
}

.profit .stat-icon {
    background-color: #e3f2fd;
    color: #3498db;
}

.balance .stat-icon {
    background-color: #fff8e1;
    color: #f39c12;
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    margin-bottom: 5px;
}

.stat-period {
    font-size: 0.8rem;
    color: #95a5a6;
    margin: 0;
}

/* الرسم البياني للإيرادات والمصروفات */
.chart-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.chart-header h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0;
}

.chart-period select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.chart-body {
    height: 300px;
    position: relative;
}

/* أدوات البحث والتصفية */
.search-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-box input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px 0 0 5px;
    font-size: 1rem;
}

.search-box .search-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-options select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
    background-color: #f9f9f9;
}

.filter-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
}

.filter-btn:hover {
    background-color: #e0e0e0;
}

/* تاريخ مخصص */
.custom-date-filter {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.date-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-input label {
    font-weight: 600;
    color: #2c3e50;
}

.date-input input {
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.apply-date-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.apply-date-btn:hover {
    background-color: #2980b9;
}

/* جدول المعاملات المالية */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.transactions-table th,
.transactions-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.transactions-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    color: #2c3e50;
}

.transactions-table tbody tr:hover {
    background-color: #f5f5f5;
}

.transaction-type {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
}

.type-income {
    background-color: #e8f5e9;
    color: #27ae60;
}

.type-expense {
    background-color: #ffebee;
    color: #e74c3c;
}

.transaction-amount {
    font-weight: 700;
}

.amount-income {
    color: #27ae60;
}

.amount-expense {
    color: #e74c3c;
}

.transaction-actions {
    display: flex;
    gap: 5px;
}



.view-btn {
    background-color: #3498db;
    color: white;
}

.view-btn:hover {
    background-color: #2980b9;
}

.edit-btn {
    background-color: #f39c12;
    color: white;
}

.edit-btn:hover {
    background-color: #d35400;
}

.delete-btn {
    background-color: #e74c3c;
    color: white;
}

.delete-btn:hover {
    background-color: #c0392b;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
    margin-bottom: 30px;
}

.pagination-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background-color: white;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination-btn:hover:not(.active):not([disabled]) {
    background-color: #f0f0f0;
}

.pagination-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

/* الأقسام المالية */
.financial-sections {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0;
}

.btn-sm {
    padding: 8px 12px;
    border: none;
    border-radius: 5px;
    background-color: #3498db;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
}

.btn-sm:hover {
    background-color: #2980b9;
}

.categories-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.category-column h4 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px dashed #eee;
}

.category-item:last-child {
    border-bottom: none;
}

.category-name {
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.9rem;
}

.income-category .category-icon {
    background-color: #e8f5e9;
    color: #27ae60;
}

.expense-category .category-icon {
    background-color: #ffebee;
    color: #e74c3c;
}

.category-actions {
    display: flex;
    gap: 5px;
}

.category-actions button {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 0.8rem;
}

.edit-category-btn {
    background-color: #f39c12;
    color: white;
}

.edit-category-btn:hover {
    background-color: #d35400;
}

.delete-category-btn {
    background-color: #e74c3c;
    color: white;
}

.delete-category-btn:hover {
    background-color: #c0392b;
}

/* تصميم متجاوب */
@media (max-width: 1200px) {
    .financial-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .search-filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .filter-options {
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .filter-options select {
        flex: 1;
        min-width: 120px;
    }
    
    .financial-stats {
        grid-template-columns: 1fr;
    }
    
    .date-range {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-input {
        width: 100%;
    }
    
    .categories-container {
        grid-template-columns: 1fr;
    }
}

/* أنماط دليل الحسابات */
.chart-of-accounts {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    padding: 25px;
}

.chart-of-accounts .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.chart-of-accounts .section-header h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-of-accounts .section-header h3 i {
    color: #3498db;
}

.chart-of-accounts .header-actions {
    display: flex;
    gap: 10px;
}

/* أدوات البحث والتصفية لدليل الحسابات */
.accounts-search-filter {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    flex-wrap: wrap;
}

.accounts-search-filter .search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.accounts-search-filter .search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.accounts-search-filter .search-box input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.accounts-search-filter .filter-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.accounts-search-filter .filter-options select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    min-width: 150px;
}

/* شجرة دليل الحسابات */
.accounts-tree-container {
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.accounts-tree {
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background-color: #fdfdfd;
}

.account-node {
    margin-bottom: 5px;
}

.account-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.account-item:hover {
    background-color: #e3f2fd;
}

.account-item.selected {
    background-color: #2196f3;
    color: white;
}

.account-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    color: #6c757d;
}

.account-toggle:hover {
    color: #2196f3;
}

.account-toggle.expanded {
    transform: rotate(90deg);
}

.account-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.account-code {
    font-weight: 600;
    color: #495057;
    min-width: 60px;
}

.account-name {
    flex: 1;
    color: #212529;
}

.account-balance {
    font-weight: 600;
    color: #28a745;
    min-width: 100px;
    text-align: left;
}

.account-balance.negative {
    color: #dc3545;
}

.account-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.account-status.active {
    background-color: #d4edda;
    color: #155724;
}

.account-status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.account-children {
    margin-right: 25px;
    border-right: 2px solid #e9ecef;
    padding-right: 10px;
    margin-top: 5px;
}

.account-children.hidden {
    display: none;
}

/* جدول تفاصيل الحسابات */
.accounts-table-container {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.accounts-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.accounts-table th,
.accounts-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.accounts-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.accounts-table tbody tr:hover {
    background-color: #f8f9fa;
}

.accounts-table .account-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.accounts-table .account-name {
    font-weight: 500;
}

.accounts-table .account-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.account-type.assets {
    background-color: #d1ecf1;
    color: #0c5460;
}

.account-type.liabilities {
    background-color: #f8d7da;
    color: #721c24;
}

.account-type.equity {
    background-color: #d4edda;
    color: #155724;
}

.account-type.revenue {
    background-color: #d1ecf1;
    color: #0c5460;
}

.account-type.expenses {
    background-color: #fff3cd;
    color: #856404;
}

/* أنماط النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

#add-account-modal {
    display: none !important;
}

#add-account-modal.active {
    display: flex !important;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h3 i {
    color: #3498db;
}

.close-modal {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #6c757d;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-modal:hover {
    background-color: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 25px;
}

/* أنماط النماذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* التجاوب مع الشاشات الصغيرة لدليل الحسابات */
@media (max-width: 768px) {
    .chart-of-accounts {
        padding: 15px;
    }

    .chart-of-accounts .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .accounts-search-filter {
        flex-direction: column;
        align-items: stretch;
    }

    .accounts-search-filter .search-box {
        min-width: auto;
    }

    .accounts-search-filter .filter-options {
        justify-content: center;
    }

    .accounts-table-container {
        overflow-x: auto;
    }

    .accounts-table {
        min-width: 600px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body {
        padding: 15px;
    }
}

/* أنماط التبويبات */
.accounting-nav {
    margin-bottom: 30px;
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs {
    display: flex;
    gap: 0;
    overflow-x: auto;
    padding-bottom: 0;
}

.nav-tab {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: fit-content;
}

.nav-tab:hover {
    color: #3498db;
    background-color: #f8f9fa;
}

.nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background-color: #f8f9fa;
}

.nav-tab i {
    font-size: 16px;
}

/* محتوى التبويبات */
.tab-content {
    min-height: 500px;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* أزرار التصدير */
.export-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.export-buttons .btn {
    padding: 8px 12px;
    font-size: 13px;
    min-width: auto;
}

.export-buttons .btn i {
    margin-left: 5px;
}

.export-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسين header-actions */
.header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .export-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .export-buttons .btn {
        width: 100%;
        justify-content: center;
    }

    .header-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

/* أنماط قيد اليومية */
.journal-entries-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.journal-entries-table-container {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 20px;
}

.journal-entries-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.journal-entries-table th,
.journal-entries-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.journal-entries-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.journal-entries-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* أنماط سند القبض */
.receipt-voucher-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.receipt-vouchers-table-container {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 20px;
}

.receipt-vouchers-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.receipt-vouchers-table th,
.receipt-vouchers-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.receipt-vouchers-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.receipt-vouchers-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* أنماط سند الصرف */
.payment-voucher-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.payment-vouchers-table-container {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 20px;
}

.payment-vouchers-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.payment-vouchers-table th,
.payment-vouchers-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.payment-vouchers-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.payment-vouchers-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* التجاوب مع الشاشات الصغيرة للتبويبات */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
        gap: 5px;
    }

    .nav-tab {
        padding: 12px 15px;
        font-size: 13px;
        flex: 1;
        min-width: auto;
        text-align: center;
        justify-content: center;
    }

    .nav-tab i {
        font-size: 14px;
    }
}

/* أنماط أزرار الإجراءات */
.action-btn {
    background: none;
    border: none;
    padding: 8px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn.view-btn {
    color: #17a2b8;
}

.action-btn.view-btn:hover {
    background-color: rgba(23, 162, 184, 0.1);
}

.action-btn.edit-btn {
    color: #ffc107;
}

.action-btn.edit-btn:hover {
    background-color: rgba(255, 193, 7, 0.1);
}

.action-btn.delete-btn {
    color: #dc3545;
}

.action-btn.delete-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* أنماط إضافية للجداول */
.journal-entries-table tbody tr:nth-child(even),
.receipt-vouchers-table tbody tr:nth-child(even),
.payment-vouchers-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.journal-entries-table tbody tr:hover,
.receipt-vouchers-table tbody tr:hover,
.payment-vouchers-table tbody tr:hover {
    background-color: #e3f2fd !important;
}

/* أنماط الحالة */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.posted {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.draft {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.approved {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-badge.pending {
    background-color: #f8d7da;
    color: #721c24;
}

/* أنماط النوافذ المنبثقة الجديدة */
#add-journal-entry-modal,
#add-receipt-voucher-modal,
#add-payment-voucher-modal {
    display: none;
}

#add-journal-entry-modal.show,
#add-receipt-voucher-modal.show,
#add-payment-voucher-modal.show {
    display: flex;
}

/* أنماط النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.form-actions .btn {
    min-width: 120px;
}

/* تحسينات للنوافذ المنبثقة */
.modal-content {
    max-width: 600px;
    width: 90%;
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    color: #2c3e50;
}

.modal-header h3 i {
    color: #3498db;
}

/* تجاوب النوافذ المنبثقة */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* أنماط تفاصيل القيود */
.entry-details {
    padding: 20px 0;
}

.entry-details h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    color: #495057;
    font-weight: 600;
    min-width: 120px;
}

.detail-row .status-badge {
    margin-right: 0;
}

/* أنماط الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 20px;
    display: block;
}

.empty-state p {
    font-size: 18px;
    margin-bottom: 20px;
    color: #6c757d;
}

.empty-state .btn {
    margin-top: 10px;
}

/* تحسينات أزرار الإجراءات */
.action-btn {
    padding: 8px 10px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.action-btn.view-btn {
    background-color: #17a2b8;
    color: white;
}

.action-btn.view-btn:hover {
    background-color: #138496;
    transform: translateY(-1px);
}

.action-btn.edit-btn {
    background-color: #ffc107;
    color: #212529;
}

.action-btn.edit-btn:hover {
    background-color: #e0a800;
    transform: translateY(-1px);
}

.action-btn.delete-btn {
    background-color: #dc3545;
    color: white;
}

.action-btn.delete-btn:hover {
    background-color: #c82333;
    transform: translateY(-1px);
}

/* تحسينات الجداول */
.data-table td.actions {
    white-space: nowrap;
    width: 120px;
}

.data-table td.amount {
    text-align: left;
    font-weight: 600;
    color: #28a745;
}

/* تجاوب تفاصيل القيود */
@media (max-width: 768px) {
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .detail-row strong {
        min-width: auto;
    }

    .action-btn {
        padding: 6px 8px;
        font-size: 12px;
    }
}