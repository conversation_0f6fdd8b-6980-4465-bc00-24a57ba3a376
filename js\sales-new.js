// ملف JavaScript لإدارة المبيعات - نسخة جديدة

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل نظام إدارة المبيعات');
    
    // تهيئة النظام
    initSalesSystem();
    
    // إضافة مستمعي الأحداث
    setupEventListeners();
});

// دالة تهيئة نظام المبيعات
function initSalesSystem() {
    console.log('تهيئة نظام المبيعات...');
    
    // تحديث الإحصائيات
    updateSalesStats();
    
    // تهيئة البحث والتصفية
    initSearchAndFilters();
}

// دالة إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر إضافة فاتورة جديدة
    const addInvoiceBtn = document.querySelector('.add-invoice-btn');
    if (addInvoiceBtn) {
        addInvoiceBtn.addEventListener('click', showAddInvoiceModal);
    }
    
    // زر مسح التصفية
    const clearFiltersBtn = document.getElementById('clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearAllFilters);
    }
    
    // حقل البحث
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // قوائم التصفية
    const statusFilter = document.getElementById('status-filter');
    const paymentFilter = document.getElementById('payment-filter');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    
    if (paymentFilter) {
        paymentFilter.addEventListener('change', applyFilters);
    }
}

// دالة لإدارة القوائم المنسدلة
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        dropdown.classList.toggle('show');
    }

    // إغلاق القوائم الأخرى
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== dropdownId) {
            menu.classList.remove('show');
        }
    });
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.matches('.dropdown-toggle')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});

// دالة لعرض نافذة إضافة فاتورة جديدة
function showAddInvoiceModal() {
    console.log('فتح نافذة إضافة فاتورة جديدة...');

    // إضافة أنماط CSS للنافذة المنبثقة
    addModalStyles();

    // إنشاء النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content-modern">
            <div class="modal-header-modern">
                <h3><i class="fas fa-plus-circle"></i> إضافة فاتورة جديدة</h3>
                <button class="close-btn" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body-modern">
                <form id="add-invoice-form" class="form-modern">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-customer">العميل *</label>
                            <select id="invoice-customer" class="select-modern" required>
                                <option value="">اختر العميل</option>
                                <option value="1">أحمد محمد علي</option>
                                <option value="2">شركة النور للتجارة</option>
                                <option value="3">سارة عبدالله</option>
                                <option value="4">محمد الأحمد</option>
                                <option value="5">مؤسسة الخير</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="invoice-date">التاريخ *</label>
                            <input type="date" id="invoice-date" class="input-modern" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-payment">طريقة الدفع *</label>
                            <select id="invoice-payment" class="select-modern" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="transfer">تحويل بنكي</option>
                                <option value="credit">آجل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="invoice-status">الحالة *</label>
                            <select id="invoice-status" class="select-modern" required>
                                <option value="completed">مكتملة</option>
                                <option value="pending">معلقة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="products-section">
                        <h4><i class="fas fa-boxes"></i> المنتجات</h4>
                        <div class="product-item">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المنتج</label>
                                    <select class="select-modern product-select" required>
                                        <option value="">اختر المنتج</option>
                                        <option value="1" data-price="2500">لابتوب HP - 2,500 ر.س</option>
                                        <option value="2" data-price="800">طابعة Canon - 800 ر.س</option>
                                        <option value="3" data-price="150">سماعات رأس - 150 ر.س</option>
                                        <option value="4" data-price="1200">شاشة Samsung - 1,200 ر.س</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الكمية</label>
                                    <input type="number" class="input-modern quantity-input" min="1" value="1" required>
                                </div>
                                <div class="form-group">
                                    <label>السعر</label>
                                    <input type="number" class="input-modern price-input" readonly>
                                </div>
                                <div class="form-group">
                                    <label>المجموع</label>
                                    <input type="number" class="input-modern total-input" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="totals-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label>المجموع الفرعي</label>
                                <input type="number" id="subtotal" class="input-modern" readonly>
                            </div>
                            <div class="form-group">
                                <label>الضريبة (15%)</label>
                                <input type="number" id="tax" class="input-modern" readonly>
                            </div>
                            <div class="form-group">
                                <label><strong>المجموع الكلي</strong></label>
                                <input type="number" id="total" class="input-modern total-final" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="invoice-notes">ملاحظات</label>
                        <textarea id="invoice-notes" class="textarea-modern" rows="3" placeholder="أضف أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-modern btn-primary">
                            <i class="fas fa-save"></i> حفظ الفاتورة
                        </button>
                        <button type="button" class="btn-modern btn-secondary" onclick="closeModal(this)">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // إضافة النافذة للصفحة
    document.body.appendChild(modal);
    
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice-date').value = today;
    
    // إضافة مستمعي الأحداث
    setupInvoiceModalEvents(modal);
    
    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('invoice-customer').focus();
    }, 100);
}

// دالة إعداد أحداث النافذة المنبثقة
function setupInvoiceModalEvents(modal) {
    const form = modal.querySelector('#add-invoice-form');
    const productSelect = modal.querySelector('.product-select');
    const quantityInput = modal.querySelector('.quantity-input');
    const priceInput = modal.querySelector('.price-input');
    const totalInput = modal.querySelector('.total-input');
    
    // تحديث السعر عند اختيار المنتج
    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const price = selectedOption.getAttribute('data-price') || 0;
        priceInput.value = price;
        updateProductTotal();
    });
    
    // تحديث المجموع عند تغيير الكمية
    quantityInput.addEventListener('input', updateProductTotal);
    
    function updateProductTotal() {
        const quantity = parseInt(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;
        totalInput.value = total;
        updateInvoiceTotals();
    }
    
    function updateInvoiceTotals() {
        const subtotal = parseFloat(totalInput.value) || 0;
        const tax = subtotal * 0.15;
        const total = subtotal + tax;
        
        document.getElementById('subtotal').value = subtotal.toFixed(2);
        document.getElementById('tax').value = tax.toFixed(2);
        document.getElementById('total').value = total.toFixed(2);
    }
    
    // معالجة إرسال النموذج
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // جمع بيانات الفاتورة
        const invoiceData = {
            customer: document.getElementById('invoice-customer').value,
            date: document.getElementById('invoice-date').value,
            payment: document.getElementById('invoice-payment').value,
            status: document.getElementById('invoice-status').value,
            product: productSelect.value,
            quantity: quantityInput.value,
            price: priceInput.value,
            subtotal: document.getElementById('subtotal').value,
            tax: document.getElementById('tax').value,
            total: document.getElementById('total').value,
            notes: document.getElementById('invoice-notes').value
        };
        
        // حفظ الفاتورة
        saveInvoice(invoiceData);
        
        // إغلاق النافذة
        document.body.removeChild(modal);
        
        // عرض رسالة نجاح
        showAlert('تم إنشاء الفاتورة بنجاح!', 'success');
    });
}

// دالة إغلاق النافذة المنبثقة
function closeModal(element) {
    const modal = element.closest('.modal-overlay');
    if (modal) {
        document.body.removeChild(modal);
    }
}

// دالة حفظ الفاتورة
function saveInvoice(invoiceData) {
    console.log('حفظ الفاتورة:', invoiceData);
    // هنا يمكن إضافة كود حفظ البيانات في قاعدة البيانات
}

// دالة عرض التنبيهات
function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        z-index: 10000;
        animation: slideIn 0.3s ease-in-out;
    `;
    
    if (type === 'success') {
        alert.style.backgroundColor = '#28a745';
    } else if (type === 'error') {
        alert.style.backgroundColor = '#dc3545';
    } else {
        alert.style.backgroundColor = '#17a2b8';
    }
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        document.body.removeChild(alert);
    }, 3000);
}

// دوال أخرى مطلوبة
function updateSalesStats() {
    console.log('تحديث إحصائيات المبيعات...');
}

function initSearchAndFilters() {
    console.log('تهيئة البحث والتصفية...');
}

function clearAllFilters() {
    console.log('مسح جميع التصفيات...');
}

function handleSearch() {
    console.log('البحث...');
}

function applyFilters() {
    console.log('تطبيق التصفيات...');
}

function editInvoice(id) {
    console.log('تعديل الفاتورة:', id);
}

function viewInvoice(id) {
    console.log('عرض الفاتورة:', id);
}

function deleteInvoice(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        console.log('حذف الفاتورة:', id);
        showAlert('تم حذف الفاتورة بنجاح!', 'success');
    }
}

function printSales() {
    console.log('طباعة تقرير المبيعات...');
}

function exportToExcel() {
    console.log('تصدير إلى Excel...');
}

function exportToPDF() {
    console.log('تصدير إلى PDF...');
}

// وظائف الإجراءات على الفواتير
function editInvoice(invoiceId) {
    console.log('تعديل الفاتورة:', invoiceId);
    alert(`سيتم فتح نافذة تعديل الفاتورة رقم: ${invoiceId}`);
    // هنا يمكن إضافة كود فتح نافذة التعديل
}

function viewInvoice(invoiceId) {
    console.log('عرض الفاتورة:', invoiceId);
    alert(`سيتم عرض تفاصيل الفاتورة رقم: ${invoiceId}`);
    // هنا يمكن إضافة كود عرض تفاصيل الفاتورة
}

function deleteInvoice(invoiceId) {
    console.log('حذف الفاتورة:', invoiceId);
    if (confirm(`هل أنت متأكد من حذف الفاتورة رقم: ${invoiceId}؟`)) {
        alert(`تم حذف الفاتورة رقم: ${invoiceId}`);
        // هنا يمكن إضافة كود حذف الفاتورة من قاعدة البيانات
        // وإعادة تحميل الجدول
        location.reload();
    }
}

// وظائف التصفية
function filterByPaymentMethod() {
    const select = document.getElementById('payment-filter');
    const selectedValue = select.value;
    const tableRows = document.querySelectorAll('.modern-table tbody tr');

    console.log('تصفية حسب طريقة الدفع:', selectedValue);

    tableRows.forEach(row => {
        if (selectedValue === 'all' || selectedValue === '') {
            row.style.display = '';
        } else {
            const paymentCell = row.cells[5]; // عمود طريقة الدفع
            const paymentText = paymentCell.textContent.trim();

            let shouldShow = false;

            switch(selectedValue) {
                case 'cash':
                    shouldShow = paymentText.includes('نقداً');
                    break;
                case 'bank':
                    shouldShow = paymentText.includes('تحويل بنكي');
                    break;
                case 'credit':
                    shouldShow = paymentText.includes('آجل');
                    break;
            }

            row.style.display = shouldShow ? '' : 'none';
        }
    });

    updateVisibleRowsCount();
}

function filterByStatus() {
    const select = document.getElementById('status-filter');
    const selectedValue = select.value;
    const tableRows = document.querySelectorAll('.modern-table tbody tr');

    console.log('تصفية حسب الحالة:', selectedValue);

    tableRows.forEach(row => {
        if (selectedValue === 'all' || selectedValue === '') {
            row.style.display = '';
        } else {
            const statusCell = row.cells[6]; // عمود الحالة
            const statusText = statusCell.textContent.trim();

            let shouldShow = false;

            switch(selectedValue) {
                case 'completed':
                    shouldShow = statusText.includes('مكتملة');
                    break;
                case 'pending':
                    shouldShow = statusText.includes('معلقة');
                    break;
                case 'cancelled':
                    shouldShow = statusText.includes('ملغية');
                    break;
            }

            row.style.display = shouldShow ? '' : 'none';
        }
    });

    updateVisibleRowsCount();
}

function clearFilters() {
    // إعادة تعيين القوائم
    document.getElementById('status-filter').value = 'all';
    document.getElementById('payment-filter').value = 'all';
    document.getElementById('search-input').value = '';

    // إظهار جميع الصفوف
    const tableRows = document.querySelectorAll('.modern-table tbody tr');
    tableRows.forEach(row => {
        row.style.display = '';
    });

    updateVisibleRowsCount();
    console.log('تم مسح جميع التصفيات');
}

function updateVisibleRowsCount() {
    const tableRows = document.querySelectorAll('.modern-table tbody tr');
    const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');
    console.log(`عدد الفواتير المعروضة: ${visibleRows.length} من أصل ${tableRows.length}`);
}

// وظيفة البحث
function searchInvoices() {
    const searchInput = document.getElementById('search-input');
    const searchTerm = searchInput.value.toLowerCase().trim();
    const tableRows = document.querySelectorAll('.modern-table tbody tr');

    console.log('البحث عن:', searchTerm);

    tableRows.forEach(row => {
        if (searchTerm === '') {
            row.style.display = '';
        } else {
            const rowText = row.textContent.toLowerCase();
            const shouldShow = rowText.includes(searchTerm);
            row.style.display = shouldShow ? '' : 'none';
        }
    });

    updateVisibleRowsCount();
}

// تفعيل البحث عند الكتابة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', searchInvoices);
    }
});

// دالة إضافة أنماط CSS للنافذة المنبثقة
function addModalStyles() {
    // التحقق من وجود الأنماط مسبقاً
    if (document.getElementById('modal-styles')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'modal-styles';
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-content-modern {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 800px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalFadeIn 0.3s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header-modern h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal-body-modern {
            padding: 25px;
        }

        .form-modern {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .input-modern, .select-modern, .textarea-modern {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s, box-shadow 0.2s;
            font-family: 'Cairo', sans-serif;
        }

        .input-modern:focus, .select-modern:focus, .textarea-modern:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .products-section {
            border: 2px solid #f1f3f4;
            border-radius: 8px;
            padding: 20px;
            background-color: #fafbfc;
        }

        .products-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.1rem;
        }

        .totals-section {
            border: 2px solid #e8f5e8;
            border-radius: 8px;
            padding: 20px;
            background-color: #f8fff8;
        }

        .total-final {
            font-weight: bold;
            background-color: #f0f8ff;
            border-color: #4a90e2;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .btn-modern {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .modal-overlay {
                padding: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-modern {
                width: 100%;
                justify-content: center;
            }
        }
    `;

    document.head.appendChild(style);
}
