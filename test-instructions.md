# تعليمات اختبار مشكلة إضافة الحسابات

## المشكلة
الحسابات تُضاف بنجاح لكن لا تظهر في دليل الحسابات.

## الحلول المطبقة

### 1. تحسين دالة حفظ الحساب
- إضافة تسجيل مفصل للعمليات
- تحديث متعدد للجدول للتأكد من الظهور
- التحقق من حفظ البيانات في التخزين المحلي

### 2. إصلاح دالة تحميل الحسابات
- منع الكتابة فوق الحسابات الجديدة
- تحسين معالجة حسابات العملاء والموردين

### 3. إضافة أدوات تشخيص
- دالة تشخيص سريعة
- أزرار اختبار في الواجهة
- تسجيل مفصل في وحدة التحكم

## خطوات الاختبار

### الاختبار السريع (الموصى به)
1. افتح صفحة المحاسبة
2. انتقل إلى تبويب "دليل الحسابات"
3. اضغط على "اختبار البنك الفرنسي" (الزر الأزرق الجديد)
4. راقب وحدة التحكم (F12) للرسائل التفصيلية
5. تحقق من ظهور البنك الفرنسي في الجدول

### الاختبار اليدوي
1. افتح صفحة المحاسبة
2. انتقل إلى تبويب "دليل الحسابات"
3. اضغط على "حساب جديد"
4. املأ البيانات:
   - رقم الحساب: 1105
   - اسم الحساب: البنك الفرنسي
   - النوع: أصول
   - الرصيد: 50000
5. اضغط "حفظ الحساب"
6. تحقق من ظهور الحساب في الجدول

### الاختبار المتقدم
1. افتح وحدة التحكم (F12)
2. اضغط على "تشخيص سريع" في الواجهة
3. راقب الرسائل في وحدة التحكم
4. جرب إضافة الحساب مرة أخرى
5. راقب الرسائل التفصيلية

### اختبار وحدة التحكم
```javascript
// في وحدة التحكم، اكتب:
diagnoseAccountSystem()

// لاختبار البنك الفرنسي مباشرة:
testAddFrenchBank()

// لتحديث الجدول بالطريقة المبسطة:
updateAccountsTableSimple()

// لإضافة حساب اختبار:
quickAddAccountTest()

// لفحص الحسابات المحفوظة:
console.log(JSON.parse(localStorage.getItem('chartOfAccounts')))
```

## الرسائل المتوقعة

### عند النجاح:
- "🔄 بدء حفظ حساب جديد..."
- "📝 بيانات الحساب الجديد: {...}"
- "✅ تم إضافة الحساب إلى المصفوفة"
- "💾 تم حفظ الحسابات في التخزين المحلي"
- "✅ تم التحقق من حفظ الحساب"
- "✅ تم العثور على الحساب في الجدول"

### عند وجود مشكلة:
- "❌ رقم الحساب مكرر"
- "❌ فشل في حفظ الحساب"
- "❌ الحساب غير موجود في الجدول"

## الأزرار الجديدة

### في واجهة دليل الحسابات:
- **اختبار شامل**: يشغل جميع الاختبارات
- **اختبار إضافة**: يضيف حساب اختبار سريع
- **مسح الاختبارات**: يزيل حسابات الاختبار
- **تشخيص سريع**: يفحص حالة النظام
- **اختبار البنك الفرنسي**: يضيف البنك الفرنسي مباشرة (جديد)

## استكشاف الأخطاء

### إذا لم يظهر الحساب:
1. تحقق من وحدة التحكم للأخطاء
2. اضغط على "تشخيص سريع"
3. تحقق من التخزين المحلي:
   ```javascript
   localStorage.getItem('chartOfAccounts')
   ```
4. جرب إعادة تحميل الصفحة

### إذا ظهرت أخطاء JavaScript:
1. تحقق من تحميل جميع الملفات
2. تأكد من عدم وجود تضارب في الدوال
3. جرب مسح ذاكرة التخزين المؤقت

## الملفات المحدثة
- `accounting.html` - الصفحة الرئيسية مع التحسينات
- `js/account-system-test.js` - ملف الاختبارات
- `test-account-system.html` - صفحة اختبار مستقلة

## نصائح إضافية
- استخدم أرقام حسابات مختلفة لكل اختبار
- راقب وحدة التحكم دائماً أثناء الاختبار
- جرب الاختبار في متصفحات مختلفة
- تأكد من تمكين JavaScript في المتصفح
