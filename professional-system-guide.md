# دليل النظام الاحترافي لشجرة الحسابات

## نظرة عامة
تم إنشاء نظام دليل حسابات احترافي جديد من الصفر لحل مشاكل النظام القديم وتوفير تجربة مستخدم محسنة.

## المميزات الجديدة

### 🌟 **التصميم الاحترافي**
- واجهة مستخدم حديثة ومتجاوبة
- شجرة حسابات تفاعلية قابلة للطي والتوسيع
- ألوان مميزة لكل نوع حساب
- أيقونات واضحة ومعبرة

### 🔧 **الوظائف المتقدمة**
- **إضافة حسابات رئيسية وفرعية** بسهولة
- **البحث السريع** في الحسابات
- **توسيع/طي الكل** بنقرة واحدة
- **تصدير البيانات** بصيغة JSON
- **إحصائيات مباشرة** للحسابات والأرصدة

### 📊 **نظام الشجرة المتسلسل**
- **المستوى 0**: الحسابات الرئيسية (1، 2، 3، 4، 5)
- **المستوى 1**: الحسابات الفرعية (11، 12، 21، إلخ)
- **المستوى 2**: الحسابات التفصيلية (1101، 1102، إلخ)
- **المستوى 3+**: حسابات فرعية إضافية

## الحسابات الافتراضية

### 1️⃣ **الأصول (1)**
- **الأصول المتداولة (11)**
  - النقدية والبنوك (1101)
  - العملاء والذمم المدينة (1102)
- **الأصول الثابتة (12)**
  - الأراضي والمباني (1201)

### 2️⃣ **الخصوم (2)**
- **الخصوم المتداولة (21)**
  - الموردون والذمم الدائنة (2101)

### 3️⃣ **حقوق الملكية (3)**
- رأس المال (3101)

### 4️⃣ **الإيرادات (4)**
- إيرادات المبيعات (4101)

### 5️⃣ **المصروفات (5)**
- تكلفة البضاعة المباعة (5101)
- المصروفات الإدارية (5201)

## كيفية الاستخدام

### 🚀 **البدء السريع**
1. افتح `chart-of-accounts-professional.html`
2. ستجد شجرة الحسابات الافتراضية جاهزة
3. اضغط على "إضافة البنك الفرنسي" لاختبار النظام
4. استخدم "اختبار النظام" للتحقق من سلامة العمل

### ➕ **إضافة حساب جديد**
1. اضغط على "إضافة حساب"
2. املأ البيانات المطلوبة:
   - رقم الحساب (مطلوب)
   - اسم الحساب (مطلوب)
   - نوع الحساب (مطلوب)
   - الحساب الأب (اختياري)
   - الرصيد الافتتاحي
   - الوصف
3. اضغط "حفظ الحساب"

### 🔍 **البحث في الحسابات**
- استخدم مربع البحث في الأعلى
- ابحث برقم الحساب أو الاسم
- النتائج تظهر فوراً أثناء الكتابة

### 🌳 **التنقل في الشجرة**
- اضغط على السهم لتوسيع/طي الحساب
- استخدم "توسيع الكل" أو "طي الكل"
- الحسابات الفرعية تظهر بمسافة بادئة

### ⚙️ **إدارة الحسابات**
- **عرض**: لرؤية تفاصيل الحساب
- **تعديل**: لتغيير بيانات الحساب
- **إضافة فرعي**: لإضافة حساب فرعي
- **حذف**: لحذف الحساب والحسابات الفرعية

## الأزرار والوظائف

### 🔧 **شريط الأدوات**
- **البحث**: للبحث السريع في الحسابات
- **إضافة حساب**: لإضافة حساب جديد
- **توسيع الكل**: لتوسيع جميع الحسابات
- **طي الكل**: لطي جميع الحسابات
- **تصدير**: لتصدير البيانات
- **إعادة تعيين**: لاستعادة الحسابات الافتراضية

### 🧪 **أزرار الاختبار**
- **إضافة البنك الفرنسي**: يضيف البنك الفرنسي تلقائياً
- **اختبار النظام**: يفحص سلامة النظام ويعرض الإحصائيات

## الإحصائيات المباشرة

### 📊 **البطاقات الإحصائية**
- **إجمالي الحسابات**: العدد الكلي للحسابات
- **الحسابات الرئيسية**: عدد الحسابات في المستوى الأول
- **الحسابات الفرعية**: عدد الحسابات الفرعية
- **إجمالي الأرصدة**: مجموع جميع الأرصدة

## المميزات التقنية

### 💾 **التخزين**
- حفظ تلقائي في التخزين المحلي
- استرداد البيانات عند إعادة تحميل الصفحة
- نسخ احتياطية آمنة

### 🎨 **التصميم المتجاوب**
- يعمل على جميع أحجام الشاشات
- تصميم متكيف للهواتف والأجهزة اللوحية
- ألوان وأيقونات واضحة

### ⚡ **الأداء**
- تحميل سريع للبيانات
- تحديث فوري للواجهة
- بحث سريع ومتقدم

## حل مشكلة البنك الفرنسي

### ✅ **الحل الجديد**
1. افتح النظام الاحترافي
2. اضغط على "إضافة البنك الفرنسي"
3. سيتم إضافة الحساب تلقائياً تحت "النقدية والبنوك"
4. ستظهر رسالة تأكيد النجاح
5. الحساب سيظهر فوراً في الشجرة

### 🔍 **التحقق من النجاح**
- ابحث عن "1105" أو "البنك الفرنسي"
- تحقق من الإحصائيات (ستزيد بحساب واحد)
- وسع حساب "النقدية والبنوك" لرؤية البنك الفرنسي

## المقارنة مع النظام القديم

| الميزة | النظام القديم | النظام الجديد |
|--------|---------------|---------------|
| سهولة الاستخدام | معقد | بسيط وواضح |
| إضافة الحسابات | مشاكل في الظهور | يعمل بشكل مثالي |
| التصميم | قديم | حديث واحترافي |
| البحث | محدود | سريع ومتقدم |
| الشجرة | مشاكل في العرض | تفاعلية ومرنة |
| الإحصائيات | غير متوفرة | مباشرة ومفصلة |
| التصدير | غير متوفر | متوفر بصيغة JSON |

## نصائح للاستخدام الأمثل

### 📝 **تنظيم الحسابات**
- استخدم نظام ترقيم متسلسل
- اجعل أرقام الحسابات الرئيسية بسيطة (1، 2، 3...)
- استخدم أرقام فرعية منطقية (11، 12، 111، 112...)

### 🔍 **البحث الفعال**
- ابحث برقم الحساب للدقة
- ابحث بالاسم للمرونة
- استخدم كلمات مفتاحية قصيرة

### 💾 **النسخ الاحتياطي**
- استخدم "تصدير" بانتظام لحفظ البيانات
- احتفظ بنسخ احتياطية خارجية
- اختبر استرداد البيانات دورياً

## الدعم والمساعدة

### 🆘 **في حالة المشاكل**
1. اضغط على "اختبار النظام" أولاً
2. تحقق من وحدة التحكم للأخطاء
3. جرب "إعادة تعيين" لاستعادة الحالة الافتراضية
4. تأكد من تمكين JavaScript في المتصفح

### 📞 **الحصول على المساعدة**
- راجع هذا الدليل أولاً
- تحقق من رسائل الأخطاء في وحدة التحكم
- جرب النظام في متصفح مختلف

---

**النظام الاحترافي جاهز للاستخدام ويحل جميع مشاكل النظام القديم!** 🎉
